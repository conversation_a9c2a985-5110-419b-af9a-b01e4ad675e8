package com.prinova.messagepoint.query.handler;

import com.prinova.messagepoint.model.audit.AuditEvent;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.query.DefaultPage;
import com.prinova.messagepoint.query.IPage;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class ObjectTypeHandler implements PostQueryHandler {

	private boolean isOrderAsc;
	
	public boolean isOrderAsc() {
		return isOrderAsc;
	}

	public void setOrderAsc(boolean isOrderAsc) {
		this.isOrderAsc = isOrderAsc;
	}
	
	@SuppressWarnings("unchecked")
	public IPage handle(IPage page) {
		List<AuditEvent> items = (List<AuditEvent>)page.getList();

		Collections.sort(items, new Comparator<>() {
            public int compare(AuditEvent ae1, AuditEvent ae2) {
                String type1 = (new AuditObjectType(ae1.getObjectType())).getDisplayText();
                String type2 = (new AuditObjectType(ae2.getObjectType())).getDisplayText();
                if (isOrderAsc()) {
                    return type1.compareTo(type2);
                } else {
                    return type2.compareTo(type1);
                }
            }
        });
		
        ((DefaultPage)page).setList(items);
		return page;
	}
}
