package com.prinova.messagepoint.controller.tpadmin;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.TouchpointAdminEvents;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.font.ParagraphStyle;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.tpadmin.BulkUpdateZoneStyleService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.NestedPageUtils;

public class ZoneParaStylesEditController extends MessagepointController {

	private static final Log log = LogUtil.getLog(ZoneParaStylesEditController.class);

	public static final String REQ_PARAM_DOCUMENT_ID_PARAM 	= "documentId";
	public static final String REQ_PARAM_ZONE_ID_PARAM 		= "zoneId";
	public static final String REQ_PARAM_SELECTED_IDS_PARAM = "selectedIds";

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		String selectedIdsS = ServletRequestUtils.getStringParameter(request, REQ_PARAM_SELECTED_IDS_PARAM, "");
		
		if (!selectedIdsS.isEmpty()) {
			List<ParagraphStyle> availableParaStyles = ParagraphStyle.findAll();
			referenceData.put("availableParaStyles", availableParaStyles);
		}
		
		referenceData.put("isBulkSelect", selectedIdsS.contains("_"));
		
		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		String selectedIdsS = ServletRequestUtils.getStringParameter(request, REQ_PARAM_SELECTED_IDS_PARAM, "");
		ZoneParaStylesEditWrapper command;
		if(this.getSelectedIds(selectedIdsS).size() == 1){
			Zone zone = Zone.findById(this.getSelectedIds(selectedIdsS).iterator().next());
			command = new ZoneParaStylesEditWrapper(zone);
		}else{
			command = new ZoneParaStylesEditWrapper();
		}
		return command;
    }

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

		AnalyticsEvent<TouchpointAdminEvents> analyticsEvent = AnalyticsUtil.requestFor(TouchpointAdminEvents.ZoneParagraphStylesEdit)
				.setAction(Actions.Update);

		try {
			long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID_PARAM, -1);
			long tpSelectionId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_ZONE_ID_PARAM, -1);
			String selectedIdsS = ServletRequestUtils.getStringParameter(request, REQ_PARAM_SELECTED_IDS_PARAM, "");
			ZoneParaStylesEditWrapper command = (ZoneParaStylesEditWrapper)commandObj;

			ServiceExecutionContext context = BulkUpdateZoneStyleService.createContext(this.getSelectedIds(selectedIdsS), null, command.getSelectedParaStyleIds(), null, -1, -1, -1, command.isOverridden());

			Service service = MessagepointServiceFactory.getInstance().lookupService(BulkUpdateZoneStyleService.SERVICE_NAME, BulkUpdateZoneStyleService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(BulkUpdateZoneStyleService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				sb.append(" selected Workgroups were not saved. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
				return super.showForm(request, response, errors);
			} else {
				Map<String, Object> parms = new HashMap<>();
				parms.put(REQ_PARAM_DOCUMENT_ID_PARAM, documentId);
				parms.put(REQ_PARAM_ZONE_ID_PARAM, tpSelectionId);
				parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
				return new ModelAndView( new RedirectView("../frameClose.jsp"), parms );
			}
		} finally {
			analyticsEvent.send();
		}
	}
	
	/**
	 * Convert selected id string to set
     */
	private List<Long> getSelectedIds(String selectedIdsS){		
		List<Long> selectionIds = new ArrayList<>();
		if(selectedIdsS != null && !selectedIdsS.isEmpty()){
			for(String idS : selectedIdsS.split("_")){
				selectionIds.add(Long.valueOf(idS));
			}
		}
		return selectionIds;
	}
}