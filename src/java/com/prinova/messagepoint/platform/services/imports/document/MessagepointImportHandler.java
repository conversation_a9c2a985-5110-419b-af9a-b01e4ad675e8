package com.prinova.messagepoint.platform.services.imports.document;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import com.prinova.messagepoint.model.MessagePointModel;
import org.xml.sax.Attributes;
import org.xml.sax.SAXException;

import com.prinova.messagepoint.model.task.TPImportTask;
import com.prinova.messagepoint.util.LogUtil;

/// This is a base class for an messagepoint VO for a SAX handler.  The subclass
/// will provide the storage for the data, while the base class provides the
/// ability to call methods on specific tag and attribute combinations.
/// The method lookup works with java's reflection to find the method to invoke.
/// This class will also convert the data to the type for the "setter" invocation
/// Note that any subclasses must have a constructor with no arguments
public abstract class MessagepointImportHandler extends MessagePointModel
{
    // Maps (tag and attribute) to a (method and the type for the argument)
    private Map<Pair<String, String>, Pair<String, Class<?>>> attrMap;
    private Class<?extends MessagepointImportHandler> handlerClazz;  // what
    private MessagepointSAXImportHandler parent = null;
    private TPImportTask tpImportTask = null;
    private String guid;
    private String dna;

    protected MessagepointImportHandler( Class<?extends MessagepointImportHandler> subclazz )
    {
        attrMap = new HashMap<>();
        handlerClazz = subclazz;
    }

    // Every child class must be able to handle children, but they don't
    // have to do anything with them.  Could this be done better?
    public abstract void addChild( MessagepointImportHandler child );

    public void startElement(
            String uri,
            String localName,
            String name,
            Attributes attributes )
    throws SAXException
    {
        // do nothing by default.  Can be overridden in subclass
    }

    public void endElement(
            String uri,
            String localName,
            String name)
    throws SAXException
    {
        // do nothing by default.  Can be overridden in subclass
    }

    public final void characters(
            String chars,
            String curTag )
    throws SAXException
    {
        setAttribute(curTag, "", chars );
    }

    /// Will cleanup the unneeded data for this class, because it will not 
    /// be processing any more data.  This data can be GC'ed. 
    public final void done()
    {
        attrMap.clear();
        handlerClazz = null;
        doneProcessing();
    }

    // can be overridden by children so that they can do work as soon as the data is ready.
    protected void doneProcessing()
    {
    }

    /// Maps a tag and attribute to a method to invoke and the type that the
    /// argument should be converted to.
    protected final void setAttrMethod(
            String tag,
            String attr,
            String method,
            Class<?> type)
    {
    	Pair<String, String> key = new Pair<>(tag, attr);
        if ( attrMap.containsKey(key))
            return;
        
        attrMap.put(key, new Pair<>(method, type));
    }

    public final void handleAttributes(
            String tagName,
            Attributes attributes )
    {
        if ( attrMap.isEmpty() )
            return;
        
        int length = attributes.getLength();
        for( int index = 0; index != length; ++index )
        {
            setAttribute(tagName, attributes.getQName(index), attributes.getValue(index));
        }
    }

    // used to set on tag value, and for attribute values.
    private void setAttribute(
            String tagName,
            String attrName,
            String attrValue )
    {
    	Pair<String, String> key = new Pair<>(tagName, attrName);
        if ( !attrMap.containsKey(key) )
        {
            if (!attrName.isEmpty() &&
                 !this.getClass().equals(NullImportHandler.class) )
            {
                // LogUtil.getLog(this.getClass()).warn("Unhandled attribute " + attrName + " on tag " + tagName);
            }
            return;
        }
        
        try
        {
            Pair<String, Class<?>> method = attrMap.get(key);
            Class<?> argType = method.getSecond();

            String methodName = method.getFirst();
            Method setAttrMethod = handlerClazz.getMethod(methodName, argType);

            if ( argType.equals(Long.class) )
            {
            	if (!attrValue.isEmpty())
            		setAttrMethod.invoke(this, Long.parseLong(attrValue));
            }
            else if ( argType.equals(String.class) )
            {
                setAttrMethod.invoke(this, attrValue);
            }
            else if ( argType.equals(Integer.class) )
            {
            	if (!attrValue.isEmpty())
            		setAttrMethod.invoke(this, Integer.parseInt(attrValue));
            }
            else if ( argType.equals(Boolean.class) )
            {
                setAttrMethod.invoke(this, attrValue.equals("true"));
            }
            else
            {
            	LogUtil.getLog(handlerClazz).warn("Cannot find the correct type to convert attribute data for tag `" + tagName + "` attribute `" + attrName + "`.  Attribute not processed");
            }
        }
        catch( Exception ex )
        {
            LogUtil.getLog(this.getClass()).error("Caught exception", ex);
        }
    }
    
    public void setParent(MessagepointSAXImportHandler parent)
    {
    	this.parent = parent;
    }
    
    public void setDataSourceAssociationId( long dataSourceAssociationId )
    {
    	if (parent != null)
    		parent.setDataSourceAssociationId(dataSourceAssociationId);
    }

    public long getDataSourceAssociationId()
    {
    	if (parent != null)
    		return parent.getDataSourceAssociationId();
    	
    	return -1L;
    }

    public boolean isGuidEmpty() {
        return guid == null || guid.isEmpty();
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public boolean isDNAEmpty() {
        return dna == null || dna.isEmpty();
    }

    public String getDNA() {
        return dna;
    }

    public void setDNA(String dna) {
        this.dna = dna;
    }

    public boolean getImportOnlyDataCollection()
    {
    	if (parent != null)
    		return parent.getImportOnlyDataCollection();
    	
    	return false;
    }

    public boolean getUpdateDataSourceAndVariables()
    {
    	if (parent != null)
    		return parent.getUpdateDataSourceAndVariables();
    	
    	return false;
    }

    public Set<Long> getDataCollectionVariables()
    {
    	if (parent != null)
    		return parent.getDataCollectionVariables();
    	
    	return null;
    }

    public MessagepointImportHandler getRoot()
    {
    	if (parent != null)
    		return parent.getRoot();
    	
    	return null;
    }

    // Allows attributes to be ignored to silence warnings.
    public final void setIgnored( String val )
    {
    }

    protected void printMap()
    {
    	for( Pair<String, String> key : attrMap.keySet() )
    	{
    		Pair<String, Class<?>> value = attrMap.get(key);
    		LogUtil.getLog(handlerClazz).info( "Tag `" + key.getFirst() + "` attribute `" + key.getSecond() +
    											"` ==> " + value.getFirst() + "(" + value.getSecond().getName() + ")");
    	}
    }

	public TPImportTask getTpImportTask() {
		return tpImportTask;
	}

	public void setTpImportTask(TPImportTask tpImportTask) {
		this.tpImportTask = tpImportTask;
	}
}
