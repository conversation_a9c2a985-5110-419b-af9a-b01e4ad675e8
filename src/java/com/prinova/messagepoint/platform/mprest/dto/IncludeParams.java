package com.prinova.messagepoint.platform.mprest.dto;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

public class IncludeParams {
    private String[] include;

    public IncludeParams() {
    }

    public IncludeParams(String[] include) {
        this.include = include;
    }

    public IncludeParams include(String include) {
        if(StringUtils.isNotBlank(include) && (this.include == null || this.include.length == 0)){
            this.include = new String[]{include.trim()};
        } else if (this.include != null && this.include.length > 0) {
            this.include = Arrays.copyOf(this.include, this.include.length + 1);
            this.include[this.include.length - 1] = include.trim();
        }

        return this;
    }

    public String[] getInclude() {
        return include;
    }

    public void setInclude(String[] include) {
        this.include = include;
    }

    public boolean hasInclude(String include){
        if(StringUtils.isBlank(include)){
            return false;
        }

        return this.include != null && this.include.length > 0 && Arrays.asList(this.include).contains(include.trim());
    }
}
