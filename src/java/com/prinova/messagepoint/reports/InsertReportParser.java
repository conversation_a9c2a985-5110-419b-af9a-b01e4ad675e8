package com.prinova.messagepoint.reports;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.logging.Log;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;

import com.prinova.messagepoint.batch.SplitReportFile;
import com.prinova.messagepoint.model.deliveryevent.DeliveryEvent;
import com.prinova.messagepoint.model.deliveryevent.Job;
import com.prinova.messagepoint.model.insert.Insert;
import com.prinova.messagepoint.reports.model.report.insert.BatchStatistics;
import com.prinova.messagepoint.reports.model.report.insert.JobInsert;
import com.prinova.messagepoint.reports.model.report.insert.JobInsertRecipient;
import com.prinova.messagepoint.reports.model.report.insert.JobInsertRecipientReport;
import com.prinova.messagepoint.reports.model.report.insert.JobInsertSchedule;
import com.prinova.messagepoint.reports.model.report.insert.JobInsertScheduleStatistic;
import com.prinova.messagepoint.reports.model.report.insert.JobInsertStatistic;
import com.prinova.messagepoint.reports.model.report.insert.JobRateSchedule;
import com.prinova.messagepoint.reports.model.report.insert.JobRateScheduleStatistic;
import com.prinova.messagepoint.reports.model.report.insert.JobWeightLimit;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;

public class InsertReportParser
{
	// List of different records that can occur in the file
	private final String RECORD_HEADER = "00";
	private final String RECORD_INSERT_SCHEDULE_DATA = "01";
	private final String RECORD_INSERT_DATA = "02";
	private final String RECORD_RATE_SCHEDULE_DATA = "03";
	private final String RECORD_RECIPIENT = "10";
	private final String RECORD_INSERT = "11";
	private final String RECORD_RECIPIENT_SUMMARY = "12";
	private final String RECORD_GLOBAL_SUMMARY = "20";
	private final String RECORD_INSERT_STAT = "21";
	private final String RECORD_INSERT_SCHED_STAT = "22";
	private final String RECORD_RATE_SCHED_STAT = "23";
	private final String RECORD_BATCH_STAT = "24";
	private final String RECORD_TERMINATION = "99";
	
	// Different fields that can occur, broken into record;
	private final String FIELD_JOB_ID = "001";
	private final String FIELD_BATCH_ID = "002";
	
	private final String FIELD_INSERT_SCHEDULE_ID = "310";
	private final String FIELD_INSERT_SCHEDULE_NAME = "311";
	private final String FIELD_INSERT_SCHEDULE_INSERT = "312";
//	private final String FIELD_INSERT_SCHEDULE_INSERT_BIN = "313";
	private final String FIELD_INSERT_SCHEDULE_RATESHEET = "314";
	private final String FIELD_INSERT_SCHEDULE_THRESHOLD = "315";
	private final String FIELD_INSERT_SCHEDULE_EXTERNAL_ID = "316";
	
	private final String FIELD_INSERT_ID = "320";
	private final String FIELD_INSERT_NAME = "321";
	private final String FIELD_INSERT_WEIGHT = "322";
	private final String FIELD_INSERT_EXTERNAL_ID = "323";
	private final String FIELD_INSERT_DELIVERY_TYPE = "324";

	private final String FIELD_RATE_SCHEDULE_ID = "330";
	private final String FIELD_RATE_SCHEDULE_NAME = "331";
	private final String FIELD_ENVELOPE_WEIGHT = "332";
	private final String FIELD_ENVELOPE_NAME = "333";
	private final String FIELD_WEIGHT_LIMIT_UNITS = "334";
	private final String FIELD_LIMIT = "335";
	private final String FIELD_COST = "336";
	
	private final String FIELD_RECIPIENT_ID = "110";
	private final String FIELD_RECIPIENT_INSERT_SCHEDULE = "111";
	private final String FIELD_RECIPIENT_RATE_SCHEDULE = "112";
	private final String FIELD_RECIPIENT_DOCUMENT_ID = "113";
	
	private final String FIELD_RECIPIENT_INSERT_ID = "120";
	private final String FIELD_RECIPIENT_INSERT_DELIVERED = "121";
	
	private final String FIELD_SUMMARY_INSERT_COUNT = "130";
	private final String FIELD_SUMMARY_PAGE_COUNT = "131";
	private final String FIELD_SUMMARY_DELIVERY_WEIGHT = "132";
	private final String FIELD_SUMMARY_DELIVERY_COST = "134";
	
	private final String FIELD_INSERT_STAT_INSERT = "210";
	private final String FIELD_INSERT_STAT_QUALIFIED = "211";
	private final String FIELD_INSERT_STAT_DELIVERED = "212";
	
	private final String FIELD_INS_SCHED_STAT_ID = "220";
	private final String FIELD_INS_SCHED_STAT_QUALIFIED = "221";
	
	private final String FIELD_RATE_STAT_ID = "230";
	private final String FIELD_RATE_STAT_USED = "231";
	
	private final String FIELD_BATCH_STAT_RECIPIENTS = "240";
	private final String FIELD_BATCH_STAT_DOCUMENT = "241";
	
	private Long jobId;
	private Long batchId;
	private SplitReportFile reader;
	
	private BatchStatistics batchStat = null;
	
	private Boolean done = Boolean.FALSE;
	
	private static class TleDecodedItem
	{
		public String field;
		public String data;
		public int end;
		public boolean last;
		
		public Long dataLong()
		{
			return Long.parseLong(data);
		}
	}

	public InsertReportParser( long jobId, int batchId, SplitReportFile reader)
	{
		this.jobId = Long.valueOf(jobId);
		this.batchId = Long.valueOf(batchId);
		this.reader = reader;

		batchStat = new BatchStatistics();
		batchStat.setJobId(jobId);
		batchStat.setBatchId(batchId);

		Job job = HibernateUtil.getManager().getObject(Job.class, jobId); 
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("job", job));
		DeliveryEvent de = HibernateUtil.getManager().getObjectUnique(DeliveryEvent.class, critList);

		if ( de != null )
		{
			batchStat.setJobType(de.getEventTypeId());
			batchStat.setMonthAndYear(de.getScheduledTime());
			batchStat.setDocumentId(de.getItem().getDocumentId());
		}

		HibernateUtil.getManager().saveObject(batchStat, true);
	}

	public Boolean isDone()
	{
		return done;
	}
	
	// Every call to this method should be followed by a call to "isDone()"
	public Object parse() throws Exception
	{
		String line = reader.GetLine();

		if ( line == null )
		{
			done = true;
			return null;
		}

		if ( line.startsWith(RECORD_RECIPIENT) )
			return parseRecipient(line);
		else if ( line.startsWith(RECORD_INSERT_SCHEDULE_DATA) )
			return parseInsertScheduleData(line);
		else if ( line.startsWith(RECORD_INSERT_DATA) )
			return parseInsertData(line);
		else if ( line.startsWith(RECORD_RATE_SCHEDULE_DATA) )
			return parseRateScheduleData(line);
		else if ( line.startsWith(RECORD_GLOBAL_SUMMARY) )
			return parseGlobalSummary(line);
		else if ( line.startsWith(RECORD_INSERT_SCHED_STAT) )
			return parseScheduleStat(line);
		else if (line.startsWith(RECORD_INSERT_STAT) )
			return parseInsertStat(line);
		else if (line.startsWith(RECORD_RATE_SCHED_STAT) )
			return parseRateScheduleStat(line);
		else if (line.startsWith(RECORD_BATCH_STAT) )
			return parseBatchStatistics(line);
		else if ( line.startsWith(RECORD_HEADER) )
			return parseHeader(line);
		else if ( line.startsWith(RECORD_TERMINATION) )
			done = Boolean.TRUE;
		else
		{
			// @TODO, change to LogUtil
			LogUtil.getLog(this.getClass()).warn("Unknown record type indicator `" + line.substring(0,2) + "`");
		}
		return null;
	}

	private TleDecodedItem parseItem( String line, int start )
	{
		TleDecodedItem item = new TleDecodedItem();
		item.field = line.substring(start, start+3);

		String sLenLen = line.substring(start+3, start+3+1);
		int lenlen = Integer.parseInt(sLenLen);
		String sLen = line.substring(start+4, start+4+lenlen);
		int len = Integer.parseInt(sLen);

		item.data = line.substring(start+4+lenlen, start+4+lenlen+len);
		item.end = start + 3 + 1 + lenlen + len;
		item.last = line.length() == item.end;

		return item;
	}
	
	private JobInsertRecipient parseRecipient( String line ) throws Exception
	{
		TleDecodedItem item;
		JobInsertRecipient recip = new JobInsertRecipient();

		recip.setBatchStatistics(batchStat);
		
		int place = 2;
		do 
		{
			item = parseItem(line, place);
			place = item.end;

			if (item.field.equals(FIELD_RECIPIENT_ID) )
			{
				recip.setRecipientId(item.data);
			}
			else if ( item.field.equals(FIELD_RECIPIENT_INSERT_SCHEDULE) )
			{
				Long itemId = item.dataLong();
				recip.setInsertScheduleId(itemId);
			}
			else if ( item.field.equals(FIELD_RECIPIENT_RATE_SCHEDULE) )
			{
				long itemId = item.dataLong();
				recip.setRateScheduleId(itemId);
			}
			else if ( item.field.equals(FIELD_RECIPIENT_DOCUMENT_ID) )
			{
				long docId = item.dataLong();
				recip.setDocumentId(docId);
			}
		} while ( !item.last );

		// Parse each qualified insert record and add item to parent
		while(true)
		{
			line = reader.GetLine();
			if ( line.startsWith(RECORD_INSERT) )
			{
				JobInsertRecipientReport jirr = parseInsert(recip, line);
				recip.getInserts().add(jirr);
			}
			else if ( line.startsWith(RECORD_RECIPIENT_SUMMARY) )
			{
				parseRecipientSummary(recip, line);
				break;
			}
		}

		return recip;
	}

	private JobInsertRecipientReport parseInsert(JobInsertRecipient recip, String line)
	{
		TleDecodedItem item;
		int place = 2;

		JobInsertRecipientReport jirr = new JobInsertRecipientReport();

		do
		{
			item = parseItem(line, place);
			place = item.end;

			if ( item.field.equals(FIELD_RECIPIENT_INSERT_ID) )
			{
				Long id = item.dataLong();
				jirr.setInsertId(id);
			}
			else if (item.field.equals(FIELD_RECIPIENT_INSERT_DELIVERED) )
			{
				boolean delivered = item.data.equals("Y");
				jirr.setDelivered(delivered);
			}
		} while (!item.last);
		
		return jirr;
	}
	
	private void parseRecipientSummary( JobInsertRecipient recip, String line )
	{
		TleDecodedItem item;
		int place = 2;

		do
		{
			item = parseItem(line, place);
			place = item.end;

			if ( item.field.equals(FIELD_SUMMARY_INSERT_COUNT) )
			{
				Long count = item.dataLong();
				// @TODO validate count == recip.inserts
			}
			else if ( item.field.equals(FIELD_SUMMARY_PAGE_COUNT) )
			{
				Long count = item.dataLong();
				recip.setSheetCount(count);
			}
			else if ( item.field.equals(FIELD_SUMMARY_DELIVERY_WEIGHT) )
			{
				Long weight = item.dataLong();
				recip.setDeliveryWeight(weight);
				// @TODO compare weight to weight of inserts and validate at end
			}
			else if ( item.field.equals(FIELD_SUMMARY_DELIVERY_COST) )
			{
				Long cost = item.dataLong();
				recip.setDeliveryCost(cost);
			}			
		} while ( !item.last );
	}
	
	private Object parseHeader( String line ) throws Exception
	{
		TleDecodedItem item;
		int place = 2;

		do
		{
			item = parseItem(line, place);
			place = item.end;
			
			if ( item.field.equals(FIELD_JOB_ID) )
			{
				Long jobId = item.dataLong();
				if ( !jobId.equals(this.jobId) )
					throw new RuntimeException("Mismatching job number");
			}
			else if ( item.field.equals(FIELD_BATCH_ID) )
			{
				Long batchId = item.dataLong();
				if ( !batchId.equals(this.batchId) && batchId.intValue() != -1 )
					throw new RuntimeException("Mismatching batch number " + batchId + ":" + this.batchId);
			}
		} while ( !item.last );
		
		return null;
	}
	
	private JobInsertSchedule parseInsertScheduleData( String line )
	{
		if ( batchId != 1 )
			return null;
		
		JobInsertSchedule jis = new JobInsertSchedule();
		
		TleDecodedItem item;
		int place = 2;
		long threshold = -1;
		
		jis.setJobId(jobId);
		
		do
		{
			item = parseItem(line, place);
			place = item.end;
			
			if ( item.field.equals(FIELD_INSERT_SCHEDULE_ID) )
			{
				Long id = item.dataLong();
				jis.setInsertScheduleId(id);
			}
			else if ( item.field.equals(FIELD_INSERT_SCHEDULE_NAME) )
			{
				jis.setName(item.data);
			}
			else if ( item.field.equals(FIELD_INSERT_SCHEDULE_INSERT) )
			{
				Long id = item.dataLong();
				jis.getInsertIds().add(id);
			}
			else if ( item.field.equals(FIELD_INSERT_SCHEDULE_THRESHOLD) )
			{
				threshold = item.dataLong();
			}
			else if ( item.field.equals(FIELD_INSERT_SCHEDULE_RATESHEET) )
			{
				Long rateSheetId = item.dataLong();
				jis.getRateSheets().put(threshold, rateSheetId);
			}
			else if ( item.field.equals(FIELD_INSERT_SCHEDULE_EXTERNAL_ID) )
			{
				jis.setScheduleId(item.data);
			}
			
		} while ( !item.last );
		
		return jis;
	}
	
	private JobInsert parseInsertData( String line )
	{
		if ( batchId != 1 )
			return null;
		
		JobInsert ji = new JobInsert();
		
		TleDecodedItem item;
		int place = 2;

		ji.setJobId(jobId);
		
		do
		{
			item = parseItem(line, place);
			place = item.end;
			
			if ( item.field.equals(FIELD_INSERT_ID) )
			{
				Long id = item.dataLong();
				ji.setInsertId(id);
			}
			else if ( item.field.equals(FIELD_INSERT_NAME) )
			{
				ji.setName(item.data);
			}
			else if ( item.field.equals(FIELD_INSERT_WEIGHT) )
			{
				Long weight = item.dataLong();
				ji.setInsertWeight(weight);
			}
			else if ( item.field.equals(FIELD_INSERT_EXTERNAL_ID) )
			{
				ji.setInsertExternalId(item.data);
			}
			else if ( item.field.equals(FIELD_INSERT_DELIVERY_TYPE) )
			{
				ji.setDeliveryType(item.data);
			}
		} while ( !item.last );
		
		return ji;
	}
	
	private JobRateSchedule parseRateScheduleData( String line )
	{
		if ( batchId != 1 )
			return null;
		
		JobRateSchedule jrs = new JobRateSchedule();
		
		TleDecodedItem item;
		int place = 2;

		jrs.setJobId(jobId);
		JobWeightLimit currentLimit = null;
		
		do
		{
			item = parseItem(line, place);
			place = item.end;

			if ( item.field.equals(FIELD_RATE_SCHEDULE_ID) )
			{
				Long id = item.dataLong();
				jrs.setRateScheduleId(id);
			}
			else if ( item.field.equals(FIELD_RATE_SCHEDULE_NAME) )
			{
				jrs.setName(item.data);
			}
			else if ( item.field.equals(FIELD_ENVELOPE_WEIGHT) )
			{
				Long value = item.dataLong();
				jrs.setEnvelopeWeight(value);
			}
			else if ( item.field.equals(FIELD_ENVELOPE_NAME) )
			{
				jrs.setEnvelopeName(item.data);
			}
			else if ( item.field.equals(FIELD_WEIGHT_LIMIT_UNITS) )
			{
				if ( item.data.equals("g") )
					jrs.setWeightLimitsUnits(Insert.INSERT_WEIGHT_UNIT_GRAM);
				else
					jrs.setWeightLimitsUnits(Insert.INSERT_WEIGHT_UNIT_OUNCE);
			}
			else if ( item.field.equals(FIELD_LIMIT) )
			{
				if ( currentLimit == null )
				{
					currentLimit = new JobWeightLimit();
					jrs.getWeightLimits().add(currentLimit);					
				
					Long limit = item.dataLong();
					currentLimit.setWeightLimit(limit);
				}
				else
				{
					Long limit = item.dataLong();
					currentLimit.setWeightLimit(limit);

					currentLimit = null;
				}
			}
			else if ( item.field.equals(FIELD_COST) )
			{
				if ( currentLimit == null )
				{
					currentLimit = new JobWeightLimit();
					jrs.getWeightLimits().add(currentLimit);
				
					Long cost = item.dataLong();
					currentLimit.setCost(cost);
				}
				else
				{
					Long cost = item.dataLong();
					currentLimit.setCost(cost);

					currentLimit = null;
				}
			}
		} while (!item.last);
		
		return jrs;
	}
	
	private Object parseGlobalSummary( String line )
	{
		// unhandled for now.  This record can be used to validate that all other
		// records received "add up"
		return null;
	}
	
	private JobInsertScheduleStatistic parseScheduleStat( String line )
	{
		JobInsertScheduleStatistic iss = new JobInsertScheduleStatistic();
		TleDecodedItem item;
		int place = 2;

		iss.setBatchStatistics(batchStat);
		
		do
		{
			item = parseItem(line, place);
			place = item.end;
			
			if ( item.field.equals(FIELD_INS_SCHED_STAT_ID) )
			{
				Long itemId = item.dataLong();
				iss.setInsertScheduleId(itemId);
			}
			else if ( item.field.equals(FIELD_INS_SCHED_STAT_QUALIFIED) )
			{
				Long count = item.dataLong();
				iss.setQualified(count);
			}
		} while (!item.last);
		
		return iss;
	}
	
	private JobInsertStatistic parseInsertStat( String line )
	{
		JobInsertStatistic jis = new JobInsertStatistic();
		TleDecodedItem item;
		int place = 2;

		jis.setBatchStatistics(batchStat);
		
		do
		{
			item = parseItem(line, place);
			place = item.end;
			
			if ( item.field.equals(FIELD_INSERT_STAT_INSERT) )
			{
				Long itemId = item.dataLong();
				jis.setInsertId(itemId);
			}
			else if ( item.field.equals(FIELD_INSERT_STAT_QUALIFIED) )
			{
				Long count = item.dataLong();
				jis.setQualified(count);
			}
			else if ( item.field.equals(FIELD_INSERT_STAT_DELIVERED) )
			{
				Long count = item.dataLong();
				jis.setDelivered(count);
			}
		} while( !item.last);
		
		return jis;
	}
	
	private JobRateScheduleStatistic parseRateScheduleStat( String line )
	{
		JobRateScheduleStatistic jrss = new JobRateScheduleStatistic();
		TleDecodedItem item;
		int place = 2;

		jrss.setBatchStatistics(batchStat);
		
		do
		{
			item = parseItem(line, place);
			place = item.end;
			
			if ( item.field.equals(FIELD_RATE_STAT_ID) )
			{
				Long itemId = item.dataLong();
				jrss.setRateScheduleId(itemId);
			}
			else if ( item.field.equals(FIELD_RATE_STAT_USED) )
			{
				Long count = item.dataLong();
				jrss.setQualified(count);
			}
		} while( !item.last );
		
		return jrss;
	}

	private BatchStatistics parseBatchStatistics( String line )
	{
		TleDecodedItem item;
		int place = 2;

		do
		{
			item = parseItem(line, place);
			place = item.end;
			
			if ( item.field.equals(FIELD_BATCH_STAT_RECIPIENTS) )
			{
				Long count = item.dataLong();
				batchStat.setRecipients(count);
			}
			else if ( item.field.equals(FIELD_BATCH_STAT_DOCUMENT) )
			{
				Long documentId = item.dataLong();
				batchStat.setDocumentId(documentId);
			}
		} while( !item.last );

		HibernateUtil.getManager().saveObjects(batchStat);
		
		return null;
	}
	
	// simple test harness with some generated data. 
	public static void main( String[] args )
	{
		Log log = LogUtil.getLog(InsertReportParser.class);
		try
		{
			long jobId = 53;
			int batchId = 1;
			SplitReportFile srf = new SplitReportFile("home/andrewl/MessagepointQE/working/qa/job53/", "CombinedMsgDelivery.dat", jobId, batchId, "0");
			
			InsertReportParser rp = new InsertReportParser(jobId, batchId, srf);
			while( !rp.isDone() )
			{
				Object o = rp.parse();
				Boolean done = rp.isDone();
				
				if ( o != null )
					log.info(o.toString());
				else if ( !done && o == null )
					log.info("No object returned");
			}
			
		} 
		catch ( Exception ex )
		{
			log.error("Caught exception: " + ex);
			ex.printStackTrace();
		}
	}
}
