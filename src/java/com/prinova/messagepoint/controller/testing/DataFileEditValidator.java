package com.prinova.messagepoint.controller.testing;

import com.prinova.messagepoint.controller.testing.DataFileEditController.DataFileCommand;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.testing.DataFile;
import com.prinova.messagepoint.model.wrapper.AsyncDataFileListVO;
import com.prinova.messagepoint.model.wrapper.AsyncDataFileListWrapper;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.springframework.validation.Errors;

import java.util.function.Predicate;

public class DataFileEditValidator extends MessagepointInputValidator {

	public void validateNotGenericInputs(Object command, Errors errors) {
   	
		DataFileCommand dataFileCommand = (DataFileCommand)command;

		DataFile dataFile = HibernateUtil.getManager().getObject(DataFile.class, dataFileCommand.getId());
		if (dataFile != null) {
			validateActionPermission(dataFile, errors, "error.message.action.not.permitted", AsyncDataFileListVO.DataFileListVOFlags::isCanUpdate);
		}


    	if (dataFileCommand.isDataFileLocal() && dataFileCommand.getDataFileSandboxFileId() == null && 
    			(dataFileCommand.getDataFileResource() == null || dataFileCommand.getDataFileResource().isEmpty())) {
        	errors.reject("error.message.mustselectfile");
        	return;
    	}
    	if( (dataFileCommand.getDocuments() == null || dataFileCommand.getDocuments().isEmpty()) &&
    			(dataFileCommand.getTpCollections() == null || dataFileCommand.getTpCollections().isEmpty()) ) {
    		errors.rejectValue("documents", "error.message.mustselecttouchpointorcollection");	
    	}
    	boolean[] selectedArray = dataFileCommand.getSelected();
    	for (int i=0; i<selectedArray.length;i++) {
    		if (selectedArray[i] == true) {
				String languageCodeString = dataFileCommand.getDataFilePreviewLanguagesArray()[i].getLanguage();
				MessagepointLocale languageLocale = MessagepointLocale.getDefaultLanguageLocale(languageCodeString);
				String[] errorArgs = new String[]{languageLocale.getLanguageDisplayName()};
    			if (dataFileCommand.getDataFilePreviewLanguagesArray()[i].getCustomerNumber().trim().isEmpty()) {
					errors.rejectValue( "dataFilePreviewLanguagesArray["+i+"].customerNumber", 
    									"error.message.customernumberrequired", 
    									errorArgs, 
    									"Please enter a customer Number for the selected language");
    			} else {
	    			if (!validateNumber(dataFileCommand.getDataFilePreviewLanguagesArray()[i].getCustomerNumber())) {
						errors.rejectValue( "dataFilePreviewLanguagesArray["+i+"].customerNumber", 
								"error.message.customernumberformat", 
								errorArgs, 
								"Please enter a valid number as customer Number for selected language.");
	    			}
    			}
    		}
    	}
	}

	private boolean validateNumber(String num) {
        try {
            int number = Integer.parseInt(num);
            if ((int) number <= 0) {
            	return false;
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

	private void validateActionPermission(DataFile dataFile, Errors errors, String errorMessage, Predicate<AsyncDataFileListVO.DataFileListVOFlags> flagChecker) {

		// Admin bypass - Feature flag hidden toggle
		if ( !Boolean.parseBoolean(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.WebAppSecurity.SERVER_SIDE_ACTION_VALIDATION)) )
			return;

		AsyncDataFileListVO vo = new AsyncDataFileListVO();
		vo.setDataFile(dataFile);

		AsyncDataFileListVO.DataFileListVOFlags flags = new AsyncDataFileListVO.DataFileListVOFlags();
		AsyncDataFileListWrapper.setActionFlags(flags);
		if (!flagChecker.test(flags)) {
			errors.reject(errorMessage);
		}
	}

}
