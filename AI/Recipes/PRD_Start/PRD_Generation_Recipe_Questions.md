# PRD Generation Recipe - Discovery Questions

*Created: July 17, 2025*

## Overview
This document contains the comprehensive question framework for developing a PRD Generation Recipe that ensures PRDs are robust and complete enough to successfully feed into the Task Creation and Execution workflow.

## Workflow Context
**Jira Case** → **PRD Generation** → **Task Generation** → **Task Execution**

The PRD Generation Recipe sits between product management's Jira cases and the development team's Task Start recipe, ensuring quality handoffs and successful feature completion.

---

## Question Categories

### **1. Jira Integration & Foundation**
- How do we extract and validate the root Jira case information?
- What Jira fields/notes are most valuable for PRD initiation?
- Should we validate that the Jira case has sufficient detail before PRD creation?
- How do we maintain traceability from Jira → PRD → Task → Implementation?
- What information from <PERSON>ra should automatically populate PRD templates?
- How do we handle cases where Jira information is incomplete or unclear?

### **2. PRD Quality Standards**
- What makes a PRD "task-generation-ready"?
- What level of technical detail is required for successful task generation?
- How specific should acceptance criteria be?
- What integration requirements clarity is needed?
- What performance/non-functional requirements must be specified?
- How do we ensure PRDs are neither too vague nor overly prescriptive?
- What are the minimum viable PRD requirements vs. nice-to-have details?

### **3. MessagePoint Feature Dimensionality Assessment**
**Core Question**: How do we ensure PRDs account for all MessagePoint-specific cross-cutting concerns?

#### **Known Dimensions to Assess:**
- **Import/Export considerations** - How does this feature affect data import/export workflows?
- **Sync considerations** - Does this impact data synchronization processes?
- **Backup considerations** - How does this affect backup/restore functionality?
- **Auditing considerations** - What audit trail requirements does this feature have?

#### **Discovery Questions:**
- What other MessagePoint-specific dimensions should be included in this assessment?
- Should this dimensionality check be a mandatory gate in the PRD process?
- How do we help product managers think through these technical dimensions?
- Should we create templates or checklists for each dimension?
- How do we prioritize dimensions (some features may not need all dimensions)?
- Who should be involved in validating dimensional completeness?

### **4. Test Case/Acceptance Criteria Integration**
- Should PRDs include detailed test scenarios?
- How granular should acceptance criteria be for test generation?
- Should we validate that acceptance criteria are testable?
- Should PRDs anticipate edge cases and testing requirements?
- What's the relationship between PRD acceptance criteria and actual test cases?
- Should PRDs specify testing approaches or leave that to task execution?
- How do we ensure acceptance criteria are measurable and verifiable?

### **4a. User Acceptance Testing (UAT) Integration**
- Should PRDs define specific UAT scenarios and criteria?
- What level of UAT detail should be included vs. left to QA/testing teams?
- How do we ensure UAT criteria align with business objectives?
- Should UAT scenarios cover all user roles and permission levels?
- What UAT criteria should be defined for edge cases and error conditions?
- How do we structure UAT criteria to be actionable for testing teams?
- Should PRDs specify UAT environments, data requirements, or testing tools?
- What's the relationship between developer acceptance criteria and UAT criteria?

### **5. Validation Checkpoints**
- How do we validate a PRD will generate good tasks?
- Can we simulate task breakdown from the PRD to test completeness?
- What are the most common gaps that cause task execution problems?
- Should we validate PRDs against existing codebase patterns?
- What quality gates must pass before handoff to task generation?
- Who should be involved in PRD validation (product, development, QA)?
- How do we catch missing requirements before they become development blockers?

### **6. Product-Developer Bridge**
- How do we help product managers create PRDs that developers can execute effectively?
- Should technical feasibility assessment be integrated into PRD creation?
- How do we identify and document dependencies during PRD creation?
- Should risk assessment be part of the PRD generation process?
- How do we balance product vision with technical constraints?
- What level of technical knowledge should we expect from product managers?
- How do we facilitate collaboration between product and development during PRD creation?

### **7. Integration with Task Recipe**
- How should the PRD Generation Recipe connect to the existing Task Start recipe?
- Should we include a "PRD → Task Preview" step to validate task-generation readiness?
- What handoff documentation is needed between PRD completion and task generation?
- How do we ensure smooth transitions from PRD to task execution?
- Should PRD validation include simulated task breakdown?
- What feedback loops should exist between task execution and PRD quality?

---

## Product Manager Interrogation Flow
*The structured questioning process that the PRD Generation Recipe should follow*

### **Phase 1: Jira Case Foundation**
**Objective**: Extract and validate the root Jira case information

**Questions for Product Manager:**
1. What is the Jira case number for this feature?
2. Can you provide the Jira case details or should I retrieve them?

**Augment Actions:**
- Retrieve Jira case information
- Analyze case description, comments, and attachments
- Extract initial feature context and business justification

### **Phase 2: Conditionality Exploration**
**Objective**: Understand when and how this feature should apply

**Questions for Product Manager:**
1. **Trigger Conditions**: When should this feature activate or be available?
   - User roles/permissions that should have access?
   - Specific workflows or contexts where it applies?
   - Data conditions that must be met?
   - Configuration settings that enable/disable it?

2. **Scope Boundaries**: Where should this feature NOT apply?
   - User types that should be excluded?
   - Scenarios where it should be disabled?
   - Data conditions that should prevent activation?
   - Integration points where it shouldn't interfere?

3. **Edge Cases**: What happens in boundary conditions?
   - Concurrent user scenarios?
   - Data validation failures?
   - System performance constraints?
   - Integration failures?

### **Phase 3: Behavioral Changes**
**Objective**: Define what changes in system behavior should occur

**Questions for Product Manager:**
1. **User Experience Changes**:
   - What new UI elements or workflows will users see?
   - How should existing workflows be modified?
   - What new user actions should be possible?
   - How should error conditions be presented to users?

2. **System Behavior Changes**:
   - What new data should be captured or processed?
   - How should existing data processing change?
   - What new business rules should be enforced?
   - What integrations need to be modified or added?

3. **Performance Expectations**:
   - Response time requirements?
   - Throughput expectations?
   - Scalability considerations?
   - Resource usage constraints?

4. **User Acceptance Testing (UAT) Requirements**:
   - What specific scenarios should be tested by end users?
   - Which user roles should participate in UAT?
   - What business processes should be validated end-to-end?
   - What data conditions should be tested during UAT?
   - What error scenarios should users validate?
   - What performance criteria should be validated by users?

### **Phase 4: Augment Codebase Reflection**
**Objective**: Leverage Augment's MessagePoint knowledge to identify gaps and implications

**Augment Analysis Actions:**
1. **Architecture Impact Assessment**:
   - Analyze affected MessagePoint components
   - Identify integration touchpoints
   - Assess technical feasibility
   - Identify potential conflicts with existing features

2. **MessagePoint Dimensionality Check**:
   - Import/Export implications
   - Sync process impacts
   - Backup/Restore considerations
   - Auditing requirements
   - [Other MessagePoint-specific dimensions]

3. **Implementation Complexity Assessment**:
   - Database schema changes required
   - Service layer modifications needed
   - UI/Controller changes required
   - Testing complexity evaluation

### **Phase 5: Augment-Informed Follow-up Questions**
**Objective**: Ask targeted questions based on codebase analysis

**Questions for Product Manager (Based on Augment Analysis):**
1. **Technical Feasibility Clarifications**:
   - "Based on the current [specific system], how should this feature handle [specific technical constraint]?"
   - "I see this will impact [specific MessagePoint component], have you considered [specific implication]?"
   - "This feature will require changes to [specific area], what's the priority if this affects [existing functionality]?"

2. **Integration Implications**:
   - "How should this feature behave during import/export operations?"
   - "What should happen to this feature's data during sync processes?"
   - "How should this feature be handled in backup/restore scenarios?"
   - "What audit trail requirements does this feature have?"

3. **Dependency Clarifications**:
   - "This feature depends on [specific component], what should happen if that component is unavailable?"
   - "I see potential conflicts with [existing feature], how should these be resolved?"
   - "This will require [specific technical change], what's the acceptable impact on [affected area]?"

### **Phase 6: PRD Suitability Reflection**
**Objective**: Validate that we have sufficient information for task generation

**Augment Self-Assessment Questions:**
1. **Completeness Check**:
   - Do we have clear trigger conditions and scope boundaries?
   - Are behavioral changes well-defined and measurable?
   - Have all MessagePoint dimensions been addressed?
   - Are technical dependencies and constraints identified?

2. **Task-Generation Readiness**:
   - Can this PRD be broken down into clear, executable phases?
   - Are acceptance criteria specific and testable?
   - Are technical risks identified and mitigated?
   - Is the scope appropriate for the available resources?

3. **Gap Identification**:
   - What information is still missing or unclear?
   - What assumptions need validation?
   - What additional stakeholder input is needed?
   - What technical investigations are required?

**Final Questions for Product Manager:**
1. "Based on our discussion, I believe this feature requires [summary of key components]. Does this align with your vision?"
2. "I've identified these potential risks/complexities: [list]. How should we prioritize addressing these?"
3. "Are there any other stakeholders who should review this PRD before we proceed to task generation?"
4. "What's the target timeline for this feature, and are there any hard deadlines we need to consider?"

---

## Success Criteria for PRD Generation
- [ ] Jira case fully analyzed and integrated
- [ ] Clear conditionality and scope boundaries defined
- [ ] Behavioral changes specified and measurable
- [ ] All MessagePoint dimensions assessed
- [ ] Technical feasibility validated
- [ ] Augment has sufficient information for task breakdown
- [ ] Product manager confirms PRD aligns with vision
- [ ] Acceptance criteria are testable and complete

---

*This interrogation flow ensures comprehensive PRD development through structured questioning and Augment's codebase intelligence.*
