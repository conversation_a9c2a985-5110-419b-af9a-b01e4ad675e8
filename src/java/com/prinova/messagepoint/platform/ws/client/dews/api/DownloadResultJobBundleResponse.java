
package com.prinova.messagepoint.platform.ws.client.dews.api;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for downloadResultJobBundleResponse complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="downloadResultJobBundleResponse">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="data" type="{http://www.w3.org/2001/XMLSchema}base64Binary"/>
 *         &lt;element name="currentchunk" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="numchunks" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="fileName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "downloadResultJobBundleResponse", propOrder = {
    "data",
    "currentchunk",
    "numchunks",
    "fileName"
})
public class DownloadResultJobBundleResponse {

    @XmlElement(required = true)
    protected byte[] data;
    protected int currentchunk;
    protected int numchunks;
    @XmlElement(required = true)
    protected String fileName;

    /**
     * Gets the value of the data property.
     * 
     * @return
     *     possible object is
     *     byte[]
     */
    public byte[] getData() {
        return data;
    }

    /**
     * Sets the value of the data property.
     * 
     * @param value
     *     allowed object is
     *     byte[]
     */
    public void setData(byte[] value) {
        this.data = value;
    }

    /**
     * Gets the value of the currentchunk property.
     * 
     */
    public int getCurrentchunk() {
        return currentchunk;
    }

    /**
     * Sets the value of the currentchunk property.
     * 
     */
    public void setCurrentchunk(int value) {
        this.currentchunk = value;
    }

    /**
     * Gets the value of the numchunks property.
     * 
     */
    public int getNumchunks() {
        return numchunks;
    }

    /**
     * Sets the value of the numchunks property.
     * 
     */
    public void setNumchunks(int value) {
        this.numchunks = value;
    }

    /**
     * Gets the value of the fileName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * Sets the value of the fileName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFileName(String value) {
        this.fileName = value;
    }

}
