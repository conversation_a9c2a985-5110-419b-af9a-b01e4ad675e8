package com.prinova.messagepoint.redis;

import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;

/**
 * Demonstration class showing how to use reflection to access the client property
 * of LettuceConnectionFactory class.
 * 
 * This class provides a practical example of the reflection code requested by the user.
 */
public class ReflectionDemonstration {
    
    /**
     * Main method to demonstrate the reflection functionality.
     * This shows how to assign a variable with the value in client property of LettuceConnectionFactory.
     */
    public static void main(String[] args) {
        System.out.println("=== LettuceConnectionFactory Client Property Reflection Demonstration ===");
        
        try {
            // Create a LettuceConnectionFactory instance for demonstration
            RedisStandaloneConfiguration config = new RedisStandaloneConfiguration("localhost", 6379);
            LettuceConnectionFactory connectionFactory = new LettuceConnectionFactory(config);
            
            // Initialize the connection factory (this may create the client)
            connectionFactory.afterPropertiesSet();
            
            System.out.println("1. Created LettuceConnectionFactory instance");
            System.out.println("   - Host: localhost");
            System.out.println("   - Port: 6379");
            System.out.println();
            
            // Demonstrate the reflection access
            System.out.println("2. Accessing client property through reflection:");
            
            // This is the main demonstration - assigning a variable with the client property value
            Object clientPropertyValue = LettuceConnectionFactoryReflectionUtil.getClientPropertyValue(connectionFactory);
            
            if (clientPropertyValue != null) {
                System.out.println("✓ Successfully assigned variable with client property value!");
                System.out.println("   - Variable type: " + clientPropertyValue.getClass().getName());
                System.out.println("   - Variable value: " + clientPropertyValue.toString());
                System.out.println("   - Variable hash: " + clientPropertyValue.hashCode());
            } else {
                System.out.println("⚠ Client property value is null (may not be initialized yet)");
            }
            
            System.out.println();
            
            // Try alternative approach to find client-like fields
            System.out.println("3. Searching for client-like fields:");
            Object clientLikeField = LettuceConnectionFactoryReflectionUtil.findAndGetClientLikeField(connectionFactory);
            
            if (clientLikeField != null) {
                System.out.println("✓ Found client-like field!");
                System.out.println("   - Field type: " + clientLikeField.getClass().getName());
            } else {
                System.out.println("⚠ No client-like fields found");
            }
            
            System.out.println();
            
            // Demonstrate the full reflection process
            System.out.println("4. Full demonstration of client property access:");
            LettuceConnectionFactoryReflectionUtil.demonstrateClientPropertyAccess(connectionFactory);
            
            // Clean up
            connectionFactory.destroy();
            
        } catch (Exception e) {
            System.err.println("❌ Error during reflection demonstration:");
            System.err.println("   " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
        System.out.println("=== Demonstration Complete ===");
    }
    
    /**
     * Alternative demonstration method that can be called from tests or other code.
     * 
     * @param connectionFactory The LettuceConnectionFactory to examine
     */
    public static void demonstrateReflectionAccess(LettuceConnectionFactory connectionFactory) {
        System.out.println("--- Reflection Access Demonstration ---");
        
        try {
            // The key line: assign a variable with the client property value through reflection
            Object clientValue = LettuceConnectionFactoryReflectionUtil.getClientPropertyValue(connectionFactory);
            
            // Display the results
            if (clientValue != null) {
                System.out.println("Variable successfully assigned with client property value:");
                System.out.println("  Type: " + clientValue.getClass().getSimpleName());
                System.out.println("  Full Type: " + clientValue.getClass().getName());
                System.out.println("  String Representation: " + clientValue.toString());
                System.out.println("  Identity Hash: " + System.identityHashCode(clientValue));
            } else {
                System.out.println("Client property value is null");
            }
            
        } catch (Exception e) {
            System.err.println("Reflection access failed: " + e.getMessage());
        }
        
        System.out.println("--- End Demonstration ---");
    }
    
    /**
     * Utility method to create a configured LettuceConnectionFactory for testing.
     * 
     * @param host Redis host
     * @param port Redis port
     * @return Configured LettuceConnectionFactory
     */
    public static LettuceConnectionFactory createTestConnectionFactory(String host, int port) {
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration(host, port);
        LettuceConnectionFactory factory = new LettuceConnectionFactory(config);
        factory.afterPropertiesSet();
        return factory;
    }
    
    /**
     * Example of how to use the reflection in a real scenario.
     * This method shows practical usage of accessing the client property.
     */
    public static void practicalReflectionExample() {
        System.out.println("=== Practical Reflection Example ===");
        
        // Create connection factory
        LettuceConnectionFactory factory = createTestConnectionFactory("localhost", 6379);
        
        try {
            // Assign variable with client property value - this is the main requirement
            Object client = LettuceConnectionFactoryReflectionUtil.getClientPropertyValue(factory);
            
            if (client != null) {
                System.out.println("Successfully obtained client through reflection:");
                System.out.println("- Client class: " + client.getClass().getName());
                
                // You could now use this client object for advanced operations
                // For example, checking if it's connected, getting configuration, etc.
                
                // Example of further reflection on the client object itself
                System.out.println("- Client methods available:");
                java.lang.reflect.Method[] methods = client.getClass().getMethods();
                for (int i = 0; i < Math.min(5, methods.length); i++) {
                    System.out.println("  * " + methods[i].getName());
                }
                if (methods.length > 5) {
                    System.out.println("  ... and " + (methods.length - 5) + " more methods");
                }
            }
            
        } catch (Exception e) {
            System.err.println("Practical example failed: " + e.getMessage());
        } finally {
            factory.destroy();
        }
        
        System.out.println("=== End Practical Example ===");
    }
}
