package com.prinova.messagepoint.controller.content;

import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.wrapper.ContentObjectContentSelectionViewWrapper;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.springframework.validation.Errors;

import java.util.*;

public class ContentObjectDynamicVariantViewValidator extends MessagepointInputValidator {

	public void validateNotGenericInputs(Object command, Errors errors) {
		ContentObjectContentSelectionViewWrapper wrapper = (ContentObjectContentSelectionViewWrapper) command;

		ContentObject contentObject = ContentObject.findById(wrapper.getContentObject().getId());
		
		int action = -1;
		if (wrapper.getActionValue() != null && !wrapper.getActionValue().trim().isEmpty()) {
			action = Integer.valueOf(wrapper.getActionValue()).intValue();
		}
		
		if (action != -1 && wrapper.getUserNote()!=null && wrapper.getUserNote().trim().length() > 255) {
			errors.reject("error.message.commentlengthexceeded");
			return;
		}

		if (action == ContentObjectDynamicVariantViewController.ACTION_CREATE_TREE_NODE) {
		
			if (wrapper.getNewTreeNode().isCreatingNewCollection()) {
				
				String[] newNodeValues = SelectableValidationUtil.removeEmptyValuesFromTail(wrapper.getNewTreeNode().getNewCollectionFirstValue());

				// Making sure that the name obeys the ObjectName conventions 
				SelectableValidationUtil.checkNodeNameValue(wrapper.getNewTreeNode().getName(), errors);
				
				if (!errors.hasErrors()) {
					// Making sure that node name is unique 
					long parentNodeId = wrapper.getContentTreeNode() != null ? wrapper.getContentTreeNode().getId() : -1;
					if (wrapper.getContentObject().getParameterGroup().isParameter())
						parentNodeId = ParameterGroupTreeNode.MASTER_VARIANCE_ID;
					SelectableValidationUtil.checkContentNodeNameUniqueness(contentObject, -1, parentNodeId, wrapper.getNewTreeNode().getName(), errors);

					if (errors.hasErrors()) {
						return;
					}

					if (wrapper.getNewTreeNode().isShared()) {
						SelectableValidationUtil.checkSharedNodeName(wrapper.getNewTreeNode().getName(), errors);
					}
	
					ParameterGroup parameterGroup = contentObject.getParameterGroup();
					List<ParameterGroupItem> pgItems = parameterGroup.getParameterGroupItemsSorted();
					// Making sure that the data values obey the input conventions
					for (int i = 0; i < newNodeValues.length; i++) {
						String label = pgItems.get(i).getParameter().getName();
						String dataValue = newNodeValues[i];
						SelectableValidationUtil.checkDataValue(label, dataValue, errors);
					}
					
					if (!errors.hasErrors()) {
						// Making sure that at least two values are provided for new overrides
						SelectableValidationUtil.checkForEmptyValues(newNodeValues, wrapper.getNewTreeNode().getParameters(), errors);
						if (errors.hasErrors()) {
							return;
						}
						
						
						// Making sure that there's no repeating entries in entered values 
						SelectableValidationUtil.checkForDuplicateInEnteredValue(newNodeValues, errors);
						
						// Making sure that none of the entered values has any overlap with other nodes
						// ALSO Can not add parent value if one or more child values exist in same node 
						SelectableValidationUtil.checkForDuplicateInOtherNodesANDExistenceOfChildren(contentObject.getDynamicVariantWorkingDataParameterGroupTreeNodeIds(), contentObject.getParameterGroup(), newNodeValues, false, errors);
					}
				}								
			} else {
				// Making sure that there's none of the entered values is has any overlap with other nodes
				long pgCollectionId = wrapper.getNewTreeNode().getPgCollectionId();
				ParameterGroupInstanceCollection pgiCollection = ParameterGroupInstanceCollection.findById(pgCollectionId);

				// If on the shared tab, then a shared collection must be selected
				SelectableValidationUtil.checkSharedCollectionSelected(pgiCollection, errors);
				if (errors.hasErrors()) {
					return;
				}
				
				// Making sure that node name is unique 
				long parentNodeId = wrapper.getContentTreeNode() != null ? wrapper.getContentTreeNode().getId() : -1;
				if (wrapper.getContentObject().getParameterGroup().isParameter())
					parentNodeId = ParameterGroupTreeNode.MASTER_VARIANCE_ID;
				SelectableValidationUtil.checkContentNodeNameUniqueness(contentObject, -1, parentNodeId, pgiCollection.getName(), errors);
				if (errors.hasErrors()) {
					return;
				}

				List<ParameterGroupInstance> leafInstances = ParameterGroupInstance.findByPgiCollectionSortedByValue(pgCollectionId);	
				if (leafInstances!=null) {
					for (ParameterGroupInstance leafInstance : leafInstances) {
					 	String[] sharedNodeValues = leafInstance.getValueAsArray();
						// Making sure that none of the entered values has any overlap with other nodes
						// ALSO Can not add parent value if one or more child values exist in same node 
						SelectableValidationUtil.checkForDuplicateInOtherNodesANDExistenceOfChildren(contentObject.getDynamicVariantWorkingDataParameterGroupTreeNodeIds(), contentObject.getParameterGroup(), sharedNodeValues, true, errors);
						if (errors.hasErrors()) {
							return;
						}
					}
				}
			}

		} else if (action == ContentObjectDynamicVariantViewController.ACTION_ADD_TO_TREE_NODE) {

			String[] valuesToAdd = SelectableValidationUtil.removeEmptyValuesFromTail(wrapper.getDataValueChanges().getDataValueToAdd());
			
			ParameterGroup parameterGroup = contentObject.getParameterGroup();
			List<ParameterGroupItem> pgItems = parameterGroup.getParameterGroupItemsSorted();
			// Making sure that the data values obey the input conventions
			for (int i = 0; i < valuesToAdd.length; i++) {
				String label = pgItems.get(i).getParameter().getName();
				String dataValue = valuesToAdd[i];
				SelectableValidationUtil.checkDataValue(label, dataValue, errors);
			}

			if (!errors.hasErrors()) {
				// Making sure that at least one value if provided for new overrides
				SelectableValidationUtil.checkForEmptyValues(valuesToAdd, wrapper.getDataValueChanges().getParameters(), errors);
				
				// Making sure that there's no repeating entries in entered values  
				SelectableValidationUtil.checkForDuplicateInEnteredValue(valuesToAdd, errors);
				
				// Making sure that there's none of the entered values is has any overlap with other nodes
				// ALSO Can not add parent value if one or more child values exist in same node 
				SelectableValidationUtil.checkForDuplicateInOtherNodesANDExistenceOfChildren(contentObject.getDynamicVariantWorkingDataParameterGroupTreeNodeIds(), contentObject.getParameterGroup(), valuesToAdd, false, errors);
			}
	
		} else if (action == ContentObjectDynamicVariantViewController.ACTION_RENAME_TREE_NODE) {
		
			SelectableValidationUtil.checkNodeNameValue(wrapper.getNewNodeName(), errors);

			// Making sure that node name is unique 
			long currentNodeId = wrapper.getContentTreeNode().getId();
			long parentNodeId = wrapper.getContentTreeNode().getParentNode() != null ? wrapper.getContentTreeNode().getParentNode().getId() : -1;
			if (wrapper.getContentObject().getParameterGroup().isParameter())
				parentNodeId = ParameterGroupTreeNode.MASTER_VARIANCE_ID;
			SelectableValidationUtil.checkContentNodeNameUniqueness(contentObject, currentNodeId, parentNodeId, wrapper.getNewNodeName(), errors);

		} else if (action == ContentObjectDynamicVariantViewController.ACTION_SEARCH_TREE_NODE) {
			
			String[] searchValues = SelectableValidationUtil.removeEmptyValuesFromTail(wrapper.getSearchValues());
			
			ParameterGroup parameterGroup = contentObject.getParameterGroup();
			List<ParameterGroupItem> pgItems = parameterGroup.getParameterGroupItemsSorted();
			// Making sure that the data values obey the input conventions
			for (int i = 0; i < searchValues.length; i++) {
				String label = pgItems.get(i).getParameter().getName();
				String dataValue = searchValues[i];
				SelectableValidationUtil.checkDataValue(label, dataValue, errors);
			}
		} else if (action == ContentObjectDynamicVariantViewController.ACTION_REMOVE_FROM_TREE_NODE) {
			boolean isTopLevel = wrapper.getContentTreeNode().isTopLevel();
			ParameterGroupTreeNode topLevelTreeNode = null;
			if (isTopLevel) {
				topLevelTreeNode = wrapper.getContentTreeNode();

				List<String> fullValuesToRemove = new ArrayList<>();
				StringTokenizer st = new StringTokenizer(wrapper.getDataValueChanges().getFullValuesToRemove(),
						ContentObjectDynamicVariantViewController.UI_ITEMS_TO_REMOVE_TOKENIZER);
				while (st.hasMoreTokens()) {
					fullValuesToRemove.add(st.nextToken().trim());
				}				
				ParameterGroupInstance.removeEmptyValuesFromEnd(fullValuesToRemove);
				Set<String> firstItemValueRemoved = new HashSet<>();
				for (String itemToRemove : fullValuesToRemove) {
					List<String> flatValue = ParameterGroupInstanceCollectionTreeNodeVO.deflat(itemToRemove);
					firstItemValueRemoved.add(flatValue.get(0));
				}
				if (!firstItemValueRemoved.isEmpty()) {
					ParameterGroupTreeNode pgTreeNode = ParameterGroupTreeNode.findValueInChildNodes(topLevelTreeNode.getId(), firstItemValueRemoved);
					StringBuilder removedValues= new StringBuilder("[");
					for (String itemRemoved : firstItemValueRemoved) {
						removedValues.append(itemRemoved).append(" ");
					}
					removedValues.append("]");
					if (pgTreeNode != null) {	
						errors.reject("error.touchpointselection.selectors.add.selection.redefined.selection.criteria.would.result.in.orphaning.the.criteria.for.the.child.selection", new String[] {removedValues.toString(), "["+pgTreeNode.getName()+"]" }, "");
					} 
				}
				
			}
		} else if (action == ContentObjectDynamicVariantViewController.ACTION_MANAGE_FROM_TREE_NODE) {

			String[] valuesToAdd = SelectableValidationUtil.removeEmptyValuesFromTail(wrapper.getDataValueChanges().getDataValueToAdd());

			if (valuesToAdd.length > 0) {
				ParameterGroup parameterGroup = contentObject.getParameterGroup();
				List<ParameterGroupItem> pgItems = parameterGroup.getParameterGroupItemsSorted();
				// Making sure that the data values obey the input conventions
				for (int i = 0; i < valuesToAdd.length; i++) {
					String label = pgItems.get(i).getParameter().getName();
					String dataValue = valuesToAdd[i];
					SelectableValidationUtil.checkDataValue(label, dataValue, errors);
				}

				if (!errors.hasErrors()) {
					// Making sure that at least one value if provided for new overrides
					SelectableValidationUtil.checkForEmptyValues(valuesToAdd, wrapper.getDataValueChanges().getParameters(), errors);

					// Making sure that there's no repeating entries in entered values
					SelectableValidationUtil.checkForDuplicateInEnteredValue(valuesToAdd, errors);

					// Making sure that there's none of the entered values is has any overlap with other nodes
					// ALSO Can not add parent value if one or more child values exist in same node
					SelectableValidationUtil.checkForDuplicateInOtherNodesANDExistenceOfChildren(contentObject.getDynamicVariantWorkingDataParameterGroupTreeNodeIds(), contentObject.getParameterGroup(), valuesToAdd, false, errors);
				}
			}


			boolean isTopLevel = wrapper.getContentTreeNode().isTopLevel();
			ParameterGroupTreeNode topLevelTreeNode = null;
			if (isTopLevel) {
				topLevelTreeNode = wrapper.getContentTreeNode();
				List<String> fullValuesToRemove = new ArrayList<>();
				if (wrapper.getDataValueChanges().getFullValuesToRemove() != null) {
					StringTokenizer st = new StringTokenizer(wrapper.getDataValueChanges().getFullValuesToRemove(),
							ContentObjectDynamicVariantViewController.UI_ITEMS_TO_REMOVE_TOKENIZER);
					while (st.hasMoreTokens()) {
						fullValuesToRemove.add(st.nextToken().trim());
					}
					ParameterGroupInstance.removeEmptyValuesFromEnd(fullValuesToRemove);
					Set<String> firstItemValueRemoved = new HashSet<>();
					for (String itemToRemove : fullValuesToRemove) {
						List<String> flatValue = ParameterGroupInstanceCollectionTreeNodeVO.deflat(itemToRemove);
						firstItemValueRemoved.add(flatValue.get(0));
					}
					if (!firstItemValueRemoved.isEmpty()) {
						ParameterGroupTreeNode pgTreeNode = ParameterGroupTreeNode.findValueInChildNodes(topLevelTreeNode.getId(), firstItemValueRemoved);
						StringBuilder removedValues= new StringBuilder("[");
						for (String itemRemoved : firstItemValueRemoved) {
							removedValues.append(itemRemoved).append(" ");
						}
						removedValues.append("]");
						if (pgTreeNode != null) {
							errors.reject("error.touchpointselection.selectors.add.selection.redefined.selection.criteria.would.result.in.orphaning.the.criteria.for.the.child.selection", new String[] {removedValues.toString(), "["+pgTreeNode.getName()+"]" }, "");
						}
					}
				}
			}
		}
	}	
}