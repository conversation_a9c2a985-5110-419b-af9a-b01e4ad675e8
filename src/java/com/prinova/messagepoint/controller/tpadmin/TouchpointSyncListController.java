package com.prinova.messagepoint.controller.tpadmin;

import com.prinova.messagepoint.controller.MessagepointController;

import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class TouchpointSyncListController extends MessagepointController {
    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
        Map<String, Object> referenceData = new HashMap<>();
        return referenceData;
    }

    @SuppressWarnings("NullableProblems")
    @Override
    protected Object formBackingObject(HttpServletRequest request) throws Exception {
        return new SyncListCommand();
    }

    private static class SyncListCommand implements Serializable {
        private static final long serialVersionUID = -1L;
    }
}
