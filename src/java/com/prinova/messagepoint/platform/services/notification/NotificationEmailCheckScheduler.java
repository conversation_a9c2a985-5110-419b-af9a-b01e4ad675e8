package com.prinova.messagepoint.platform.services.notification;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.notification.NotificationEmail;
import com.prinova.messagepoint.model.notification.NotificationEventUtil;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.common.DeleteModelService;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.EmailManager;
import com.prinova.messagepoint.util.EmailUtil;
import com.prinova.messagepoint.util.LogUtil;


public class NotificationEmailCheckScheduler extends MessagePointRunnable{
	private static final Log log = LogUtil.getLog(NotificationEmailCheckScheduler.class);
	
	@Override
	public void performMainProcessing() {
		log.debug("Entering NotificationEmailCheckScheduler.performMainProcessing()...");
		if(ApplicationUtil.buildFullyQualifiedServerURLForEmail() == null)
			return;
		List<Long> userIds = NotificationEmail.findAllUserIds();
		String server_url = ApplicationUtil.buildFullyQualifiedServerURLForEmail();
		
		for(Long userId: userIds){
			
			List<NotificationEmail> list = NotificationEmail.findByUser(userId.longValue());
			
			User recipient = User.findById(userId);
			Locale appLocale = new Locale(recipient.getAppLocaleLangCode());
			String formattedSubject = MessageFormat.format(ApplicationUtil.getMessage("email.digest.subject.your.messagepoint.notifications", appLocale),
					new Object[] { recipient.getName(), ApplicationUtil.getMessagepointBrandedLabel(appLocale) });
			Node node = Node.getCurrentNode();
			String instanceBranchName = "";
			if(node != null){
				instanceBranchName = node.getBranch().getName().concat("/" + node.getName());
			}
			String instanceBranchNameIntro = MessageFormat.format(ApplicationUtil.getMessage("email.content.body.instance.branch.name", appLocale),
					new Object[] { instanceBranchName, ApplicationUtil.getMessagepointBrandedLabel(appLocale) });
			String bodyHeader = instanceBranchNameIntro.concat(MessageFormat.format(ApplicationUtil.getMessage("email.content.body.intro", appLocale),
					new Object[] { recipient.getFullName(), ApplicationUtil.getMessagepointBrandedLabel(appLocale) }));
			String dateTime = DateUtil.formatDateTime(DateUtil.now());
			String htmlBodyHeader = "<tr><td><table width=\"100%\"><tr><td  style=\"height:40px;\" align=\"left\">"+ dateTime + "</td>";
			htmlBodyHeader += "<td style=\"height:40px;\" align=\"right\"><<a href=\"" + server_url + "\" > " + instanceBranchName + "</a>></td></tr></table></td></tr>";
			htmlBodyHeader += "<tr><td>" + MessageFormat.format(ApplicationUtil.getMessage("email.content.body.intro", appLocale),
					new Object[] { recipient.getUsername(), ApplicationUtil.getMessagepointBrandedLabel(appLocale) }).replaceAll("\n", "<br>") + "</td></tr>";
			String footer = MessageFormat.format(ApplicationUtil.getMessage("email.content.footer", appLocale),
					new Object[] { recipient.getName(), ApplicationUtil.getMessagepointBrandedLabel(appLocale) });
			String htmlFooter = "<tr><td>" + footer.replaceAll("\n", "<br/>") + "</td></tr>";
			StringBuilder formattedContent = new StringBuilder();
			StringBuilder formattedHtmlContent = new StringBuilder();
			int actionType = 0;
			int prevActionType = 0;
			List<NotificationEmail> tobeDeleted = new ArrayList<>();
			for(int i=0; i < list.size(); i++){
				NotificationEmail email = list.get(i);
				tobeDeleted.add(email);
				actionType = email.getNotificationActionType();
				if(actionType != prevActionType){
					formattedHtmlContent.append("<tr><td class=\"actionContent\">");
					formattedHtmlContent.append(NotificationEventUtil.generateActionHeader(actionType, appLocale, true, false, "").concat("<br/><br/>"));
					formattedContent.append(NotificationEventUtil.generateActionHeader(actionType, appLocale, false, false, ""));
				}	
				formattedContent.append("\n");
				formattedContent.append(email.getNotificationActionText().concat("\n"));
				formattedHtmlContent.append(email.getNotificationActionText().replaceAll("\n", "<br/>"));
				int nextAction = (i+1 < list.size()?list.get(i+1).getNotificationActionType():0);
				if(actionType !=nextAction){
					formattedHtmlContent.append("</td></tr><tr><td style=\"padding: 10px 20px; height: 15px;\" ></td></tr>");
				}else{
					formattedHtmlContent.append("<br/>");
				}
				prevActionType = email.getNotificationActionType();
				tobeDeleted.add(email);
			}
			formattedHtmlContent.append("</td></tr>");
			EmailManager emailManager = EmailUtil.getEmailManagerBean();
			emailManager.sendMail(recipient.getEmail(), 
					bodyHeader.concat(formattedContent.toString()).concat(footer),
					formattedSubject, 
					htmlBodyHeader.concat(formattedHtmlContent.toString()).concat(htmlFooter));
			if(!tobeDeleted.isEmpty())
				for(NotificationEmail toDeletedEmail:tobeDeleted){
					ServiceExecutionContext context = DeleteModelService.createContext(toDeletedEmail.getId(), NotificationEmail.class.getName());
					Service deleteModelService = MessagepointServiceFactory.getInstance().lookupService(DeleteModelService.SERVICE_NAME, DeleteModelService.class);
					deleteModelService.execute(context);
				}
		}
	}
}
