package com.prinova.messagepoint.platform.services.backgroundtask.sync.workers;

import com.prinova.messagepoint.model.common.SyncObjectType;
import com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncRequest;
import com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncTouchpointVariantsService;

public class SyncTouchpointVariantsWorker extends AbstractSyncWorker{
    public SyncTouchpointVariantsWorker(SyncRequest syncRequest) {
        super(syncRequest);
    }

    @Override
    public SyncWorkerType getType() {
        return SyncWorkerType.SYNC_TOUCHPOINT_VARIANT_WORKER;
    }

    @Override
    protected void performSync() {
        syncObjects(getType(),
                SyncObjectType.ID_VARIANT,
                SyncTouchpointVariantsService.SERVICE_NAME,
                SyncTouchpointVariantsService.class);
    }
}
