# 🔄 Versioning Standards for API Generation

> **Purpose**: API versioning requirements and patterns for automated REST API generation  
> **Audience**: AI Agent  
> **Usage**: Apply these versioning patterns to all generated APIs

## 🎯 VERSIONING STRATEGY

### **✅ REQUIRED: URL Path Versioning**
```java
// ✅ CURRENT IMPLEMENTATION (No versioning)
@RequestMapping("/mp/rest/variables")

// ✅ FUTURE IMPLEMENTATION (Versioned)
@RequestMapping("/mp/rest/v1/variables")
@RequestMapping("/mp/rest/v2/variables")  // Future breaking changes
```

**Why URL Path Versioning**:
- ✅ **Clear and Explicit**: Version immediately visible in URL
- ✅ **Cacheable**: Different versions can be cached separately
- ✅ **Tool-Friendly**: Works well with API documentation tools
- ✅ **Industry Standard**: Used by GitHub, Stripe, Twitter APIs

## 📋 VERSIONING CONFIGURATION

### **✅ Version Constants**:
```java
public interface RestApiConstants {
    String CURRENT_API_VERSION = "v1";
    String API_URL_PREFIX = "rest";
    
    // Supported versions
    String[] SUPPORTED_VERSIONS = {"v1"};
    String DEFAULT_VERSION = "v1";
    
    // Version deprecation info
    Map<String, String> VERSION_DEPRECATION = Map.of(
        // Future: "v1", "2024-12-31"  // Deprecation date
    );
}
```

### **✅ Versioned Base Controller**:
```java
@RestController
@RequestMapping("/mp/rest/v1")
public abstract class RestResourceBaseV1 extends RestResourceBase {
    
    @Override
    protected String getApiVersion() {
        return "v1";
    }
    
    @Override
    protected Map<String, String> getVersionHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("API-Version", "v1");
        headers.put("API-Supported-Versions", "v1");
        return headers;
    }
}
```

## 🔧 VERSION RESPONSE HEADERS

### **✅ REQUIRED Response Headers**:
```http
HTTP/1.1 200 OK
API-Version: v1
API-Supported-Versions: v1
API-Deprecation-Date: null
Content-Type: application/json
```

### **✅ Response Metadata**:
```json
{
  "responseResult": {
    "data": [...],
    "metadata": {
      "apiVersion": "v1",
      "supportedVersions": ["v1"]
    }
  }
}
```

## 🔄 BACKWARD COMPATIBILITY STRATEGY

### **✅ Migration Pattern**:
```java
// ✅ STEP 1: Create versioned controller
@RestController
@RequestMapping("/mp/rest/v1/variables")
public class VariablesV1Controller extends RestResourceBaseV1 {
    // Current implementation
}

// ✅ STEP 2: Maintain backward compatibility
@RestController
@RequestMapping("/mp/rest/variables")
@Deprecated
public class VariablesController extends VariablesV1Controller {
    // Delegates to v1 implementation
    // Adds deprecation warnings
    
    @Override
    protected Map<String, String> getVersionHeaders() {
        Map<String, String> headers = super.getVersionHeaders();
        headers.put("Warning", "299 - \"This API version is deprecated. Please migrate to /mp/rest/v1/variables\"");
        headers.put("Sunset", "2024-12-31");
        return headers;
    }
}
```

## 📊 BREAKING CHANGE POLICY

### **✅ Breaking Changes (Require New Version)**:
- Removing fields from responses
- Changing field types or formats
- Removing endpoints
- Changing HTTP status codes
- Modifying authentication requirements
- Changing URL structure
- Modifying request/response formats

### **✅ Non-Breaking Changes (Same Version)**:
- Adding new fields to responses
- Adding new endpoints
- Adding new optional parameters
- Improving error messages
- Performance improvements
- Bug fixes

## 🕐 VERSION LIFECYCLE MANAGEMENT

### **Version States**:
1. **Current** (v1): Actively developed, fully supported
2. **Deprecated**: Supported but discouraged, migration timeline provided
3. **Sunset**: No longer supported, returns 410 Gone

### **Deprecation Timeline**:
```
Phase 1: Announcement (3 months before deprecation)
├── Add deprecation warnings to responses
├── Update documentation with migration guide
└── Notify API consumers

Phase 2: Deprecation (6 months support)
├── Mark version as deprecated
├── Continue full support
└── Provide migration assistance

Phase 3: Sunset (End of support)
├── Return 410 Gone for deprecated endpoints
├── Redirect to current version documentation
└── Log sunset access attempts
```

## 📚 VERSION DOCUMENTATION

### **✅ OpenAPI Configuration**:
```java
@Configuration
public class OpenApiVersionConfig {
    
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
            .info(new Info()
                .title("Messagepoint REST API")
                .version("v1")
                .description("Messagepoint REST API - Version 1.0"))
            .servers(List.of(
                new Server().url("/mp/rest/v1").description("Version 1 (Current)"),
                new Server().url("/mp/rest").description("Legacy (Deprecated)")
            ));
    }
}
```

### **✅ Version-Specific Documentation**:
```java
@Tag(name = "Variables v1", description = "Variables Management API - Version 1")
@RestController
@RequestMapping("/mp/rest/v1/variables")
public class VariablesV1Controller {
    
    @Operation(
        summary = "List variables (v1)",
        description = "Get variables with v1 response format. " +
                     "For latest features, see v2 documentation."
    )
    public ResponseEntity<String> getVariables() {
        // Implementation
    }
}
```

## 🧪 VERSION TESTING

### **✅ Version Compatibility Tests**:
```java
@Test
public void testVersionedEndpointAccess() {
    // Test v1 endpoint
    mockMvc.perform(get("/mp/rest/v1/variables"))
        .andExpect(status().isOk())
        .andExpect(header().string("API-Version", "v1"));
    
    // Test legacy endpoint (deprecated)
    mockMvc.perform(get("/mp/rest/variables"))
        .andExpect(status().isOk())
        .andExpect(header().exists("Warning"));
}

@Test
public void testUnsupportedVersionReturns404() {
    mockMvc.perform(get("/mp/rest/v99/variables"))
        .andExpect(status().isNotFound());
}

@Test
public void testVersionHeadersPresent() {
    mockMvc.perform(get("/mp/rest/v1/variables"))
        .andExpect(header().string("API-Version", "v1"))
        .andExpect(header().string("API-Supported-Versions", "v1"));
}
```

## 🔮 FUTURE VERSION CONSIDERATIONS

### **v2 Planning Example**:
```http
# Potential v2 improvements
GET /mp/rest/v2/variables
{
  "data": [...],           # Flattened response structure
  "pagination": {...},     # Separate pagination object
  "metadata": {...}        # Enhanced metadata
}

# vs v1 structure
{
  "responseResult": {
    "data": [...],         # Nested structure
    "total_records": 100   # Snake_case pagination
  }
}
```

### **Version Support Matrix**:
| Version | Status | Support End | Breaking Changes |
|---------|--------|-------------|------------------|
| v1 | Current | TBD | None |
| v2 | Future | TBD | Response format changes |

## 🚨 VERSIONING REQUIREMENTS

### **✅ MANDATORY FOR GENERATED APIS**
- [ ] **URL path versioning** with `/mp/rest/v1/` prefix
- [ ] **Version headers** in all responses
- [ ] **Backward compatibility** for existing endpoints
- [ ] **Deprecation warnings** for old versions
- [ ] **Version-specific documentation** in OpenAPI
- [ ] **Version compatibility tests** for all endpoints

### **✅ REQUIRED VERSION METADATA**
- [ ] **API-Version header** with current version
- [ ] **API-Supported-Versions header** with all supported versions
- [ ] **Warning header** for deprecated versions
- [ ] **Sunset header** with deprecation date
- [ ] **Response metadata** with version information

### **❌ FORBIDDEN PATTERNS**
- [ ] **Header-based versioning** (use URL path instead)
- [ ] **Query parameter versioning** (not RESTful)
- [ ] **Content-type versioning** (complex for clients)
- [ ] **Breaking changes** without version increment
- [ ] **Immediate deprecation** without migration period

## 🔧 GENERATION RULES

### **Auto-Generate Versioned APIs**
1. **Start with v1** for all new APIs
2. **Include version headers** in all responses
3. **Generate backward compatibility** controllers
4. **Add deprecation warnings** to legacy endpoints
5. **Create version-specific documentation**
6. **Include version tests** in test suites

### **Version URL Template**:
```java
// Generated controller pattern
@RestController
@RequestMapping("/mp/rest/v1/{resources}")
public class {ResourceName}V1Controller extends RestResourceBaseV1 {
    // Implementation with version headers
}

// Backward compatibility controller
@RestController
@RequestMapping("/mp/rest/{resources}")
@Deprecated
public class {ResourceName}Controller extends {ResourceName}V1Controller {
    // Deprecation warnings
}
```

## 📋 VALIDATION CHECKLIST

### **Before Generation**
- [ ] Version strategy defined
- [ ] Breaking change policy understood
- [ ] Deprecation timeline planned
- [ ] Documentation structure prepared

### **After Generation**
- [ ] All endpoints have version prefixes
- [ ] Version headers included in responses
- [ ] Backward compatibility maintained
- [ ] Version-specific tests created
- [ ] Documentation includes version information
