package com.prinova.messagepoint.tag;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;
import javax.servlet.jsp.JspException;
import javax.servlet.jsp.PageContext;
import javax.servlet.jsp.tagext.Tag;
import javax.servlet.jsp.tagext.TagSupport;
import java.util.Collection;
import java.util.LinkedHashSet;

public class IfAuthGranted extends TagSupport {
	private static final long serialVersionUID = -1653765949586399451L;

	protected static final Log logger = LogFactory.getLog(IfAuthGranted.class);
	private String authority = "";
	private Boolean elseStatement = false;

	public int doStartTag() throws JspException {
		if ((null == authority) || (authority.isEmpty())) {
			logger.debug("(null or empty auth property found, returning SKIP_BODY...");
			return Tag.SKIP_BODY;
		}

		Authentication auth = SecurityContextHolder.getContext().getAuthentication();
		if (auth == null) {
			logger.debug("(auth == null), returning SKIP_BODY...");
			if (elseStatement) {
				return Tag.EVAL_BODY_INCLUDE;
			}
			return Tag.SKIP_BODY;
		}

		Object principal = auth.getPrincipal();
		if (principal == null) {
			logger.debug("auth.getPrincipal() returned a null Object, returning SKIP_BODY...");
			if (elseStatement) {
				return Tag.EVAL_BODY_INCLUDE;
			}
			return Tag.SKIP_BODY;
		}

		if (principal instanceof UserDetails) {
			UserDetails user = (UserDetails) principal;
			Collection<? extends GrantedAuthority> userAuthorities = user.getAuthorities();
			userAuthorities = CollectionUtils.isEmpty(userAuthorities) ? new LinkedHashSet<>() : userAuthorities;

			if (CollectionUtils.isEmpty(userAuthorities)) {
				logger.debug("user.getAuthorities() returned an empty list, returning SKIP_BODY...");
				if (elseStatement) {
					return Tag.EVAL_BODY_INCLUDE;
				}
				return Tag.SKIP_BODY;
			} else {
				logger.debug("auth='" + auth + "'.");
				for (GrantedAuthority grantedAuthority : userAuthorities) {
					String authString = grantedAuthority.getAuthority();
					logger.debug("authString: '" + authString + "'");
					if (authString.equalsIgnoreCase(authority)) {
						logger.debug("Authority '" + authString + "' matched requested authority '" + authority + "'! Returning EVAL_BODY_INCLUDE...");
						if (elseStatement) {
							return Tag.SKIP_BODY;
						}
						return Tag.EVAL_BODY_INCLUDE;
					}
				}
			}
		} else {
			logger.debug("!(principal instanceof UserDetails), returning SKIP_BODY...");
			if (elseStatement) {
				return Tag.EVAL_BODY_INCLUDE;
			}
			return Tag.SKIP_BODY;
		}

		logger.debug("Reached the end of the IfAuthGranted Tag code, returning SKIP_BODY...");
		if (elseStatement) {
			return Tag.EVAL_BODY_INCLUDE;
		}
		return Tag.SKIP_BODY;
	}

	/**
	 * Allows test cases to override where application context obtained from.
	 * 
	 * @param pageContext
	 *            so the <code>ServletContext</code> can be accessed as required
	 *            by Spring's <code>WebApplicationContextUtils</code>
	 * 
	 * @return the Spring application context (never <code>null</code>)
	 */
	protected ApplicationContext getContext(PageContext pageContext) {
		ServletContext servletContext = pageContext.getServletContext();
		return WebApplicationContextUtils.getRequiredWebApplicationContext(servletContext);
	}

	/*
	 * private Integer[] parseIntegersString(String integersString) throws
	 * NumberFormatException { final Set integers = new HashSet(); final
	 * StringTokenizer tokenizer; tokenizer = new
	 * StringTokenizer(integersString, ",", false); while
	 * (tokenizer.hasMoreTokens()) { String integer = tokenizer.nextToken();
	 * integers.add(Integer.valueOf(integer)); } return (Integer[])
	 * integers.toArray(new Integer[] {}); }
	 */

	public void setAuthority(String authority) {
		this.authority = authority;
	}

	public Boolean getElseStatement() {
		return elseStatement;
	}

	public void setElseStatement(Boolean elseStatement) {
		this.elseStatement = elseStatement;
	}

}
