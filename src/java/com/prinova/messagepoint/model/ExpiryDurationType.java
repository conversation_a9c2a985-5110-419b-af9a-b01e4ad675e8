package com.prinova.messagepoint.model;

import com.prinova.messagepoint.model.common.StaticType;
import com.prinova.messagepoint.util.ApplicationUtil;

import java.util.ArrayList;
import java.util.List;

public class ExpiryDurationType extends StaticType {

	private static final long serialVersionUID = 8777270858406678914L;

	public static final int ID_ZERO_DAY				= 0;
	public static final int ID_ONE_DAY				= 1;
	public static final int ID_ONE_WEEK 			= 2;
	public static final int ID_ONE_MONTH			= 3;
	public static final int ID_TWO_MONTHS 			= 4;
	public static final int ID_THREE_MONTHS			= 5;
	public static final int ID_SIX_MONTHS 			= 6;
	public static final int ID_YEAR					= 7;

	public static final String MESSAGE_CODE_ZERO_DAY		= "page.label.expiry.duration.zero.day";
	public static final String MESSAGE_CODE_ONE_DAY 		= "page.label.expiry.duration.one.day";
	public static final String MESSAGE_CODE_ONE_WEEK 		= "page.label.expiry.duration.one.week";
	public static final String MESSAGE_CODE_ONE_MONTH 		= "page.label.expiry.duration.one.month";
	public static final String MESSAGE_CODE_TWO_MONTHS 		= "page.label.expiry.duration.two.months";
	public static final String MESSAGE_CODE_THREE_MONTHS 	= "page.label.expiry.duration.three.months";
	public static final String MESSAGE_CODE_SIX_MONTHS 		= "page.label.expiry.duration.six.months";
	public static final String MESSAGE_CODE_YEAR 			= "page.label.expiry.duration.year";

	public ExpiryDurationType() {
		super();
	}

	public ExpiryDurationType(Integer id) {
		super();
		switch (id) {
		case ID_ZERO_DAY:
			this.setId(ID_ZERO_DAY);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ZERO_DAY));
			break;
		case ID_ONE_DAY:
			this.setId(ID_ONE_DAY);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ONE_DAY));
			break;
		case ID_ONE_WEEK:
			this.setId(ID_ONE_WEEK);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ONE_WEEK));
			break;
		case ID_ONE_MONTH:
			this.setId(ID_ONE_MONTH);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ONE_MONTH));
			break;
		case ID_TWO_MONTHS:
			this.setId(ID_TWO_MONTHS);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_TWO_MONTHS));
			break;
		case ID_THREE_MONTHS:
			this.setId(ID_THREE_MONTHS);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_THREE_MONTHS));
			break;
		case ID_SIX_MONTHS:
			this.setId(ID_SIX_MONTHS);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_SIX_MONTHS));
			break;
		case ID_YEAR:
			this.setId(ID_YEAR);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_YEAR));
			break;
		default:
			break;
		}
	}

	public ExpiryDurationType(String name) {
		super();
		if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_ZERO_DAY))) {
			this.setId(ID_ZERO_DAY);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ZERO_DAY));
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_ONE_DAY))) {
			this.setId(ID_ONE_DAY);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ONE_DAY));
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_ONE_WEEK))) {
			this.setId(ID_ONE_WEEK);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ONE_WEEK));
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_ONE_MONTH))) {
			this.setId(ID_ONE_MONTH);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ONE_MONTH));
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_TWO_MONTHS))) {
			this.setId(ID_TWO_MONTHS);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_TWO_MONTHS));
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_THREE_MONTHS))) {
			this.setId(ID_THREE_MONTHS);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_THREE_MONTHS));
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_SIX_MONTHS))) {
			this.setId(ID_SIX_MONTHS);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_SIX_MONTHS));
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_YEAR))) {
			this.setId(ID_YEAR);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_YEAR));
		}
	}
	
	public static List<ExpiryDurationType> listAll() {
		List<ExpiryDurationType> allRepeatingZoneTypes = new ArrayList<>();
		
		ExpiryDurationType type = null;
		
		type = new ExpiryDurationType(ID_ONE_DAY);
		allRepeatingZoneTypes.add(type);
		
		type = new ExpiryDurationType(ID_ONE_WEEK);
		allRepeatingZoneTypes.add(type);

		type = new ExpiryDurationType(ID_ONE_MONTH);
		allRepeatingZoneTypes.add(type);

		type = new ExpiryDurationType(ID_TWO_MONTHS);
		allRepeatingZoneTypes.add(type);

		type = new ExpiryDurationType(ID_THREE_MONTHS);
		allRepeatingZoneTypes.add(type);

		type = new ExpiryDurationType(ID_SIX_MONTHS);
		allRepeatingZoneTypes.add(type);

		type = new ExpiryDurationType(ID_YEAR);
		allRepeatingZoneTypes.add(type);

		return allRepeatingZoneTypes;
	}	
}
