package com.prinova.messagepoint.platform.services.elasticsearch.search.query;

import com.google.gson.JsonObject;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

public class QueryFilter implements QueryChild {
    private List<QueryChild> filterChildrenList;

    @java.beans.ConstructorProperties({"filterChildrenList"})
    public QueryFilter(List<QueryChild> queryChildren) {
        this.filterChildrenList = queryChildren;
    }

    @Override
    public String getKey() {
        return "filter";
    }

    @Override
    public JsonObject buildJsonObject() {
        JsonObject resultJsonObject = new JsonObject();
        if (CollectionUtils.isNotEmpty(filterChildrenList)) {
            for (QueryChild child : filterChildrenList) {
                resultJsonObject.add(child.getKey(), child.buildJsonObject());
            }
        }
        return resultJsonObject;
    }
}