package com.prinova.messagepoint.platform.services.tpadmin;

import org.springframework.web.multipart.MultipartFile;

import com.prinova.messagepoint.platform.services.ServiceRequest;

import java.util.Set;

public class CreateOrUpdateCompositionFileSetServiceRequest implements ServiceRequest{

	private static final long serialVersionUID = -4358925985456579522L;

	protected String 		name;
	protected Boolean		touchpointDefault;
	protected Boolean		collectionDefault;
	
	private MultipartFile 	templateFile;
	private MultipartFile 	compositionConfigurationFile;
    private Set<MultipartFile> additionalFiles;

    private Long 			documentId;
    private Long			tpCollectionId;
    protected Long 			compositionFileSetId;
    
    protected int			actionId;
    protected String		fileUploadSyncKey;

	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	public Boolean getTouchpointDefault() {
		return touchpointDefault;
	}
	public void setTouchpointDefault(Boolean touchpointDefault) {
		this.touchpointDefault = touchpointDefault;
	}

	public Boolean getCollectionDefault() {
		return collectionDefault;
	}
	public void setCollectionDefault(Boolean collectionDefault) {
		this.collectionDefault = collectionDefault;
	}
	public MultipartFile getTemplateFile() {
		return templateFile;
	}
	public void setTemplateFile(MultipartFile templateFile) {
		this.templateFile = templateFile;
	}

	public MultipartFile getCompositionConfigurationFile() {
		return compositionConfigurationFile;
	}
	public void setCompositionConfigurationFile(
			MultipartFile compositionConfigurationFile) {
		this.compositionConfigurationFile = compositionConfigurationFile;
	}

    public Set<MultipartFile> getAdditionalFiles() {
        return additionalFiles;
    }
    public void setAdditionalFiles(Set<MultipartFile> additionalFiles) {
        this.additionalFiles = additionalFiles;
    }

	public Long getDocumentId() {
		return documentId;
	}
	public void setDocumentId(Long documentId) {
		this.documentId = documentId;
	}
	
	public Long getTpCollectionId() {
		return tpCollectionId;
	}
	public void setTpCollectionId(Long tpCollectionId) {
		this.tpCollectionId = tpCollectionId;
	}
	
	public Long getCompositionFileSetId() {
		return compositionFileSetId;
	}
	public void setCompositionFileSetId(Long compositionFileSetId) {
		this.compositionFileSetId = compositionFileSetId;
	}

	public int getActionId() {
		return actionId;
	}
	public void setActionId(int actionId) {
		this.actionId = actionId;
	}
	
	public String getFileUploadSyncKey() {
		return fileUploadSyncKey;
	}
	public void setFileUploadSyncKey(String fileUploadSyncKey) {
		this.fileUploadSyncKey = fileUploadSyncKey;
	}

}
