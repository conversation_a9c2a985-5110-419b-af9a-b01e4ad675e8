package com.prinova.messagepoint.connected.rest.data;

import java.util.Map;

public class PmgrApplication extends PmgrBaseResponse {

    public Map<String, String> getDefaultVersionIds() {
        return defaultVersionIds;
    }

    public void setDefaultVersionIds(Map<String, String> defaultVersionIds) {
        this.defaultVersionIds = defaultVersionIds;
    }

    protected Map<String, String> defaultVersionIds;

    public String getDefaultVersionId(String environment) {
        if (this.defaultVersionIds != null)
            return this.defaultVersionIds.get(environment);

        return null;
    }
}
