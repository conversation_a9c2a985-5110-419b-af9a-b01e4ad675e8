package com.prinova.messagepoint.platform.services.imports;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.platform.services.imports.document.MessagepointImportHandler;


public final class MetadataFormItemsListImportHandler extends
        MessagepointImportHandler
{
    private TouchpointMetadataImportHandler parent;
    private List<MetadataFormItemImportHandler> children;
    
    public MetadataFormItemsListImportHandler()
    {
        super( MetadataFormItemsListImportHandler.class );
        children = new ArrayList<>();
    }
    
    @Override
    public void addChild(MessagepointImportHandler child)
    {
    	if(child instanceof MetadataFormItemImportHandler) {
    		MetadataFormItemImportHandler handler = (MetadataFormItemImportHandler) child;
    		handler.setParent(this);
    		children.add(handler);
    	}
    }

    @Override
    protected void doneProcessing()
    {

    }
    
    public void setParent(TouchpointMetadataImportHandler p)
    {
        parent = p;
    }
    
    public TouchpointMetadataImportHandler getParent()
    {
        return parent;
    }

	public List<MetadataFormItemImportHandler> getChildren() {
		return children;
	}

	public void setChildren(List<MetadataFormItemImportHandler> children) {
		this.children = children;
	}
}
