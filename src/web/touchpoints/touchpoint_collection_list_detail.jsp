
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>

	<msgpt:HeaderNew title="page.label.messages"  extendedScripts="true" viewType="<%= MessagepointHeader.ViewType.EDIT %>">

		
		
		
		<msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css" />
		
		<msgpt:Script src="includes/javascript/popupActions.js" />
		
		<msgpt:Script src="includes/javascript/jQueryPlugins/sectionImagePopup/jquery.sectionImagePopup.js" />
		<msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js" />

		<style type="text/css">
			.detailsSummaryTable {
				margin-bottom: 24px;
			}
			.detailsSummaryTable td {
				padding: 0px 8px 0px 16px;
				text-align: left;
				font-size: 11px;
			}
			.detailSummaryLeftBorder {
				border-left: 1px solid #f2f2f2;
			}
		</style>

        <msgpt:Script>
            <script>

                function compositionPackageAction(ele) {

                    if ( $(ele).attr('id') == 'compositionPackageUpdateBtn' ) {

                        var packageId = 0;
                        $("input[id^='compositionPackageListItemCheck_']:checked").each(function() {
                            packageId = this.id.replace('compositionPackageListItemCheck_','');
                        });

                        $(ele).iFramePopup({
                            width			: 820,
                            displayOnInit	: true,
                            title			: ${isCollectionFilePackage ? 'client_messages.title.edit_file_package' : 'client_messages.title.edit_composition_package' },
                            src				: context+"/tpadmin/composition_files_upload.form",
                            appliedParams	: {tpCollectionId : "${param.tpCollectionId}", connectorType: "${collectionConnectorTypeId}", compositionFileSetId: packageId, tk : "${param.tk}"},
                            closeBtnId		: "fileUploadButton_close",
                            beforePopupClose: function() {
                                getTopFrame().location.reload();
                            }
                        });
                        return;
                    }

                    var actionId = parseId( $(ele).find('option:selected') );
                    if (actionId == 20)
                        $(ele).iFramePopup({
                            width			: 820,
                            displayOnInit	: true,
                            title			: ${isCollectionFilePackage ? 'client_messages.title.add_file_package' : 'client_messages.title.add_composition_package' },
                            src				: context+"/tpadmin/composition_files_upload.form",
                            appliedParams	: {tpCollectionId : "${param.tpCollectionId}", connectorType: "${collectionConnectorTypeId}", tk : "${param.tk}"},
                            closeBtnId		: "fileUploadButton_close",
                            beforePopupClose: function() {
                                getTopFrame().location.reload();
                            }
                        });
                    else if (actionId = 21)
                        actionSelected(ele);
                }

                // Composition package List Validation
                function validateCompositionPackageActionReq(compositionPackageId) {
					if (${collectionTemplateControlled == false || isCollectionFilePackage})
					{
						var singleSelect = true;

                    	// Resolve selection flags
                    	if ( $("input[id^='compositionPackageListItemCheck_']:checked").length != 1 )
                        	singleSelect = false;

                    	// Disable all actions
                    	$('#compositionPackageUpdateBtn').disableElement();
                    	$("#compositionPackageActionMenu").disableAllOptions();

                    	if ( $("input[id^='compositionPackageListItemCheck_']:checked").length > 0 ){
                    	    $('#compositionPackageUpdateBtn').enableElement();
                    	    $('#compositionPackageActionMenu').enableOption('actionOption_21');  // Delete composition package
                    	}
                    	$('#compositionPackageActionMenu').enableOption('actionOption_20');  // Add composition package
                	}
				}

                $( function() {
                    $("input:button").styleActionElement();
                    $("#actionMenu").styleActionElement();
                    $("#compositionPackageActionMenu").styleActionElement();

                    validateCompositionPackageActionReq();

                    common.refreshParentIframeHeight();
                });
            </script>
        </msgpt:Script>
	</msgpt:HeaderNew>

	<msgpt:BodyNew type="minimal" cssClass="bg-transparent">
	
		<form:form modelAttribute="command" enctype="multipart/form-data">

			<form:errors path="*">
                <msgpt:Information errorMsgs="${messages}" type="error"/>
            </form:errors>
                    
			<form:hidden path="actionValue" id="actionElement"/>
			<table class="detailsSummaryTable" width="100%" cellspacing="0" cellpadding="0" border="0">
				<tr>
					<td width="15%">
						<!-- Composition Packages -->
						<c:set var="compositionPackages" value="${command.compositionFileSetsList}" />
						<c:choose>
							<c:when test="${collectionTemplateControlled == false || isCollectionFilePackage}">
								<!-- COMPOSITION PACKAGES ACTION BAR: ACTIONS -->
								<div class="actionBarContainer" style="margin-top: 8px;">
									<table width="100%" cellspacing="0" cellpadding="0" border="0"><tbody><tr>
										<td width="1%" align="center" style="padding-right: 8px;">
											<input title="${msgpt:getMessage('action.button.label.update')}" type="button" id="compositionPackageUpdateBtn" disabled="disabled" onclick="compositionPackageAction(this)" style="display: none;" />
										</td>
										<td align="left" style="padding: 0px;">
											<select title="${msgpt:getMessage('page.label.actions')}" id="compositionPackageActionMenu" class="inputM style_menu" onchange="compositionPackageAction(this)" style="display: none;">
												<option id="actionOption_20"><fmtSpring:message code="page.label.add"/></option>
												<option id="actionOption_21"><fmtSpring:message code="page.label.delete"/></option>
											</select>
										</td>
										<td/>
									</tr></tbody></table>
								</div>
							</c:when>
						</c:choose>
						<c:choose>
							<c:when test="${!isCollectionFilePackage && collectionTemplateControlled == true}">
								<!-- Composition Packages: List -->
								<msgpt:DataTable listHeader="page.text.channel.template.control">
									<msgpt:TableListGroup>
										<msgpt:TableElement align="left" width="7%" style="vertical-align: middle;">
											<fmtSpring:message code=""/>
										</msgpt:TableElement>
									</msgpt:TableListGroup>
								</msgpt:DataTable>
							</c:when>
							<c:otherwise>
								<c:choose>
									<c:when test="${isCollectionFilePackage && fn:length(compositionPackages) == 0}">
										<!-- Composition Packages: List 0 packages -->
										<msgpt:DataTable listHeader="${msgpt:getMessage('page.label.file.packages.zero')}">
											<msgpt:TableListGroup>
												<msgpt:TableElement align="left" width="7%" style="vertical-align: middle;">
													<fmtSpring:message code="page.text.no.file.package.uploaded"/>
												</msgpt:TableElement>
											</msgpt:TableListGroup>
										</msgpt:DataTable>
									</c:when>
									<c:when test="${!isCollectionFilePackage && fn:length(compositionPackages) == 0}">
										<!-- File Packages: List 0 packages -->
										<msgpt:DataTable listHeader="${msgpt:getMessage('page.label.composition.packages.zero')}">
											<msgpt:TableListGroup>
												<msgpt:TableElement align="left" width="7%" style="vertical-align: middle;">
													<fmtSpring:message code="page.text.no.composition.package.uploaded"/>
												</msgpt:TableElement>
											</msgpt:TableListGroup>
										</msgpt:DataTable>
									</c:when>
									<c:otherwise>
										<!-- Composition Packages: List > 0 Packages -->
										<msgpt:DataTable listHeader="${isCollectionFilePackage ? msgpt:getMessage('page.label.file.packages') : msgpt:getMessage('page.label.composition.packages')} (${fn:length(compositionPackages)})">
											<c:forEach var="currentPackageVO" items="${compositionPackages}" varStatus="status">
												<c:set var="currentPackage" value="${currentPackageVO.compositionFileSet}" />
												<msgpt:TableListGroup>
													<msgpt:TableElement align="left" width="7%" label="" style="vertical-align: middle;">
														<form:checkbox path="compositionFileSetsMap[${currentPackage.id}].selectedForAction" id="compositionPackageListItemCheck_${currentPackage.id}"
															   cssClass="checkbox checkboxAlign" onclick="validateCompositionPackageActionReq(${currentPackage.id});" />
													</msgpt:TableElement>
													<msgpt:TableElement label="page.label.name">
														<span id="compositionPackage_${currentPackage.id}"
														  packageName="${currentPackage.name}"
														  templateFileName="${currentPackage.templateFileName}"
														  configurationFileName="${currentPackage.compositionConfigurationFileName}" >
															<c:out value="${currentPackage.name}"/>
														</span>
													</msgpt:TableElement>
													<msgpt:TableElement label="page.label.created">
														<fmtJSTL:formatDate value="${currentPackage.created}" pattern="${dateTimeFormat}" />
													</msgpt:TableElement>
													<msgpt:TableElement label="page.label.default">
														<c:if test="${defaultCompositionPackageId == currentPackage.id}">
															<div class="checkmarkIcon dataTable_icon_align" />
														</c:if>
													</msgpt:TableElement>
												</msgpt:TableListGroup>
											</c:forEach>
										</msgpt:DataTable>
									</c:otherwise>
								</c:choose>
						</c:otherwise>
						</c:choose>
						</div>					
					</td>
				</tr>
				<tr>
					<td>
						
					</td>		
				</tr>								
			</table>
			
			<!-- POPUP DATA -->
			<div id="actionSpecs" style="display: none;">
				<!-- ACTIONS POPUP DATA -->
				<div id="actionSpec_21" type="simpleConfirm" submitId="21" contentWidth="330px"> <!-- Delete composition package -->
					<div id="actionTitle_21">
						<c:choose>
							<c:when test="${isCollectionFilePackage}">
								<fmtSpring:message code="page.label.confirm.delete.file.packages"/>
							</c:when>
							<c:otherwise>
								<fmtSpring:message code="page.label.confirm.delete.composition.packages"/>
							</c:otherwise>
						</c:choose>
					</div>
					<div id="actionInfo_21">
						<c:choose>
							<c:when test="${isCollectionFilePackage}">
								<fmtSpring:message code="page.text.delete.selected.file.packages"/>
							</c:when>
							<c:otherwise>
								<fmtSpring:message code="page.text.delete.selected.composition.packages"/>
							</c:otherwise>
						</c:choose>
					</div>
				</div>
			</div>
			
			<!-- POPUP INTERFACE -->
			<msgpt:Popup id="actionPopup">
				<div id="actionPopupInfoFrame">
					<div id="actionPopupInfo">&nbsp;</div>
				</div>
				<div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
					<span id="cancelBtnDisabled" style="display: none;"><msgpt:Button URL="#" label="page.label.cancel" disabled="true" /></span>
					<span id="cancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel" /></span>
					<span id="continueBtnDisabled" style="display: none;"><msgpt:Button URL="#" label="page.label.continue" disabled="true" /></span>
					<span id="continueBtnEnabled"><msgpt:Button URL="#" label="page.label.continue" primary="true" /></span>
				</div>				
			</msgpt:Popup>			
		</form:form>
	</msgpt:BodyNew>
</msgpt:Html5>