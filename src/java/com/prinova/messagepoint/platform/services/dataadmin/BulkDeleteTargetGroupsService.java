package com.prinova.messagepoint.platform.services.dataadmin;

import com.prinova.messagepoint.controller.tasks.TaskListItemFilterType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.List;

public class BulkDeleteTargetGroupsService extends AbstractService {
	public static final String SERVICE_NAME = "dataadmin.BulkDeleteTargetGroupsService";
	private static final Log log = LogUtil.getLog(BulkDeleteTargetGroupsService.class);
	
	public void execute(ServiceExecutionContext context) {
		BulkTargetGroupsActionServiceRequest request = (BulkTargetGroupsActionServiceRequest) context.getRequest();
		List<TargetGroup> targetGroupsList = request.getTargetGroupsList();
		if(targetGroupsList == null || targetGroupsList.isEmpty()){
			return;
		}
		try {
			validate(context);
			if(hasValidationError(context)){
				return;
			}
			User requestor = UserUtil.getPrincipalUser();
			for(TargetGroup targetGroup: targetGroupsList){
				// If it is associated with task, delete the task
				Task task = Task.findOneByItemType(TaskListItemFilterType.ID_TARGET_GROUP, targetGroup.getId());
				if(task != null){
					task.delete();
				}

				ServiceExecutionContext context2 = DeleteTargetGroupService.createContext(targetGroup.getId(), requestor.getId());
				Service service = MessagepointServiceFactory.getInstance()
						.lookupService(DeleteTargetGroupService.SERVICE_NAME, DeleteTargetGroupService.class);
				service.execute(context2);
				if(!context2.getResponse().isSuccessful()){
					context2.getResponse().mergeResultMessages(context2.getResponse());
				}
			}
		} catch (Exception e) {
			log.error(" unexpected exception when invoking BulkDeleteTargetGroupsService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {
	}
	
	public static SimpleExecutionContext createContext(List<TargetGroup> targetGroupsList) {
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		BulkTargetGroupsActionServiceRequest request = new BulkTargetGroupsActionServiceRequest();
		context.setRequest(request);
		
		request.setTargetGroupsList(targetGroupsList);
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}	

}
