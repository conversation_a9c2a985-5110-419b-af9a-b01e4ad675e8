package com.prinova.messagepoint.platform.services.dataadmin.jsonlayout;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataDefinition;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataElement;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataModelHelper;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDefinitionType;

import com.prinova.messagepoint.model.util.DataTypeIdentifierUtil;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;

import java.io.File;
import java.io.IOException;
import java.util.*;

public class ImportDataSourceLayoutFromJSONService extends AbstractService{
	
	public static final String SERVICE_NAME="dataadmin.ImportDataSourceLayoutFromJSONService";
	private static final Log log = LogUtil.getLog(ImportDataSourceLayoutFromJSONService.class);
	
	private void saveDefsToDatabase(DataDefinitionWrapper rootDefinition, DataSource dataSource, JSONDataDefinition parentDefinition) {
		JSONDataDefinition currentDefinition = null;
		switch (rootDefinition.definitionType) {
			case JSONDefinitionType.ID_OBJECT:
				currentDefinition = JSONDataModelHelper.createJSONDataDefinition(dataSource, rootDefinition.definitionName, JSONDefinitionType.ID_OBJECT, "No", "No", null);
				break;
			case JSONDefinitionType.ID_ARRAY:
				currentDefinition = JSONDataModelHelper.createJSONDataDefinition(dataSource, rootDefinition.definitionName, JSONDefinitionType.ID_ARRAY, "No", rootDefinition.isRepeating?"Yes":"No", null);
				break;
			case JSONDefinitionType.ID_KEY:
				currentDefinition = JSONDataModelHelper.createJSONDataDefinition(dataSource, rootDefinition.definitionName, JSONDefinitionType.ID_KEY, rootDefinition.isStartCustomer?"Yes":"No", "No", null);

				if(rootDefinition.hasDataElement()) {
					String defName = rootDefinition.definitionName;
					String deValue = rootDefinition.dataElement.value;
					int dataType = DataTypeIdentifierUtil.identifyDataTypeWithName(defName, deValue);
					JSONDataElement dataElement = JSONDataModelHelper.createJSONDataElement(currentDefinition, rootDefinition.definitionName, 0, "Keep Blanks", 0, false, null);
					dataElement.setDataTypeId(dataType);
					dataElement.setDataSubtypeId(DataTypeIdentifierUtil.getSubTypeFromDataType(dataType));
					dataElement.setAnonymized(false);
					dataSource.getDataElements().add(dataElement);
					dataElement.save(true);
					currentDefinition.setJsonDataElement(dataElement);
				}

				break;
			default:
				throw new RuntimeException("Invalid definition type");
		}
        currentDefinition.save(true);
		currentDefinition.setParentDefinition(parentDefinition);

        for(DataDefinitionWrapper childDef : rootDefinition.children) {
			saveDefsToDatabase(childDef, dataSource, currentDefinition);
		}

		dataSource.save();
	}
	
	private DataDefinitionWrapper buildRootDefinition(File tempFile) {
		String jsonFile = FileUtil.getFileAsString(tempFile.getPath());
		String mergedJsonFile = mergeJson(jsonFile);
		Object rootObject = new JSONTokener(mergedJsonFile).nextValue();
		return this.buildDefinitionTree(rootObject, null);
	}

	private DataDefinitionWrapper buildDefinitionTree(Object rootObject, DataDefinitionWrapper parentDefinition){
		if(rootObject instanceof JSONObject rootJSONObject){
			DataDefinitionWrapper objectDef = new DataDefinitionWrapper();
			objectDef.definitionName = "OBJECT";
			objectDef.definitionType = JSONDefinitionType.ID_OBJECT;
			if(parentDefinition!=null){
				objectDef.parent = parentDefinition;
				parentDefinition.children.add(objectDef);
			}
			Iterator<String> keys = rootJSONObject.keys();
			while(keys.hasNext()){
				String key = keys.next();
				// Key definition
				DataDefinitionWrapper keyDef = new DataDefinitionWrapper();
				keyDef.definitionName = key;
				keyDef.definitionType = JSONDefinitionType.ID_KEY;
				keyDef.parent = objectDef;
				objectDef.children.add(keyDef);
				// Value definition
				Object value = rootJSONObject.get(key);
				buildDefinitionTree(value, keyDef);
			}
			return objectDef;
		}else if(rootObject instanceof JSONArray rootJSONArray){
			DataDefinitionWrapper arrayDef = new DataDefinitionWrapper();
			arrayDef.definitionName = "ARRAY";
			arrayDef.definitionType = JSONDefinitionType.ID_ARRAY;
			arrayDef.isRepeating = true;	// Assure it is repeating
			if(parentDefinition!=null){
				arrayDef.parent = parentDefinition;
				parentDefinition.children.add(arrayDef);
			}
			for(int i=0; i<rootJSONArray.length(); i++){
				Object value = rootJSONArray.get(i);
				buildDefinitionTree(value, arrayDef);
			}
			return arrayDef;
		}else{
			// Data Element
			String value = rootObject.toString();
			DataElementWrapper dataElement = new DataElementWrapper();
			dataElement.value = value;
			if(parentDefinition!=null){
				parentDefinition.dataElement = dataElement;
				dataElement.ownerDefinition = parentDefinition;
			}
			return null;
		}
	}

	/**
	 * Merges the JSON input by removing duplicate keys and merging array elements
     */
	public static String mergeJson(String jsonInput) {
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			JsonNode root = objectMapper.readTree(jsonInput);

			if (root.isObject()) {
				ObjectNode mergedObject = objectMapper.createObjectNode();
				mergeObjects(mergedObject, (ObjectNode) root, objectMapper);
				return objectMapper.writeValueAsString(mergedObject);
			} else if (root.isArray()) {
				ArrayNode mergedArray = objectMapper.createArrayNode();
				mergeArrayElements(mergedArray, root, objectMapper);
				return objectMapper.writeValueAsString(mergedArray);
			} else {
				return jsonInput; // If it's neither an object nor an array, return as is
			}
		} catch (Exception e) {
			return null;
		}
	}

	private static void mergeObjects(ObjectNode mergedObject, ObjectNode objectToMerge, ObjectMapper objectMapper) {
		Iterator<Map.Entry<String, JsonNode>> fields = objectToMerge.fields();

		while (fields.hasNext()) {
			Map.Entry<String, JsonNode> entry = fields.next();
			String key = entry.getKey();
			JsonNode value = entry.getValue();

			if (!mergedObject.has(key)) {
				if (value.isObject()) {
					ObjectNode nestedObject = objectMapper.createObjectNode();
					mergeObjects(nestedObject, (ObjectNode) value, objectMapper);
					mergedObject.set(key, nestedObject);
				} else if (value.isArray()) {
					ArrayNode nestedArray = objectMapper.createArrayNode();
					mergeArrayElements(nestedArray, value, objectMapper);
					mergedObject.set(key, nestedArray);
				} else {
					mergedObject.set(key, value);
				}
			}
		}
	}

	private static void mergeArrayElements(ArrayNode mergedArray, JsonNode arrayNode, ObjectMapper objectMapper) {
		ObjectNode mergedObject = objectMapper.createObjectNode();
		for (JsonNode arrayElement : arrayNode) {
			if (arrayElement.isObject()) {
				mergeObjects(mergedObject, (ObjectNode) arrayElement, objectMapper);
			}
		}
		mergedArray.add(mergedObject);
	}

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			ImportDataSourceLayoutFromJSONServiceRequest request = (ImportDataSourceLayoutFromJSONServiceRequest) context.getRequest();
			DataSource dataSource = request.getDataSource();
		    File importedJSONFile = request.getImportedJSONFile();
		    DataDefinitionWrapper rootTag = buildRootDefinition(importedJSONFile);
		    saveDefsToDatabase(rootTag, dataSource, null);
		} catch (Exception e) {
			log.error(" unexpected exception when invoking ImportDataSourceLayoutFromJSONService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
				context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
		
	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
		ImportDataSourceLayoutFromJSONServiceRequest request = (ImportDataSourceLayoutFromJSONServiceRequest) context.getRequest();
		File importedJSONFile = request.getImportedJSONFile();
		boolean parsed = true;
		try {
			ObjectMapper objectMapper = new ObjectMapper();

			// Parse the JSON file and check for any parsing errors
			JsonNode jsonNode = objectMapper.readTree(importedJSONFile);

			// If no exceptions are thrown, the JSON file is valid
		} catch (JsonParseException | JsonMappingException e) {
			// The JSON file is invalid
			parsed = false;
		} catch (IOException e) {
			// Handle file IO errors
			parsed = false;
		}

		if(!parsed) {
			context.getResponse().addErrorMessage("importFilename", "error.message.json.parse_failed", new Object[]{ request.getOriginalFileName() }, ImportDataSourceLayoutFromJSONService.SERVICE_NAME, context.getLocale());
		}
	}
	public static ServiceExecutionContext createContext(){
		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		ImportDataSourceLayoutFromJSONServiceRequest request = new ImportDataSourceLayoutFromJSONServiceRequest();
		context.setRequest(request);	
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);
		
		return context;
	}

    private static class DataElementWrapper {
    	public DataDefinitionWrapper ownerDefinition;
    	public String value;
    	public DataElementWrapper() {
    		ownerDefinition = null;
    		value = null;
    	}
    }
    
	public static class DataDefinitionWrapper {
		public DataDefinitionWrapper parent;
		private int definitionType;
		public String definitionName;
		public DataElementWrapper dataElement;
		public List<DataDefinitionWrapper> children;
//		public String value;
		public boolean isRepeating;
		public boolean isStartCustomer;
		
		public DataDefinitionWrapper() {
			this.parent = null;
			this.definitionType = JSONDefinitionType.ID_OBJECT;
			this.definitionName = "";
			this.dataElement = null;
			this.children = new ArrayList<>();
//			value = null;
			this.isRepeating = false;
			this.isStartCustomer = false;
		}
		public boolean hasDataElement() {
			return dataElement!=null;
		}
	}
}
