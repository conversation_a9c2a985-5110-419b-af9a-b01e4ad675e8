package com.prinova.messagepoint.controller.content;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.content.ContentAssociationType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.message.content.TouchpointContentObjectContentSelection;
import com.prinova.messagepoint.model.proof.Proof;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.util.UserUtil;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 
 * Prinova Inc. 1998-2011
 * 678 Belmont Ave West, Unit 201, KW, ON N2M 1N6
 * All rights reserved.
 * 
 * ContentObjectContentViewController
 *
 * @contentRefactor
 * 
 * @since 3.5
 * <AUTHOR> Team
 */
public class ContentObjectContentViewController extends ContentObjectViewController {

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = super.referenceData(request);
		//adding your own reference data
		ContentObject contentObject = ContentObject.findByHttpServletRequest(request);
		if (contentObject != null) {
			if(contentObject.isStructuredContentEnabled()) {
				referenceDataForTPCS(referenceData, contentObject, request);
			} else {
				referenceDataForRegular(referenceData, contentObject);
			}

			Document touchpointContext = UserUtil.getCurrentTouchpointContext();
			List<MessagepointLocale> locales = touchpointContext.getTouchpointLanguagesAsLocales();
			if (contentObject.isGlobalContentObject())
			{
				referenceData.put("focusLocaleId", MessagepointLocale.getDefaultSystemLanguageLocale().getId());
			}
			else {
				MessagepointLocale currentLanguageLocale = UserUtil.getCurrentLanguageLocaleContext();
				if (currentLanguageLocale != null && locales.contains(currentLanguageLocale)) {
					referenceData.put("focusLocaleId", currentLanguageLocale.getId());
				} else {
					referenceData.put("focusLocaleId", locales.get(0).getId());
				}
			}
		}

		referenceData.put("contentType_CUSTOM",ContentAssociationType.ID_OWNS);
		referenceData.put("contentType_SUPPRESSES",ContentAssociationType.ID_SUPPRESSES);
		referenceData.put("contentType_SAME_AS",ContentAssociationType.ID_REFERENCES);

		return referenceData;
	}
	
	private void referenceDataForTPCS(Map<String, Object> referenceData, ContentObject contentObject, HttpServletRequest request) throws Exception {
		Document document;
		TouchpointSelection touchpointSelection;

		referenceData.put("contentObject", contentObject);
		
		TouchpointContentObjectContentSelection contentSelection = TouchpointContentObjectContentSelection.findDefaultByContentObject(contentObject);
		document = contentSelection.getTouchpointSelection().getDocument();
		touchpointSelection = contentSelection.getTouchpointSelection();
		referenceData.put("contentSelection", contentSelection);
		List<MessagepointLocale> langLocales = contentObject.getContentObjectLanguagesAsLocales();
		referenceData.put("locales", langLocales);
		referenceData.put("languagesJSON", MessagepointLocale.getAsJSON(langLocales));
		referenceData.put("defaultLocaleId", contentObject.getDefaultContentObjectLanguageAsLocale().getId());
		referenceData.put("defaultLanguage", contentObject.getDefaultContentObjectLanguageAsLocale().getName());

		referenceData.put("document", document);

		if ( contentObject.getZone() != null )
			referenceData.put("zoneId", contentObject.getZone().getId());

		referenceData.put("touchpointSelection", touchpointSelection);

		referenceData.put("searchParameters", document.getSelectionParameterGroup().getParameterGroupItemsSorted());

		Map<Long, List<Proof>> proofs = new HashMap<>();
		referenceData.put("proofs", proofs);
		for (MessagepointLocale langLocale : langLocales) {
			List<Proof> langProofs = new ArrayList<>();
			if(contentObject.isFocusOnActiveData()){
				langProofs.addAll(Proof.findActiveProofsForLang(touchpointSelection, langLocale.getLanguageCode(), document.getIsOmniChannel()));
			}else {
				langProofs.addAll(Proof.findAllNoneActiveProofsForLang(touchpointSelection, langLocale.getLanguageCode(), document.getIsOmniChannel()));
			}
			List<Proof> twoContentLangProofs = new ArrayList<>();
			List<Proof> twoVariantLangProofs = new ArrayList<>();
			for (Proof proof : langProofs) {
				if(proof.getType()==Proof.PROOF_TYPE_VARIANT){
					if (twoVariantLangProofs.size()<2) {
						twoVariantLangProofs.add(proof);
					}
				}else{
					if(proof.getContentObject() != null &&
							proof.getContentObject().getId()==contentObject.getId() &&
							twoContentLangProofs.size()<2){
						twoContentLangProofs.add(proof);
					}
				}
			}
			proofs.put(langLocale.getId(), twoContentLangProofs);
			proofs.get(langLocale.getId()).addAll(twoVariantLangProofs);
		}

		referenceData.put("canProofForLanguage", touchpointSelection.getCanProofForLanguage(langLocales));
		referenceData.put("isStatusViewActive", contentObject.isFocusOnActiveData());

		TouchpointSelection currentSelection = touchpointSelection;
		List<TouchpointSelection> selectionHierarchyList = new ArrayList<>();
		selectionHierarchyList.add(currentSelection);

		while (currentSelection.getParent() != null) {
			currentSelection = currentSelection.getParent();
			selectionHierarchyList.add(currentSelection);
		}
		Collections.reverse(selectionHierarchyList);
		referenceData.put("selectionHierarchy", selectionHierarchyList);

		ContentObjectContentWrapper contentWrapper = new ContentObjectContentWrapper(contentObject, ContentObjectContentWrapper.ACTION_VIEW);
		referenceData.put("wrapper", contentWrapper);
	}
	
	private void referenceDataForRegular(Map<String, Object> referenceData, ContentObject contentObject) throws Exception {

		if(contentObject.isMultipartType()){
			ContentObjectContentWrapper contentWrapper = new ContentObjectContentWrapper(contentObject, ContentObjectContentWrapper.ACTION_VIEW);
			
			referenceData.put("wrapper", contentWrapper);
		}

		TouchpointContentObjectContentSelection contentSelection = TouchpointContentObjectContentSelection.findDefaultByContentObject(contentObject);
		referenceData.put("contentSelection", contentSelection);

		List<MessagepointLocale> langLocales = contentObject.getContentObjectLanguagesAsLocales();
		referenceData.put("locales", langLocales);
		referenceData.put("languagesJSON", MessagepointLocale.getAsJSON(langLocales));
		referenceData.put("defaultLocaleId", contentObject.getDefaultContentObjectLanguageAsLocale().getId());
		referenceData.put("defaultLanguage", contentObject.getDefaultContentObjectLanguageAsLocale().getName());

		if (contentObject.isStructuredContentEnabled())
			referenceData.put("touchpointContentSelection", TouchpointContentObjectContentSelection.findDefaultByContentObject(contentObject));

		referenceData.put("userHasTouchpointSelectionContentViewPermission", UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ROLE_LICENCED_TOUCHPOINT_SELECTIONS_VIEW));
	}
}
