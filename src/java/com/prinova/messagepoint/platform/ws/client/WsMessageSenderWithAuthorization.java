package com.prinova.messagepoint.platform.ws.client;

import java.io.IOException;
import java.net.HttpURLConnection;

import org.apache.commons.codec.binary.Base64;
import org.springframework.ws.transport.http.HttpUrlConnectionMessageSender;

public class WsMessageSenderWithAuthorization extends HttpUrlConnectionMessageSender {
	private String username;
	private String password;

	@Override
	protected void prepareConnection(HttpURLConnection connection)
			throws IOException {
		
		String userpassword = getUsername() + ":" + getPassword();
		
		String encodedAuthorization = Base64.encodeBase64String(userpassword.getBytes());
		connection.setRequestProperty("Authorization", "Basic " + encodedAuthorization);
		super.prepareConnection(connection);
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}
}