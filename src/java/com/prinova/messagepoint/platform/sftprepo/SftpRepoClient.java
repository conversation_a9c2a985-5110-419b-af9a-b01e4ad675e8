package com.prinova.messagepoint.platform.sftprepo;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.prinova.messagepoint.controller.content.ContentObjectSftpImageUploadController;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.admin.SubContentType;
import com.prinova.messagepoint.model.content.Content;
import com.prinova.messagepoint.util.SftpException;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class SftpRepoClient {

	private static final Log log = LogUtil.getLog(ContentObjectSftpImageUploadController.class);

	private MpSftpClient sftp;
	private String host;
	private Integer port;
	private String user;
	private String passwd;
	private String folder;
	private String baseFolder;
	
	public SftpRepoClient()
	{
		host = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.SftpRepoSettings.KEY_SftpRepoHost);
		port = SystemPropertyManager.getInstance().getIntegerSystemProperty(SystemPropertyKeys.SftpRepoSettings.KEY_SftpRepoPort, 22);
		user = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.SftpRepoSettings.KEY_SftpRepoUser);
		passwd = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.SftpRepoSettings.KEY_SftpRepoPassword);
		folder = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.SftpRepoSettings.KEY_SftpRepoFolder);
		if ( folder == null || folder.isEmpty() )
			folder = ".";
		baseFolder = folder;
		
		try {
			sftp = new JSchSftpClient(host, port, user, passwd);
		} catch ( SftpException e ) {
			log.error( String.format("Error connecting to %s@%s:%d password %d characters long. Detail: ", user, host, port, passwd.length()), e );
			sftp = null;
		}
	}

	public String getFolder() { return this.folder; }
	public void setFolder( String folder )
	{
		if ( folder.startsWith(this.baseFolder) )
		{
			if ( folder.endsWith("/..") ) {
				int i = folder.lastIndexOf('/', folder.length() - 4);
				this.folder = folder.substring( 0, i );
			} else
				this.folder = folder;
		}
	}
	
	public List<SftpRepoFolder> listFolder() 
	{
		if ( !this.isValid() )
			return null;
		
		try {
			boolean disconnect = false;
			if ( !sftp.isConnected() ) {
				if ( !sftp.connect() )
					return null;
				disconnect = true;
			}
			List<String> strFolders = sftp.listFolders(folder);
			ArrayList<SftpRepoFolder> folders = new ArrayList<>(strFolders.size());
			if( !folder.equals(baseFolder) ) {
				folders.add(new SftpRepoFolder(getFolder()));
				folders.add(new SftpRepoFolder(getFolder().concat("/..")));
			} else { 
				folders.add(new SftpRepoFolder(baseFolder));
			}
			for ( int i = 0; i < strFolders.size(); i++ )
				folders.add(new SftpRepoFolder(getFolder().concat("/" + strFolders.get(i))));
			if ( disconnect )
				sftp.disconnect();
			return folders;
		} catch( SftpException e ) {
			log.error( String.format("Error listing folder %s from %s@%s:%d password %d characters long. Detail: ", folder, user, host, port, passwd.length()), e );
			return null;
		}
	}
	

	public List<SftpRepoImage> listImages(boolean checkAlreadyUploaded) {
        return listImages( checkAlreadyUploaded, 3, 10 );
    }

	public List<SftpRepoImage> listImages(boolean checkAlreadyUploaded, int retries, int waitSeconds)
	{
		List<SftpRepoImage> sftpImages = null;
		
		if ( !this.isValid() )
			return null;
		
		boolean disconnect = false;
		boolean downloaded = false;
		while( !downloaded && retries > 0 ) {
			try {
				if ( !sftp.isConnected() ) {
					if ( !sftp.connect() )
						return null;
					disconnect = true;
				}
				List<MpSftpFile> files = sftp.listFiles(folder, SubContentType.getGraphicSupportedExtensions());
				downloaded = true;
				
				// From sftp image files list, transform it in SftpRepoImage
				Function<MpSftpFile, SftpRepoImage> toSftpImage =
                        new Function<>() {
                            @Override
                            public SftpRepoImage apply(MpSftpFile file) {
                                SftpRepoImage img = new SftpRepoImage(getFolder(), file.getFilename());
                                img.setUpdatedDate(file.getLastModifiedDate());
                                img.setSize(file.getSize());

                                if (checkAlreadyUploaded) {
                                    // This increases time of iteration through the list
                                    // Used by UI
                                    List<Content> contentIds = Content.findAllByAssetId(img.getId());
                                    if (!contentIds.isEmpty()) {
                                        img.setAlreadyUploaded(true);
                                    }
                                }

                                return img;
                            }
                        };
						
				sftpImages = Lists.transform(files, toSftpImage);
	
			} catch( SftpException e ) {
				log.warn(String.format("SFTP Exception (retry: %d): %s", retries, e.getMessage() ));
				try {
					sftp.disconnect();
					if (retries > 2)
						TimeUnit.SECONDS.sleep(2);
					else
						TimeUnit.SECONDS.sleep(waitSeconds);
	                sftp.connect();
	                disconnect = false;
	            } catch (InterruptedException e1) {
	                // stop retrying because the thread was suspended
	                retries = 0; 
	            }
			} finally {
				if ( disconnect )
					sftp.disconnect();
				retries--;
			}
		}
		
		return sftpImages;
	}
	
	
	public static class SftpRepoFolder {
		private String id;
		private String name;
		public SftpRepoFolder(String name) {
			this.setName(name);
		}
		public String getId() {
			return id;
		}
		public String getName() {
			return name;
		}
		private void setId(String id) {
			this.id = id.replaceAll("[./\\-\\(\\)\\[\\]#|\"'\\* ]", "_");
		}
		private void setName(String name) {
			setId(name);
			this.name = name;
		}
	}
	
	public static class SftpRepoImage {
		private String id;
		private String name;
		private String folder;
		private String filename;
		private Date updatedDate;
		private long size;
		private boolean alreadyUploaded;
		
		public SftpRepoImage(String folder, String filename) {
			setFolder(folder);
			setFilename(filename);
			setName(filename.substring(0, filename.lastIndexOf('.')));
			setId(getFilePath());
			setAlreadyUploaded(false);
		}
		
		private void setId(String id) {
			this.id = id.replaceAll("[./\\-\\(\\)\\[\\]#|\"'\\* ]", "_");
		}
		private void setFolder(String folder) {
			this.folder = folder;
		}
		private void setFilename(String filename) {
			this.filename = filename;
		}
		
		public String getId() {
			return id;
		}
		public String getName() {
			return name;
		}
		public void setName(String name) {
			this.name = name.replaceAll("[^a-zA-Z0-9 _'\\-]", "");
		}
		public String getFolder() {
			return folder;
		}
		public String getFilename() {
			return filename;
		}
		public String getFilePath() {
			return this.folder + "/" + this.filename;
		}

		public boolean getAlreadyUploaded() {
			return alreadyUploaded;
		}
		public void setAlreadyUploaded(boolean alreadyUploaded) {
			this.alreadyUploaded = alreadyUploaded;
		}

		public Date getUpdatedDate() {
			return updatedDate;
		}

		public void setUpdatedDate(Date updatedDate) {
			this.updatedDate = updatedDate;
		}

		public long getSize() {
			return size;
		}

		public void setSize(long size) {
			this.size = size;
		}
	}

    public byte[] downloadFile( String filenameFullPath ) {
        return downloadFile( filenameFullPath, 3, 10 );
    }

	public byte[] downloadFile( String filenameFullPath, int retries, int waitSeconds ) {
		byte[] fileContent = null;
		boolean disconnect = false;
		boolean downloaded = false;
		while( !downloaded && retries > 0 ) {
    		try {
    			if ( !sftp.isConnected() ) {
    				if ( !sftp.connect() )
    					return fileContent;
    				disconnect = true;
    			}
    			fileContent = sftp.downloadFile( filenameFullPath );
                downloaded = true;
    		} catch( SftpException e ) {
    			log.warn(String.format("SFTP Exception (retry: %d): %s", retries, e.getMessage() ));
    			try {
    				sftp.disconnect();
    				if (retries > 2)
    					TimeUnit.SECONDS.sleep(2);
    				else
    					TimeUnit.SECONDS.sleep(waitSeconds);
                    sftp.connect();
                    disconnect = false;
                } catch (InterruptedException e1) {
                    // stop retrying because the thread was suspended
                    retries = 0; 
                }
    		} finally {
    			if ( disconnect )
    				sftp.disconnect();
    			retries--;
    		}
		}
		return fileContent;
	}
	
	public boolean connect() {
		return sftp.connect();
	}
	
	public void disconnect() {
		sftp.disconnect();
	}

	public boolean isValid() {
		return sftp != null;
	}
	
	public SftpRepoImage createSftpRepoImage(String filename) {
		SftpRepoImage image = new SftpRepoImage(getFolder(), filename);
		image.setUpdatedDate(getLastModifiedDate(image.getFilePath(), 3, 10));
		return image;
	}
	
	public Date getLastModifiedDate(String filenameFullPath, int retries, int waitSeconds) {
		Date date = null;
		boolean disconnect = false;
		boolean downloaded = false;
		while ( !downloaded && retries > 0 ) {
    		try {
    			if ( !sftp.isConnected() ) {
    				if ( !sftp.connect() )
    					return date;
    				disconnect = true;
    			}
    			date = sftp.getLastModifiedDate( filenameFullPath );
                downloaded = true;
    		} catch( SftpException e ) {
                log.warn(String.format("SFTP Exception (retry: %d): %s", retries, e.getMessage() ));
                try {
    				sftp.disconnect();
    				if (retries > 2)
    					TimeUnit.SECONDS.sleep(2);
    				else
    					TimeUnit.SECONDS.sleep(waitSeconds);
                    sftp.connect();
                    disconnect = false;
                } catch (InterruptedException e1) {
                    // stop retrying because the thread was suspended
                    retries = 0; 
                }
    		} finally {
    			if ( disconnect )
    				sftp.disconnect();
                retries--;
    		}
		}
		
		return date;
	}

}
