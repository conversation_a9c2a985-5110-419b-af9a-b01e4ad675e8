package com.prinova.messagepoint.controller.admin;

import java.text.DateFormatSymbols;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.TouchpointLanguage;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.TouchpointLocale;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.admin.UpdateLocaleSettingsService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.NestedPageUtils;

public class UpdateLocaleSettingsController extends MessagepointController {
	
	public static final String LOCALE_SETTINGS_ID_PARAMETER 	= "localeSettingsId";
	public static final String TOUCHPOINT_LANGUAGE_ID_PARAMETER = "touchpointLanguageId";
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		
		long localeSettingsId = ServletRequestUtils.getLongParameter(request, LOCALE_SETTINGS_ID_PARAMETER, -1);
		long touchpointLanguageId = ServletRequestUtils.getLongParameter(request, TOUCHPOINT_LANGUAGE_ID_PARAMETER, -1);

		if ( touchpointLanguageId > 0 ) {
			TouchpointLanguage touchpointLanguage = TouchpointLanguage.findById(touchpointLanguageId);
			TouchpointLocale touchpointLocale = null;
			if( touchpointLanguage.getTouchpointLocale() == null ) {
				touchpointLocale = new TouchpointLocale(touchpointLanguage.getMessagepointLocale());
			} else {
				touchpointLocale = touchpointLanguage.getTouchpointLocale();
			}
			return new Command(touchpointLocale);	
		} else {
			MessagepointLocale messagepointLocale = null;
			if( localeSettingsId == -1 ) {
				messagepointLocale = new MessagepointLocale();
			} else {
				messagepointLocale = HibernateUtil.getManager().getObject(MessagepointLocale.class, localeSettingsId);
			}
			return new Command(messagepointLocale);
		}
		
		
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command, BindException error) throws Exception {
		Command c = (Command)command;

		long localeSettingsId = ServletRequestUtils.getLongParameter(request, LOCALE_SETTINGS_ID_PARAMETER, -1);
		long touchpointLanguageId = ServletRequestUtils.getLongParameter(request, TOUCHPOINT_LANGUAGE_ID_PARAMETER, -1);
		
		String[] shortMonthNames = null;
		String[] longMonthNames = null;
		
		if (c.getOverrideMonths())
		{
			shortMonthNames = c.getShortMonthNames();
			longMonthNames = c.getLongMonthNames();
		}
		
		ServiceExecutionContext context = UpdateLocaleSettingsService.createContext(localeSettingsId,
																					touchpointLanguageId,
																					c.getName(),
																					c.isFavourite(),
																					c.isDefaultLocale(),
																					c.getLanguageCode(),
																					c.getDateFormat(),
																					c.getCurrencySymbol(),
																					c.isSuffixCurrencySymbol(),
																					c.getThousandsSeparator(),
																					c.getDecimalSymbol(),
																					c.getNumberOfDecimals(),
																					c.getBooleanSymbolTrue(),
																					c.getBooleanSymbolFalse(), 
																					shortMonthNames, 
																					longMonthNames,
																					c.getDictionary(), 
																					null,
																					c.getDropDecimalForWholeNum());

		Service updateLocaleSettingsService = MessagepointServiceFactory.getInstance().lookupService(UpdateLocaleSettingsService.SERVICE_NAME, UpdateLocaleSettingsService.class);
		updateLocaleSettingsService.execute(context);
		ServiceResponse serviceResponse = context.getResponse();
		if (!serviceResponse.isSuccessful()) {
			//serviceResponse.convertToSpringErrors(errors);
			ServiceResponseConverter.convertToSpringErrors(serviceResponse, error);
			return super.showForm(request, response, error);
		} else {
			Map<String, Object> parms = new HashMap<>();
			parms.put(LOCALE_SETTINGS_ID_PARAMETER, localeSettingsId);
			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
			return new ModelAndView(new RedirectView("../frameClose.jsp"),parms);
		}
	}

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		referenceData.put("dateFormats", getDateFormats());
		referenceData.put("months",new DateFormatSymbols().getMonths());
		return referenceData;
	}

	private static List<String> getDateFormats() {
		List<String> values = Arrays.asList("m/d/yy", "m/d/yyyy", "mm/dd/yy", "mm/dd/yyyy", "mm-dd-yy", 
		                            		"mm-dd-yyyy", "mmddyy", "mmddyyyy", "d/m/yyyy", "dd/mm/yy", 
		                            		"dd/mm/yyyy", "dd-mm-yy", "ddmmyy", "ddmmyyyy", "yyddmm", "yymmdd", "yyyyddmm",
		                            		"yyyymmdd", "yyyy-mm-dd", "yyyy/mm/dd", "m/yy", "mm/yy", "m/yyyy", "mm/yyyy",
		                            		"mmyy", "dd/mm", "mm/dd", "M dd", "M dd, yyyy", "M d, yyyy", "MM dd, yyyy", "MM d, yyyy", "MM yyyy",
		                            		"E, MM d, yyyy", "dd MM yyyy", "d MM yyyy", "d M yyyy", "MM d", "yyyy", "m", "MM", "dd", "d", "N", "E", "M" );
		return values;
	}
	
	public static class Command {
		
		private long localeId;
		private String name;
		private String code;
		private boolean favourite;
		private boolean defaultLocale;
		private String languageName;
		private String languageDisplayName;
		private String languageCode;
		private String dictionary;
		private String dateFormat;
		private String currencySymbol;
		private boolean suffixCurrencySymbol;
		private String thousandsSeparator;
		private String decimalSymbol;
		private String numberOfDecimals;
		private String booleanSymbolTrue;
		private String booleanSymbolFalse;
		private String[] shortMonthNames;
		private String[] longMonthNames;
		private Boolean overrideMonths;
		private Boolean dropDecimalForWholeNum;
		
		private Command (MessagepointLocale messagepointLocale) {
			this.localeId = messagepointLocale.getId();
			this.name = messagepointLocale.getName();
			this.code = messagepointLocale.getCode();
			this.favourite = messagepointLocale.isFavourite();
			this.defaultLocale = messagepointLocale.isDefaultLocale();
			this.languageName = messagepointLocale.getLanguageName();
			this.languageDisplayName = messagepointLocale.getLanguageDisplayName();
			this.languageCode = messagepointLocale.getLanguageCode();
			this.dictionary = messagepointLocale.getDictionary();
			this.dateFormat = messagepointLocale.getDateFormat();
			this.currencySymbol = messagepointLocale.getCurrencySymbol();
			this.suffixCurrencySymbol = messagepointLocale.isSuffixCurrencySymbol();
			this.thousandsSeparator = messagepointLocale.getThousandsSeparator();
			this.decimalSymbol = messagepointLocale.getDecimalSymbol();
			this.numberOfDecimals = messagepointLocale.getNumberOfDecimals();
			this.booleanSymbolTrue = messagepointLocale.getBooleanSymbolTrue();
			this.booleanSymbolFalse = messagepointLocale.getBooleanSymbolFalse();
			if (messagepointLocale.getShortMonthNames() != null && !messagepointLocale.getShortMonthNames().trim().isEmpty()) {
				this.shortMonthNames = messagepointLocale.getShortMonthNames().split(MessagepointLocale.MONTH_NAME_SEPARATOR);
			} else {
				this.shortMonthNames = new String[12];
			}
			if (messagepointLocale.getLongMonthNames() != null && !messagepointLocale.getLongMonthNames().trim().isEmpty()) {
				this.longMonthNames = messagepointLocale.getLongMonthNames().split(MessagepointLocale.MONTH_NAME_SEPARATOR);
			} else {
				this.longMonthNames = new String[12];
			}
			this.overrideMonths = (messagepointLocale.getShortMonthNames() != null && !messagepointLocale.getShortMonthNames().trim().isEmpty()) ||
								  (messagepointLocale.getLongMonthNames() != null && !messagepointLocale.getLongMonthNames().trim().isEmpty());
			this.dropDecimalForWholeNum = messagepointLocale.getDropDecimalForWholeNum();
		}
		
		private Command (TouchpointLocale touchpointLocale) {
			this.localeId = touchpointLocale.getMasterLocale().getId();
			this.name = touchpointLocale.getMasterLocale().getName();
			this.code = touchpointLocale.getMasterLocale().getCode();
			this.favourite = touchpointLocale.getMasterLocale().isFavourite();
			this.defaultLocale = touchpointLocale.getMasterLocale().isDefaultLocale();
			this.languageName = touchpointLocale.getMasterLocale().getLanguageName();
			this.languageDisplayName = touchpointLocale.getMasterLocale().getLanguageDisplayName();
			this.languageCode = touchpointLocale.getMasterLocale().getLanguageCode();
			
			this.dictionary = touchpointLocale.getDictionary();
			this.dateFormat = touchpointLocale.getDateFormat();
			this.currencySymbol = touchpointLocale.getCurrencySymbol();
			this.suffixCurrencySymbol = touchpointLocale.isSuffixCurrencySymbol();
			this.thousandsSeparator = touchpointLocale.getThousandsSeparator();
			this.decimalSymbol = touchpointLocale.getDecimalSymbol();
			this.numberOfDecimals = touchpointLocale.getNumberOfDecimals();
			this.booleanSymbolTrue = touchpointLocale.getBooleanSymbolTrue();
			this.booleanSymbolFalse = touchpointLocale.getBooleanSymbolFalse();
			if (touchpointLocale.getShortMonthNames() != null && !touchpointLocale.getShortMonthNames().trim().isEmpty()) {
				this.shortMonthNames = touchpointLocale.getShortMonthNames().split(MessagepointLocale.MONTH_NAME_SEPARATOR);
			} else {
				this.shortMonthNames = new String[12];
			}
			if (touchpointLocale.getLongMonthNames() != null && !touchpointLocale.getLongMonthNames().trim().isEmpty()) {
				this.longMonthNames = touchpointLocale.getLongMonthNames().split(MessagepointLocale.MONTH_NAME_SEPARATOR);
			} else {
				this.longMonthNames = new String[12];
			}
			this.overrideMonths = (touchpointLocale.getShortMonthNames() != null && !touchpointLocale.getShortMonthNames().trim().isEmpty()) ||
								  (touchpointLocale.getLongMonthNames() != null && !touchpointLocale.getLongMonthNames().trim().isEmpty());
			this.dropDecimalForWholeNum = touchpointLocale.getDropDecimalForWholeNum();
		}

		public long getLocaleId() {
			return localeId;
		}

		public String getName() {
			return name;
		}

		public String getCode() {
			return code;
		}
		
		public boolean isFavourite() {
			return favourite;
		}
		public void setFavourite(boolean favourite) {
			this.favourite = favourite;
		}

		public boolean isDefaultLocale() {
			return defaultLocale;
		}
		public void setDefaultLocale(boolean defaultLocale) {
			this.defaultLocale = defaultLocale;
		}

		public String getLanguageName() {
			return languageName;
		}

		public String getLanguageDisplayName() {
			return languageDisplayName;
		}

		public String getLanguageCode() {
			return languageCode;
		}
		public void setLanguageCode(String languageCode) {
			this.languageCode = languageCode;
		}

		public String getDictionary() {
			return dictionary;
		}
		public void setDictionary(String dictionary) {
			this.dictionary = dictionary;
		}

		public String getDateFormat() {
			return dateFormat;
		}
		public void setDateFormat(String dateFormat) {
			this.dateFormat = dateFormat;
		}

		public String getCurrencySymbol() {
			return currencySymbol;
		}
		public void setCurrencySymbol(String currencySymbol) {
			this.currencySymbol = currencySymbol;
		}
		
		public boolean isSuffixCurrencySymbol() {
			return suffixCurrencySymbol;
		}
		public void setSuffixCurrencySymbol(boolean suffixCurrencySymbol) {
			this.suffixCurrencySymbol = suffixCurrencySymbol;
		}

		public String getThousandsSeparator() {
			return thousandsSeparator;
		}
		public void setThousandsSeparator(String thousandsSeparator) {
			this.thousandsSeparator = thousandsSeparator;
		}

		public String getDecimalSymbol() {
			return decimalSymbol;
		}
		public void setDecimalSymbol(String decimalSymbol) {
			this.decimalSymbol = decimalSymbol;
		}

		public String getNumberOfDecimals() {
			return numberOfDecimals;
		}
		public void setNumberOfDecimals(String numberOfDecimals) {
			this.numberOfDecimals = numberOfDecimals;
		}

		public String getBooleanSymbolTrue() {
			return booleanSymbolTrue;
		}
		public void setBooleanSymbolTrue(String booleanSymbolTrue) {
			this.booleanSymbolTrue = booleanSymbolTrue;
		}

		public String getBooleanSymbolFalse() {
			return booleanSymbolFalse;
		}
		public void setBooleanSymbolFalse(String booleanSymbolFalse) {
			this.booleanSymbolFalse = booleanSymbolFalse;
		}

		public String[] getShortMonthNames() {
			return shortMonthNames;
		}
		public void setShortMonthNames(String[] shortMonthNames) {
			this.shortMonthNames = shortMonthNames;
		}

		public String[] getLongMonthNames() {
			return longMonthNames;
		}
		public void setLongMonthNames(String[] longMonthNames) {
			this.longMonthNames = longMonthNames;
		}

		public Boolean getOverrideMonths() {
			return overrideMonths;
		}
		public void setOverrideMonths(Boolean overrideMonths) {
			this.overrideMonths = overrideMonths;
		}

		public Boolean getDropDecimalForWholeNum() {
			return dropDecimalForWholeNum;
		}
		public void setDropDecimalForWholeNum(Boolean dropDecimalForWholeNum) {
			this.dropDecimalForWholeNum = dropDecimalForWholeNum;
		}

	}
}
