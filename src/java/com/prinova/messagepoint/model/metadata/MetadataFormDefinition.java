package com.prinova.messagepoint.model.metadata;

import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.tagcloud.TagCloudType;
import com.prinova.messagepoint.model.webservice.WebServiceConfiguration;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.tagcloud.UpdateTagCloudService;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import com.prinova.messagepoint.wtu.ReferencableObject;
import org.hibernate.query.NativeQuery;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import static com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils.getNonNullOrEmptyString;

public final class MetadataFormDefinition extends IdentifiableMessagePointModel {

	private static final long serialVersionUID = 6233753734684280921L;

	private int								type					= MetadataFormDefinitionType.ID_GENERAL;
	
	private Set<MetadataFormItemDefinition>	formItemDefinitions		= new HashSet<>();
	
	private WebServiceConfiguration			webServiceConfiguration;

    private boolean                     needRecalculateHash = true;
    private String                      sha256Hash;

	// Public default constructor
	//
	public MetadataFormDefinition(){
	}

	// Copy constructor (not public) for cloning
	//
	protected MetadataFormDefinition(MetadataFormDefinition cloneFrom){
		super(cloneFrom);

        if (MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
            this.setParentObject(cloneFrom);        // Set parent
        } else {
            this.setGuid(cloneFrom.getGuid());
        }

        CloneHelper.execInSaveSession(()-> {
            this.save();
        });

        copyDataFrom(cloneFrom);

        CloneHelper.addNewClonedMetadataFormDefinition(cloneFrom, this);
	}
	
	@Override
	public Object clone(){
		return new MetadataFormDefinition(this);
	}

    public void copyDataFrom(MetadataFormDefinition cloneFrom) {
        this.name = cloneFrom.getName();
        this.description = cloneFrom.description;
        this.type = cloneFrom.type;
        this.metatags = cloneFrom.metatags;

        if (cloneFrom.formItemDefinitions != null) {
            boolean isCloneInSameInstance = ! this.getGuid().equals(cloneFrom.getGuid());
            Set<MetadataFormItemDefinition> newItems = new HashSet<>();
            Set<Long> itemIdsInTargetOnly = CloneHelper.queryInSaveSession(()->new HashSet<>(getFormItemDefinitions().stream().map(MetadataFormItemDefinition::getId).collect(Collectors.toSet())));
            for (MetadataFormItemDefinition sourceMetadataFormItemDefinition : cloneFrom.getMetadataFormItemDefinitionsInOrder()) {
                MetadataFormItemDefinition targetFormItemDefinition = isCloneInSameInstance ? null : CloneHelper.assignAlreadyClonedObject(sourceMetadataFormItemDefinition);
                if(targetFormItemDefinition == null) {
                    MetadataFormItemDefinition cloneFormItemDefinition = CloneHelper.assign(sourceMetadataFormItemDefinition, o -> {
                        MetadataFormItemDefinition clonedMetadataFormItemDefinition = o.clone(this);
                        return clonedMetadataFormItemDefinition;
                    });

                    CloneHelper.mapNewClonedObject(sourceMetadataFormItemDefinition, cloneFormItemDefinition);

                    CloneHelper.mapClonedObject(sourceMetadataFormItemDefinition, cloneFormItemDefinition);

                    CloneHelper.execInSaveSession(() -> {
                        cloneFormItemDefinition.save();
                        this.formItemDefinitions.add(cloneFormItemDefinition);
                        newItems.add(cloneFormItemDefinition);
                    });
                }
                else {
                    targetFormItemDefinition.copyMetadataFormItemDefinitionFrom(sourceMetadataFormItemDefinition, this, true);
                    CloneHelper.execInSaveSession(() -> {
                        targetFormItemDefinition.save();
                    });
                    itemIdsInTargetOnly.remove(targetFormItemDefinition.getId());
                }
            }
            CloneHelper.execInSaveSession(()->{
                for(Long itemId : itemIdsInTargetOnly) {
                    MetadataFormItemDefinition metadataFormItemDefinition = MetadataFormItemDefinition.findById(itemId);
                    this.formItemDefinitions.remove(metadataFormItemDefinition);
                    MetadataFormItemDefinition.findMetadataFormItemsByDefinition(metadataFormItemDefinition).forEach(fi->{
                        if(fi.getMetadataForm() != null) {
                            fi.getMetadataForm().getFormItems().remove(fi);
                        }
                        fi.delete();
                    });
                    metadataFormItemDefinition.delete();
                }
                if(!newItems.isEmpty()) {
                    Set<MetadataForm> metadataForms = new HashSet<>(MetadataForm.findByDefinition(this.getId()));
                    if (! metadataForms.isEmpty()) {
                        for (MetadataFormItemDefinition newMetadataFormItemDefinition : newItems) {
                            for (MetadataForm metadataForm : metadataForms) {
                                MetadataFormItem newItem = new MetadataFormItem();
                                newItem.setItemDefinition(newMetadataFormItemDefinition);
                                newItem.setMetadataFormDefinition(this);
                                newItem.setMetadataForm(metadataForm);
                                newItem.save(true);
                                metadataForm.getFormItems().add(newItem);
                                metadataForm.save();
                            }
                        }
                    }
                }
            });
        }

        CloneHelper.execInSaveSession(()-> {
            if(this.webServiceConfiguration != null){
                this.webServiceConfiguration.delete();
            }
            this.webServiceConfiguration = null;
        });

        if (cloneFrom.webServiceConfiguration != null) {
            this.webServiceConfiguration = CloneHelper.clone(cloneFrom.webServiceConfiguration, o->o.clone());
            CloneHelper.execInSaveSession(()-> {
                this.webServiceConfiguration.save();
            });
        }
    }

	public List<MetadataFormItemDefinition> getMetadataFormItemDefinitionsInOrder() {
		Set<MetadataFormItemDefinition> formItemDefSet = getFormItemDefinitions();
		List<MetadataFormItemDefinition> formItemDefList = new ArrayList<>();
		for (MetadataFormItemDefinition currentFormItemDef: formItemDefSet) {
			if (currentFormItemDef.getOrder() >= 0) {
				formItemDefList.add(currentFormItemDef);
			}
		}
		formItemDefList.sort((o1, o2) -> {
			if (o1.getOrder() > o2.getOrder()) return 1;
			else if (o1.getOrder() < o2.getOrder()) return -1;
			return 0;
		});
		
		return formItemDefList;
	}

	public List<MetadataFormItemDefinition> getAllMetadataFormItemDefinitionsInAlphabeticalOrder() {
		Set<MetadataFormItemDefinition> formItemDefSet = getFormItemDefinitions();
        List<MetadataFormItemDefinition> formItemDefList = new ArrayList<>(formItemDefSet);
		formItemDefList.sort((o1, o2) -> {
			final String primaryConnector1 = getNonNullOrEmptyString(o1.getPrimaryConnector());
			final String primaryConnector2 = getNonNullOrEmptyString(o2.getPrimaryConnector());

			return primaryConnector1.compareToIgnoreCase(primaryConnector2);
		});
		return formItemDefList;
	}

    public static List<MetadataFormDefinition> findAll() {
        List<MetadataFormDefinition> list = HibernateUtil.getManager().getObjects(MetadataFormDefinition.class);
        return list;
    }

	public static MetadataFormDefinition findUniqueByParentId(long parentId){
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("parentObject.id", parentId));
		List<MetadataFormDefinition> list = HibernateUtil.getManager().getObjectsAdvanced(MetadataFormDefinition.class, critList, MessagepointOrder.desc("created"));
		if (!list.isEmpty()) {
			return list.iterator().next();
		} else {
			return null;
		}
	}
	
	public static MetadataFormDefinition findById(long id) {
		return HibernateUtil.getManager().getObject(MetadataFormDefinition.class, id);
	}

	public static MetadataFormDefinition findByGuid(String guid) {
        return HibernateUtil.getManager().getObjectByGuid(MetadataFormDefinition.class, guid);
    }
	
	@SuppressWarnings("unchecked")
	public static List<MetadataFormDefinition> findByType(int typeId) {
		String hql = 	"FROM 		MetadataFormDefinition mfd " +
						"WHERE 		mfd.type = 1 OR mfd.type = :typeId " +
						"ORDER BY 	name";

		Map<String, Object> params = new HashMap<>();
		params.put("typeId", typeId);
		return (List<MetadataFormDefinition>) HibernateUtil.getManager().getObjectsAdvanced(hql,params);
	}
	@SuppressWarnings("unchecked")
	public static List<MetadataFormDefinition> findByTypeStrict(int typeId) {
		String hql = 	"FROM 		MetadataFormDefinition mfd " +
						"WHERE 		mfd.type = :typeId " +
						"ORDER BY 	name";

		Map<String, Object> params = new HashMap<>();
		params.put("typeId", typeId);
		return (List<MetadataFormDefinition>) HibernateUtil.getManager().getObjectsAdvanced(hql,params);
	}

    @Override
    public String getSha256Hash() {
        return sha256Hash;
    }

    @Override
    public void setSha256Hash(String sha256Hash) {
        this.sha256Hash = sha256Hash;
    }

    @Override
	public void preSave(Boolean isNew) {
		super.preSave(isNew);

		if(needRecalculateHash || sha256Hash == null) {
		    makeHash(false);
        }

		Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateTagCloudService.SERVICE_NAME, UpdateTagCloudService.class);
		ServiceExecutionContext context = UpdateTagCloudService.createContext(TagCloudType.ID_METADATA_FORM, this.getMetatags(), null, false);
		if ( service != null && context != null )
			service.execute(context);
	}

    public void makeHash(boolean isAlgorithmChanged) {
        StringBuilder hashDataStringBuilder = new StringBuilder();
        hashDataStringBuilder.append("metadataFormDefinition");

        hashDataStringBuilder.append(" name:").append(getName());
        if(getMetatags() != null && !getMetatags().isEmpty()) {
            hashDataStringBuilder.append(" metatags:").append(getMetatags());
        }

        if(getDescription() != null && !getDescription().isEmpty()) {
            hashDataStringBuilder.append(" description:").append(getDescription());
        }

        hashDataStringBuilder.append(" type:").append(getType());

        for(MetadataFormItemDefinition item : getMetadataFormItemDefinitionsInOrder()) {
            StringBuilder itemHashDataStringBuilder = new StringBuilder();
            itemHashDataStringBuilder.append("metadataFormItemDefinition");
            itemHashDataStringBuilder.append(" name:").append(item.getName());
            if(item.getDataElement() != null) {
                itemHashDataStringBuilder.append(" dataElement:").append(item.getDataElement().getDna());
            }
            itemHashDataStringBuilder.append(" typeId:").append(item.getTypeId());
            if(item.getMenuValueItems() != null) {
                itemHashDataStringBuilder.append(" menuValueItems:").append(calculateSha256Hash(null, false, null, item.getMenuValueItems()));
            }
            itemHashDataStringBuilder.append(" webServiceRefreshTypeId:").append(item.getWebServiceRefreshTypeId());
            if(item.getPrimaryConnector() != null && ! item.getPrimaryConnector().isEmpty()) {
                itemHashDataStringBuilder.append(" primaryConnector:").append(item.getPrimaryConnector());
            }
            if(item.getConnectorElementMap() != null && ! item.getConnectorElementMap().isEmpty()) {
                Map<String, String> sortedConnectorElementMap =
						new TreeMap<>(
								item.getConnectorElementMap().entrySet().stream()
										.collect(Collectors.toMap(ce -> ce.getKey(), ce -> ce.getValue().getDna()))
						);

                String itemConnectorElementMap = sortedConnectorElementMap.entrySet().stream()
                        .sequential().map(ek->"[" + ek.getKey() + "]=" + ek.getValue()).collect(Collectors.joining(","));

                itemHashDataStringBuilder.append(" connectorElementMap:").append(itemConnectorElementMap);

            }

            itemHashDataStringBuilder.append(" isManadatory:").append(item.getIsManadatory() ? "T" : "F");

            if(item.getDescription() != null && ! item.getDescription().isEmpty()) {
                itemHashDataStringBuilder.append(" description:").append(item.getDescription());
            }

            if(item.getDisplayTriggerValues() != null && ! item.getDisplayTriggerValues().isEmpty()) {
                itemHashDataStringBuilder.append(" displayTriggerValues:").append(item.getDisplayTriggerValues());
            }

            itemHashDataStringBuilder.append(" fieldSizeTypeId:").append(item.getFieldSizeTypeId());

            if(item.getFieldMaxLength() != null) {
                itemHashDataStringBuilder.append(" fieldMaxLength:").append(item.getFieldMaxLength());
            }

            if(item.getInputValidationTypeId() != null) {
                itemHashDataStringBuilder.append(" inputValidationTypeId:").append(item.getInputValidationTypeId());
            }

            if(item.getDefaultInputValue() != null) {
                itemHashDataStringBuilder.append(" defaultInputValue:").append(item.getDefaultInputValue());
            }

            if(item.isDefaultDateValueToTodaysDate()) {
                itemHashDataStringBuilder.append(" defaultDateValueToTodaysDate:").append(item.isDefaultDateValueToTodaysDate() ? "T" : "F");
            }

            if(item.isUniqueValue()) {
                itemHashDataStringBuilder.append(" uniqueValue:").append(item.isUniqueValue() ? "T" : "F");
            }

            String itemHash = calculateSha256Hash(null, false, null, itemHashDataStringBuilder.toString());
            hashDataStringBuilder.append(" item:").append(item.getOrder()).append(":").append(itemHash);
        }

        String objectHashKey = getObjectHashKey();
        sha256Hash = calculateSha256Hash(objectHashKey, isAlgorithmChanged, sha256Hash, hashDataStringBuilder.toString());
    }

    @Override
    public Map<String, Object> getAttributesMap() {
        Map<String, Object> attributesMap = super.getAttributesMap();

        attributesMap.put("page.label.attribute.metadata.form.definition.name", getName());

        if(getMetatags() != null && !getMetatags().isEmpty()) {
            attributesMap.put("page.label.attribute.metadata.form.definition.metatags", getMetatags());
        }

        if(getDescription() != null && !getDescription().isEmpty()) {
            attributesMap.put("page.label.attribute.metadata.form.definition.description", getDescription());
        }

        attributesMap.put("page.label.attribute.metadata.form.definition.type", new MetadataFormDefinitionType(getType()).getDisplayText());

        if(getWebServiceConfiguration() != null) {
            attributesMap.put("page.label.attribute.metadata.form.definition.web.service.configuration", getWebServiceConfiguration().getUrl() == null ? "" : getWebServiceConfiguration().getUrl());
        }

        return attributesMap;
    }


    public boolean isReferenced() {
    	String[] queries = {
    		"FROM Document AS d WHERE d.touchpointMetadataFormDefinition = :mfd OR d.variantMetadataFormDefinition = :mfd",
    		"FROM Task AS t WHERE t.metadataForm.formDefinition = :mfd",
    		"FROM Project AS p WHERE p.metadataForm.formDefinition = :mfd",
    		"FROM TouchpointSelection AS ts WHERE ts.metadataForm.formDefinition = :mfd",
    		"FROM ContentObject AS co LEFT JOIN co.contentObjectDataTypeMap AS cod WHERE cod.metadataForm.formDefinition = :mfd ",
    		"FROM TouchpointCollection AS tc WHERE tc.metadataForm.formDefinition = :mfd"
    	};
		
    	Map<String, Object> params = new HashMap<>();
    	params.put("mfd", this);
    	List<?> list = null;
    	for( String query : queries ) {
	    	list = HibernateUtil.getManager().getObjectsAdvanced(query, params);
	    	if( !list.isEmpty() ) return true;
    	}
    	return false;
	}
	
	public MetadataFormItemDefinition findItemDefinitionByConnector(String connector) {
		for ( MetadataFormItemDefinition currentItemDefinition: formItemDefinitions )
			if ( currentItemDefinition.getPrimaryConnector().equalsIgnoreCase(connector) )
				return currentItemDefinition;
		return null;
	}

	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}

	public Set<MetadataFormItemDefinition> getFormItemDefinitions() {
		return formItemDefinitions;
	}
	public void setFormItemDefinitions(Set<MetadataFormItemDefinition> formItemDefinitions) {
		this.formItemDefinitions = formItemDefinitions;
	}

	public WebServiceConfiguration getWebServiceConfiguration() {
		return webServiceConfiguration;
	}
	public void setWebServiceConfiguration(WebServiceConfiguration webServiceConfiguration) {
		this.webServiceConfiguration = webServiceConfiguration;
	}

	public String getTypeDisplayLabel() {
		return new MetadataFormDefinitionType(this.type).getName();
	}

    public static Map<Long, List<ReferencableObject>> getAllReferencesMap() {
        Map<Long, List<ReferencableObject>> allReferencesMap = new HashMap<>();
        Map<Long, Set<Long>> alreadyMappedObjects = new HashMap<>();

        {
            String query =
                "SELECT DISTINCT MFD.ID AS METADATA_FORM_DEFINITION_ID, CO.ID CONTENT_OBJECT_ID " +
                    "FROM METADATA_FORM_DEFINITION MFD " +
                    "JOIN METADATA_FORM MF ON MF.FORM_DEFINITION_ID = MFD.ID " +
                    "JOIN CONTENT_OBJECT_DATA COD ON COD.DATA_TYPE IN (1, 2) AND COD.METADATA_FORM_ID = MF.ID " +
                    "JOIN CONTENT_ObjECT CO ON CO.ID = COD.CONTENT_OBJECT_ID"
                ;

            getAllReferencesMapByQuery(query, ContentObject.class, allReferencesMap, alreadyMappedObjects);
        }

        {
            String query =
                "SELECT DISTINCT MFD.ID AS METADATA_FORM_DEFINITION_ID, TS.ID VARIANT_ID " +
                    "FROM METADATA_FORM_DEFINITION MFD " +
                    "JOIN METADATA_FORM MF ON MF.FORM_DEFINITION_ID = MFD.ID " +
                    "JOIN touchpoint_selection ts ON ts.METADATA_FORM_ID = MF.ID "
                ;

            getAllReferencesMapByQuery(query, TouchpointSelection.class, allReferencesMap, alreadyMappedObjects);
        }

        return allReferencesMap;
    }

    private static void getAllReferencesMapByQuery(String query, Class<? extends IdentifiableMessagePointModel> referencesClass, Map<Long, List<ReferencableObject>> allReferencesMap, Map<Long, Set<Long>> alreadyMappedObjects) {
        NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
        for(Object obj : sqlQuery.list()){
            Object[] ids = (Object[])obj;
            MetadataFormDefinition metadataFormDefinition = findById(((BigInteger)ids[0]).longValue());
            IdentifiableMessagePointModel referencingModelObject = HibernateUtil.getManager().getObject(referencesClass, ((BigInteger)ids[1]).longValue());

            if(metadataFormDefinition != null && referencingModelObject != null) {
                Set<Long> mappedObjectsForThisTemplate = alreadyMappedObjects.get(metadataFormDefinition.getId());
                if(mappedObjectsForThisTemplate == null) {
                    mappedObjectsForThisTemplate = new HashSet<>();
                    alreadyMappedObjects.put(metadataFormDefinition.getId(), mappedObjectsForThisTemplate);
                }
                if(! mappedObjectsForThisTemplate.contains(referencingModelObject.getId())) {
                    ReferencableObject referencableObject = new ReferencableObject(referencingModelObject);
                    List<ReferencableObject> list = allReferencesMap.get(metadataFormDefinition.getId());
                    if(list == null) {
                        list = new ArrayList<>();
                        allReferencesMap.put(metadataFormDefinition.getId(), list);
                    }
                    list.add(referencableObject);
                    mappedObjectsForThisTemplate.add(referencingModelObject.getId());
                }
            }
        }
    }

}