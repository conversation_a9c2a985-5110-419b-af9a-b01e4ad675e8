import {JQueryUtil} from "../../util/JQueryUtil";

export class ComputeBodyTabInterval {

    public constructor(
        private rootJQuery: JQuery,
    ) {}

    public execute(): string {
        let bodyJQuery = this.rootJQuery.is('body') ?
            this.rootJQuery :
            this.rootJQuery.find('body');
        if (bodyJQuery.length === 0) {
            return null;
        }
        let bodyStyle = bodyJQuery.attr('style');
        if (!bodyStyle || bodyStyle.length === 0) {
            return null;
        }
        let bodyTabInterval = JQueryUtil.parseCssPropertyFromStyleAttribute(bodyStyle, 'tab-interval');
        if (!bodyTabInterval || bodyTabInterval.length === 0) {
            return null;
        }

        return bodyTabInterval;
    }
}