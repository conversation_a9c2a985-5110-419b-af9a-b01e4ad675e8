package com.prinova.messagepoint.platform.ws;

import java.io.File;

import com.prinova.messagepoint.model.testing.DataFile;
import org.apache.commons.logging.Log;
import org.jdom2.Element;
import org.springframework.ws.server.endpoint.annotation.Endpoint;
import org.springframework.ws.server.endpoint.annotation.PayloadRoot;
import org.springframework.ws.server.endpoint.annotation.RequestPayload;
import org.springframework.ws.server.endpoint.annotation.ResponsePayload;

import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.LogUtil;

@Endpoint
public class DataFileDownloadEndpoint extends FileReturningEndpoint
{
    private static final Log log = LogUtil.getLog(DataFileDownloadEndpoint.class);

    public DataFileDownloadEndpoint()
    {
        super("DataFileDownloadResponse", "DataFileDownloadRequest");
    }

    @PayloadRoot(namespace = NAMESPACE_URI, localPart = "DataFileDownloadRequest")
    @ResponsePayload
    public Element handleRequest(@RequestPayload Element request) throws Exception
    {
        try
        {
            return handleRequestImpl(request);
        }
        catch( Exception ex )
        {
            log.error("Web service exception:", ex);
            throw ex;
        }
    }

    private Element handleRequestImpl(Element request) throws Exception {
        Long chunkNum = Long.valueOf(0);
        long chunkSize = FileReturningEndpoint.chunkSize; // default value
        String dfName = getChild(request, "Name", true).getText();
        if (childExists(request, "ChunkNumber")) {
            String chunkNumStr = getChild(request, "ChunkNumber", false).getText();
            if (chunkNumStr != null)
                chunkNum = Long.parseLong(chunkNumStr);
        }
        if (childExists(request, "ChunkSize")) {
            String chunkSizeStr = getChild(request, "ChunkSize", false).getText();
            if (chunkSizeStr != null) {
                chunkSize = Long.parseLong(chunkSizeStr);
                if (chunkSize < 1) {
                    return makeFailureResponse("Chunk size should be positive number");
                }
            }
        }

        DataFile df = DataFile.findByName(dfName);
        if (df == null) {
            return makeFailureResponse("No data file was found with name = " + dfName);
        }
        File dfFile = df.getFile();
        if (dfFile == null) {
            return makeFailureResponse("Corrupted data file with name = " + dfName);
        }

        if (chunkNum == 0) { // Return one response
            return makeFileResponse(dfFile.getPath());
        } else { // Return in chunks
            long chunks = countChunks(dfFile, chunkSize);
            if ( chunkNum > chunks || chunkNum < 0 )
                return makeFailureResponse("Chunk requested outside boundary");

            return makeFilePartResponse(dfFile, chunkNum, chunks, chunkSize);
        }
    }

    public String getDisplayName() {
        return ApplicationUtil.getMessage("page.label.web.service.download.data.file");
    }
}
