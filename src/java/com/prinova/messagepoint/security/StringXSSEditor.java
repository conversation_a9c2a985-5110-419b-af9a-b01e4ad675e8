package com.prinova.messagepoint.security;

import java.beans.PropertyEditorSupport;
import java.nio.charset.StandardCharsets;

import com.prinova.messagepoint.util.DataSecurityUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.owasp.validator.html.AntiSamy;
import org.owasp.validator.html.CleanResults;
import org.owasp.validator.html.Policy;
import org.owasp.validator.html.PolicyException;
import org.owasp.validator.html.ScanException;

import com.prinova.messagepoint.util.ApplicationUtil;

public class StringXSSEditor extends PropertyEditorSupport {

	private static final Log log = LogUtil.getLog(StringXSSEditor.class);
	
	private static Policy policy;
	
    public StringXSSEditor() {
        super();
    }

	public static String getCleanString(String value) {
		if ( value == null )
			return null;

		try {
			AntiSamy antiSamy = new AntiSamy();
			Policy policy = getPolicy();
			CleanResults cleanResults = antiSamy.scan(value, policy);
			return cleanResults.getCleanHTML().trim();
		} catch (PolicyException e) {
			log.error("Error: Could no generate policy for XSS input scrubbing", e);
		} catch (ScanException e) {
			log.error("Error: Unabled to scan input for XSS scrubbing", e);
		}
		return value;
	}
    
    private static Policy getPolicy() {
    	try {
    		if ( policy == null)
				policy = Policy.getInstance(ApplicationUtil.getRootPath() + "/WEB-INF/security/antisamy-prinova-custom.xml");
		} catch (PolicyException e) {
			log.error("Error: Could no generate policy for XSS input scrubbing", e);
		}
    	return policy;
    }

    public void setAsText(String text) {
        if (text == null) {
            setValue(null);
        } else {
			//	Convert ISO-8859-1 to UTF-8
			byte[] bytes = text.getBytes(StandardCharsets.ISO_8859_1);
			String value = new String(bytes, StandardCharsets.UTF_8);
			
            value = value.replace("&#65279;", "");
            value = value.replace("&#8203;", "");

            if ( value.indexOf("<") != -1 ) {
				try {

					AntiSamy antiSamy = new AntiSamy();
					Policy policy = getPolicy();

					// Insert placeholder for conditional comments: Antisamy will remove them otherwise
					String[] commentsplit = value.split("(<!--\\[if)|(<!\\[endif\\]-->)");
					StringBuilder commentPlaceholder = new StringBuilder();
					for ( int i = 0 ; i < commentsplit.length; i++ ) {
						if ( (i & 1) == 0 )
							commentPlaceholder.append( commentsplit[i] );
						else
							commentPlaceholder.append("||COND_COMMENT").append(i).append("||");
					}

					// Clean current value
					CleanResults cleanResults;
					cleanResults = antiSamy.scan(commentPlaceholder.toString(), policy);
					value = cleanResults.getCleanHTML().trim();

					// Restore any conditional comments
					for ( int i = 0 ; i < commentsplit.length; i++ ) {
						if ((i & 1) != 0)
							value = value.replace("||COND_COMMENT" + i + "||", "<!--[if" + commentsplit[i] + "<![endif]-->");
					}

				} catch (PolicyException e) {
					log.error("Error: Could no generate policy for XSS input scrubbing", e);
				} catch (ScanException e) {
					log.error("Error: Unabled to scan input for XSS scrubbing", e);
				}
            }
            
            boolean containsNullByte = false;
			try {
				containsNullByte 		= !DataSecurityUtils.verifyNullByteDoesNotExist(value);
			} catch (Exception e) {
				containsNullByte 		= true;
			}
			if ( containsNullByte )
				value = null;

            setValue(value);
        }
    }

    public String getAsText() {
        Object value = getValue();
        return (value != null ? value.toString() : "");
    }
}