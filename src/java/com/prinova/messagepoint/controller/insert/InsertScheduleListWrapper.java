package com.prinova.messagepoint.controller.insert;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;

public class InsertScheduleListWrapper implements Serializable {

	private static final long serialVersionUID = 5133913305729752404L;

	private Date 								dateFilter = DateUtil.today();
	private String 								userNote;
	private User 								assignedToUser;
	private boolean 							includeInsertTargeting;
	private String 								actionValue;
	private List<Long>							selectedIds;

	public InsertScheduleListWrapper() {
		super();
	}

	public Date getDateFilter() {
		return dateFilter;
	}

	public void setDateFilter(Date dateFilter) {
		this.dateFilter = dateFilter;
	}

	public String getUserNote() {
		return userNote;
	}
	public void setUserNote(String userNote) {
		this.userNote = userNote;
	}
	public User getAssignedToUser() {
		return assignedToUser;
	}
	public void setAssignedToUser(User assignedToUser) {
		this.assignedToUser = assignedToUser;
	}

	public String getActionValue() {
		return actionValue;
	}

	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}

	public List<Long> getSelectedIds() {
		return selectedIds;
	}

	public void setSelectedIds(List<Long> selectedIds) {
		this.selectedIds = selectedIds;
	}
	public List<InsertSchedule> getSelectedList(){
		List<InsertSchedule> selectedList = new ArrayList<>();
		if(this.selectedIds != null){
			for(Long selectedId : this.selectedIds){
				selectedList.add(HibernateUtil.getManager().getObject(InsertSchedule.class, selectedId));
			}
		}
		return selectedList;
	}

	public boolean isIncludeInsertTargeting() {
		return includeInsertTargeting;
	}
	public void setIncludeInsertTargeting(boolean includeInsertTargeting) {
		this.includeInsertTargeting = includeInsertTargeting;
	}
	
}