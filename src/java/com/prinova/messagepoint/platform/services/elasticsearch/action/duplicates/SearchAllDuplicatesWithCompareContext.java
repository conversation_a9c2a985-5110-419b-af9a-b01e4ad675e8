package com.prinova.messagepoint.platform.services.elasticsearch.action.duplicates;

import ai.mpr.marcie.content.rationalizer.ApplicationService;
import ai.mpr.marcie.content.rationalizer.misc.JsonArrayBuilder;
import ai.mpr.marcie.content.rationalizer.misc.JsonObjectBuilder;
import ai.mpr.marcie.content.rationalizer.steps.AbstractStep;
import ai.mpr.marcie.content.rationalizer.steps.StepOutput;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.platform.services.elasticsearch.Utils;
import com.prinova.messagepoint.platform.services.elasticsearch.search.SearchRequestBody;
import com.prinova.messagepoint.platform.services.elasticsearch.search.SearchRequestItem;
import com.prinova.messagepoint.platform.services.elasticsearch.search.query.BoolQueryChild;
import com.prinova.messagepoint.platform.services.elasticsearch.search.query.MatchAllQueryChild;
import com.prinova.messagepoint.platform.services.elasticsearch.search.query.QueryChild;
import com.prinova.messagepoint.platform.services.elasticsearch.search.query.QuerySearchRequestItem;
import com.prinova.messagepoint.platform.services.elasticsearch.search.query.QueryUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class SearchAllDuplicatesWithCompareContext extends AbstractStep<SearchAllDuplicatesWithCompareContext.Request, Map> {

    private static final Log log = LogUtil.getLog(SearchAllDuplicatesWithCompareContext.class);

    @Override
    public StepOutput<Map> apply(ApplicationService service, SearchAllDuplicatesWithCompareContext.Request req) {

        Map<String, Integer> resultMap;
        List<DashboardFilter> dashboardFilters = req.dashboardResultsFilterList();
        if (CollectionUtils.isEmpty(req.sourceMetadataFilters()) && CollectionUtils.isEmpty(req.targetMetadataFilters())) {

            resultMap = new ContentWorkQueue().apply(
                    service,
                    ContentWorkQueue.Request.builder()
                            .appId(req.appId())
                            .dashboardFilters(dashboardFilters)
                            .queryFieldValue(new JsonObjectBuilder()
                                    .add("match_all", new JsonObject())
                                    .build())
                            .build()
            ).result();

            return computeSortedMapStepOutput(resultMap);
        }

        if (CollectionUtils.isEmpty(req.targetMetadataFilters())) {
            Map<String, Integer> targetContentWorkQueueMap = new ContentWorkQueue().apply(
                    service,
                    ContentWorkQueue.Request.builder()
                            .appId(req.appId())
                            .dashboardFilters(dashboardFilters)
                            .queryFieldValue(new JsonObjectBuilder()
                                    .add("match_all", new JsonObject())
                                    .build())
                            .build()).result();

            JsonObject sourceQueryFieldValue = new QueryFieldHandler(req.sourceMetadataFilters(), dashboardFilters).buildQueryFieldValue();
            final Set<String> sourceHashes = new HashesForQuery().apply(
                    service,
                    HashesForQuery.Request.builder()
                            .appId(req.appId())
                            .queryFieldValue(sourceQueryFieldValue)
                            .build()
            ).result();

            resultMap = sourceHashes.stream()
                    .filter(hash -> targetContentWorkQueueMap.containsKey(hash))
                    .map(hash -> Pair.of(hash , targetContentWorkQueueMap.get(hash)))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldValue, newValue) -> oldValue, LinkedHashMap::new));

            return computeSortedMapStepOutput(resultMap);
        }

        // This block handles 2 cases:
        // CollectionUtils.isEmpty(req.sourceMetadataFilters())
        // CollectionUtils.isNotEmpty(req.sourceMetadataFilters())

        JsonObject targetQueryFieldValue = new QueryFieldHandler(req.targetMetadataFilters(), dashboardFilters).buildQueryFieldValue();
        JsonObject sourceQueryFieldValue = new QueryFieldHandler(req.sourceMetadataFilters(), dashboardFilters).buildQueryFieldValue();

        final Map<String, ContentWorkQueueWithFirstId.CountAndFirstGuid> targetContentWorkQueueWithFirstIdMap = new ContentWorkQueueWithFirstId().apply(
                service,
                ContentWorkQueueWithFirstId.Request.builder()
                        .appId(req.appId())
                        .queryFieldValue(targetQueryFieldValue)
                        .minIds(1)
                        .build()
        ).result();

        Map<String, ContentWorkQueueWithFirstId.CountAndFirstGuid> filteredContentWorkQueueWithFirstIdMap = targetContentWorkQueueWithFirstIdMap;
        if (CollectionUtils.isNotEmpty(req.sourceMetadataFilters())) {
            final Set<String> sourceHashes = new HashesForQuery().apply(
                    service,
                    HashesForQuery.Request.builder()
                            .appId(req.appId())
                            .queryFieldValue(sourceQueryFieldValue)
                            .build()
            ).result();

            filteredContentWorkQueueWithFirstIdMap = sourceHashes.stream()
                    .filter(hash -> targetContentWorkQueueWithFirstIdMap.containsKey(hash))
                    .map(hash -> Pair.of(hash, targetContentWorkQueueWithFirstIdMap.get(hash)))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldValue, newValue) -> oldValue, LinkedHashMap::new));
        }

        resultMap = filteredContentWorkQueueWithFirstIdMap.entrySet().stream()
                .filter(item -> item.getValue().getCount() > 1)
                .map(item -> Pair.of(item.getKey(), item.getValue().getCount()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldValue, newValue) -> oldValue, LinkedHashMap::new));

        final LinkedHashSet<HashAndFirstId> targetHashesWithSingleId = filteredContentWorkQueueWithFirstIdMap.entrySet().stream()
                .filter(item -> item.getValue().getCount() <= 1)
                .map(item -> new HashAndFirstId() {{
                    setHash(item.getKey());
                    setFirstId(item.getValue().getFirstGuid());
                }})
                .collect(Collectors.toCollection(LinkedHashSet::new));

        if (targetHashesWithSingleId.isEmpty()) {
            return computeSortedMapStepOutput(resultMap);
        }

        final JsonObject termsQueryForTargetHashesWithSingleId = new JsonObjectBuilder()
                .add("terms", new JsonObjectBuilder()
                        .add("hash", new JsonArrayBuilder()
                                .addAll(
                                        (List<? extends Object>) targetHashesWithSingleId.stream()
                                                .map(item -> item.getHash())
                                                .collect(Collectors.toCollection(LinkedList::new))
                                )
                                .build())
                        .build())
                .build();
        final Map<String, Set<String>> sourceHashToIdsMap = new HashToIdsForQuery().apply(
                service,
                HashToIdsForQuery.Request.builder()
                        .appId(req.appId())
                        .queryFieldValue(new JsonObjectBuilder()
                                .add("bool", new JsonObjectBuilder()
                                        .add("must", new JsonArrayBuilder()
                                                .addAll(Arrays.asList(
                                                        sourceQueryFieldValue,
                                                        termsQueryForTargetHashesWithSingleId
                                                ))
                                                .build()
                                        )
                                        .add("must_not", new JsonArrayBuilder()
                                                .addAll(Collections.singletonList(
                                                        targetQueryFieldValue
                                                ))
                                                .build()
                                        )
                                        .build()
                                )
                                .build()
                        )
                        .build()
        ).result();

        targetHashesWithSingleId.stream()
                .forEach(item -> {
                    final String hash = item.getHash();
                    final Set<String> sourceIdsSet = sourceHashToIdsMap.get(hash);
                    if (CollectionUtils.isEmpty(sourceIdsSet)) {
                        return;
                    }

                    final String firstId = item.getFirstId();
                    String otherId = sourceIdsSet.stream()
                            .filter(id -> ! StringUtils.equals(id, firstId))
                            .findFirst()
                            .orElse(null);
                    if (otherId != null) {
                        resultMap.put(hash, 1);
                    }
                });

        return computeSortedMapStepOutput(resultMap);
    }

    private StepOutput<Map> computeSortedMapStepOutput(Map<String, Integer> resultMap) {
        LinkedHashMap<String, Integer> sortedResultMap = resultMap.entrySet().stream()
                .filter(entry -> entry.getValue() > 0)
                .sorted(Collections.reverseOrder(Map.Entry.comparingByValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, LinkedHashMap::new));

        return StepOutput.builder(Map.class).result(sortedResultMap).build();
    }

    private static class HashAndFirstId {
        private String hash;
        private String firstId;

        public String getHash() {
            return hash;
        }

        public void setHash(String hash) {
            this.hash = hash;
        }

        public String getFirstId() {
            return firstId;
        }

        public void setFirstId(String firstId) {
            this.firstId = firstId;
        }
    }

    public static class QueryFieldHandler {

        private List<Map<String, String>> documentFilters;
        private List<DashboardFilter> dashboardResultsFilterList;

        public QueryFieldHandler(List<Map<String, String>> documentFilters, List<DashboardFilter> dashboardResultsFilterList) {
            this.documentFilters = documentFilters;
            this.dashboardResultsFilterList = dashboardResultsFilterList;
        }

        public JsonObject buildQueryFieldValue() {
            final JsonObject query = buildQuery();
            final JsonElement queryFieldValueElement = query.get("query");

            return queryFieldValueElement != null ?
                    queryFieldValueElement.getAsJsonObject() :
                    new JsonObjectBuilder()
                            .add("match_all", new JsonObject())
                            .build();

        }

        private JsonObject buildQuery() {
            List<SearchRequestItem> searchRequestItems = new LinkedList<>();
            Utils.addNonNullItemToList(searchRequestItems, buildQuerySearchRequestItem());

            SearchRequestBody searchRequestBody = SearchRequestBody.builder()
                    .searchRequestItems(searchRequestItems)
                    .build();

            return searchRequestBody.buildJsonObject();
        }

        private QuerySearchRequestItem buildQuerySearchRequestItem() {
            List<QueryChild> queryChildList = new LinkedList<>();

            DashboardFilter.addDashboardFiltersToQueryChildList(dashboardResultsFilterList, queryChildList);

            if (CollectionUtils.isEmpty(documentFilters)) {
                queryChildList.add(BoolQueryChild.builder()
                        .mustQueryChildValues(List.of(
                                new MatchAllQueryChild()
                        ))
                        .build()
                );

                return new QuerySearchRequestItem(Collections.singletonList(
                        BoolQueryChild.builder()
                                .mustQueryChildValues(queryChildList)
                                .boost(null)
                                .build()
                ));
            }

            queryChildList.add(
                    BoolQueryChild.builder()
                            .shouldQueryChildValues(QueryUtils.buildQueryChildListForMultipleFilters(documentFilters))
                            .minimumShouldMatch(null)
                            .boost(null)
                            .build()
            );

            return new QuerySearchRequestItem(Collections.singletonList(
                    BoolQueryChild.builder()
                            .mustQueryChildValues(queryChildList)
                            .boost(null)
                            .build()
            ));
        }
    }

    public interface Request {

        String appId();

        List<Map<String, String>> sourceMetadataFilters();

        List<Map<String, String>> targetMetadataFilters();

        List<DashboardFilter> dashboardResultsFilterList();

        static SearchAllDuplicatesWithCompareContext.Request.Builder builder() {
            return new SearchAllDuplicatesWithCompareContext.Request.Builder();
        }

        class Builder {
            private String appId;
            private List<Map<String, String>> sourceMetadataFilters;
            private List<Map<String, String>> targetMetadataFilters;
            private List<DashboardFilter> dashboardResultsFilterList;

            private Builder() {
            }

            public Builder appId(String appId) {
                this.appId = appId;

                return this;
            }

            public Builder sourceMetadataFilters(List<Map<String, String>> sourceMetadataFilters) {
                this.sourceMetadataFilters = sourceMetadataFilters;

                return this;
            }

            public Builder targetMetadataFilters(List<Map<String, String>> targetMetadataFilters) {
                this.targetMetadataFilters = targetMetadataFilters;

                return this;
            }

            public Builder dashboardResultsFilterList(List<DashboardFilter> dashboardResultsFilterList) {
                this.dashboardResultsFilterList = dashboardResultsFilterList;

                return this;
            }

            public SearchAllDuplicatesWithCompareContext.Request build() {
                return new SearchAllDuplicatesWithCompareContext.Request() {

                    @Override
                    public String appId() {
                        return appId;
                    }

                    @Override
                    public List<Map<String, String>> sourceMetadataFilters() {
                        return sourceMetadataFilters;
                    }

                    @Override
                    public List<Map<String, String>> targetMetadataFilters() {
                        return targetMetadataFilters;
                    }

                    @Override
                    public List<DashboardFilter> dashboardResultsFilterList() { return dashboardResultsFilterList; }

                };
            }
        }
    }
}
