package com.prinova.messagepoint.model.workgroup;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.springframework.orm.hibernate5.SessionHolder;

import com.prinova.messagepoint.controller.workgroup.WorkgroupEditController;
import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.admin.LookupValue;
import com.prinova.messagepoint.model.admin.Parameter;
import com.prinova.messagepoint.model.admin.ParameterGroupInstance;
import com.prinova.messagepoint.model.admin.ParameterValueVO;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.HibernateUtil;

public class Workgroup extends IdentifiableMessagePointModel {

	private static final long serialVersionUID = 4166919785262085047L;

	public static long DEFAULT_WORKGROUP		= 1L;
	
	private boolean 					defaultWorkgroup;
	private Set<Zone> 					zones 						= new HashSet<>();
	private Set<User> 					users;

	public boolean isDefaultWorkgroup() {
		return defaultWorkgroup;
	}
	public void setDefaultWorkgroup(boolean defaultWorkgroup) {
		this.defaultWorkgroup = defaultWorkgroup;
	}

	public Set<Zone> getZones() {
		return zones;
	}
	public void setZones(Set<Zone> zones) {
		this.zones = zones;
	}

	public Set<User> getUsers() {
		return users;
	}
	public void setUsers(Set<User> users) {
		this.users = users;
	}

	@Override
	public int hashCode() {
		final int PRIME = 31;
		int result = 1;
		result = PRIME * result + ((name == null) ? 0 : name.hashCode());
		result = PRIME * result + (defaultWorkgroup ? 1231 : 1237);
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (!(obj instanceof Workgroup))
			return false;
		final Workgroup other = (Workgroup) obj;
		if (name == null) {
			if (other.getName() != null)
				return false;
		} else if (!name.equals(other.getName()))
			return false;
		if (defaultWorkgroup != other.isDefaultWorkgroup())
			return false;
		return true;
	}
	/**
	 * ind all workgroup associated with a given touchpoint
     */
	public static List<Workgroup> findByDocumentIdOrderByName(long documentId) {		
		List<Workgroup> wgList = new ArrayList<>();
		List<Zone> zoneList = Zone.findByDocumentIdOrderByName(documentId);
		if(zoneList!=null){
			for (Iterator<Zone> iterator = zoneList.iterator(); iterator.hasNext();) {
				Zone zone = (Zone) iterator.next();
				if(zone!=null){
					wgList.addAll(zone.getWorkgroups());
				}
			}
		}
		return convertToSortedByNameList(wgList);
	}
	
	public static List<Workgroup> convertToSortedByNameList(List<Workgroup> wgList){
		if(wgList!=null){
            List<Workgroup> sorted = new ArrayList<>(wgList);
			Collections.sort(sorted, new WorkgroupNameComparator());
			return Collections.unmodifiableList(sorted);
		}
		return null;
	}

	public static List<Workgroup> findOrderByName() {		
		List<Workgroup> wgList;
		wgList = HibernateUtil.getManager().getObjects(Workgroup.class);
		
		return convertToSortedByNameList(wgList);
	}
	
	public static Workgroup getDefaultWorkgroupFromDB() {		
		List<Workgroup> wgList;
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("defaultWorkgroup", true ));
		wgList = HibernateUtil.getManager().getObjectsAdvanced(Workgroup.class, critList);
		
		if (wgList != null && !wgList.isEmpty()) {
			return wgList.get(0);	
		} else {
			return null;
		}
	}

	public static Workgroup findById(long id){
		return HibernateUtil.getManager().getObject(Workgroup.class, id);
	}
	
	public static Workgroup findByIdAndNode(long id, String schemaName){
		SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schemaName);
		Workgroup workgroup = HibernateUtil.getManager().getObject(Workgroup.class, id);
		HibernateUtil.getManager().restoreSession(mainSessionHolder);
		return workgroup;
	}

	public static Workgroup findByNameAndNode(String name, String schemaName){
		SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schemaName);
		Workgroup workgroup = findByName(name);
		HibernateUtil.getManager().restoreSession(mainSessionHolder);
		return workgroup;
	}

	public static List<Workgroup> findAll(){
		return HibernateUtil.getManager().getObjects(Workgroup.class);
	}
	
	public static List<Workgroup> findAllByNode(String schemaName){
		SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schemaName);
		List<Workgroup> list = HibernateUtil.getManager().getObjects(Workgroup.class, MessagepointOrder.asc("name"));
		HibernateUtil.getManager().restoreSession(mainSessionHolder);
		return list;
	}

	@SuppressWarnings("unchecked")
	public static List<Workgroup> findWorkgroupsOfParameter(long parameterId){
		String query = "select w " +
					   "from Workgroup as w " +
					   "inner join w.visibleValues as vv " +
					   "inner join vv.parameter as p " +
					   "where p.id = :parameterId";
    	
		Map<String, Object> params = new HashMap<>();
    	params.put("parameterId", parameterId);
		return (List<Workgroup>) HibernateUtil.getManager().getObjectsAdvanced(query, params);
	}

	public static Workgroup findByName(String name){
		Workgroup workgroup = null;

		if(StringUtils.isNotBlank(name)){
			HashMap<String, Object> params = new HashMap<>();
			params.put("name", name.trim().toUpperCase());
			List<Workgroup> workgroups = (List<Workgroup>) HibernateUtil.getManager().getObjectsAdvanced("from Workgroup w where upper(w.name) = :name", params);

			if(!workgroups.isEmpty()){
				workgroup = workgroups.get(0);
			}
		}

		return workgroup;
	}


	public static void delete(long id){
		HibernateUtil.getManager().deleteObject(Workgroup.class, id);
	}
	
	@Override
	public void save() {
		super.save();
	}
	
	@SuppressWarnings("unchecked")
	public static boolean isNameUnique(String name){
		HashMap<String, Object> params = new HashMap<>();
		params.put("name", name.trim());	
		List<Workgroup> workgroups = (List<Workgroup>) HibernateUtil.getManager().getObjectsAdvanced("from Workgroup w where w.name = :name", params);
		if (workgroups != null && !workgroups.isEmpty()) {
			return false;
		}
		return true;
	}
	
	public static  boolean isExistingDefaultWorkgroup(){
		boolean result = false;
		String sql = "from Workgroup where defaultWorkgroup = 1";
		long count = HibernateUtil.getManager().getPersistedCount(sql);
		if(count>0)
			result = true;
		return result;
	}
	
	public boolean isDeletable() {
		boolean deletable = true;
		if (this.isDefaultWorkgroup()) {
			deletable = false;
		} else if (this.getZones() != null && !this.getZones().isEmpty()) {
			deletable = false;
		} else if (this.getUsers() != null && !this.getUsers().isEmpty()) {
			deletable = false;
		} 
		return deletable;
	}
	
	public static Map<Parameter, List<ParameterValueVO>> getParameterAvailableValuesMap(List<Parameter> parameters) {
		Map<Parameter, List<ParameterValueVO>> parameterValuesMap = new HashMap<>();
		for (Parameter parameter : parameters) {
			Set<ParameterValueVO> allAvailableValuesForParameter = getAllAvailableValuesForParameter(parameter);
			List<ParameterValueVO> sortedValuesForParameter = new ArrayList<>(allAvailableValuesForParameter);
			Collections.sort(sortedValuesForParameter);
			parameterValuesMap.put(parameter, sortedValuesForParameter);
		}
		return parameterValuesMap;
	}
	
	private static Set<ParameterValueVO> getAllAvailableValuesForParameter(Parameter parameter) {
		Set<ParameterValueVO> availableValuesForParameter = new HashSet<>();
		Set<LookupValue> parameterLookupValues = parameter.getDataElementVariable().getLookupValues();
		for (LookupValue parameterLookupValue : parameterLookupValues) {
			ParameterValueVO parameterValueVO = new ParameterValueVO();
			parameterValueVO.setValue(parameterLookupValue.getId() + WorkgroupEditController.VALUE_SEPARATOR + parameter.getId());
			parameterValueVO.setLabel(parameterLookupValue.getName().trim() + " (" + parameterLookupValue.getValue().trim() + ")");
			availableValuesForParameter.add(parameterValueVO);
		}
		List<ParameterGroupInstance> parameterGroupInstances = ParameterGroupInstance.findBy1stParameter(parameter);
		for (ParameterGroupInstance parameterGroupInstance : parameterGroupInstances) {
			LookupValue lookupValue = LookupValue.findByValue(parameter.getId(), parameterGroupInstance.getPgItemValue1());
			if (lookupValue == null) {
				ParameterValueVO parameterValueVO = new ParameterValueVO();
				parameterValueVO.setValue("-" + parameterGroupInstance.getId());
				parameterValueVO.setLabel(parameterGroupInstance.getPgItemValue1());
				availableValuesForParameter.add(parameterValueVO);
			}
		}
		return availableValuesForParameter;
	}

}