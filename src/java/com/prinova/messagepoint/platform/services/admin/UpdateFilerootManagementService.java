package com.prinova.messagepoint.platform.services.admin;

import com.prinova.messagepoint.integrator.MessagepointCronTriggerFactoryBean;
import com.prinova.messagepoint.integrator.MessagepointQuartzUtils;
import com.prinova.messagepoint.model.ExpiryDurationType;
import com.prinova.messagepoint.model.FilerootManagementProfile;
import com.prinova.messagepoint.platform.services.job.*;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleResourceAwareServiceExeContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import org.quartz.CronExpression;

public class UpdateFilerootManagementService extends AbstractService{

    public static final String SERVICE_NAME="admin.UpdateFilerootManagementService";
    private static final Log log = LogUtil.getLog(UpdateFilerootManagementService.class);

    public void execute(ServiceExecutionContext context) {
        try {
            validate(context);
            if (hasValidationError(context)) {
                return;
            }
            UpdateFilerootManagementServiceRequest request =  (UpdateFilerootManagementServiceRequest) context.getRequest();
            if(request!=null){
                boolean purgeExpiredProdJobs = request.getPurgeExpiredProdJobs();
                boolean purgeExpiredTests = request.getPurgeExpiredTests();
                boolean purgeExpiredProofs = request.getPurgeExpiredProofs();
                boolean purgeExpiredConnectedArtifacts = request.getPurgeExpiredConnectedArtifacts();
                boolean purgeExpiredCommunications = request.getPurgeExpiredCommunications();
                boolean purgeExpiredOrphans = true;
                int prodJobExpiryDuration = request.getProdJobExpiryDuration();
                int testExpiryDuration = request.getTestExpiryDuration();
                int proofExpiryDuration = request.getProofExpiryDuration();
                int connectedOrderExpiryDuration = request.getConnectedOrderExpiryDuration();
                String prodJobExpiryDurationInput = request.getProdJobExpiryDurationInput();
                String testExpiryDurationInput = request.getTestExpiryDurationInput();
                String proofExpiryDurationInput = request.getProofExpiryDurationInput();
                String connectedOrderExpiryDurationInput = request.getConnectedOrderExpiryDurationInput();
                String orphanedExpiryDurationInput = request.getOrphanedExpiryDurationInput();

                FilerootManagementProfile currentProfile = FilerootManagementProfile.getFilerootManagementProfile();
                currentProfile.setPurgeExpiredProdJobs(purgeExpiredProdJobs);
                currentProfile.setPurgeExpiredTests(purgeExpiredTests);
                currentProfile.setPurgeExpiredProofs(purgeExpiredProofs);
                currentProfile.setPurgeExpiredConnectedArtifacts(purgeExpiredConnectedArtifacts);
                currentProfile.setPurgeExpiredCommunications(purgeExpiredCommunications);
                currentProfile.setProdJobExpiryDuration(prodJobExpiryDuration);
                currentProfile.setTestExpiryDuration(testExpiryDuration);
                currentProfile.setProofExpiryDuration(proofExpiryDuration);
                currentProfile.setConnectedOrderExpiryDuration(connectedOrderExpiryDuration);
                currentProfile.save();

                FilerootManagementProfile.prodJobPeriod = request.getProdJobPeriod();
                FilerootManagementProfile.testJobPeriod = request.getTestJobPeriod();
                FilerootManagementProfile.proofJobPeriod = request.getProofJobPeriod();
                FilerootManagementProfile.connectedOrderJobPeriod = request.getConnectedOrderJobPeriod();

                if(purgeExpiredProdJobs){
                    MessagepointCronTriggerFactoryBean prodJobTriggerBean = new MessagepointCronTriggerFactoryBean();
                    String cronExpr = getCronExpression();
                    if(         prodJobExpiryDurationInput!=null
                            &&  !prodJobExpiryDurationInput.isEmpty()
                            &&  !prodJobExpiryDurationInput.equals(cronExpr)
                            &&  CronExpression.isValidExpression(prodJobExpiryDurationInput)){
                        cronExpr = prodJobExpiryDurationInput;
                    }
                    prodJobTriggerBean.setCronExpression(cronExpr);
                    MessagepointQuartzUtils.rescheduleJob(ProdJobPurgingScheduler.class, prodJobTriggerBean);
                }else{
                    MessagepointQuartzUtils.deleteJob(ProdJobPurgingScheduler.class);
                }

                if(purgeExpiredTests){
                    MessagepointCronTriggerFactoryBean testTriggerBean = new MessagepointCronTriggerFactoryBean();
                    String cronExpr = getCronExpression();
                    if(         testExpiryDurationInput!=null
                            &&  !testExpiryDurationInput.isEmpty()
                            &&  !testExpiryDurationInput.equals(cronExpr)
                            &&  CronExpression.isValidExpression(testExpiryDurationInput)){
                        cronExpr = testExpiryDurationInput;
                    }
                    testTriggerBean.setCronExpression(cronExpr);
                    MessagepointQuartzUtils.rescheduleJob(TestJobPurgingScheduler.class, testTriggerBean);
                }else{
                    MessagepointQuartzUtils.deleteJob(TestJobPurgingScheduler.class);
                }

                if(purgeExpiredProofs){
                    MessagepointCronTriggerFactoryBean proofTriggerBean = new MessagepointCronTriggerFactoryBean();
                    String cronExpr = getCronExpression();
                    if(         proofExpiryDurationInput!=null
                            &&  !proofExpiryDurationInput.isEmpty()
                            &&  !proofExpiryDurationInput.equals(cronExpr)
                            &&  CronExpression.isValidExpression(proofExpiryDurationInput)){
                        cronExpr = proofExpiryDurationInput;
                    }
                    proofTriggerBean.setCronExpression(cronExpr);
                    MessagepointQuartzUtils.rescheduleJob(ProofJobPurgingScheduler.class, proofTriggerBean);
                }else{
                    MessagepointQuartzUtils.deleteJob(ProofJobPurgingScheduler.class);
                }

                if(purgeExpiredConnectedArtifacts){
                    MessagepointCronTriggerFactoryBean connectedOrderTriggerBean = new MessagepointCronTriggerFactoryBean();
                    String cronExpr = getCronExpression();
                    if(         connectedOrderExpiryDurationInput!=null
                            &&  !connectedOrderExpiryDurationInput.isEmpty()
                            &&  !connectedOrderExpiryDurationInput.equals(cronExpr)
                            &&  CronExpression.isValidExpression(connectedOrderExpiryDurationInput)){
                        cronExpr = connectedOrderExpiryDurationInput;
                    }
                    connectedOrderTriggerBean.setCronExpression(cronExpr);
                    MessagepointQuartzUtils.rescheduleJob(ConnectedJobPurgingScheduler.class, connectedOrderTriggerBean);
                }else{
                    MessagepointQuartzUtils.deleteJob(ConnectedJobPurgingScheduler.class);
                }

                if(purgeExpiredOrphans){
                    MessagepointCronTriggerFactoryBean orphanedTriggerBean = new MessagepointCronTriggerFactoryBean();
                    String cronExpr = getCronExpression();
                    if(         orphanedExpiryDurationInput!=null
                            &&  !orphanedExpiryDurationInput.isEmpty()
                            &&  !orphanedExpiryDurationInput.equals(cronExpr)
                            &&  CronExpression.isValidExpression(orphanedExpiryDurationInput)){
                        cronExpr = orphanedExpiryDurationInput;
                    }
                    orphanedTriggerBean.setCronExpression(cronExpr);
                    MessagepointQuartzUtils.rescheduleJob(OrphanedJobPurgingScheduler.class, orphanedTriggerBean);
                }
            }
        } catch (Exception e) {
            log.error(" unexpected exception when invoking UpdateFilerootManagementService execute method", e);
            this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
                    context.getLocale());
            throw new RuntimeException(e.getMessage());
        }
    }

    private static String getCronExpression() {
        return "0 0 0 * * ?";
    }

    private String getCronExpression(int expiryDurationType){
        String cronExpression = "0 0 0 * * ?";
        switch (expiryDurationType){
            case ExpiryDurationType.ID_ONE_DAY:{
                cronExpression = "0 0 0 * * ?";
                break;
            }
            case ExpiryDurationType.ID_ONE_WEEK:{
                cronExpression = "0 0 0 ? * 2";
                break;
            }
            case ExpiryDurationType.ID_ONE_MONTH:{
                cronExpression = "0 0 0 ? * 2#1";
                break;
            }
            case ExpiryDurationType.ID_TWO_MONTHS:{
                cronExpression = "0 0 0 ? */2 2#1";
                break;
            }
            case ExpiryDurationType.ID_THREE_MONTHS:{
                cronExpression = "0 0 0 ? */3 2#1";
                break;
            }
            case ExpiryDurationType.ID_SIX_MONTHS:{
                cronExpression = "0 0 0 ? */6 2#1";
                break;
            }
            case ExpiryDurationType.ID_YEAR:{
                cronExpression = "0 0 0 ? */12 2#1";
                break;
            }
        }
        return cronExpression;
    }

    public void validate(ServiceExecutionContext context) {
        // TODO Auto-generated method stub

    }

    public static ServiceExecutionContext createContext(){
        FilerootManagementProfile currentProfile = FilerootManagementProfile.getFilerootManagementProfile();
        return createContext(currentProfile.getPurgeExpiredProdJobs(),
                currentProfile.getPurgeExpiredTests(),
                currentProfile.getPurgeExpiredProofs(),
                currentProfile.getPurgeExpiredConnectedArtifacts(),
                currentProfile.getPurgeExpiredCommunications(),
                currentProfile.getProdJobExpiryDuration(),
                currentProfile.getTestExpiryDuration(),
                currentProfile.getProofExpiryDuration(),
                currentProfile.getConnectedOrderExpiryDuration(),
                getCronExpression(),
                getCronExpression(),
                getCronExpression(),
                getCronExpression(),
                getCronExpression(),
                FilerootManagementProfile.prodJobPeriod,
                FilerootManagementProfile.testJobPeriod,
                FilerootManagementProfile.proofJobPeriod,
                FilerootManagementProfile.connectedOrderJobPeriod,
                FilerootManagementProfile.orphanedJobPeriod);
    }

    public static ServiceExecutionContext createContext(Boolean purgeExpiredProdJobs,
                                                        Boolean purgeExpiredTests,
                                                        Boolean purgeExpiredProofs,
                                                        Boolean purgeExpiredConnectedArtifacts,
                                                        Boolean purgeExpiredCommunications,
                                                        Integer prodJobExpiryDuration,
                                                        Integer testExpiryDuration,
                                                        Integer proofExpiryDuration,
                                                        Integer connectedOrderExpiryDuration,
                                                        String prodJobExpiryDurationInput,
                                                        String testExpiryDurationInput,
                                                        String proofExpiryDurationInput,
                                                        String connectedOrderExpiryDurationInput,
                                                        String orphanedExpiryDurationInput,
                                                        long prodJobPeriod,
                                                        long testJobPeriod,
                                                        long proofJobPeriod,
                                                        long connectedOrderJobPeriod,
                                                        long orphanedJobPeriod) {

        SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
        context.setServiceName(SERVICE_NAME);

        UpdateFilerootManagementServiceRequest request = new UpdateFilerootManagementServiceRequest();
        context.setRequest(request);

        request.setPurgeExpiredProdJobs(purgeExpiredProdJobs);
        request.setPurgeExpiredTests(purgeExpiredTests);
        request.setPurgeExpiredProofs(purgeExpiredProofs);
        request.setPurgeExpiredConnectedArtifacts(purgeExpiredConnectedArtifacts);
        request.setPurgeExpiredCommunications(purgeExpiredCommunications);
        request.setProdJobExpiryDuration(prodJobExpiryDuration);
        request.setTestExpiryDuration(testExpiryDuration);
        request.setProofExpiryDuration(proofExpiryDuration);
        request.setConnectedOrderExpiryDuration(connectedOrderExpiryDuration);
        request.setProdJobExpiryDurationInput(prodJobExpiryDurationInput);
        request.setTestExpiryDurationInput(testExpiryDurationInput);
        request.setProofExpiryDurationInput(proofExpiryDurationInput);
        request.setConnectedOrderExpiryDurationInput(connectedOrderExpiryDurationInput);
        request.setOrphanedExpiryDurationInput(orphanedExpiryDurationInput);
        request.setProdJobPeriod(prodJobPeriod);
        request.setTestJobPeriod(testJobPeriod);
        request.setProofJobPeriod(proofJobPeriod);
        request.setConnectedOrderJobPeriod(connectedOrderJobPeriod);
        request.setOrphanedJobPeriod(orphanedJobPeriod);

        SimpleServiceResponse serviceResp = new SimpleServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }

}