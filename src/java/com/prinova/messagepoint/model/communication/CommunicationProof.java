package com.prinova.messagepoint.model.communication;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;

import com.prinova.messagepoint.MessagePointDeletable;
import com.prinova.messagepoint.model.DeliverableMessagePointModel;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.admin.Channel;
import com.prinova.messagepoint.model.deliveryevent.DeliveryEvent;
import com.prinova.messagepoint.model.message.MessagepointDeleteResponse;
import com.prinova.messagepoint.reports.model.JobMetadataDetail;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.HibernateUtil;

public class CommunicationProof extends DeliverableMessagePointModel implements MessagePointDeletable {
	
	private static final long serialVersionUID = -8210589731276019084L;

	private Communication communication;
	
	private boolean 	noProductionContentError		= false;
	private boolean 	noMatchingRecipientError		= false;
	
	private Document 	preProofDocument;
	private String 		preProofCustomerIdentifier;
	private String		preProofReferenceData;
	private String		proofEmailRecipient;
	private boolean 	isPreProof						= false;
	private boolean		appliesWebService				= false;
	private boolean		webServiceResultComplete		= false;
	
	public Communication getCommunication() {
		return communication;
	}
	public void setCommunication(Communication communication) {
		this.communication = communication;
	}

	public boolean isNoProductionContentError() {
		return noProductionContentError;
	}
	public void setNoProductionContentError(boolean noProductionContentError) {
		this.noProductionContentError = noProductionContentError;
	}

	public boolean isNoMatchingRecipientError() {
		return noMatchingRecipientError;
	}
	public void setNoMatchingRecipientError(boolean noMatchingRecipientError) {
		this.noMatchingRecipientError = noMatchingRecipientError;
	}

	public Document getPreProofDocument() {
		return preProofDocument;
	}
	public void setPreProofDocument(Document preProofDocument) {
		this.preProofDocument = preProofDocument;
	}

	public String getPreProofCustomerIdentifier() {
		return preProofCustomerIdentifier;
	}
	public void setPreProofCustomerIdentifier(String preProofCustomerIdentifier) {
		this.preProofCustomerIdentifier = preProofCustomerIdentifier;
	}

	public String getPreProofReferenceData() {
		return preProofReferenceData;
	}
	public void setPreProofReferenceData(String preProofReferenceData) {
		this.preProofReferenceData = preProofReferenceData;
	}

	public String getProofEmailRecipient() {
		return proofEmailRecipient;
	}
	public void setProofEmailRecipient(String proofEmailRecipient) {
		this.proofEmailRecipient = proofEmailRecipient;
	}

	public boolean getIsPreProof() {
		return isPreProof;
	}
	public void setIsPreProof(boolean isPreProof) {
		this.isPreProof = isPreProof;
	}

	public boolean isAppliesWebService() {
		return appliesWebService;
	}
	public void setAppliesWebService(boolean appliesWebService) {
		this.appliesWebService = appliesWebService;
	}

	public boolean isWebServiceResultComplete() {
		return webServiceResultComplete;
	}
	public void setWebServiceResultComplete(boolean webServiceResultComplete) {
		this.webServiceResultComplete = webServiceResultComplete;
	}

	public static List<CommunicationProof> findProofsByCommunicationId(Long communicationId) {
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("communication.id", communicationId));
		critList.add(MessagepointRestrictions.eq("isPreProof", false));
		return HibernateUtil.getManager().getObjectsAdvanced(CommunicationProof.class, critList, MessagepointOrder.desc("requestDate") );
	}
	public static List<CommunicationProof> findPreProofsByCommunicationId(Long communicationId) {
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("communication.id", communicationId));
		critList.add(MessagepointRestrictions.eq("isPreProof", true));
		return HibernateUtil.getManager().getObjectsAdvanced(CommunicationProof.class, critList, MessagepointOrder.desc("requestDate") );
	}

	public static CommunicationProof findById(long id) {
		return HibernateUtil.getManager().getObject(CommunicationProof.class, id);
	}

	public static List<CommunicationProof> findAllByDocument(Document document){
		if (document != null){
			return HibernateUtil.getManager().getObjectsAdvanced(CommunicationProof.class, MessagepointRestrictions.eq("preProofDocument.id", document.getId()));
		}else{
			return null;
		}
	}
	
	public DeliveryEvent getDeliveryEvent() {
		Collection<DeliveryEvent> deliveryEvents = getDeliveryEvents();
		if( deliveryEvents == null ) return null;
		if(deliveryEvents.isEmpty()) return null;
		return deliveryEvents.iterator().next();
	}

	@Override
	public String getOutputPath( ) {
		return this.outputPath;
	}

	@Override
	public String getEncodedOutputPath( ) {
		String path = getOutputPath();
	    try {
	    	path = URLEncoder.encode( getOutputPath().replace("\\", "/") ,"UTF-8");
	    } catch (UnsupportedEncodingException e) {
	    }
		return path;
	}
	
	@Override
	public void setOutputPath(String outputPath) {
		this.outputPath = outputPath;
	}

	@Override
	public Document getDocument()
	{
		if ( preProofDocument != null )
			return preProofDocument;
		if ( communication != null )
			return communication.getDocument();
		return null;
	}

	@Override
	public boolean isRequiresOutputResultFile() {
		return false;
	}
	
	public boolean getIsEmailOrWebPreview() {
		if ( communication != null && communication.getChannelContextId() != null && (communication.getChannelContextId() == Channel.CHANNEL_EMAIL_ID || communication.getChannelContextId() == Channel.CHANNEL_WEB_ID))
			return true;
		
		Document doc = communication != null ? communication.getDocument() : preProofDocument;
		return doc.isEmailTouchpoint() || doc.isWebTouchpoint();
	}
	public int getEmailTemplateWidth() {
		Document doc = communication != null ? communication.getDocument() : preProofDocument;
		if (getIsEmailOrWebPreview())
			return doc.getEmailSection().getWidth();
		else
			return -1;
	}
	public int getEmailTemplateHeight() {
		Document doc = communication != null ? communication.getDocument() : preProofDocument;
		if (getIsEmailOrWebPreview())
			return doc.getEmailSection().getHeight();
		else
			return -1;
	}


	@Override
	public int hashCode() {
		final int PRIME = 31;
		int result = super.hashCode();
		result = PRIME * result + ((communication == null) ? 0 : communication.hashCode());
		return result;
	}
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (!super.equals(obj))
			return false;
		if (!(obj instanceof CommunicationProof))
			return false;
		return true;
	}
	
	@Override
	public void beforeDelete() {
		//Delete the Delivery Events
		Collection<DeliveryEvent> deliveryEvents = getDeliveryEvents();
		
		if(deliveryEvents == null || deliveryEvents.isEmpty()){
			return;
		}
		
		for (DeliveryEvent deliveryEvent : deliveryEvents) {
			if( deliveryEvent.getJob() != null ) {
				String path = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_OutputDir) + "/" + deliveryEvent.getJob().getId() +  "/";
				FileUtil.deleteDirectory(new File( path ));
				
				// Delete the XML produced by the QE for this job.
				HibernateUtil.getManager().deleteObjects(JobMetadataDetail.class, MessagepointRestrictions.eq("jobId", deliveryEvent.getJob().getId()));

			}
			
			HibernateUtil.getManager().sessionSafeDelete(deliveryEvent);
		}
		
	}
	
	@Override
	public void afterDelete() { }
	@Override
	public DeleteResponse canDelete() { return MessagepointDeleteResponse.DELETE_RESPONSE_TRUE; }
}
