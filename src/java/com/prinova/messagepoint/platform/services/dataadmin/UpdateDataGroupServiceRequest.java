package com.prinova.messagepoint.platform.services.dataadmin;

import com.prinova.messagepoint.model.admin.DataGroup;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class UpdateDataGroupServiceRequest implements ServiceRequest{

	/**
	 * 
	 */
	private static final long serialVersionUID = -595728803280083293L;
	
	private DataGroup dataGroup;

	public DataGroup getDataGroup() {
		return dataGroup;
	}

	public void setDataGroup(DataGroup dataGroup) {
		this.dataGroup = dataGroup;
	}

}
