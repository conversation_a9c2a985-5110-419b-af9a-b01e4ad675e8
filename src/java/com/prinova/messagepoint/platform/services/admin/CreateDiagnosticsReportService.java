package com.prinova.messagepoint.platform.services.admin;

import java.io.File;
import java.io.PrintWriter;
import java.io.StringWriter;

import org.apache.commons.logging.Log;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.util.DiagnosticsReport;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.export.ExportDiagnosticsToXMLService;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;

public class CreateDiagnosticsReportService extends AbstractService {

	public static final String SERVICE_NAME = "admin.CreateDiagnosticsReportService";
	
	private static final Log log = LogUtil.getLog(CreateDiagnosticsReportService.class);
	
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			CreateDiagnosticsReportServiceRequest request = (CreateDiagnosticsReportServiceRequest) context.getRequest();
			
			DiagnosticsReportRunner diagnosticsReportRunner = new DiagnosticsReportRunner(
                    request.getReportId(),
                    request.getRequestingUser());
			MessagePointRunnableUtil.startThread(diagnosticsReportRunner, Thread.MAX_PRIORITY);
			
			context.getResponse().setResultValueBean(request.getReportId());

		} catch (Exception e) {
			log.error(" unexpected exception when invoking CreateDiagnosticsReportService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION", ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	private static class DiagnosticsReportRunner extends MessagePointRunnable {

		private long 				reportId;
		private User				user;
		
		public DiagnosticsReportRunner(long reportId, User user) {
			this.reportId 				= reportId;
			this.user					= user;
		}

		@Transactional(propagation = Propagation.REQUIRED, readOnly = false)
		public void performMainProcessing() {
			
			try {
				// Delete File - remove any existing diagnostics reports for user
				File srcDir = new File(DiagnosticsReport.getReportsRootPath());
				if (!srcDir.exists())
					srcDir.mkdir();
				String userDiagnosticsFileMarker = DiagnosticsReport.REPORT_NAME_PREFIX + "_" + user.getId() +"_*";
				FileUtil.deleteAllByRegex(userDiagnosticsFileMarker, srcDir);
				
				// *****************************************
				// ********* Generate XML ******************
				// *****************************************
		    	ServiceExecutionContext exportDiagnosticsToXMLServiceContext = ExportDiagnosticsToXMLService.createContext(
		    																	this.reportId,
																				this.user); 
	
		    	Service exportService = MessagepointServiceFactory.getInstance().lookupService(ExportDiagnosticsToXMLService.SERVICE_NAME, ExportDiagnosticsToXMLService.class);
		    	exportService.execute(exportDiagnosticsToXMLServiceContext);

			} catch (Throwable throwable) {
		    	// Throw ERROR.
				Log log = LogUtil.getLog(this.getClass());
				StringWriter sw = new StringWriter();
				PrintWriter pw = new PrintWriter(sw);
				throwable.printStackTrace(pw);
				log.error("Diagnostics Report Runner caught exception: " + sw);
			}
		}
	}
	
	public void validate(ServiceExecutionContext context) {
	}
	
	public static ServiceExecutionContext createContext(long reportId, User requestingUser) {

		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		CreateDiagnosticsReportServiceRequest request = new CreateDiagnosticsReportServiceRequest();

		request.setReportId(reportId);
		request.setRequestingUser(requestingUser);

		context.setRequest(request);

		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}	

}
