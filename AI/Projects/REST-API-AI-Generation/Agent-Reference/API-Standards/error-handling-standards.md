# 🚨 Error Handling & Status Codes Standards for API Generation

> **Purpose**: Error handling requirements and status code mapping for automated REST API generation
> **Audience**: AI Agent
> **Usage**: Apply these error patterns to all generated APIs
> **Status**: Complete - May require validation/refinement during actual API generation phase

## 📊 HTTP STATUS CODE MAPPING

### **Success Status Codes**
| **Operation** | **Status Code** | **Usage** | **Method** |
|---------------|-----------------|-----------|------------|
| **List/Get** | `200 OK` | Successful retrieval | `sendSuccessResponse()` |
| **Create** | `201 CREATED` | Resource created | `sendSuccessResponse()` |
| **Update** | `200 OK` | Resource updated | `sendSuccessResponse()` |
| **Delete** | `204 NO_CONTENT` | Resource deleted | `sendSuccessResponse()` |

### **Client Error Status Codes (4xx)**
| **Status Code** | **Error Category** | **Usage** | **Method** |
|-----------------|-------------------|-----------|------------|
| **400 BAD_REQUEST** | Invalid request format, malformed JSON | Request parsing errors | `sendFailedResponse(BAD_REQUEST, message)` |
| **401 UNAUTHORIZED** | Authentication required | Missing/invalid auth | Handled by security layer |
| **403 FORBIDDEN** | Authorization failed | Insufficient permissions | Handled by @PreAuthorize |
| **404 NOT_FOUND** | Resource not found | GUID not found | `sendResourceNotFoundResponse(message)` |
| **422 UNPROCESSABLE_ENTITY** | Validation errors | Business logic validation | `sendResourceProcessingFailedResponse(messages)` |

### **Server Error Status Codes (5xx)**
| **Status Code** | **Error Category** | **Usage** | **Method** |
|-----------------|-------------------|-----------|------------|
| **500 INTERNAL_SERVER_ERROR** | System errors | Unexpected exceptions | `sendFailedResponse(INTERNAL_SERVER_ERROR, message)` |

## 🔧 ERROR HANDLING METHODS

### **RestResourceBase Error Methods**
```java
// ✅ REQUIRED: Use these methods for all error responses

// 404 Not Found
return sendResourceNotFoundResponse("Resource with guid " + guid + " not found");

// 422 Validation Errors (single message)
return sendResourceProcessingFailedResponse("Validation failed: " + errorMessage);

// 422 Validation Errors (multiple messages)
return sendResourceProcessingFailedResponse(validationErrors);

// 400 Bad Request
return sendFailedResponse(HttpStatus.BAD_REQUEST, "Invalid request format");

// 500 Internal Server Error
return sendFailedResponse(HttpStatus.INTERNAL_SERVER_ERROR, "Operation failed");
```

### **Exception Handling Pattern**
```java
@GetMapping(value = "/{guid}")
public ResponseEntity<String> getResource(@PathVariable String guid) {
    try {
        // Business logic
        Resource resource = resourceService.findByGuid(guid);
        
        if (resource == null) {
            return sendResourceNotFoundResponse("Resource with guid " + guid + " not found");
        }
        
        ResourceDto dto = new ResourceDto(resource);
        return sendSuccessResponse(dto, RestResourceType.RESOURCE, 
            "Resource retrieved successfully", null, partialResponseParams);
            
    } catch (ValidationException e) {
        return sendResourceProcessingFailedResponse(e.getErrors());
    } catch (Exception e) {
        log.error("Error retrieving resource: " + guid, e);
        return sendFailedResponse(HttpStatus.INTERNAL_SERVER_ERROR, 
            "Error retrieving resource");
    }
}
```

## 📋 ERROR RESPONSE FORMAT

### **Standard Error Response Structure**
```json
{
  "responseCode": 404,
  "responseMessage": "An error occurred while processing your request, see details for more information",
  "failed": true,
  "responseResult": null,
  "errors": {
    "errors": [
      {
        "code": null,
        "httpStatusCode": 404,
        "message": "Resource with guid 12345 not found",
        "details": null
      }
    ]
  }
}
```

### **ErrorsDto Structure**
```java
// ✅ Automatic error response building
ErrorsDto errorsDto = ErrorsDto.build(status.value(), errors);

ResponseDto<ErrorsDto> responseDto = new ResponseDto<ErrorsDto>()
    .errors(errorsDto)
    .responseMessage("An error occurred while processing your request, see details for more information")
    .responseCode(status.value());
```

## 🎯 ERROR CATEGORIZATION RULES

### **1. Resource Not Found (404)**
**When to use**: Resource lookup by GUID fails
```java
// ✅ Pattern
if (resource == null) {
    return sendResourceNotFoundResponse("Resource with guid " + guid + " not found");
}
```

**Message Format**: `"{ResourceType} with guid {guid} not found"`

### **2. Validation Errors (422)**
**When to use**: Business logic validation fails
```java
// ✅ Single validation error
if (dto.getName() == null || dto.getName().isEmpty()) {
    return sendResourceProcessingFailedResponse("Resource name is required");
}

// ✅ Multiple validation errors
List<String> errors = new ArrayList<>();
if (dto.getName() == null) errors.add("Name is required");
if (dto.getType() == null) errors.add("Type is required");
if (!errors.isEmpty()) {
    return sendResourceProcessingFailedResponse(errors);
}
```

### **3. Bad Request (400)**
**When to use**: Request format/parsing errors
- Malformed JSON
- Invalid parameter types
- Missing required headers

**Handled automatically by**: `@ExceptionHandler` in RestResourceBase

### **4. Internal Server Error (500)**
**When to use**: Unexpected system errors
```java
// ✅ Pattern
try {
    // Business logic
} catch (Exception e) {
    log.error("Unexpected error in operation", e);
    return sendFailedResponse(HttpStatus.INTERNAL_SERVER_ERROR, 
        "An unexpected error occurred");
}
```

## 🔒 SECURITY ERROR HANDLING

### **Authentication Errors (401)**
**Handled by**: Security framework (automatic)
**When**: Missing or invalid JWT token/API key

### **Authorization Errors (403)**
**Handled by**: `@PreAuthorize` annotations (automatic)
**When**: User lacks required permissions

## 📝 ERROR MESSAGE STANDARDS

### **Message Format Guidelines**
```java
// ✅ GOOD: Specific and actionable
"Variable with guid 12345 not found"
"Name is required and cannot be empty"
"Invalid email format: user@domain"

// ❌ BAD: Vague or technical
"Error occurred"
"Null pointer exception"
"Database constraint violation"
```

### **Message Templates**
| **Error Type** | **Template** | **Example** |
|----------------|--------------|-------------|
| **Not Found** | `{ResourceType} with guid {guid} not found` | `Variable with guid 12345 not found` |
| **Required Field** | `{FieldName} is required` | `Name is required` |
| **Invalid Format** | `Invalid {FieldName} format: {value}` | `Invalid email format: user@domain` |
| **Duplicate** | `{ResourceType} with {field} '{value}' already exists` | `Variable with name 'test' already exists` |

## 🚨 ERROR HANDLING REQUIREMENTS

### **✅ MANDATORY**
- [ ] **Use RestResourceBase methods** for all error responses
- [ ] **Consistent status codes** according to mapping table
- [ ] **Structured error format** using ErrorsDto
- [ ] **Descriptive error messages** following templates
- [ ] **Exception logging** for server errors
- [ ] **Validation error collection** (multiple errors in single response)

### **❌ FORBIDDEN**
- [ ] **Custom error formats** (must use ResponseDto<ErrorsDto>)
- [ ] **Wrong status codes** (e.g., 200 for errors)
- [ ] **Stack trace exposure** in production (except debug mode)
- [ ] **Generic error messages** without context
- [ ] **Unhandled exceptions** (must catch and respond appropriately)

## 🔧 GENERATION RULES

### **Auto-Generate Error Handling**
1. **Wrap all operations** in try-catch blocks
2. **Add resource not found checks** for GUID-based operations
3. **Include validation error handling** for create/update operations
4. **Use appropriate status codes** based on error type
5. **Generate descriptive messages** using templates

### **Error Handling Template**
```java
@{HttpMethod}Mapping(value = "{path}")
public ResponseEntity<String> {operationName}({parameters}) {
    try {
        // 1. Validation
        if (invalidInput) {
            return sendResourceProcessingFailedResponse("Validation error message");
        }
        
        // 2. Resource lookup (for GUID operations)
        if (resource == null) {
            return sendResourceNotFoundResponse("{ResourceType} with guid " + guid + " not found");
        }
        
        // 3. Business logic
        // ... operation logic
        
        // 4. Success response
        return sendSuccessResponse(result, RestResourceType.{TYPE}, 
            "Operation completed successfully", headers, partialResponseParams);
            
    } catch (ValidationException e) {
        return sendResourceProcessingFailedResponse(e.getErrors());
    } catch (Exception e) {
        log.error("Error in {operationName}: " + parameters, e);
        return sendFailedResponse(HttpStatus.INTERNAL_SERVER_ERROR, 
            "Error processing {operation}");
    }
}
```

## 🔄 BULK OPERATIONS ERROR HANDLING

### **Bulk Operation Status Codes**
| **Scenario** | **Status Code** | **Usage** | **Rationale** |
|--------------|-----------------|-----------|---------------|
| **All Success** | `201 CREATED` (bulk create) / `200 OK` (bulk update/delete) | All items processed successfully | Standard success codes |
| **All Failures** | `400 BAD_REQUEST` | All items failed validation/processing | Client error - fix request |
| **Partial Success** | `200 OK` | Some items succeeded, some failed | Operation completed, check details |
| **System Error** | `500 INTERNAL_SERVER_ERROR` | Bulk operation failed to execute | Server error - retry later |

### **Bulk Response Format**
```json
{
  "responseCode": 200,
  "responseMessage": "Bulk operation completed",
  "failed": false,
  "responseResult": {
    "data": {
      "totalRequested": 5,
      "totalSuccessful": 3,
      "totalFailed": 2,
      "results": [
        {
          "index": 0,
          "guid": "new-guid-123",
          "status": "SUCCESS",
          "resource": { /* created/updated resource */ }
        },
        {
          "index": 1,
          "status": "ERROR",
          "errors": [
            {
              "code": null,
              "httpStatusCode": 422,
              "message": "Name is required",
              "details": null
            }
          ]
        },
        {
          "index": 2,
          "guid": "new-guid-456",
          "status": "SUCCESS",
          "resource": { /* created/updated resource */ }
        },
        {
          "index": 3,
          "status": "ERROR",
          "errors": [
            {
              "code": null,
              "httpStatusCode": 422,
              "message": "Email format is invalid",
              "details": null
            }
          ]
        },
        {
          "index": 4,
          "guid": "new-guid-789",
          "status": "SUCCESS",
          "resource": { /* created/updated resource */ }
        }
      ]
    }
  }
}
```

### **Bulk Error Handling Strategies**

#### **1. Continue on Error (Recommended)**
```java
// ✅ RECOMMENDED: Process all items, collect errors
BulkOperationResult result = new BulkOperationResult();

for (int i = 0; i < request.getItems().size(); i++) {
    try {
        ResourceDto item = request.getItems().get(i);
        Resource created = resourceService.create(item);
        result.addSuccess(i, created.getGuid(), created);
    } catch (ValidationException e) {
        result.addError(i, e.getErrors());
    } catch (Exception e) {
        result.addError(i, "Unexpected error: " + e.getMessage());
    }
}

return sendSuccessResponse(result, RestResourceType.RESOURCE,
    "Bulk operation completed", null, partialResponseParams);
```

#### **2. Stop on First Error (Optional)**
```java
// ⚠️ OPTIONAL: Stop processing on first error
for (int i = 0; i < request.getItems().size(); i++) {
    try {
        ResourceDto item = request.getItems().get(i);
        Resource created = resourceService.create(item);
        result.addSuccess(i, created.getGuid(), created);
    } catch (Exception e) {
        result.addError(i, e.getMessage());
        if (!request.getOptions().isContinueOnError()) {
            break; // Stop processing remaining items
        }
    }
}
```

### **Bulk Operation Options**
```java
// ✅ Support bulk operation options
public class BulkOperationOptions {
    private boolean continueOnError = true;    // Process all items even if some fail
    private boolean validateOnly = false;      // Validate without making changes
    private boolean returnResources = true;    // Include full resources in response
}
```

### **Bulk Error Categories**

#### **Item-Level Errors (Most Common)**
- **Validation Errors**: Individual item validation failures
- **Business Logic Errors**: Item violates business rules
- **Duplicate Errors**: Item already exists (for create operations)
- **Not Found Errors**: Item doesn't exist (for update/delete operations)

#### **Operation-Level Errors**
- **Request Format Errors**: Malformed bulk request structure
- **Authorization Errors**: User lacks permission for bulk operation
- **System Errors**: Database connection, service unavailable

### **Bulk Error Response Templates**

#### **Individual Item Error**
```json
{
  "index": 2,
  "status": "ERROR",
  "errors": [
    {
      "code": "VALIDATION_ERROR",
      "httpStatusCode": 422,
      "message": "Name is required and cannot be empty",
      "details": {
        "field": "name",
        "rejectedValue": null
      }
    }
  ]
}
```

#### **Multiple Validation Errors for Single Item**
```json
{
  "index": 3,
  "status": "ERROR",
  "errors": [
    {
      "code": "VALIDATION_ERROR",
      "httpStatusCode": 422,
      "message": "Name is required",
      "details": {"field": "name"}
    },
    {
      "code": "VALIDATION_ERROR",
      "httpStatusCode": 422,
      "message": "Email format is invalid",
      "details": {"field": "email", "rejectedValue": "invalid-email"}
    }
  ]
}
```

### **Bulk Operation Limits**
```java
// ✅ RECOMMENDED: Set reasonable limits
public static final int MAX_BULK_ITEMS = 100;
public static final int DEFAULT_BULK_ITEMS = 50;

// Validate bulk request size
if (request.getItems().size() > MAX_BULK_ITEMS) {
    return sendFailedResponse(HttpStatus.BAD_REQUEST,
        "Bulk operation limited to " + MAX_BULK_ITEMS + " items");
}
```

### **Bulk Error Logging**
```java
// ✅ Log bulk operation summary
log.info("Bulk {} operation completed: {} total, {} successful, {} failed",
    operationType, result.getTotalRequested(),
    result.getTotalSuccessful(), result.getTotalFailed());

// ✅ Log individual errors for monitoring
for (BulkItemResult item : result.getResults()) {
    if (item.getStatus() == BulkItemStatus.ERROR) {
        log.warn("Bulk {} item {} failed: {}",
            operationType, item.getIndex(), item.getErrors());
    }
}
```

## 📋 VALIDATION CHECKLIST

### **Before Generation**
- [ ] Error categories identified for resource type
- [ ] Message templates prepared
- [ ] Status code mapping confirmed
- [ ] Exception types planned
- [ ] Bulk operation limits defined
- [ ] Bulk error handling strategy chosen

### **After Generation**
- [ ] All operations have error handling
- [ ] Status codes match operation types
- [ ] Error messages follow templates
- [ ] Exception logging implemented
- [ ] Validation errors collected properly
- [ ] Bulk operations handle partial failures
- [ ] Bulk response format includes detailed results
