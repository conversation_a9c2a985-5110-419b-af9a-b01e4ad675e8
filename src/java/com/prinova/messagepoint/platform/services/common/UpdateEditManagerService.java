package com.prinova.messagepoint.platform.services.common;

import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.Date;

public class UpdateEditManagerService extends AbstractService {

	public static final String SERVICE_NAME = "common.UpdateEditManagerService";
	
	private static final Log log = LogUtil.getLog(UpdateEditManagerService.class);

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			UpdateEditManagerServiceRequest request = (UpdateEditManagerServiceRequest) context.getRequest();
			IdentifiableMessagePointModel model = request.getModel();

			if ( model instanceof ContentObject ) {
				((ContentObject) model).setLastLockTime( request.getLastLockTime() );
				((ContentObject) model).setNeedRecalculateHash(false);
				model.save();
                ((ContentObject) model).setNeedRecalculateHash(true);

			} else if (model instanceof Communication) {
				Communication comm = Communication.findById(model.getId());
				if (comm != null) {
					comm.setLastLockTime(request.getLastLockTime());
					comm.save();
				}
			}
		} catch (Exception e) {
			log.error(" unexpected exception when invoking UpdateEditManagerService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
				context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
		
	}

	public static ServiceExecutionContext createContext(IdentifiableMessagePointModel model, Date lastLockTime) {

		SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
		context.setServiceName(SERVICE_NAME);

		UpdateEditManagerServiceRequest request = new UpdateEditManagerServiceRequest();
		context.setRequest(request);

		request.setModel(model);
		request.setLastLockTime(lastLockTime);

		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}
}
