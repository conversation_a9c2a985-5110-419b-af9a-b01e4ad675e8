package com.prinova.messagepoint.model.workflow;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.common.StaticType;
import com.prinova.messagepoint.util.ApplicationUtil;

public class WorkflowDueByType extends StaticType {

	private static final long serialVersionUID = -3801532628923045688L;

	public static final int ID_DUE_BY_TYPE_NOTIFY 	= 0;
	public static final int ID_DUE_BY_TYPE_NOTIFY_AND_APPROVE 	= 1;

	public static final String MESSAGE_CODE_DUE_BY_TYPE_NOTIFY 	= "page.label.workflow.due.by.type.notify";
	public static final String MESSAGE_CODE_DUE_BY_TYPE_NOTIFY_AND_APPROVE 	= "page.label.workflow.due.by.type.notify.and.approve";

	public WorkflowDueByType() {
		super();
	}
	
	public WorkflowDueByType(Integer id) {
		super();
		switch (id) {
		case ID_DUE_BY_TYPE_NOTIFY_AND_APPROVE:
			this.setId(ID_DUE_BY_TYPE_NOTIFY_AND_APPROVE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_DUE_BY_TYPE_NOTIFY_AND_APPROVE));
			this.setDisplayMessageCode(MESSAGE_CODE_DUE_BY_TYPE_NOTIFY_AND_APPROVE);
			break;
		default:
			this.setId(ID_DUE_BY_TYPE_NOTIFY);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_DUE_BY_TYPE_NOTIFY));
			this.setDisplayMessageCode(MESSAGE_CODE_DUE_BY_TYPE_NOTIFY);
			break;
		}
	}

	public WorkflowDueByType(String name) {
		super();
		if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_DUE_BY_TYPE_NOTIFY_AND_APPROVE))) { 
			this.setId(ID_DUE_BY_TYPE_NOTIFY_AND_APPROVE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_DUE_BY_TYPE_NOTIFY_AND_APPROVE));
			this.setDisplayMessageCode(MESSAGE_CODE_DUE_BY_TYPE_NOTIFY_AND_APPROVE);
		} else {
			this.setId(ID_DUE_BY_TYPE_NOTIFY);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_DUE_BY_TYPE_NOTIFY));
			this.setDisplayMessageCode(MESSAGE_CODE_DUE_BY_TYPE_NOTIFY);
		}
	}
	
	public static List<WorkflowDueByType> listAll() {
		List<WorkflowDueByType> allWorkflowDueByTypes = new ArrayList<>();
		
		WorkflowDueByType border = null;
		
		border = new WorkflowDueByType(ID_DUE_BY_TYPE_NOTIFY);
		border = new WorkflowDueByType(ID_DUE_BY_TYPE_NOTIFY_AND_APPROVE);
		allWorkflowDueByTypes.add(border);

		return allWorkflowDueByTypes;
	}
	
}