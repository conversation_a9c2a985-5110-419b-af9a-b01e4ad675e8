package com.prinova.migrate.models;

import java.util.HashMap;
import java.util.Map;

public class ColumnDefinition
{
   public static final int TYPE_BOOLEAN = 1;
   public static final int TYPE_INT = 2;
   public static final int TYPE_LONG = 3;
   public static final int TYPE_DATE = 4;
   public static final int TYPE_STRING = 5;
   public static final int TYPE_LONG_STRING = 6;

   public static final int FLAVOR_ORACLE = 1;
   public static final int FLAVOR_MSSQL = 2;
   public static final int FLAVOR_POSTGRES = 3;

   private static final Map<Integer, Map<Integer, String>> dataTypeDefs;

   private String name;
   private int type;
   private int precision;
   private String defaultValue;
   private boolean primaryKey;
   private boolean unique;
   private boolean nullable;

   static {
      dataTypeDefs = new HashMap<>();

      dataTypeDefs.put(TYPE_BOOLEAN, new HashMap<>());
      dataTypeDefs.put(TYPE_INT, new HashMap<>());
      dataTypeDefs.put(TYPE_LONG, new HashMap<>());
      dataTypeDefs.put(TYPE_DATE, new HashMap<>());
      dataTypeDefs.put(TYPE_STRING, new HashMap<>());
      dataTypeDefs.put(TYPE_LONG_STRING, new HashMap<>());

      dataTypeDefs.get(TYPE_BOOLEAN).put(FLAVOR_ORACLE, " NUMBER(1,0)");
      dataTypeDefs.get(TYPE_BOOLEAN).put(FLAVOR_MSSQL, " TINYINT");
      dataTypeDefs.get(TYPE_BOOLEAN).put(FLAVOR_POSTGRES, " BOOLEAN");

      dataTypeDefs.get(TYPE_INT).put(FLAVOR_ORACLE, " NUMBER(10,0)");
      dataTypeDefs.get(TYPE_INT).put(FLAVOR_MSSQL, " INT");
      dataTypeDefs.get(TYPE_INT).put(FLAVOR_POSTGRES, " INT4");

      dataTypeDefs.get(TYPE_LONG).put(FLAVOR_ORACLE, " NUMBER(19,0)");
      dataTypeDefs.get(TYPE_LONG).put(FLAVOR_MSSQL, " NUMERIC(19,0)");
      dataTypeDefs.get(TYPE_LONG).put(FLAVOR_POSTGRES, " INT8");

      dataTypeDefs.get(TYPE_DATE).put(FLAVOR_ORACLE, " TIMESTAMP");
      dataTypeDefs.get(TYPE_DATE).put(FLAVOR_MSSQL, " DATETIME");
      dataTypeDefs.get(TYPE_DATE).put(FLAVOR_POSTGRES, " TIMESTAMP");

      dataTypeDefs.get(TYPE_STRING).put(FLAVOR_ORACLE, " VARCHAR2(%d CHAR)");
      dataTypeDefs.get(TYPE_STRING).put(FLAVOR_MSSQL, " VARCHAR(%d)");
      dataTypeDefs.get(TYPE_STRING).put(FLAVOR_POSTGRES, " VARCHAR(%d)");

      dataTypeDefs.get(TYPE_LONG_STRING).put(FLAVOR_ORACLE, " CLOB");
      dataTypeDefs.get(TYPE_LONG_STRING).put(FLAVOR_MSSQL, " TEXT");
      dataTypeDefs.get(TYPE_LONG_STRING).put(FLAVOR_POSTGRES, " TEXT");
   }


   /**
    * Method to allow a table to be constructed from a series of columns.
    * @param columnName The name of the new column
    * @param columnType The type of the column  ie ColumnDefinition.TYPE_LONG
    * @param maxPrecision For string type only.  The number of characters needed.  Ignored otherwise
	* @param defaultValue If a default value should be used for this column.  Quotes will be forced around strings if not provided
    * @param isPrimaryKey  is this column part of the primary key
    * @param isUnique    Is a unique constraint required for this column
    * @param isNullable  Should the column allow NULL (true), or NOT NULL (false)
    */
   public ColumnDefinition( String columnName,
                            int columnType,
                            int maxPrecision,
                            String defaultValue,
                            boolean isPrimaryKey,
                            boolean isUnique,
                            boolean isNullable )
   {
      this.name = columnName;
      type = columnType;
      precision = maxPrecision;
      this.defaultValue = defaultValue;
      primaryKey = isPrimaryKey;
      unique = isUnique;
      nullable = isNullable;
   }

   /**
    * @param columnName The name of the new column
    * @param columnType The type of the column  ie ColumnDefinition.TYPE_LONG
    * @param maxPrecision For string type only.  The number of characters needed.  Ignored otherwise
    * @param isNullable  Should the column allow NULL (true), or NOT NULL (false)
    */ 
   protected ColumnDefinition( String columnName,
           					int columnType,
           					int maxPrecision,   
           					boolean isNullable )
   {
	   this.name = columnName;
	   type = columnType;
	   precision = maxPrecision;
	   this.defaultValue = null;
	   primaryKey = false;
	   nullable = isNullable;
   }   
   
   public boolean isPrimaryKey()
   {
      return primaryKey;
   }
   public boolean isUnique()
   {
      return unique;
   }

   public static ColumnDefinition createBoolean( String name, boolean nullable )
   {
	   return new ColumnDefinition(name, TYPE_BOOLEAN, 0, nullable);
   }
      public static ColumnDefinition createInt( String name, boolean nullable )
   {
	   return new ColumnDefinition(name, TYPE_INT, 0, nullable);
   }
   public static ColumnDefinition createLong( String name, boolean nullable )
   {
	   return new ColumnDefinition(name, TYPE_LONG, 0, nullable);
   }
   public static ColumnDefinition createDate( String name, boolean nullable )
   {
	   return new ColumnDefinition(name, TYPE_DATE, 0, nullable);
   }

   public static ColumnDefinition createString( String name, int length, boolean nullable )
   {
      if ( length > 4000 )
         return ColumnDefinition.createLongString(name,nullable);
      return new ColumnDefinition(name, TYPE_STRING, length, nullable);
   }

   public static ColumnDefinition createString( int sqlFlavor, String name, int length, boolean nullable )
   {
      if ( (sqlFlavor != FLAVOR_POSTGRES) && length > 4000 )
         return ColumnDefinition.createLongString(name,nullable);
      return new ColumnDefinition(name, TYPE_STRING, length, nullable);
   }

   public static ColumnDefinition createLongString( String name, boolean nullable )
   {
	   return new ColumnDefinition(name, TYPE_LONG_STRING, 0, nullable);
   }
   
   /**
    * Creates the SQL to add this column to an existing table where the column doesn't yet exist 
    */
   public String createAddColumnSql( String tableName, int sqlFlavor )
   {
	   return "ALTER TABLE " + tableName + " ADD " + createSQL(sqlFlavor, true);
   }
   
   /**
    * Creates the SQL to add this column to an existing table where the column already exists 
    */
   public String createAlterColumnSql( String tableName, int sqlFlavor )
   {
	   return "ALTER TABLE " + tableName + (sqlFlavor == FLAVOR_ORACLE ? " MODIFY " : " ALTER COLUMN ") + createSQL(sqlFlavor, false);
   }
   
   private String createSQL( int sqlFlavor, boolean isNewColumn )
   {
      StringBuilder buf = new StringBuilder();
      buf.append(name);

      if (sqlFlavor == FLAVOR_POSTGRES && !isNewColumn) {
         buf.append(" TYPE ");
      }

      if (type == TYPE_STRING) {
         buf.append(String.format(dataTypeDefs.get(type).get(sqlFlavor), precision));
      } else {
         buf.append(dataTypeDefs.get(type).get(sqlFlavor));
      }

      if ( defaultValue != null && !defaultValue.equalsIgnoreCase("") )
      {
         if (sqlFlavor == FLAVOR_POSTGRES) {
            if (!isNewColumn) {
               buf.append(", ALTER COLUMN ");
               buf.append(name);
            }
            buf.append(" SET ");
         }

         if ( (type == TYPE_STRING || type == TYPE_LONG_STRING) && !defaultValue.startsWith("'") )
            buf.append(" DEFAULT '").append(defaultValue).append("'");
         else
            buf.append(" DEFAULT ").append(defaultValue);
      }

      if (sqlFlavor == FLAVOR_POSTGRES) {
         if (!isNewColumn) {
            buf.append(", ALTER COLUMN ");
            buf.append(name);

            if (nullable)
               buf.append(" DROP NOT NULL");
            else
               buf.append(" SET ");
         }
      }

      if ( !nullable )
    	  buf.append(" NOT NULL");
      else if (sqlFlavor != FLAVOR_POSTGRES)
    	  buf.append(" NULL");

      return buf.toString();
   }
}
