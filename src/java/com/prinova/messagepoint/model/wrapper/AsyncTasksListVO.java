package com.prinova.messagepoint.model.wrapper;

import com.prinova.messagepoint.controller.tasks.TaskListItemFilterType;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.insert.Insert;
import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;

import static com.prinova.messagepoint.controller.dashboards.GlobalDashboardTagUtil.constructLocaleSelectionOnClick;

public class AsyncTasksListVO extends AsyncListVO {
	private Task						task;
	private Boolean						isSnapOffView;
	private AsyncListVOIFrameData		iFrameData;
	private TasksListVOFlags			flags;
	
	public AsyncTasksListVO(){
		super();
	}

	public void setTask(Task task) {
		this.task = task;
	}
	public void setSnapOffView(Boolean snapOffView) {
		isSnapOffView = snapOffView;
	}

	public String getName() {
		IdentifiableMessagePointModel model = task.getItem();
		if(model!=null){
			if (model instanceof RationalizerDocumentContent) {
				String doc = ApplicationUtil.getMessage("page.label.document") + ": " + ((RationalizerDocumentContent) model).getRationalizerDocument().getName();
				String order = ApplicationUtil.getMessage("page.label.order") + ": " + ((RationalizerDocumentContent) model).getOrder();
				String name = doc + " | " + order;
				String guid = ((RationalizerDocumentContent) model).buildElasticSearchGuid();
				return constructObjectNameHtml(guid, name);
			} else if (model instanceof RationalizerSharedContent) {

				String guid = ((RationalizerSharedContent) model).buildElasticSearchGuid();
				String name = model.getName();
				return constructObjectNameHtml(guid, name);
			} else {
				String guid = model.getId() +"";
				String name = model.getName();
				return constructObjectNameHtml(guid, name);
			}
		}else {
			return "";
		}
	}

	public String getObjectName(){
		IdentifiableMessagePointModel model = task.getItem();
		if(model!=null){
			if (model instanceof RationalizerDocumentContent) {
				String doc = ApplicationUtil.getMessage("page.label.document") + ": " + ((RationalizerDocumentContent) model).getRationalizerDocument().getName();
				String order = ApplicationUtil.getMessage("page.label.order") + ": " + ((RationalizerDocumentContent) model).getOrder();
				String name = doc + " | " + order;
				String guid = ((RationalizerDocumentContent) model).buildElasticSearchGuid();
				return constructObjectNameHtml(guid, name);
			} else if (model instanceof RationalizerSharedContent) {

				String guid = ((RationalizerSharedContent) model).buildElasticSearchGuid();
				String name = model.getName();
				return constructObjectNameHtml(guid, name);
			} else {
				String guid = model.getId() +"";
				String name = model.getName();
				return constructObjectNameHtml(guid, name);
			}
		}else {
			return "";
		}
	}

	private String constructObjectNameHtml(String guid, String name) {
		String nameHTML = "<a id=\"actionLabel_" + guid + "\" itemName=\"ITEM_NAME\" href=\"#\" " + generateOnClickLink() + " class=\"d-inline-block w-100 text-truncate dataTableItemName \">" + name + "</a>";
		nameHTML = nameHTML.replace("ITEM_NAME", name);
		return "<div class=\"position-relative mt--2\"><div class=\"text-truncate-wrapper\" title=\"" + name + "\">" + nameHTML + "</div></div>";
	}

	public String getReporter(){
		if(this.task.getReporter()!=null){
			return this.task.getReporter().getFullName();
		}else{
			return "";
		}
	}

	public String getAssignee(){
		return task.getAssetAssignees();
	}

	public String getObjectType(){
		IdentifiableMessagePointModel model = this.task.getItem();
		TaskListItemFilterType taskListItemFilterType = TaskListItemFilterType.getInstance(model);
		if(taskListItemFilterType != null){
			return taskListItemFilterType.getName();
		}
		return "";
	}

	public String getTaskType(){
		return this.task.getTaskTypeStr();
	}

	public String getAction(){
		String statusHTML = "<span class=\"d-inline-block w-100 text-truncate dataTableItemName \">" + this.task.getAction() + "</span>";
		return "<div class=\"position-relative mt--2\"><div class=\"text-truncate-wrapper\">" + statusHTML + "</div></div>";
	}

	public String getLanguage(){
		if(this.task.getMessagepointLocale() != null){
			return this.task.getMessagepointLocale().getName();
		}else{
			return "";
		}
	}
	
	public String getDueby(){
		if(this.task.getDueDate()!=null) {
			String dueDateStr =  "<span>";
			if(!this.task.isComplete()) {
				if (this.task.isOverdue()) {
					dueDateStr = "<span class='text-danger'>";
				} else if (this.task.isNearTerm()) {
					dueDateStr = "<span class='text-warning'>";
				}
			}
			dueDateStr += DateUtil.formatDate(this.task.getDueDate()) + "</span>";
			return dueDateStr;
		}else{
			return "";
		}
	}

	public String getDescription(){
		if(this.task.getRequirement()!=null){
			String descriptionHTML = "<span class=\"d-inline-block w-100 text-truncate dataTableItemName \">" + new String(this.task.getRequirement()) + "</span>";
			return "<div class=\"position-relative mt--2\"><div class=\"text-truncate-wrapper\">" + descriptionHTML + "</div></div>";
		}else{
			return "";
		}
	}
	
	public String getCreatedBy(){
		return this.task.getCreatedByName();
	}

	public String getCreatedDate(){
		if(this.task.getCreated()!=null) {
			return DateUtil.formatDate(this.task.getCreated());
		}else{
			return "";
		}
	}

	public String getType(){
		if(this.task.getMetadataForm() != null){
			return this.task.getMetadataForm().getFormDefinition().getName();
		}else{
			return ApplicationUtil.getMessage("page.label.general");
		}
	}
	
	public String getProject(){
		if(this.task.isReferenced()) {
			String taskStatus = "";
			if(task.getWorkflowAction() == null){
				taskStatus = ApplicationUtil.getMessage("page.label.pending.initial.task.complete");
			}else{
				if(task.getWorkflowAction().isActive()){
					taskStatus = ApplicationUtil.getMessage("page.label.complete");
				}else{
					if(!task.isComplete()){
						taskStatus = ApplicationUtil.getMessage("page.label.pending.task.complete");
					}else{
						taskStatus = ApplicationUtil.getMessage("page.label.pending") + " " + task.getWorkflowAction().getConfigurableWorkflowStep().getState();
					}
				}
			}
			return "<div class=\"text-center\"><i reportLabel=\"" + ApplicationUtil.getMessage("page.label.yes") + "\" class=\"far fa-check table-icon txtFmtTip\" title=\"|<div class='txtFmtTipText'>" + taskStatus + "</div>\"></i></div>";
		} else {
			return "<div reportLabel=\"" + ApplicationUtil.getMessage("page.label.no") + "\"></div>";
		}
	}

	public AsyncListVOIFrameData getIFrameData() {
		return iFrameData;
	}
	public void setIFrameData(AsyncListVOIFrameData iFrameData) {
		this.iFrameData = iFrameData;
	}

	public String getBinding() {
		return "<input id='listItemCheck_" + this.task.getId() + "' type='checkbox' value='" + this.task.getId() +
				"' style='display : none;' name='selectedIds'><input type='hidden' value='on' name='_selectedIds'>";
	}
	
	public TasksListVOFlags getFlags() {
		return flags;
	}

	public void setFlags(TasksListVOFlags flags) {
		this.flags = flags;
	}

	public String generateOnClickLink(){
		IdentifiableMessagePointModel model = this.task.getItem();
		String onClickLink = "";

		if (model instanceof ContentObject) {
			ContentObject contentObject = (ContentObject) model;
			String languageSelection = task.getMessagepointLocale() == null ? "" : constructLocaleSelectionOnClick(contentObject, String.valueOf(task.getMessagepointLocale().getId()));
			onClickLink = "onclick=\"javascript:crossLinkOpen('content/content_object_view.form?documentId=" + contentObject.getFirstDocumentDelivery().getId() + "&contentObjectId=" + contentObject.getId() +
					"&statusViewId=" + contentObject.getFocusOnDataType() + "');  " + languageSelection + " return false;\"";
		} else if (model instanceof TargetGroup) {
			onClickLink += "onclick=\"javascript:crossLinkOpen('dataadmin/target_group_edit.form?targetgroupid=" + model.getId() + "&newtgedit=true" +  "'); return false;\"";
		} else if(model instanceof TouchpointSelection) {
			TouchpointSelection ts = (TouchpointSelection)model;
			onClickLink += "onclick=\"javascript:crossLinkOpen('touchpoints/touchpoint_variant_list.form?touchpointSelectionId=" + model.getId() + "&documentId=" + ts.getDocument().getId() +  "'); return false;\"";
		} else if(model instanceof Insert) {
			Insert insert = (Insert)model;
			onClickLink += "onclick=\"javascript:crossLinkOpen('insert/insert_view.form?insertId=" + model.getId() +  "'); return false;\"";
		} else if(model instanceof InsertSchedule) {
			InsertSchedule insertSchedule = (InsertSchedule)model;
			onClickLink += "onclick=\"javascript:crossLinkOpen('insert/insert_schedule_view.form?insertSchedId=" + model.getId() + "&documentId=" + insertSchedule.getScheduleCollection().getDocument().getId() +  "'); return false;\"";
		} else if(model instanceof Document){
			Document tp = (Document)model;
			onClickLink += "onclick=\"javascript:crossLinkOpen('tpadmin/document_view.form?docid=" + model.getId()  + "'); return false;\"";
		}

		if (model instanceof RationalizerDocumentContent) {
			RationalizerDocumentContent rdc = (RationalizerDocumentContent) model;
			String documentEditURL = ApplicationUtil.getWebRoot() + "rationalizer/rationalizer_document_edit.form";
			onClickLink = "onclick=\"javascript:iFrameView('"
					+ documentEditURL
					+ "?rationalizerApplicationId=" + rdc.getRationalizerDocument().getRationalizerApplication().getId()
					+ "&rationalizerDocumentId="+ rdc.getRationalizerDocument().getId()
					+ "&selectedContentId=" + rdc.buildElasticSearchGuid()
					+ "', 'actionLabel_" + rdc.buildElasticSearchGuid() + "', event); return false;\"";

		} else if (model instanceof RationalizerSharedContent){
			RationalizerSharedContent rationalizerSharedContent = (RationalizerSharedContent) model;
			String sharedEditURL = ApplicationUtil.getWebRoot() + "rationalizer/rationalizer_shared_content_edit.form?rationalizerSharedContentIds=";
			onClickLink = "onclick=\"javascript:iFrameView('"
					+ sharedEditURL
			    	+ rationalizerSharedContent.buildElasticSearchGuid() + "&action=1"
					+ "', 'actionLabel_" + rationalizerSharedContent.buildElasticSearchGuid() + "', event); return false;\"";

		}
		return onClickLink;
	}

	public static class TasksListVOFlags{
		private boolean	canUpdate;
		private boolean canRemove;
		private boolean canMarkComplete;
		private boolean canReopen;
		private boolean canReassign;
		private boolean	canDiscard;
		private boolean	canApprove;
		private boolean	canReject;
		private boolean canReleaseFromTranslation;
		private boolean canAbort;
		private boolean	canReleaseForApproval;
		private boolean canActivate;
		private boolean	hasNoStepForWorkflow;
		private boolean	isWorkflowOwner;
		private boolean canCreateWorkingCopy;
		
		public boolean isCanUpdate() {
			return canUpdate;
		}
		public void setCanUpdate(boolean canUpdate) {
			this.canUpdate = canUpdate;
		}
		public boolean isCanRemove() {
			return canRemove;
		}
		public void setCanRemove(boolean canRemove) {
			this.canRemove = canRemove;
		}
		public boolean isCanMarkComplete() {
			return canMarkComplete;
		}
		public void setCanMarkComplete(boolean canMarkComplete) {
			this.canMarkComplete = canMarkComplete;
		}
		public boolean isCanReopen() {
			return canReopen;
		}
		public void setCanReopen(boolean canReopen) {
			this.canReopen = canReopen;
		}
		public boolean isCanReassign() {
			return canReassign;
		}
		public void setCanReassign(boolean canReassign) {
			this.canReassign = canReassign;
		}
		public boolean isCanDiscard() {
			return canDiscard;
		}
		public void setCanDiscard(boolean canDiscard) {
			this.canDiscard = canDiscard;
		}
		public boolean isCanApprove() {
			return canApprove;
		}
		public void setCanApprove(boolean canApprove) {
			this.canApprove = canApprove;
		}
		public boolean isCanReject() {
			return canReject;
		}
		public void setCanReject(boolean canReject) {
			this.canReject = canReject;
		}
		public boolean isCanReleaseFromTranslation() {
			return canReleaseFromTranslation;
		}
		public void setCanReleaseFromTranslation(boolean canReleaseFromTranslation) {
			this.canReleaseFromTranslation = canReleaseFromTranslation;
		}
		public boolean isCanAbort() {
			return canAbort;
		}
		public void setCanAbort(boolean canAbort) {
			this.canAbort = canAbort;
		}
		public boolean isCanReleaseForApproval() {
			return canReleaseForApproval;
		}
		public void setCanReleaseForApproval(boolean canReleaseForApproval) {
			this.canReleaseForApproval = canReleaseForApproval;
		}
		public boolean isCanActivate() {
			return canActivate;
		}
		public void setCanActivate(boolean canActivate) {
			this.canActivate = canActivate;
		}
		public boolean isHasNoStepForWorkflow() {
			return hasNoStepForWorkflow;
		}
		public void setHasNoStepForWorkflow(boolean hasNoStepForWorkflow) {
			this.hasNoStepForWorkflow = hasNoStepForWorkflow;
		}
		public boolean isWorkflowOwner() {
			return isWorkflowOwner;
		}
		public void setWorkflowOwner(boolean isWorkflowOwner) {
			this.isWorkflowOwner = isWorkflowOwner;
		}

		public boolean isCanCreateWorkingCopy() {
			return canCreateWorkingCopy;
		}

		public void setCanCreateWorkingCopy(boolean canCreateWorkingCopy) {
			this.canCreateWorkingCopy = canCreateWorkingCopy;
		}
	}
}
