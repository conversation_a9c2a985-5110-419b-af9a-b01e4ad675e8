package com.prinova.messagepoint.platform.services.elasticsearch.action;

import ai.mpr.marcie.content.rationalizer.ApplicationService;
import ai.mpr.marcie.content.rationalizer.steps.AbstractStep;
import ai.mpr.marcie.content.rationalizer.steps.CompositeAggregation;
import ai.mpr.marcie.content.rationalizer.steps.StepOutput;
import com.google.gson.JsonObject;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

import static ai.mpr.marcie.content.rationalizer.SearchContext.Defualts.AGGREGATION_BUCKET_SIZE;
import static ai.mpr.marcie.content.rationalizer.misc.ApplicationUtils.prettyPrintJsonElement;
import static ai.mpr.marcie.content.rationalizer.misc.ApplicationUtils.readJsonObject;
import static com.prinova.messagepoint.model.wrapper.elastic.util.ElasticSearchQueryUtil.readCompositeAggregationNode;

public class AggregateOnField extends AbstractStep<AggregateOnField.Request, Map> {

    private static final String RESULT_BUCKETS = "result_buckets";

    @Override
    public StepOutput<Map> apply(ApplicationService service, AggregateOnField.Request req) {
        final String FIELD_NAME = req.aggregationFieldName();
        JsonObject query = readJsonObject("{\n" +
                "  \"size\": 0,\n" +
                "  \"track_total_hits\": false,\n" +
                "  \"query\": " + prettyPrintJsonElement(req.query()) + "," +
                "  \"aggs\": {\n" +
                "    \"" + RESULT_BUCKETS + "\": {\n" +
                "      \"composite\": {\n" +
                "        \"sources\": [\n" +
                "          {\n" +
                "            \"" + FIELD_NAME + "\": {\n" +
                "              \"terms\": {\n" +
                "                \"field\": \"" + FIELD_NAME + "\"\n" +
                "              }\n" +
                "            }\n" +
                "          }\n" +
                "        ],\n" +
                "        \"size\": " + AGGREGATION_BUCKET_SIZE + "\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}");
        JsonObject compositeNodeValue = readCompositeAggregationNode(query, RESULT_BUCKETS);
        query.remove("aggs");
        Map<String, Integer> collectedDataMap = new LinkedHashMap<>();
        new CompositeAggregation().apply(service, CompositeAggregation.Request.builder()
                .appId(req.appId())
                .queryWithoutAggs(query)
                .compositeAggregation(new CompositeAggregation.CompositeAggregationDto(RESULT_BUCKETS, compositeNodeValue))
                .bucketItemConsumer(bucketItemDto -> {
                    String currentResult = bucketItemDto.getItemKey().get(req.aggregationFieldName());
                    int currentCount = Long.valueOf(bucketItemDto.getDocCount()).intValue();

                    if (currentCount >= 1) {
                        collectedDataMap.put(currentResult, currentCount);
                    }
                })
                .continueAfterPage(currentPage -> true)
                .build()
        );

        LinkedHashMap<Object, Object> resultMap = collectedDataMap.entrySet().stream()
                .sorted(Collections.reverseOrder(Map.Entry.comparingByValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, LinkedHashMap::new));

        return StepOutput.builder(Map.class).result(resultMap).build();
    }

    public interface Request {

        String appId();

        String aggregationFieldName();

        JsonObject query();

        static Request.Builder builder() {
            return new Request.Builder();
        }

        class Builder {
            private String appId;
            private String aggregationFieldName;
            private JsonObject query;

            private Builder() {
            }

            public Request.Builder appId(String appId) {
                this.appId = appId;

                return this;
            }

            public Request.Builder aggregationFieldName(String aggregationFieldName) {
                this.aggregationFieldName = aggregationFieldName;

                return this;
            }

            public Request.Builder query(JsonObject query) {
                this.query = query;

                return this;
            }

            public Request build() {
                return new Request() {

                    @Override
                    public String appId() {
                        return appId;
                    }

                    @Override
                    public String aggregationFieldName() {
                        return aggregationFieldName;
                    }

                    @Override
                    public JsonObject query() {
                        return query;
                    }

                };
            }
        }
    }
}
