package com.prinova.messagepoint.model.admin;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.HibernateUtil;

public class CompoundKey extends IdentifiableMessagePointModel {

	private static final long serialVersionUID = 568916306122401585L;

	private Set<CompoundKeyItem> keyItems = new HashSet<>();

    private boolean needRecalculateHash = true;
    private String sha256Hash = null;

	public CompoundKey() {

    }

    public CompoundKey(CompoundKey cloneFrom) {
	    super(cloneFrom);
	    this.save();
	    setKeyItems(CloneHelper.clone(cloneFrom.getKeyItems(), o->o.clone(this)));
    }

    @Override
    public CompoundKey clone() {
	    return new CompoundKey(this);
    }

    public List<CompoundKeyItem> getKeyItemsOrdered()
	{
		List<CompoundKeyItem> toReturn = new ArrayList<>();
		for( int i = 1; i <= keyItems.size(); ++i )
		{
			for( CompoundKeyItem cki : keyItems )
			{
				if ( cki.getItemOrder() == i )
					toReturn.add(cki);
			}
		}
		
		return toReturn;
	}

	public Set<CompoundKeyItem> getKeyItems() {
		return keyItems;
	}
	public void setKeyItems(Set<CompoundKeyItem> keyItems) {
		this.keyItems = keyItems;
	}
	public static CompoundKey parse(String compoundKeyString) {
		if (compoundKeyString == null || compoundKeyString.trim().isEmpty()) {
			return null;
		}
		CompoundKey compoundKey = new CompoundKey();
		String[] keyItemIDs = compoundKeyString.split(",");
		if (keyItemIDs.length > 0) {
			Set<CompoundKeyItem> compoundKeyItems = new HashSet<>();
			for (int i = 0; i < keyItemIDs.length; i++) {
				CompoundKeyItem compoundKeyItem = new CompoundKeyItem(DataElementVariable.findById(Long.parseLong(keyItemIDs[i])));
				compoundKeyItem.setItemOrder(i+1);
				compoundKeyItems.add(compoundKeyItem);
			}
			compoundKey.setKeyItems(compoundKeyItems);
		}
		return compoundKey;
	}
	
	@Override
	public String toString() {
		StringBuilder compoundKeyString = new StringBuilder();
		for (CompoundKeyItem keyItem : keyItems) {
			if (!compoundKeyString.toString().trim().isEmpty()) {
				compoundKeyString.append(",");
			}
			compoundKeyString.append(keyItem.getVariable().getId());
		}
		return compoundKeyString.toString();
	}
	
	public static CompoundKey findById(long id) {
		return HibernateUtil.getManager().getObject(CompoundKey.class, id);
	}

	@Override
	public int hashCode() {
		return getGuid().hashCode();
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (!(obj instanceof CompoundKey))
			return false;
		final CompoundKey other = (CompoundKey) obj;
		return (getGuid().equals(other.getGuid()));
	}
	
	public static void synchronizeCompoundKeys(CompoundKey dbCompoundKey, CompoundKey inputCompoundKey  ) {
		if (dbCompoundKey != null && inputCompoundKey != null) {
			//Removing unselected variables from database CompoundKey.
            Set<CompoundKeyItem> tempDBKeyItems = new HashSet<>(dbCompoundKey.getKeyItems());
			for (CompoundKeyItem dbKeyItem : tempDBKeyItems) {
				if (!inputCompoundKey.getKeyItems().contains(dbKeyItem)) {
					dbCompoundKey.getKeyItems().remove(dbKeyItem);
				}
			}
			for (CompoundKeyItem newKeyItem : inputCompoundKey.getKeyItems()) {
				if (!dbCompoundKey.getKeyItems().contains(newKeyItem)) {
					//Adding newly selected variables to database CompoundKey.
					dbCompoundKey.getKeyItems().add(newKeyItem);
				} else {
					//Updating the order of the variables in database CompoundKey based on the new orders.
					for (CompoundKeyItem dbKeyItem : dbCompoundKey.getKeyItems()) {
						if (newKeyItem.getVariable().equals(dbKeyItem.getVariable())) {
							dbKeyItem.setItemOrder(newKeyItem.getItemOrder());
							break;
						}
					}
				}
			}
		}
	}

    @Override
    public void preSave(Boolean isNew) {
        super.preSave(isNew);
        if(needRecalculateHash) {
            makeHash(false);
        }
    }

    public void makeHash(boolean isAlgorithmChanged) {
        String objectHashKey = getObjectHashKey();
        StringBuilder hashDataStringBuilder = new StringBuilder();

        hashDataStringBuilder.append("compoundKey");

        hashDataStringBuilder.append(" keyItems: ").append(getKeyItemsOrdered().stream()
                .map(ki -> ki.getVariable() == null ? "NO VARIABLE" : ki.getVariable().getDna())
                .collect(Collectors.joining(",")));

        sha256Hash = calculateSha256Hash(objectHashKey, isAlgorithmChanged, sha256Hash, hashDataStringBuilder.toString());
    }

	@Override
	public String getSha256Hash() {
		return sha256Hash;
	}

	@Override
	public void setSha256Hash(String sha256Hash) {
		this.sha256Hash = sha256Hash;
	}
}