package com.prinova.messagepoint.model.insert;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.hibernate.query.NativeQuery;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.UserNameComparator;
import com.prinova.messagepoint.model.admin.ParameterGroup;
import com.prinova.messagepoint.model.admin.ParameterGroupInstanceCollection;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.stateprovider.StateProviderVersionModel;
import com.prinova.messagepoint.model.tagcloud.TagCloud;
import com.prinova.messagepoint.model.util.SimpleDOM;
import com.prinova.messagepoint.model.version.ModelVersionMapping;
import com.prinova.messagepoint.model.version.OneCopyVersionInstance;
import com.prinova.messagepoint.model.version.VersionStatus;
import com.prinova.messagepoint.model.version.VersionedModel;
import com.prinova.messagepoint.platform.services.CloneServiceResponse;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.UserUtil;

public class InsertSchedule extends StateProviderVersionModel implements OneCopyVersionInstance {

	private static final long serialVersionUID = -2661995764981706380L;

	private String 								scheduleId;
	private String 								keywords;
	private int 								numberOfBins;
	private boolean 							defaultSchedule	= false;
	private ParameterGroupInstanceCollection 	parameterGroupInstanceCollection;
	private InsertScheduleCollection 			scheduleCollection;
	private InsertSchedule 						nextSchedule;
	private InsertSchedule 						previousSchedule;
	private Map<Long, RateScheduleCollection> 	insertRateSchedules = new HashMap<>();
	private Set<InsertScheduleBinAssignment> 	binAssignments = new HashSet<>();
	
	// Public default constructor
	//
	public InsertSchedule()
	{
	}

	// Copy constructor (not public) for cloning 
	//
	protected InsertSchedule(InsertSchedule cloneFrom, InsertScheduleCollection clonedCollection)
	{
		super(cloneFrom);

		this.scheduleId = cloneFrom.scheduleId;
		this.keywords = cloneFrom.keywords;
		this.numberOfBins = cloneFrom.numberOfBins;
		
		this.parameterGroupInstanceCollection = (ParameterGroupInstanceCollection)cloneFrom.parameterGroupInstanceCollection.clone();
		
		InsertScheduleCollection newCollection = (InsertScheduleCollection)clonedCollection.clone();		
		this.scheduleCollection = newCollection;
		this.scheduleCollection.getInsertSchedules().add(this);
		newCollection.save();

		for ( Long key: cloneFrom.insertRateSchedules.keySet() )
			this.insertRateSchedules.put(key, cloneFrom.insertRateSchedules.get(key));
		
		for ( InsertScheduleBinAssignment currentBinAssignment: cloneFrom.binAssignments ) {
			InsertScheduleBinAssignment clonedBinAssignment = (InsertScheduleBinAssignment)currentBinAssignment.clone();
			clonedBinAssignment.setInsertSchedule(this);
			clonedBinAssignment.save();
			this.binAssignments.add( clonedBinAssignment );
		}

	}
	
	@Override
	public Object clone()
	{
		return new InsertSchedule(this, this.getScheduleCollection());
	}

	public String getScheduleId() {
		return scheduleId;
	}
	public void setScheduleId(String scheduleId) {
		this.scheduleId = scheduleId;
	}
	public String getKeywords() {
		return keywords;
	}
	public void setKeywords(String keywords) {
		this.keywords = keywords;
	}
	public int getNumberOfBins() {
		return numberOfBins;
	}
	public void setNumberOfBins(int numberOfBins) {
		this.numberOfBins = numberOfBins;
	}
	public boolean isDefaultSchedule() {
		return defaultSchedule;
	}
	public void setDefaultSchedule(boolean defaultSchedule) {
		this.defaultSchedule = defaultSchedule;
	}
	public ParameterGroupInstanceCollection getParameterGroupInstanceCollection() {
		return parameterGroupInstanceCollection;
	}
	public void setParameterGroupInstanceCollection(
			ParameterGroupInstanceCollection parameterGroupInstanceCollection) {
		this.parameterGroupInstanceCollection = parameterGroupInstanceCollection;
	}
	public InsertScheduleCollection getScheduleCollection() {
		return scheduleCollection;
	}
	public void setScheduleCollection(InsertScheduleCollection scheduleCollection) {
		this.scheduleCollection = scheduleCollection;
	}
	public InsertSchedule getNextSchedule() {
		return nextSchedule;
	}
	public void setNextSchedule(InsertSchedule nextSchedule) {
		this.nextSchedule = nextSchedule;
	}
	public InsertSchedule getPreviousSchedule() {
		return previousSchedule;
	}
	public void setPreviousSchedule(InsertSchedule previousSchedule) {
		this.previousSchedule = previousSchedule;
	}
	public Map<Long, RateScheduleCollection> getInsertRateSchedules() {
		return insertRateSchedules;
	}
	public void setInsertRateSchedules(
			Map<Long, RateScheduleCollection> insertRateSchedules) {
		this.insertRateSchedules = insertRateSchedules;
	}
	public Set<InsertScheduleBinAssignment> getBinAssignments() {
		return binAssignments;
	}
	public List<InsertScheduleBinAssignment> getBinAssignmentsSortedOnBinNo() {
		List<InsertScheduleBinAssignment> binAssignmentsSorted = new ArrayList<>(binAssignments);
		Collections.sort(binAssignmentsSorted, new InsertScheduleBinAssignmentComparator());
		return binAssignmentsSorted;
	}
	public List<InsertScheduleBinAssignment> getBinAssignmentsSortedOnInsertName() {
		List<InsertScheduleBinAssignment> binAssignmentsSorted = new ArrayList<>();
		if (getBinAssignments() != null) {
			for (InsertScheduleBinAssignment binAssignment : getBinAssignments()) {
				if (binAssignment.getInsert() != null) {
					binAssignmentsSorted.add(binAssignment);
				}
			}
		}
		Collections.sort(binAssignmentsSorted, new InsertScheduleBinAssignmentByInsertNameComparator());
		return binAssignmentsSorted;
	}
	private List<InsertScheduleBinAssignment> getMandatoryBinAssignments()
	{
		List<InsertScheduleBinAssignment> result = new ArrayList<>();
		for (InsertScheduleBinAssignment binAssignment : binAssignments) 
		{
			if (binAssignment != null && 
				binAssignment.getInsert() != null && 
				( binAssignment.getInsert().getDeliveryType().getId() == Insert.INSERT_DELIVERY_TYPE_MANDATORY ||
				  binAssignment.getInsert().getDeliveryType().getId() == Insert.INSERT_DELIVERY_TYPE_NON_SELECTABLE )) 
			{
				result.add(binAssignment);
			}
		}
		return result;
	}
	public List<InsertScheduleBinAssignment> getOptionalBinAssignmentsSorted() {
		List<InsertScheduleBinAssignment> binAssignmentsSorted = new ArrayList<>();
		for (InsertScheduleBinAssignment binAssignment : binAssignments) {
			if (binAssignment != null && 
				binAssignment.getInsert() != null && 
				binAssignment.getInsert().getDeliveryType().getId() == Insert.INSERT_DELIVERY_TYPE_OPTIONAL) 
			{
				binAssignmentsSorted.add(binAssignment);
			}
		}
		Collections.sort(binAssignmentsSorted, new InsertScheduleOptionalBinAssignmentComparator());
		return binAssignmentsSorted;
	}
	public void setBinAssignments(Set<InsertScheduleBinAssignment> binAssignments) {
		this.binAssignments = binAssignments;
	}	

	@Override
	public int hashCode() {
		return getGuid().hashCode();
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (!(obj instanceof InsertSchedule))
			return false;
		final InsertSchedule other = (InsertSchedule) obj;
		return (getGuid().equals(other.getGuid()));
	}
	
	public static InsertSchedule findById(long id) {
		return HibernateUtil.getManager().getObject(InsertSchedule.class, id);
	}

	public static InsertSchedule findByScheduleId(String scheduleId) {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("scheduleId", scheduleId));
		
		List<InsertSchedule> insertScheduleList =  HibernateUtil.getManager().getObjectsAdvanced(InsertSchedule.class, critList);
		if (insertScheduleList != null && !insertScheduleList.isEmpty()) {
			return insertScheduleList.get(0); 
		}
		return null;
	}

	public static List<InsertSchedule> findAll(){
		return HibernateUtil.getManager().getObjects(InsertSchedule.class);
	}
	
	@SuppressWarnings("unchecked")
	public static List<InsertSchedule> findAllByAdvancedQuery(Document document, String nameSearchStr, int numCap) {
		StringBuilder query = new StringBuilder("	SELECT 		sched ");
		query.append("							FROM		InsertSchedule as sched ");
		query.append("							WHERE 		sched.scheduleCollection.document.id = :documentId ");
		query.append("							AND 		sched.status.id != :removedStatusId ");

		// SEARCH
		if ( nameSearchStr != null && !nameSearchStr.isEmpty()) {
			query.append("			AND ");
			
			Map<String, List<String>> searchLists = TagCloud.tokenizeSearchString(nameSearchStr);
			
			for (int i=0; i < searchLists.get("names").size(); i++) {
				if ( i != 0 )
					query.append("	AND ");
				query.append("		LOWER(sched.name) LIKE '%").append(searchLists.get("names").get(i).toLowerCase().replaceAll("_", "|_").replaceAll("%", "|%")).append("%' ESCAPE '|' ");
			}
		}
		
		Map<String, Object> params = new HashMap<>();
		params.put("documentId", document.getId());
		params.put("removedStatusId", VersionStatus.ONE_vERSION_REMOVED);		
		
		List<InsertSchedule> insertSchs = null;
		if ( numCap > 0 )
			insertSchs = (List<InsertSchedule>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params, 1, numCap, "");
		else
			insertSchs = (List<InsertSchedule>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
		
		return insertSchs;
	}	

	public boolean isMine() {
		if (this.getLockedFor() == null)
			return false;
		return this.getLockedFor().longValue() == UserUtil.getPrincipalUserId();
	}

	public User getTaskUser() {
		Long lockedForUserId = getLockedFor();
		if (lockedForUserId != null)
			return User.findById(lockedForUserId);
		else
			return null;
	}
	
	/*
	 * The following methods are required by super class, but since Insert is not fully version managed, the
	 * following methods are not implemented.
	 * 
	 */
	public CloneServiceResponse cloneNewVersion(User user) {
		// TODO Auto-generated method stub
		return null;
	}

	public VersionedModel getModel() {
		// TODO Auto-generated method stub
		return null;
	}

	public ModelVersionMapping getVersionInfo() {
		// TODO Auto-generated method stub
		return null;
	}

	public <E extends ModelVersionMapping> Set<E> getVersionMappings() {
		// TODO Auto-generated method stub
		return null;
	}

	public VersionedModel newModel() {
		// TODO Auto-generated method stub
		return null;
	}
	
	public ParameterGroup getParameterGroup() {
		ParameterGroup parameterGroup = null;
		InsertScheduleCollection insertScheduleCollection = this.getScheduleCollection();
		if (insertScheduleCollection != null) {
			Document document = insertScheduleCollection.getDocument();
			if (document != null) {
				parameterGroup = document.getInsertParameterGroup();
			}
		}
		return parameterGroup;
	}

	public boolean hasWorkFlow() {
		return false;
	}

	public String toString() {
		StringBuilder sb = new StringBuilder("Insert Schedule ID:");
		sb.append(getId());
		sb.append(" name:");
		sb.append(getName());
		return sb.toString();
	}

	public List<User> getCurrentStateUsers() {
		Set<User> users = new HashSet<>();
		if (getStatus().getId() == VersionStatus.ONE_VERSION_INACTIVE) {
			users.addAll(UserUtil.getAuthUsers(Permission.ROLE_INSERT_SCHEDULE_EDIT));
			users.addAll(UserUtil.getAuthUsers(Permission.ROLE_INSERT_SCHEDULE_SETUP));
		} else if (getStatus().getId() == VersionStatus.ONE_VERSION_WAITING_APPROVAL) {
			users.addAll(UserUtil.getAuthUsers(Permission.ROLE_INSERT_SCHEDULE_APPROVE));
		}
		List<User> usersList = new ArrayList<>(users);
		Collections.sort(usersList, new UserNameComparator());
		return usersList;
	}
	
	public List<User> getNextStateUsers() {
		Set<User> users = new HashSet<>();
		if (getStatus().getId()==VersionStatus.ONE_VERSION_INACTIVE) {
			users.addAll(UserUtil.getAuthUsers(Permission.ROLE_INSERT_SCHEDULE_APPROVE));			
		}
		List<User> usersList = new ArrayList<>(users);
		Collections.sort(usersList, new UserNameComparator());
		return usersList;
	}
	
	public List<User> getEditObjectUsers() {
		Set<User> users = new HashSet<>();
		users.addAll(UserUtil.getAuthUsers(Permission.ROLE_LICENCED_INSERT_SCHEDULE_EDIT));
		users.addAll(UserUtil.getAuthUsers(Permission.ROLE_INSERT_SCHEDULE_SETUP));
		List<User> usersList = new ArrayList<>(users);
		Collections.sort(usersList, new UserNameComparator());
		return usersList;
	}
	
	public SimpleDOM toXml(Date requestDate)
	{
	    SimpleDOM dom = new SimpleDOM("InsertSchedule");
	    dom.setAttribute("id", getId());
	    
		if ( getParameterGroupInstanceCollection() != null )
		    dom.setAttribute("parametersetrefid", getParameterGroupInstanceCollection().getId());

		dom.addChildCDATA("Name", getName());
		dom.addChildCDATA("ExternalId", getScheduleId());

		SimpleDOM rsListDom = dom.addChild("RateScheduleList");
		Map<Long, RateScheduleCollection> rscMap = getInsertRateSchedules();
		
		for( Long rsKey : rscMap.keySet() )
		{
			RateScheduleCollection rsc = rscMap.get(rsKey);
			RateSchedule item = rsc.getRateScheduleForDate(requestDate);
			if ( item == null )
				continue;

			SimpleDOM rsDom = rsListDom.addChild("RateSchedule");
			rsDom.setAttribute("refid", item.getId());
			rsDom.setAttribute("sheetlimit", rsKey);
		}
		
		String unitsProp = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Insert.KEY_WeightUnits);
		String units = unitsProp.equals(WeightUnit.ID_GRAMS_STRING) ? "grams" : "ounces";
		
		SimpleDOM baListDom = dom.addChild("BinAssignments");
		baListDom.setAttribute("units", units);
		baListDom.setAttribute("bins", numberOfBins);

		// make a list of bins, in priority order
		List<InsertScheduleBinAssignment> allBinsSorted = new ArrayList<>();
		allBinsSorted.addAll(getMandatoryBinAssignments());
		allBinsSorted.addAll(getOptionalBinAssignmentsSorted());
		
		for( InsertScheduleBinAssignment isba : allBinsSorted )
		{
			if ( isba.getInsert() == null )
				continue;

			Date startDate = isba.getStartDate();
			Date endDate = isba.getEndDate();

			SimpleDOM binDom = baListDom.addChild("Bin");
			binDom.setAttribute("number", isba.getBinNo());
			if ( startDate != null )
			    binDom.setAttribute("startdate", DateUtil.formatDateYYYYsMMsDD(startDate));
			if ( endDate != null )
			    binDom.setAttribute("enddate", DateUtil.formatDateYYYYsMMsDD(endDate));

			binDom.addChild("Insert").setAttribute("refid", isba.getInsert().getId());
		}
		
		return dom;
	}
	
	public static List<Insert> getListOfCorrespondingInserts( 
			List<InsertSchedule> insertSchedules,
			boolean onlyActive ) 
	{
		List<Insert> toReturn = new ArrayList<>();
		Set<Long> usedIds = new HashSet<>();
		
		for( InsertSchedule is : insertSchedules )
		{
			for( InsertScheduleBinAssignment isba : is.binAssignments )
			{
				Insert ins = isba.getInsert();
				if ( ins == null || usedIds.contains(ins.getId()) )
					continue;
				usedIds.add(ins.getId());

				if ( ins.getIsArchived() || (onlyActive && !ins.getIsActive()) )
				{
					continue;
				}
				
				toReturn.add(ins);
			}
		}
		
		return toReturn;
	}
	
	public static List<RateSchedule> getListOfCorrespondingRateSchedules(
			List<InsertSchedule> insertSchedules,
			Date referenceDate ) 
	{
		List<RateSchedule> toReturn = new ArrayList<>();
		Set<Long> usedIds = new HashSet<>();
		for( InsertSchedule is : insertSchedules )
		{
			for( RateScheduleCollection rsc : is.insertRateSchedules.values() )
			{
				RateSchedule item = rsc.getRateScheduleForDate(referenceDate);
				if ( item == null )
					continue;

				if ( usedIds.contains(item.getId()) )
					continue;

				usedIds.add(item.getId());
				toReturn.add(item);
			}
		}
		return toReturn;
	}
	
	public Boolean getHasTiming() {
		for (InsertScheduleBinAssignment currentBinAssignment: this.binAssignments)
			if (currentBinAssignment.getStartDate() != null || currentBinAssignment.getEndDate() != null)
				return true;
		
		return false;
	}
	
	public int getNumberOfBinReservations() {
		int binResCount = 0;
		for (InsertScheduleBinAssignment currentBinAssignment: this.binAssignments)
			if (currentBinAssignment.getInsert() != null && 
				currentBinAssignment.getInsert().getDeliveryTypeId() == InsertDeliveryType.ID_NON_SELECTABLE)
				binResCount++;
		
		return binResCount;
	}
	
	public Boolean getIsSetup() {
		return this.getStatus().getId() == VersionStatus.ONE_VERSION_SETUP;
	}
	public Boolean getIsArchived() {
		return this.getStatus().getId() == VersionStatus.ONE_VERSION_ARCHIVE;
	}
	public Boolean getIsActive() {
		return this.getStatus().getId() == VersionStatus.ONE_VERSION_ACTIVE;
	}
	public Boolean getIsInactive() {
		return this.getStatus().getId() == VersionStatus.ONE_VERSION_INACTIVE;
	}
	public Boolean getIsReleasedForApproval() {
		return this.getStatus().getId() == VersionStatus.ONE_VERSION_WAITING_APPROVAL;
	}
	public Boolean getIsMine() {
		if (this.getLockedFor() == null)
			return false;
		return this.getLockedFor().longValue() == UserUtil.getPrincipalUserId();
	}
	public Boolean getCanUpdate() {
		return (this.getIsMine() || this.getLockedFor() == null) && (this.getStatus().getId() == VersionStatus.ONE_VERSION_INACTIVE || this.getStatus().getId() == VersionStatus.ONE_VERSION_SETUP);
	}

	@SuppressWarnings("unchecked")
	public static List<InsertSchedule> listAllOverlappingSchedules(InsertSchedule insertSchedule, Date startDate, Date endDate) {
		String query = "SELECT 		sched " +
					   "FROM		InsertSchedule as sched " +
					   "WHERE 		sched.id <> :scheduleId " +
					   "AND 		sched.scheduleCollection.document.id = :documentId " +
					   "AND			((sched.startDate >= :startDate " + (endDate == null ? "" : "AND sched.startDate <= :endDate") + ") " +
					   "OR			(sched.endDate >= :startDate " + (endDate == null ? "" : "AND sched.endDate <= :endDate") + ") " +
					   "OR			(sched.startDate <= :startDate AND (sched.endDate IS NULL " + (endDate == null ? "" : "OR sched.endDate >= :endDate") + "))) ";
					   					
		Map<String, Object> params = new HashMap<>();
		
		params.put("scheduleId", insertSchedule.getId());
		params.put("documentId", insertSchedule.getScheduleCollection().getDocument().getId());
		params.put("startDate", startDate);
		if (endDate != null) {
			params.put("endDate", endDate);
		}
		List<InsertSchedule> inserts = (List<InsertSchedule>)HibernateUtil.getManager().getObjectsAdvanced(query, params);
		return inserts;
	}

	public static List<InsertSchedule> findByRateScheduleCollection(RateScheduleCollection rateScheduleCollection) {
		List<Long> isIds = HibernateUtil.getManager().getObjectsOfLongIDsByNativeQuery("select irs.insert_schedule_id from insert_rate_schedule irs where irs.rate_schedule_collection_id =" + rateScheduleCollection.getId(), null);
		return HibernateUtil.getManager().getObjectsAdvanced(InsertSchedule.class, MessagepointRestrictions.in("id", isIds));
	}

	public static List<InsertSchedule> findDefaultsByRateScheduleCollection(RateScheduleCollection rateScheduleCollection) {
		List<Long> isIds = HibernateUtil.getManager().getObjectsOfLongIDsByNativeQuery("select irs.insert_schedule_id from insert_rate_schedule irs where irs.number_of_sheets = 0 and irs.rate_schedule_collection_id =" + rateScheduleCollection.getId(), null);
		return HibernateUtil.getManager().getObjectsAdvanced(InsertSchedule.class, MessagepointRestrictions.in("id", isIds));
	}

	@SuppressWarnings("unchecked")
	public static List<InsertSchedule> findByDocumentAndSetupView(Document document, boolean isGlobal, boolean isSetup) {
		String query = "SELECT 		sched " +
					   "FROM		InsertSchedule as sched " +
					   "WHERE 		sched.status.id != :removedStatusId ";
		if(!isGlobal){
			query +=   "AND 		sched.scheduleCollection.document.id = :documentId ";
		}
		if(isSetup){
			query +=   "AND 		sched.status.id = :setupStatusId ";
		}else{
			query +=   "AND 		sched.status.id != :setupStatusId ";
		}

		Map<String, Object> params = new HashMap<>();
		params.put("removedStatusId", VersionStatus.ONE_vERSION_REMOVED);
		params.put("setupStatusId", VersionStatus.ONE_VERSION_SETUP);
		if(!isGlobal){
			params.put("documentId", document.getId());
		}
		
		List<InsertSchedule> insertSchedules = (List<InsertSchedule>)HibernateUtil.getManager().getObjectsAdvanced(query, params);
		return insertSchedules;
	}
	
	@SuppressWarnings("unchecked")
	public static List<InsertSchedule> findByDocument(Document document) {
		String query = "SELECT 		sched " +
					   "FROM		InsertSchedule as sched " +
					   "WHERE 		sched.scheduleCollection.document.id = :documentId " +
					   "AND 		sched.status.id != :removedStatusId ";

		Map<String, Object> params = new HashMap<>();
		params.put("documentId", document.getId());
		params.put("removedStatusId", VersionStatus.ONE_vERSION_REMOVED);
		List<InsertSchedule> insertSchedules = (List<InsertSchedule>)HibernateUtil.getManager().getObjectsAdvanced(query, params);
		return insertSchedules;
	}

	@SuppressWarnings("unchecked")
	public static List<InsertSchedule> findByDocumentHavingSelection(Document document) {
		String query = "SELECT 		sched " +
					   "FROM		InsertSchedule as sched " +
					   "WHERE 		sched.scheduleCollection.document.id = :documentId " +
					   "AND			sched.parameterGroupInstanceCollection IS NOT NULL ";
		   					
		Map<String, Object> params = new HashMap<>();
		params.put("documentId", document.getId());
		List<InsertSchedule> insertSchedules = (List<InsertSchedule>)HibernateUtil.getManager().getObjectsAdvanced(query, params);
		return insertSchedules;
	}
	
	public boolean isExpired() {
		if (getEndDate() != null && getEndDate().before(DateUtil.todayZeroTime())) {
			return true;
		} else {
			return false;
		}
	}
	public static List<Long> findAllByDocumentIds(List<Long> docIds) {
		String query = 	"SELECT 			sched.id " +
				   		"FROM				insert_schedule sched, insert_schedule_collection schedcol " +
				   		"WHERE 				sched.schedule_collection_id = schedcol.id " + 
				   		"AND 				sched.status_id != :removedStatusId ";
		if ( docIds != null && !docIds.isEmpty() ){
			query +=    "AND 				schedcol.document_id IN (:documentIds)";
		}
		
		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
		sqlQuery.setParameter("removedStatusId", VersionStatus.ONE_vERSION_REMOVED);
		if ( docIds != null && !docIds.isEmpty() ){
			sqlQuery.setParameterList("documentIds", docIds);
		}
		List ids = sqlQuery.list();
		List<Long> itemIds = new ArrayList<>();
		for(Object idObj : ids)
			itemIds.add(((BigInteger)idObj).longValue());
		
		return itemIds;
	}
	
	public InsertSchedule findCurrentSegmentForSchedule() {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();

		Date today = DateUtil.getDateWithZeroTime(new Date());
  
		critList.add(MessagepointRestrictions.le("startDate", today));
		critList.add(MessagepointRestrictions.or(MessagepointRestrictions.isNull("endDate"),MessagepointRestrictions.ge("endDate", today)));
		critList.add(MessagepointRestrictions.eq("scheduleCollection.id", this.getScheduleCollection().getId()));
		
		List<InsertSchedule> insertScheduleList =  HibernateUtil.getManager().getObjectsAdvanced(InsertSchedule.class, critList);
		if ( insertScheduleList != null && !insertScheduleList.isEmpty())
			return insertScheduleList.get(0); 

		return null;
	}
}