package com.prinova.messagepoint.controller.admin;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.model.admin.proxylogin.Pod;
import com.prinova.messagepoint.model.admin.proxylogin.PodType;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.HttpRequestUtil;
import com.prinova.messagepoint.util.UserUtil;

public class PodViewController extends MessagepointController implements Serializable {

	private static final long serialVersionUID = 6159626656207109284L;

	public static final int ACTION_UPDATE 		= 1;
	public static final int ACTION_IMPORT 		= 2;
	public static final int ACTION_WHERE_USED 	= 3;

	public static final String REQ_PARAM_POD_ID = "podId";
	public static final String REQ_PARAM_ACTION = "action";

	private String editView;
	
	
	public String getEditView() {
		return editView;
	}
	public void setEditView(String editView) {
		this.editView = editView;
	}

	@Override
    protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
    	Map<String, Object> referenceData = new HashMap<>();
    	referenceData.put("allPodTypes", PodType.getAllPodTypes());
    	return referenceData;
    }
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}

    protected Command formBackingObject(HttpServletRequest request) {
		HttpRequestUtil.saveBackToListURL(request, HttpRequestUtil.getAllParamenterFromRequest(request), HttpRequestUtil.HTTP_ATTRIBUTE_BACKURL_VARIABLE);
    	long podId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_POD_ID, -1);

    	Pod pod = HibernateUtil.getManager().getObject(Pod.class, podId);
		
		
		
    	Command command = new Command();
		command.setPod(pod);
		if (UserUtil.getPrincipalUser().isPodAdminUser()) {
			command.setUpdatePermitted(true);
		}
		
		return command;
    }
    
    protected ModelAndView onSubmit(HttpServletRequest request,
            						HttpServletResponse response,
            						Object commandObj,
            						BindException errors) throws Exception {
		int action = ServletRequestUtils.getIntParameter(request, REQ_PARAM_ACTION, -1);
		long podId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_POD_ID, -1);
		Map<String, Object> parms = PodUtils.getContextMapParms(request);
		parms.put(REQ_PARAM_POD_ID, podId);
		
		switch (action) {
			case (ACTION_UPDATE): {
				return new ModelAndView(new RedirectView(getEditView()), parms);
			} 
			default: {
				break;
			}
		}
		return null;
    }

	public static class Command {
		private Pod pod;
		private boolean updatePermitted = false;
		private String[] indRefSelections;		

		
		public String[] getIndRefSelections() {
			return indRefSelections;
		}
		public void setIndRefSelections(String[] indRefSelections) {
			this.indRefSelections = indRefSelections;
		}
		public Pod getPod() {
			return pod;
		}
		public void setPod(Pod pod) {
			this.pod = pod;
		}
		public boolean isUpdatePermitted() {
			return updatePermitted;
		}
		public void setUpdatePermitted(boolean updatePermitted) {
			this.updatePermitted = updatePermitted;
		}			
	}
}