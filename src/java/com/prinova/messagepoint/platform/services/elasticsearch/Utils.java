package com.prinova.messagepoint.platform.services.elasticsearch;

import ai.mpr.marcie.text.TextProcessor;
import com.google.common.hash.Hashing;
import com.prinova.messagepoint.model.TouchpointLanguage;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpResponse;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.safety.Safelist;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.prinova.messagepoint.model.content.ContentObject.DATA_TYPE_WORKING_AND_ACTIVE;


public class Utils {

	
	
	public static ApplicationError createApplicationError(String appId, String method, IOException e) {
		return  createApplicationError(appId, method, e.getMessage(), null, ApplicationErrorType.HTTP_CONNECTION_ERROR);
	}

	public static ApplicationError createApplicationError(String appId, String method, RuntimeException e) {
		return  createApplicationError(appId, method, e.getMessage(), null, ApplicationErrorType.HTTP_REQUEST_ERROR);
	}

	public static ApplicationError createApplicationError(String appId, String method, HttpResponse response) {
		return createApplicationError(appId, method, response.getStatusLine().getReasonPhrase(), response.getStatusLine().getStatusCode(), ApplicationErrorType.HTTP_REQUEST_ERROR);
	}

	public static ApplicationError createApplicationError(String appId, String method, String message, Integer statusCode, ApplicationErrorType type) {
		return new ApplicationError(statusCode, type, message, appId, method);
	}

	public static RationalizerApplicationException createApplicationException(Throwable throwable, ApplicationError applicationError) {
		if (throwable instanceof RationalizerApplicationException) {
			return (RationalizerApplicationException) throwable;
		}

		return new RationalizerApplicationException(applicationError, throwable);
	}

	public static <T> void addNonNullItemToList(List<T> list, T item) {
		if (item != null) {
			list.add(item);
		}
	}

	public static String calculateHash(String text) {
		String textToProcess = (text != null)
				? hashPreprocess(text)
				: StringUtils.EMPTY;

		return Hashing.murmur3_128().hashString(textToProcess, StandardCharsets.UTF_8).toString();
	}

	/**
	 * When calculationg the Hash for Markup when auto-consolidating on upload, we will not take into consideration the type of the mpr_variable, and the item list tags
	 * @return hash
	 */
	public static String calculateAutoConsolidateHtmlHash(String originalMarkup) {
		originalMarkup = (originalMarkup != null) ? originalMarkup : StringUtils.EMPTY;
		Safelist wl = Safelist.relaxed();
		wl.addTags("mpr_variable");
		wl.addAttributes("span", "style");
		wl.removeTags("li", "ul", "ol");
		wl.removeAttributes("mpr_variable", "type");
		String cleanedMarkup = Jsoup.clean(originalMarkup, "", wl, new Document.OutputSettings().prettyPrint(false));
		return Hashing.murmur3_128().hashString(cleanedMarkup, StandardCharsets.UTF_8).toString();
	}
	public static String hashPreprocess(String text) {
		Objects.requireNonNull(text);

		return text
				.replaceAll("\\h", " ") //normalize horizontal spaces
				.replaceAll("\r\n?", "\n") //normalize line break
				;
	}

	public static final TextProcessor TEXT_HASHER = Utils::calculateHash;

	public static Map<String, Float> sortByScoreWithRscIdsOnTopForSameScore(Map<String, Float> contentIdToScoreMap) {
		List<Map.Entry<String, Float>> entries = new LinkedList<>(contentIdToScoreMap.entrySet());
		Function<String, String> computeKeyForComparison = (key) -> {
			if (StringUtils.isEmpty(key)) {
				return "";
			}

			return StringUtils.startsWithIgnoreCase(key, RationalizerSharedContent.RSC_ELASTICSEARCH_GUID_PREFIX) ? "2_" + key : "1_" + key;
		};

		Collections.sort(entries, (entry01, entry02) -> {
			final Float score01 = entry01.getValue();
			final Float score02 = entry02.getValue();

			if (score01 == null || score02 == null) {
				return 0;
			}

			if (score01.floatValue() < score02.floatValue()) {
				return -1;
			}

			if (score01.floatValue() > score02.floatValue()) {
				return 1;
			}

			String key01 = computeKeyForComparison.apply(entry01.getKey());
			String key02 = computeKeyForComparison.apply(entry02.getKey());

			return key01.compareTo(key02);
		});

		Collections.reverse(entries);

		return entries.stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
				(oldValue, newValue) -> oldValue, LinkedHashMap::new));
	}


	public static String escapeStringForRegEx(String value) {
		String[] tokens = value.split("\\s+");
		return escapeForRegularExpression(Arrays.asList(tokens));
	}


	private static String escapeForRegularExpression(List<String> keywords) {
		if(keywords == null || keywords.isEmpty()) {
			return "";
		}

		String concat = StreamSupport.stream(keywords.spliterator(), false)
				.map(kw -> escapeKeywordForRegex(kw.trim()))
				.collect(Collectors.joining(".*"));

		return ".*" + concat + ".*";
	}


	private static String escapeKeywordForRegex(String term) {
		List<Character> reserved_words = Arrays.asList('.', '?', '+', '*', '|', '{', '}', '[', ']', '(', ')', '"', '\\');
		StringBuilder sb = new StringBuilder();
		char[] chars = term.toCharArray();
		for(char c : chars) {
			if(reserved_words.contains(c)) {
				sb.append("\\").append(c);
			} else {
				sb.append(c);
			}
		}

		return sb.toString();
	}

	public static String generateStatusFromId(int selectedStatusId) {
		String statusFilerValue ="";
		switch (selectedStatusId) {
			case DATA_TYPE_WORKING_AND_ACTIVE:
				statusFilerValue = "WC,Active";
				break;
			case ContentObject.DATA_TYPE_WORKING:
				statusFilerValue = "WC";
				break;
			case ContentObject.DATA_TYPE_ACTIVE:
				statusFilerValue = "Active";
				break;
			case ContentObject.DATA_TYPE_ARCHIVED:
				statusFilerValue = "Archived";
				break;
			default:

		}
		return statusFilerValue;
	}

	public static List<String> constructSelectedLanguagesList(String selectedLanguageCode) {
		List<String> selecetdLanguagesList = new ArrayList<>();
		if(org.apache.commons.lang3.StringUtils.isEmpty(selectedLanguageCode) || "all".equalsIgnoreCase(selectedLanguageCode)) {

			for(MessagepointLocale mp : TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales()) {
				selecetdLanguagesList.add(mp.getCode());
			}
		} else {
			selecetdLanguagesList.add(selectedLanguageCode);
		}
		return selecetdLanguagesList;
	}
}
