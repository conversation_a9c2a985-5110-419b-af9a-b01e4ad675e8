package com.prinova.messagepoint.platform.services.backgroundtask;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplicationTpReference;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.export.ExportRationalizerDocumentsToMessagepointService;
import com.prinova.messagepoint.platform.services.export.ExportToXMLServiceResponse;
import com.prinova.messagepoint.platform.services.rationalizer.dto.TouchpointExportUserPreferencesDto;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.lang.StringUtils;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;

public class ExportRationalizerToMessagepointBackgroundTask extends MessagePointRunnable {

    private long rationalizerApplicationId;

    private User user;

    private TouchpointExportUserPreferencesDto userPreferencesDto;

    private final StatusPollingBackgroundTask statusPollingBackgroundTask;

    public ExportRationalizerToMessagepointBackgroundTask(long rationalizerApplicationId, User user, TouchpointExportUserPreferencesDto userPreferencesDto) {
        this.rationalizerApplicationId = rationalizerApplicationId;
        this.user = user;
        this.userPreferencesDto = userPreferencesDto;
        this.statusPollingBackgroundTask = new StatusPollingBackgroundTask();
        this.statusPollingBackgroundTask.setCreatedBy(user.getId());
    }

    public String getBackgroundTaskId() {
        return statusPollingBackgroundTask.getGuid();
    }

    @Override
    public void performMainProcessing() {
        try {
            this.statusPollingBackgroundTask.setType(StatusPollingBackgroundTask.TYPE_OBJECT_EXPORT);
            this.statusPollingBackgroundTask.setName(StatusPollingBackgroundTask.SUB_TYPE_EXPORT_RATIONLIZER_TO_TOUCHPOINT);

            this.statusPollingBackgroundTask.setRequestDate(new Date(System.currentTimeMillis()));
            this.statusPollingBackgroundTask.setBackgroundThread(this.getOwningThread());
            this.statusPollingBackgroundTask.setBackgroundThreadId(this.getOwningThread().getId());
            this.statusPollingBackgroundTask.setProgressInPercentInThread(0);
            this.statusPollingBackgroundTask.setActive(true);
            this.statusPollingBackgroundTask.save();

            // Delete File - remove any existing where used reports for user
            File srcDir = new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_MessagepointObjectExportDir));
            if (!srcDir.exists()) {
                srcDir.mkdir();
            }

            ServiceExecutionContext context = null;
            Service exportService = null;
            context = ExportRationalizerDocumentsToMessagepointService.createContext(
                    rationalizerApplicationId,
                    this.user,
                    this.statusPollingBackgroundTask,
                    this.userPreferencesDto);
            exportService = MessagepointServiceFactory.getInstance().lookupService(ExportRationalizerDocumentsToMessagepointService.SERVICE_NAME, ExportRationalizerDocumentsToMessagepointService.class);

            exportService.execute(context);
            ServiceResponse serviceResponse = context.getResponse();

            if (serviceResponse.isSuccessful()) {
                ExportToXMLServiceResponse eosr = (ExportToXMLServiceResponse) serviceResponse;
                String generatedFilePath = eosr.getFilePath();
                RationalizerApplication app = RationalizerApplication.findById(rationalizerApplicationId);
                RationalizerApplicationTpReference tpReference = getRationalzierApplicationTpRefference();
                tpReference.setRationalizerApplication(app);
                tpReference.save();
                enrichExportRationalizerDocumentsResponse(generatedFilePath);
            }
            this.statusPollingBackgroundTask.setProgressInPercentInThread(100);
            this.statusPollingBackgroundTask.setCompletedDate(new Date(System.currentTimeMillis()));
            this.statusPollingBackgroundTask.setComplete(true);
            this.statusPollingBackgroundTask.setActive(false);
            this.statusPollingBackgroundTask.save();
        } catch (Exception ex) {
            LogUtil.getLog(this.getClass()).error("Caught exception: ", ex);
            this.statusPollingBackgroundTask.setError(true);
            this.statusPollingBackgroundTask.setActive(false);
            this.statusPollingBackgroundTask.save();
        }

    }
    private RationalizerApplicationTpReference getRationalzierApplicationTpRefference(){
        RationalizerApplicationTpReference applicationTpReference = new RationalizerApplicationTpReference();
        applicationTpReference.setRationalizerApplicationId(rationalizerApplicationId);
        applicationTpReference.setTpGuid(userPreferencesDto.getTouchpointGuid());
        applicationTpReference.setTpRequestDate(new Date());
        applicationTpReference.setTpRequestedBy(user.getId());
        return applicationTpReference;
    }
    private void enrichExportRationalizerDocumentsResponse(String generatedFilePath) {
        if (StringUtils.isEmpty(generatedFilePath)) {
            return;
        }
        Path path = Paths.get(generatedFilePath);
        String generatedFileName = path.getFileName().toString();
        this.statusPollingBackgroundTask.setOutputPath(generatedFilePath);
        this.statusPollingBackgroundTask.setOutputFilename(generatedFileName);
    }
}