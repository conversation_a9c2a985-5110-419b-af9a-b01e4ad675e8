package com.prinova.messagepoint.model.wrapper;

import com.prinova.messagepoint.model.admin.AggregationOperator;
import com.prinova.messagepoint.model.admin.DataRecordLevel;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;

public class DataSourceDataElementBridgeVO {

    private DataSource dataSource;
    private AbstractDataElement dataElement;
    private AggregationOperator aggOperator;
    private DataRecordLevel level;


    public DataSourceDataElementBridgeVO(DataSource dataSource,
                                         AbstractDataElement dataElement,
                                         AggregationOperator aggOperator,
                                         DataRecordLevel level) {
        this.dataSource = dataSource;
        this.dataElement = dataElement;
        this.aggOperator = aggOperator;
        this.level = level;

    }

    public DataSource getDataSource() {
        return dataSource;
    }

    public void setDataSource(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    public AbstractDataElement getDataElement() {
        return dataElement;
    }

    public void setDataElement(AbstractDataElement dataElement) {
        this.dataElement = dataElement;
    }

    public AggregationOperator getAggOperator() {
        return aggOperator;
    }

    public void setAggOperator(AggregationOperator aggOperator) {
        this.aggOperator = aggOperator;
    }

    public DataRecordLevel getLevel() {
        return level;
    }

    public void setLevel(DataRecordLevel level) {
        this.level = level;
    }
}
