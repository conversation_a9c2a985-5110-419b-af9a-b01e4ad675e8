package com.prinova.messagepoint.platform.services.imports;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.admin.ContentType;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.content.ContentAssociationType;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.platform.services.imports.document.ContentImportHandler;
import com.prinova.messagepoint.platform.services.imports.document.MessagepointImportHandler;
import com.prinova.messagepoint.platform.services.imports.document.TPSelectionImportHandler;
import com.prinova.messagepoint.platform.services.imports.document.TouchpointImportHandler;
import com.prinova.messagepoint.platform.services.imports.document.ZoneImportHandler;
import com.prinova.messagepoint.platform.services.imports.document.ZonePartImportHandler;

public final class MessagePartImportHandler extends ContentImportHandler
{
    private int number; // part number
    private long zonePartRefId;
    private ContentType contentType = null;
    
    boolean empty;

    private MessageDeliveryImportHandler parent;
    
    public MessagePartImportHandler()
    {
        super(MessagePartImportHandler.class);
        // SUPER CLASS HANDLES CONTENT
        
        setAttrMethod("Part", "num", "setNumber", Integer.class);
        setAttrMethod("Part", "zonepart", "setZonePartRefId", Long.class);
        setAttrMethod("Part", "refid", "setZonePartRefId", Long.class);
        setAttrMethod("Part", "contenttype", "setContentType", String.class);
        setAttrMethod("Part", "empty", "setEmpty", Boolean.class);
    }
    
    @Override
    public void addChild(MessagepointImportHandler child)
    {
    }

    @Override
    protected void doneProcessing()
    {
    	if ( contentType != null && !this.getImportOnlyDataCollection() && !this.getTpImportTask().isImportOnlySharedObjects() )
    	{
    		if ( contentType.getId() == ContentType.GRAPHIC )
    		{
    			super.createGraphicFiles(false);
    		}
    	}
    }

    public void setParent( MessageDeliveryImportHandler p )
    {
        parent = p;
    }
    
    public List<ContentObjectAssociation> createPartContent(TouchpointImportHandler docHandler, boolean isLocal )
    throws Exception
    {
        long zoneRefId = parent.getZoneRefId();
        ZoneImportHandler zih = docHandler.getZone(zoneRefId);
        ZonePartImportHandler zpih = zih.getZonePart(zonePartRefId);

        if ( empty || contentType == null )
        {
            return createEmptyContent(zpih, null, docHandler);
        }
        else if ( contentType.getId() == ContentType.TEXT )
        {
            return createTextContentsForContentObject(zpih, !isLocal, false);
        }
        else if ( contentType.getId() == ContentType.GRAPHIC )
        {
            return createGraphicContentsForContentObject(zpih, false);
        }
        
        return new ArrayList<>();
    }

    public void setNumber( Integer val )
    {
        number = val;
    }

    public int getNumber()
    {
        return number;
    }

    public void setEmpty( Boolean val )
    {
        empty = val;
    }
    
    public void setZonePartRefId( Long val )
    {
        zonePartRefId = val;
    }
    
    public void setContentType( String val)
    {
        if ( val.equals("Multi-part") )
            contentType = ContentType.findById(ContentType.MULTIPART);
        else if ( val.equals("Graphic") )
            contentType = ContentType.findById(ContentType.GRAPHIC);
        else
            contentType = ContentType.findById(ContentType.TEXT);
    }
    
    private List<ContentObjectAssociation> createEmptyContent(
            ZonePartImportHandler zpih,
            TPSelectionImportHandler tpsih,
            TouchpointImportHandler docHandler)
    {    	
        List<ContentObjectAssociation> caList = new ArrayList<>();
        for ( MessagepointLocale locale : docHandler.getDocument().getTouchpointLanguagesAsLocales() )
        {
            ContentObjectAssociation ca = new ContentObjectAssociation();
            caList.add(ca);
            
            ca.setMessagepointLocale(locale);
            ca.setTypeId(ContentAssociationType.ID_EMPTY);
            ca.setZonePart( zpih.getZonePart() );
        }
        
        return caList;
    }
    
}
