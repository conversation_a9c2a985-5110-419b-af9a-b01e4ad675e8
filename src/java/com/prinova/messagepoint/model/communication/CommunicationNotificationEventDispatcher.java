package com.prinova.messagepoint.model.communication;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.webservice.WebServiceConfiguration;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowAction;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowStep;
import com.prinova.messagepoint.platform.ws.client.WsClientCustomer;
import com.prinova.messagepoint.platform.ws.client.mpcustomer.api.schemas.*;
import org.apache.commons.logging.Log;
import com.prinova.messagepoint.util.LogUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class CommunicationNotificationEventDispatcher extends MessagePointRunnable {

    private static final Log log = LogUtil.getLog(com.prinova.messagepoint.model.communication.CommunicationNotificationEventDispatcher.class);

    public static final int NOTIFICATION_EVENT_NEW = 1;
    public static final int NOTIFICATION_EVENT_UPDATE = 2;
    public static final int NOTIFICATION_EVENT_DELETE = 4;
    public static final int NOTIFICATION_EVENT_RELEASE_FOR_APPROVAL = 8;
    public static final int NOTIFICATION_EVENT_APPROVAL_STEP = 32;
    public static final int NOTIFICATION_EVENT_ACTIVATE = 64;
    public static final int NOTIFICATION_EVENT_PRE_PROOF = 128;
    public static final int NOTIFICATION_EVENT_PROOF = 256;
    public static final int NOTIFICATION_EVENT_REASSIGN = 512;

    private final Document document;
        private  List<Communication> communications = new ArrayList<>();
        private final int eventType;
        private final User requestor;

         public CommunicationNotificationEventDispatcher(int eventType, Document document, User requestor, List<Communication> communications) {
            this.document = document;
            this.eventType = eventType;
            this.requestor = requestor;
            this.communications.addAll(communications);
        }

        @Override
        public void performMainProcessing() {
            log.info("Send notifications for connected orders, event type = " + eventType);
            if ( document != null) {
                WebServiceConfiguration webServiceConfig = document.getCommunicationNotificationWebService();
                if (webServiceConfig != null) {
                    String url = webServiceConfig.getUrl();
                    String username = webServiceConfig.getUsername();
                    String password = webServiceConfig.getPassword();

                    WsClientCustomer client = null;
                    boolean useSSL = false; // No SSL supported yet
                    log.info("Target customer URL is: " + url);
                    client = new WsClientCustomer(url, username, password);
                    try {
                        for (Communication order: communications) {
                            UserInfoType authorInfo = null;
                            UserInfoType assignedInfo = null;
                            UserInfoType deletedBy = null;
                            UserInfoType releasedForApprovalBy = null;
                            UserInfoListType releasedForApprovalTo = null;

                            switch (eventType) {
                            case NOTIFICATION_EVENT_DELETE:
                                    deletedBy = makeUserInfo(requestor, new UserInfoType());
                                    break;
                                case NOTIFICATION_EVENT_RELEASE_FOR_APPROVAL:
                                    releasedForApprovalBy = makeUserInfo(requestor, new UserInfoType());
                                    List<User> approvalFrom = new ArrayList<>();
                                    ConfigurableWorkflowAction action = order.getWorkflowAction();
                                    ConfigurableWorkflowStep wfStep = action.getConfigurableWorkflowStep();
                                    while ( wfStep != null) {
                                        Set<User> approvalUsers = wfStep.getApprovalUsers();
                                        if (approvalUsers != null && !approvalUsers.isEmpty()) {
                                            approvalFrom.addAll(approvalUsers);
                                        }
                                        wfStep = wfStep.getNextStep();
                                    }
                                    if (!approvalFrom.isEmpty()) {
                                        releasedForApprovalTo = new UserInfoListType();
                                        for (User approver : approvalFrom) {
                                            releasedForApprovalTo.getUserInfo().add(makeUserInfo(approver, new UserInfoType()));
                                        }
                                    }
                                    break;
                                default:
                                    ;
                            }

                            // Prepare author
                            User author = User.findById(order.getCreatedBy());
                            if (author != null) {
                                authorInfo = makeUserInfo(author, new UserInfoType());
                            }

                            // Prepare assignedInfo
                            User assigned = User.findById(order.getAssigneeId());
                            if (assigned != null) {
                                assignedInfo = makeUserInfo(author, new UserInfoType());
                            }

                            boolean returnCode = client.orderNotificationRequest(eventType, deletedBy, releasedForApprovalBy, releasedForApprovalTo, order.getGuid(), order.getCustomerIdentifier(), document.getGuid(), order.isTestOrder(), authorInfo, order.getCommunicationOrderStatusTypeDisplay(), order.getChannelTypeDisplay(), assignedInfo);
                            if (returnCode == false) {
                                log.error("Failed sending notification for event type = " + eventType + " for order with guid = " + order.getGuid());
                            } else {
                                log.info("Successfully sent notification for event type = " + eventType + " for order with guid = " + order.getGuid());
                            }
                        }
                    } catch(Exception e) {
                        log.error("Failed sending communication notifications. Error: ", e);
                    }

                } else {
                    log.error("Missing customer web service configuration");
                }

            log.info("Finished sending communication notifications ");
        }
    }

    private UserInfoType makeUserInfo(User user, UserInfoType userInfo) {
        if (user != null) {
            userInfo.setGuid(user.getGuid());
            userInfo.setEmail(user.getEmail());
            userInfo.setFirstName(user.getFirstName());
            userInfo.setLastName(user.getLastName());
            userInfo.setUserName(user.getUsername());
        }
        return userInfo;
    }
}
