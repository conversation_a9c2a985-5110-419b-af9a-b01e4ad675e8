package com.prinova.migrate.steps.messagepoint;

        import com.prinova.migrate.MigrationStep;
        import com.prinova.migrate.models.ColumnDefinition;

        import java.sql.Connection;

public class Step2544_v021200_LicenseMPHCS extends MigrationStep {

    @Override
    public void migrate(Connection conn) throws Exception {

        if (!columnExists(conn, "licence", "sefas_hcs_authorized")) {
            addColumn(conn, "licence", ColumnDefinition.createBoolean("sefas_hcs_authorized", true));
            executeUpdate(conn, "UPDATE licence SET sefas_hcs_authorized = true");
        }

        if (!columnExists(conn, "licence_history", "sefas_hcs_authorized")) {
            addColumn(conn, "licence_history", ColumnDefinition.createBoolean("sefas_hcs_authorized", true));
            executeUpdate(conn, "UPDATE licence_history SET sefas_hcs_authorized = false");
        }

    }

}

