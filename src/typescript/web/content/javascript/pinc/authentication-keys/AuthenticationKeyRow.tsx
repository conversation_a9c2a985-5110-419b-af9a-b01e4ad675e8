
interface AuthenticationKey {
    id: string;
    name: string;
    type: string;
    data: string;
    metadata: {
        type: string;
        comment: string;
        length: number;
        fingerprints: {
            md5: string;
            sha1: string;
            sha256: string;
        }
    }
}

interface AuthenticationKeyRowProps {
    rowData: AuthenticationKey;
}

const AuthenticationKeyRow = (props: AuthenticationKeyRowProps) => {

    const { rowData } = props;

    const { handleDrillDownChange, isRowExpanded } = React.useContext(ListPageContext);

    const showDrillDown = isRowExpanded(rowData.id);

    return (
        <React.Fragment>
            <tr>
                <td/>{/* not selectable */}
                <AsyncJsonTableRowDrillDownCell
                    rowId={rowData.id}
                    show={showDrillDown}
                    toggleHandler={handleDrillDownChange}
                />
                <td><div>{rowData.name}</div></td>
                <td><div>{rowData.metadata.fingerprints.md5}</div></td>
                <td><div>{rowData.type}</div></td>
            </tr>
            {showDrillDown &&
                <tr key={`${rowData.id}_drill-down`}>
                    <td colSpan={5}>
                        <div className="container">
                            <div className="row">
                                <div className="col font-weight-bold">
                                    {client_messages.pinc.id}
                                </div>
                            </div>
                            <div className="row">
                                <div className="col text-break">
                                    {rowData.id}
                                </div>
                            </div>

                            <div className="row mt-3">
                                <div className="col font-weight-bold">
                                    {client_messages.pinc.data}
                                </div>
                            </div>
                            <div className="row">
                                <div className="col text-break">
                                    {rowData.data}
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            }
        </React.Fragment>
    );
};