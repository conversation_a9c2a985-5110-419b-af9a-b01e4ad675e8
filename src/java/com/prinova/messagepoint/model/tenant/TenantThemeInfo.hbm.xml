<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="com.prinova.messagepoint.model.tenant.TenantThemeInfo" table="tenant_theme_info"  >
		
		<id name="id" column="tenant_id">
       		<generator class="foreign">
           		<param name="property">owner</param>
       		</generator>
   		</id> 
		
		<one-to-one name="owner" class="com.prinova.messagepoint.model.tenant.Tenant" constrained="false"/>
		
		<property name="guid"         		  column="guid"                  not-null="true" unique="true" />                                  
		<property name="name" length="96"     column="name"                   />		                                  
		<property name="logoLocation"         column="logo_location"          />
		<property name="headerText"           column="header_text"  type="org.hibernate.type.TextType" />
		<property name="providerLogoLocation" column="provider_logo_location" />
		<property name="providerText"         column="provider_text"  type="org.hibernate.type.TextType" />
		<property name="headerThemeColor"     column="header_theme_color"  type="org.hibernate.type.TextType" not-null="true" />
		<property name="headerThemeTypeId" 	  column="header_theme_type_id" not-null="true" />
		
		<many-to-one name="systemTheme"       class="com.prinova.messagepoint.model.SystemTheme" column="system_theme" lazy="false"/>
		<many-to-one name="backgroundTheme"   class="com.prinova.messagepoint.model.BackgroundTheme" column="background_theme" lazy="false"/>
		
	</class>
</hibernate-mapping>