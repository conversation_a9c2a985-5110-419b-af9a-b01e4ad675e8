package com.prinova.messagepoint.controller.tpadmin;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.TouchpointAdminEvents;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.email.EmailTemplateUtils;
import com.prinova.messagepoint.email.TemplateZone;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.DocumentSection;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.email.TemplateModifier;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.tpadmin.CleanupEMailTemplateService;
import com.prinova.messagepoint.platform.services.tpadmin.UpdateSectionService;
import com.prinova.messagepoint.platform.services.tpadmin.UpdateTemplateModifiersService;
import com.prinova.messagepoint.platform.services.tpadmin.UpdateZoneService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.FileUtil;
import org.apache.commons.lang.SystemUtils;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.nio.file.Files;
import java.util.*;

public class EmailTemplatePreviewController extends MessagepointController {
	
	public static final String REQ_PARAM_DOCUMENTID = "docid";
	
	private static final String PARAM_SUBMIT_TYPE	= "submittype";
	private static final String FORM_SUBMIT			= "submit";
	private static final String FORM_CANCEL			= "cancel";
	
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
	    Map<String, Object> referenceData = new HashMap<>();
	    	
	    long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1L);
	    Document document = Document.findById(documentId);
	    List<MessagepointLocale> langLocales = document.getRootDocument().getTouchpointLanguagesAsLocales();
	    
	    referenceData.put("languages", langLocales);
	    
	    Map<String,String> templatePaths = new HashMap<>();
	    for (MessagepointLocale locale: langLocales) {
	    	String currentTemplatePath = EmailTemplateUtils.getWebrootTemplatesBrowserPath(documentId, locale.getLanguageCode()) + 
	    								 EmailTemplateUtils.PATH_SEPARATOR + EmailTemplateUtils.WEB_TEMPLATE_PARSED_FILE_NAME;
	    	templatePaths.put(locale.getLanguageCode(), currentTemplatePath);
	    }
	    referenceData.put("templatePaths", templatePaths);
	    
	    referenceData.put("defaultLanguage", document.getRootDocument().getDefaultTouchpointLanguageLocale() );
	    
	    referenceData.put("isFirstTemplateUpload", document.getZones().isEmpty());
	    	
	    return referenceData;
	 }

	@Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(String.class, new StringXSSEditor());
    }
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1L);
		
		EmailTemplatePreviewWrapper command = new EmailTemplatePreviewWrapper(documentId);
		
		return command;
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request,
			HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

		AnalyticsEvent<TouchpointAdminEvents> analyticsEvent = AnalyticsUtil.requestFor(TouchpointAdminEvents.EmailTemplatePreview);

		try {
			String submitType 		= ServletRequestUtils.getStringParameter(request, PARAM_SUBMIT_TYPE, "");
			long documentId 		= ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1L);
			String requester		= ServletRequestUtils.getStringParameter(request, TouchpointLayoutManagerController.REQUEST_PARM_REQUESTER, null);

			Document document		= Document.findById(documentId);
			Document rootDocument 	= document.getRootDocument();

			EmailTemplatePreviewWrapper command = (EmailTemplatePreviewWrapper)commandObj;

			if (FORM_SUBMIT.equals(submitType)) {

				analyticsEvent.setAction(Actions.Update);

				String defaultLanguageCode = rootDocument.getDefaultTouchpointLanguageCode();

				List<TemplateZone> relativeTemplateZones = EmailTemplateUtils.convertTemplateZonesToRelativePositioning(
																command.getTemplateZoneMap().get(defaultLanguageCode),
																command.getTemplateWidth(),
																command.getTemplateHeight() );

				Set<Zone> touchpointZones = EmailTemplateUtils.populateTouchpointZones(relativeTemplateZones, documentId, command.getUpdateZoneAttributes());

				DocumentSection section = EmailTemplateUtils.setTemplateSectionDimensions(documentId, command.getTemplateWidth(), command.getTemplateHeight());

				// Service starts here
				//TODO: the service for moving templates files from webroot to fileroot should be implemented
				// which prefer to combine two independent services together, try to figure out a solution later
				// Additional: Third service added to update section (try to combine into single service sequence)

				// Service: Update Section
				ServiceExecutionContext sectionContext = UpdateSectionService.createContext(section);
				Service updateSectionService = MessagepointServiceFactory.getInstance().lookupService(UpdateSectionService.SERVICE_NAME, UpdateSectionService.class);
				updateSectionService.execute(sectionContext);
				ServiceResponse sectionServiceResponse = sectionContext.getResponse();

				if(!sectionServiceResponse.isSuccessful())
					ServiceResponseConverter.convertToSpringErrors(sectionServiceResponse, errors);

				// Service: Update Zones
				ServiceExecutionContext context = UpdateZoneService.createContextForDigitalZoneUpdate(touchpointZones.toArray(new Zone[]{}));
				Service updateZoneService = MessagepointServiceFactory.getInstance().lookupService(UpdateZoneService.SERVICE_NAME, UpdateZoneService.class);
				updateZoneService.execute(context);
				ServiceResponse serviceResponse = context.getResponse();

				// Service: Update Modifiers
				List<TemplateModifier> newModifiers = new ArrayList<>();
				for ( TemplateModifier currentModifier: command.getTemplateModifierMap().get(defaultLanguageCode) )
					if ( currentModifier.getId() <= 0 || !currentModifier.getIsActive() )
						newModifiers.add(currentModifier);
				
				ServiceExecutionContext modifierContext = UpdateTemplateModifiersService.createContext(document, newModifiers, command.getTemplateModifierMap().get(defaultLanguageCode), command.getRemovedModifiers());
				Service updateTemplateModifiersService = MessagepointServiceFactory.getInstance().lookupService(UpdateTemplateModifiersService.SERVICE_NAME, UpdateTemplateModifiersService.class);
				updateTemplateModifiersService.execute(modifierContext);
				ServiceResponse modifierServiceResponse = modifierContext.getResponse();

				if(!modifierServiceResponse.isSuccessful())
					ServiceResponseConverter.convertToSpringErrors(modifierServiceResponse, errors);

				if(modifierServiceResponse.isSuccessful()) {
					// Future Service: Clean up and move Files
					String srcPath 					= EmailTemplateUtils.getWebrootTemplatesBasePath(documentId);
					String webrootTemplateSrcPath	= EmailTemplateUtils.getWebrootTemplatesFullPath(documentId);
					String templateFilerootPath 	= EmailTemplateUtils.getFilerootTemplatesBasePath(documentId);
					String previewFilerootPath		= EmailTemplateUtils.getFilerootPreviewBasePath(documentId);

					File srcDir = new File(srcPath);
					if(srcDir.exists()) {
						File srcTemplateDir = new File(webrootTemplateSrcPath);
						// Delete the images folder
						FileUtil.deleteAllByRegex("images", new File(previewFilerootPath));
						Files.walk(srcTemplateDir.toPath())
								.filter(path -> path.getFileName().toString().equals("images"))
								.forEach(path -> FileUtil.deleteDir(path.toFile()));

						// Copy webroot template files/dirs to fileroot
						EmailTemplateUtils.deleteMsgptZoneMarkerImg(srcDir);
						FileUtil.deleteDir(new File(templateFilerootPath + EmailTemplateUtils.PATH_SEPARATOR + EmailTemplateUtils.TEMPLATES_DIR));
						FileUtil.copyDirContents(srcDir, new File(templateFilerootPath));

						// Copy webroot template image files/dirs to fileroot
						FileUtil.copyDirContents(srcTemplateDir, new File(previewFilerootPath));
						FileUtil.deleteAllByRegex("template\\.html", new File(previewFilerootPath));
						FileUtil.deleteAllByRegex("template_parsed\\.html", new File(previewFilerootPath));

						FileUtil.deleteDir(srcDir);
					}

					// Rename the template_prevalidation.zip to template.zip
					String emailTemplatePath = EmailTemplateUtils.getFilerootTemplatesBasePath(documentId) + EmailTemplateUtils.PATH_SEPARATOR;
					File zipPackage = new File(emailTemplatePath + EmailTemplateUtils.PACKAGE_TEMPLATE_TEMP_FILE_NAME);
					if(zipPackage.exists()){
						File templateFile = new File(emailTemplatePath + EmailTemplateUtils.PACKAGE_TEMPLATE_FILE_NAME);
						if(templateFile.exists()) {
							templateFile.delete();
						}
						zipPackage.renameTo(new File(emailTemplatePath + EmailTemplateUtils.PACKAGE_TEMPLATE_FILE_NAME));
					}
				} else {
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return super.showForm(request, response, errors);
				}
			} else if (FORM_CANCEL.equals(submitType)) {
				analyticsEvent.setAction(Actions.Cancel);

				// Cleanup ?service? here
				String emailTemplatePath = EmailTemplateUtils.getFilerootTemplatesBasePath(documentId) + SystemUtils.FILE_SEPARATOR;
				ServiceExecutionContext context = CleanupEMailTemplateService.createContext(documentId, emailTemplatePath + EmailTemplateUtils.PACKAGE_TEMPLATE_TEMP_FILE_NAME);
				Service cleanupEMailTemplateService = MessagepointServiceFactory.getInstance().lookupService(CleanupEMailTemplateService.SERVICE_NAME, CleanupEMailTemplateService.class);
				cleanupEMailTemplateService.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (!serviceResponse.isSuccessful()) {
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return super.showForm(request, response, errors);
				}
			}

			Map<String, Object> params = new HashMap<>();

			if ( requester != null && requester.equals(TouchpointLayoutManagerController.REQUESTER_VALUE_LAYOUT_MANAGER) ) {
				long returnDocumentId = documentId;
				if ( document.isChannelAlternate() )
					returnDocumentId = document.getChannelParent().getId();
				params.put(TouchpointLayoutManagerController.REQUEST_PARM_DOCUMENT_ID, returnDocumentId);
				return new ModelAndView(new RedirectView("touchpoint_layout_manager.form"), params);
			} else {
				params.put(REQ_PARAM_DOCUMENTID, documentId);
				return new ModelAndView(new RedirectView(getSuccessView()), params);
			}
		} finally {
			analyticsEvent.send();
		}
	}

}