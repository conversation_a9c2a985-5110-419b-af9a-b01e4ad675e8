package com.prinova.messagepoint.query.handler;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import com.prinova.messagepoint.model.project.Project;
import com.prinova.messagepoint.query.DefaultPage;
import com.prinova.messagepoint.query.IPage;
import com.prinova.messagepoint.util.ApplicationUtil;

public class ProjectTypeHandler implements PostQueryHandler {

	private boolean isOrderAsc;
	
	public boolean isOrderAsc() {
		return isOrderAsc;
	}

	public void setOrderAsc(boolean isOrderAsc) {
		this.isOrderAsc = isOrderAsc;
	}
	
	@SuppressWarnings("unchecked")
	public IPage handle(IPage page) {
		List<Project> items = (List<Project>)page.getList();
		
		Collections.sort(items, new Comparator<>() {
            public int compare(Project o1, Project o2) {
                String at1 = getProjectType(o1);
                String at2 = getProjectType(o2);
                if (isOrderAsc()) {
                    return at1.compareTo(at2);
                } else {
                    return at2.compareTo(at1);
                }
            }
        });
		
        ((DefaultPage)page).setList(items);
		return page;
	}
	
	public String getProjectType(Project project){
		String type = ApplicationUtil.getMessage("page.label.general");
		if(project.getMetadataForm() != null){
			type = project.getMetadataForm().getFormDefinition().getName();
		}
		return type;
	}
}
