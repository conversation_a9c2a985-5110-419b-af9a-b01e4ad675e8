package com.prinova.messagepoint.platform.services.message;

import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.admin.DataSourceAssociation;
import com.prinova.messagepoint.model.admin.ParameterGroup;
import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.testing.DataFile;
import com.prinova.messagepoint.model.util.SyncTouchpointUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.common.SyncObjectType;

import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleResourceAwareServiceExeContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;

public class UpdateMessageHashService extends AbstractService {

	public static final String SERVICE_NAME = "message.UpdateMessageHashService";
	private static final Log log = LogUtil.getLog(UpdateMessageHashService.class);

	@Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = false)
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			UpdateMessageHashServiceRequest request = (UpdateMessageHashServiceRequest) context.getRequest();

			Long modelId = request.getModelId();
			Long instanceId = request.getInstanceId();
			Integer objectType = request.getObjectType();
            boolean isAlgorithmChanged = request.getIsAlgorithmChanged();;

			boolean updateContentAssociationHash = request.isUpdateContentAssociationHash();

			Node node = Node.findById(instanceId);

			if(node != null) {
			    String schema = node.getSchemaName();
			    CloneHelper.execInSchema(schema, ()->{

			    	switch(objectType) {
                        case SyncObjectType.ID_MESSAGE:
                        case SyncObjectType.ID_LOCAL_SMART_TEXT:
                        case SyncObjectType.ID_LOCAL_IMAGE:
                        case SyncObjectType.ID_SMART_TEXT:
                        case SyncObjectType.ID_CONTENT_LIBRARY:
                            SyncTouchpointUtil.updateContentObjectHash(modelId, updateContentAssociationHash, isAlgorithmChanged);
                            break;
                        case SyncObjectType.ID_TARGET_GROUP:
                            SyncTouchpointUtil.updateObjectHash(TargetGroup.findById(modelId), isAlgorithmChanged);
                            break;
                        case SyncObjectType.ID_TARGET_RULE:
                            SyncTouchpointUtil.updateObjectHash(ConditionElement.findById(modelId), isAlgorithmChanged);
                            break;
                        case SyncObjectType.ID_PARAMETER_GROUP:
                            SyncTouchpointUtil.updateObjectHash(ParameterGroup.findById(modelId), isAlgorithmChanged);
                            break;
                        case SyncObjectType.ID_VARIABLE:
                            SyncTouchpointUtil.updateObjectHash(DataElementVariable.findById(modelId), isAlgorithmChanged);
                            break;
                        case SyncObjectType.ID_DATASOURCE:
                            SyncTouchpointUtil.updateObjectHash(DataSource.findById(modelId), isAlgorithmChanged);
                            break;
                        case SyncObjectType.ID_DATACOLLECTION:
                            SyncTouchpointUtil.updateObjectHash(DataSourceAssociation.findById(modelId), isAlgorithmChanged);
                            break;
                        case SyncObjectType.ID_LOOKUPTABLE:
//                            SyncTouchpointUtil.updateLookupTableHash(modelId);
                            break;
                        case SyncObjectType.ID_DATA_FILE:
                            SyncTouchpointUtil.updateObjectHash(DataFile.findById(modelId), isAlgorithmChanged);
                            break;
                        case SyncObjectType.ID_VARIANT:
                            SyncTouchpointUtil.updateTouchpointVariantHash(modelId, updateContentAssociationHash, isAlgorithmChanged);
                            break;
			    	}
			    });
			}
		} catch (Exception e) {
			log.error(" unexpected exception when invoking UpdateMessageHashService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub

	}

	public static ServiceExecutionContext createContext(Long modelId, Long instanceId, Integer objectType, boolean isAlgorithmChanged) {
		return createContext(modelId, instanceId, objectType, isAlgorithmChanged,true);
	}

	public static ServiceExecutionContext createContext(Long modelId, Long instanceId, Integer objectType, boolean isAlgorithmChanged, boolean updateContentAssociationHash) {
		SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
		context.setServiceName(SERVICE_NAME);

		UpdateMessageHashServiceRequest request = new UpdateMessageHashServiceRequest();
		context.setRequest(request);

		request.setModelId(modelId);
		request.setInstanceId(instanceId);
		request.setObjectType(objectType);
        request.setIsAlgorithmChanged(isAlgorithmChanged);
		request.setUpdateContentAssociationHash(updateContentAssociationHash);

        SimpleServiceResponse serviceResp = new SimpleServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

		return context;
	}

}
