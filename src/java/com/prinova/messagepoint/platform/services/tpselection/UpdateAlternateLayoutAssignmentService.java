package com.prinova.messagepoint.platform.services.tpselection;

import java.util.List;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.controller.tpadmin.TouchpointSelectionsListVO;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;

public class UpdateAlternateLayoutAssignmentService extends AbstractService {

	public static final String SERVICE_NAME = "tpselection.UpdateAlternateLayoutAssignmentService";
	
	private static final Log log = LogUtil.getLog(UpdateAlternateLayoutAssignmentService.class);

	public void execute(ServiceExecutionContext context) {
		
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			UpdateAlternateLayoutAssignmentServiceRequest request = (UpdateAlternateLayoutAssignmentServiceRequest) context.getRequest();
			long documentId = request.getDocumentId();
			boolean variedByTouchpointVariant = request.isVariedByTouchpointVariant();

			List<TouchpointSelectionsListVO> tpSelectionVOlist = request.getTpSelectionVOlist();
			for (int i = 0; i < tpSelectionVOlist.size(); i++) {
				TouchpointSelection tpSelection = TouchpointSelection.findById( tpSelectionVOlist.get(i).getTouchpointSelection().getId() );
				tpSelection.setAlternateLayout( tpSelectionVOlist.get(i).getTouchpointSelection().getAlternateLayout() );
				tpSelection.save();
			}

			Document document = Document.findById(documentId);
			if(document != null) {
				document.setVariedByTouchpointVariant(variedByTouchpointVariant);
				document.save();
			}

		} catch (Exception e) {
			log.error(" unexpected exception when invoking UpdateAlternateLayoutAssignmentService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION", ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e, context.getLocale());
			throw new RuntimeException(e);
		}
	}

	// if master and if there are Empty content associations, reject
	public void validate(ServiceExecutionContext context) {

	}

	public static ServiceExecutionContext createContext(List<TouchpointSelectionsListVO> tpSelectionVOlist,
														long documentId,
														boolean variedByTouchpointVariant) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		UpdateAlternateLayoutAssignmentServiceRequest request = new UpdateAlternateLayoutAssignmentServiceRequest();
		context.setRequest(request);

		request.setTpSelectionVOlist(tpSelectionVOlist);
		request.setDocumentId(documentId);
		request.setVariedByTouchpointVariant(variedByTouchpointVariant);

		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}
}