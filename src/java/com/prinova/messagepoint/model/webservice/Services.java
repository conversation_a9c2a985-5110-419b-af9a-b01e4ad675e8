package com.prinova.messagepoint.model.webservice;

import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.admin.TranslationProvider;
import com.prinova.messagepoint.model.file.DatabaseFile;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowStep;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import com.prinova.messagepoint.util.*;
import org.springframework.orm.hibernate5.SessionHolder;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Services extends IdentifiableMessagePointModel {

	private static final long serialVersionUID = -4275919247568699082L;

	public static int SERVICE_TYPE_NOT_SET 			=  0; // 0000 0000
	public static int SERVICE_TYPE_APPLICATION 		=  1; // 0000 0001
	public static int SERVICE_TYPE_API 				=  2; // 0000 0010
	public static int SERVICE_TYPE_API_APP 			=  3; // 0000 0011
	public static int SERVICE_TYPE_TRANSLATION 		=  4; // 0000 0100

	public static final String SERVICE_TYPE_NOT_SET_TEXT 			= "page.label.services.not.set.type";
	public static final String SERVICE_TYPE_APPLICATION_TEXT 		= "page.label.services.application.type";
	public static final String SERVICE_TYPE_API_TEXT 				= "page.label.services.api.type";
	public static final String SERVICE_TYPE_API_APP_TEXT 			= "page.label.services.api.app.type";
	public static final String SERVICE_TYPE_TRANSLATION_TEXT 		= "page.label.services.translation.type";

	public static int SERVICE_STATUS_NOT_ACTIVATED	=  0; // 0000 0000
	public static int SERVICE_STATUS_ACTIVATED 		=  1; // 0000 0001
	public static int SERVICE_STATUS_ENABLED 		=  2; // 0000 0010
	public static int SERVICE_STATUS_ACTIVE 		=  3; // 0000 0011

	public static final String SERVICE_STATUS_NOT_ACTIVATED_TEXT 	= "page.label.services.not.activated.status";
	public static final String SERVICE_STATUS_ACTIVATED_TEXT 		= "page.label.services.activated.status";
	public static final String SERVICE_STATUS_ENABLED_TEXT 			= "page.label.services.enabled.status";
	public static final String SERVICE_STATUS_ACTIVE_TEXT 			= "page.label.services.active.status";

	public static int SERVICE_DEFAULT_TOKEN_EXPIRATION 				=  36000;

	// from inherited class IdentifiableMessagePointModel
	// guid also as API ID
	// name

	private int serviceType = SERVICE_TYPE_NOT_SET;
	private int serviceSubType;
	private int serviceStatus = SERVICE_STATUS_NOT_ACTIVATED;

	private String apiIdentifier;

	private String clientID;
	private String clientSecret;
	private String allowedOrigins;
	private String allowedRedirectURLs;
	private String allowedLogoutRedirectURLs;

	private int tokenExpiration = SERVICE_DEFAULT_TOKEN_EXPIRATION;
	private boolean rotationEnabled;
	private int reuseInterval;
	private boolean absoluteExpirationEnabled;
	private int absoluteExpiration;
	private boolean inactivityExpirationEnabled;
	private int inactivityExpiration;

	private DatabaseFile databaseFile;
	private String allowedDomains;

	private Date lastUsed;

	private String scopes = "";
	private TranslationProvider translationProvider;

	@Override
	public Services clone()
	{
		return new Services(this);
	}

	public Services()
	{
	}

	protected Services(Services cloneFrom)
	{
		super(cloneFrom);
	}

	public int getServiceType() {
		return serviceType;
	}

	public void setServiceType(int serviceType) {
		this.serviceType = serviceType;
	}

	public String getServiceTypeText() {
		if (this.serviceType == SERVICE_TYPE_APPLICATION)
			return ApplicationUtil.getMessage(SERVICE_TYPE_APPLICATION_TEXT);
		if (this.serviceType == SERVICE_TYPE_API)
			return ApplicationUtil.getMessage(SERVICE_TYPE_API_TEXT);
		if (this.serviceType == SERVICE_TYPE_API_APP)
			return ApplicationUtil.getMessage(SERVICE_TYPE_API_APP_TEXT);
		if (this.serviceType == SERVICE_TYPE_TRANSLATION)
			return ApplicationUtil.getMessage(SERVICE_TYPE_TRANSLATION_TEXT);
		return ApplicationUtil.getMessage(SERVICE_TYPE_NOT_SET_TEXT);
	}

	public boolean isServiceTypeApplication() {
		return this.serviceType == SERVICE_TYPE_APPLICATION;
	}

	public boolean isServiceTypeAPI() {
		return this.serviceType == SERVICE_TYPE_API;
	}

	public boolean isServiceTypeAPIApp() {
		return this.serviceType == SERVICE_TYPE_API_APP;
	}

	public boolean isServiceTypeTranslation() {
		return this.serviceType == SERVICE_TYPE_TRANSLATION;
	}

	public int getServiceSubType() {
		return serviceSubType;
	}

	public void setServiceSubType(int serviceSubType) {
		this.serviceSubType = serviceSubType;
	}

	public int getServiceStatus() {
		return serviceStatus;
	}

	public void setServiceStatus(int serviceStatus) {
		this.serviceStatus = serviceStatus;
	}

	public String getServiceStatusText() {
		if (this.serviceStatus == SERVICE_STATUS_NOT_ACTIVATED)
			return ApplicationUtil.getMessage(SERVICE_STATUS_NOT_ACTIVATED_TEXT);
		if (this.serviceStatus == SERVICE_STATUS_ACTIVATED)
			return ApplicationUtil.getMessage(SERVICE_STATUS_ACTIVATED_TEXT);
		if (this.serviceStatus == SERVICE_STATUS_ENABLED)
			return ApplicationUtil.getMessage(SERVICE_STATUS_ENABLED_TEXT);
		if (this.serviceStatus == SERVICE_STATUS_ACTIVE)
			return ApplicationUtil.getMessage(SERVICE_STATUS_ACTIVE_TEXT);
		return ApplicationUtil.getMessage(SERVICE_STATUS_NOT_ACTIVATED_TEXT);
	}

	public boolean isServiceStatusNotActivated() {
		return this.serviceStatus == SERVICE_STATUS_NOT_ACTIVATED;
	}

	public boolean isServiceStatusActivated() {
		return (this.serviceStatus & SERVICE_STATUS_ACTIVATED) == SERVICE_STATUS_ACTIVATED;
	}

	public boolean isServiceStatusEnabled() {
		return (this.serviceStatus & SERVICE_STATUS_ENABLED) == SERVICE_STATUS_ENABLED;
	}

	public boolean isServiceStatusActive() {
		return (this.serviceStatus & SERVICE_STATUS_ACTIVE) == SERVICE_STATUS_ACTIVE;
	}

	public String getApiIdentifier() {
		return apiIdentifier;
	}

	public void setApiIdentifier(String apiIdentifier) {
		this.apiIdentifier = apiIdentifier;
	}

	public String getClientID() {
		return clientID;
	}

	public void setClientID(String clientID) {
		this.clientID = clientID;
	}

	public String getClientSecret() {
		return clientSecret;
	}

	public void setClientSecret(String clientSecret) {
		this.clientSecret = clientSecret;
	}

	public String getAllowedOrigins() {
		return allowedOrigins;
	}

	public void setAllowedOrigins(String allowedOrigins) {
		this.allowedOrigins = allowedOrigins;
	}

	public String getAllowedRedirectURLs() {
		return allowedRedirectURLs;
	}

	public void setAllowedRedirectURLs(String allowedRedirectURLs) {
		this.allowedRedirectURLs = allowedRedirectURLs;
	}

	public String getAllowedLogoutRedirectURLs() {
		return allowedLogoutRedirectURLs;
	}

	public void setAllowedLogoutRedirectURLs(String allowedLogoutRedirectURLs) {
		this.allowedLogoutRedirectURLs = allowedLogoutRedirectURLs;
	}

	public int getTokenExpiration() {
		return tokenExpiration;
	}

	public void setTokenExpiration(int tokenExpiration) {
		this.tokenExpiration = tokenExpiration;
	}

	public boolean isRotationEnabled() {
		return rotationEnabled;
	}

	public void setRotationEnabled(boolean rotationEnabled) {
		this.rotationEnabled = rotationEnabled;
	}

	public int getReuseInterval() {
		return reuseInterval;
	}

	public void setReuseInterval(int reuseInterval) {
		this.reuseInterval = reuseInterval;
	}

	public boolean isAbsoluteExpirationEnabled() {
		return absoluteExpirationEnabled;
	}

	public void setAbsoluteExpirationEnabled(boolean absoluteExpirationEnabled) {
		this.absoluteExpirationEnabled = absoluteExpirationEnabled;
	}

	public int getAbsoluteExpiration() {
		return absoluteExpiration;
	}

	public void setAbsoluteExpiration(int absoluteExpiration) {
		this.absoluteExpiration = absoluteExpiration;
	}

	public boolean isInactivityExpirationEnabled() {
		return inactivityExpirationEnabled;
	}

	public void setInactivityExpirationEnabled(boolean inactivityExpirationEnabled) {
		this.inactivityExpirationEnabled = inactivityExpirationEnabled;
	}

	public int getInactivityExpiration() {
		return inactivityExpiration;
	}

	public void setInactivityExpiration(int inactivityExpiration) {
		this.inactivityExpiration = inactivityExpiration;
	}

	public DatabaseFile getDatabaseFile() {
		return databaseFile;
	}

	public void setDatabaseFile(DatabaseFile databaseFile) {
		this.databaseFile = databaseFile;
	}

	public String getAllowedDomains() {
		return allowedDomains;
	}

	public void setAllowedDomains(String allowedDomains) {
		this.allowedDomains = allowedDomains;
	}

	public Date getLastUsed() {
		return lastUsed;
	}

	public void setLastUsed(Date lastUsed) {
		this.lastUsed = lastUsed;
	}

	public String getLastUsedString(){
		return this.getLastUsed() == null ? ApplicationUtil.getMessage("page.label.never") : DateUtil.formatDate(this.getLastUsed());
	}

	public String getScopes() {
		return this.scopes;
	}

	public void setScopes(String scopes) {
		this.scopes = scopes;
	}

	public TranslationProvider getTranslationProvider() {
		return translationProvider;
	}

	public void setTranslationProvider(TranslationProvider translationProvider) {
		this.translationProvider = translationProvider;
	}

	/**
	 * Returns the code of the translation provider or null if the translation provider is null
	 * @return String code or null
	 */
	public String getTranslationProviderCode() {
		if(translationProvider != null){
			return translationProvider.getCode();
		}
		return null;
	}
	/*
	public String getHashedPassword() {

		if (updatedPassword != null) {
			return updatedPassword;
		}

		return StringUtils.isEmpty(password) ? password : DigestUtils.md5Hex("messagepoint-" + password);
	}

	public void setHashedPassword(String newPassword) {

		if (!newPassword.equals(getHashedPassword())) {
			updatedPassword = newPassword;
		}
	}

	public String getUpdatedPassword() {
		return updatedPassword;
	}
	*/

	public static List<Services> findServicesByFilter(String servicesFilter) {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		if (servicesFilter != null && !servicesFilter.isEmpty()) {
			if (servicesFilter.equals(ApplicationUtil.getMessage("page.label.services.api")))
				critList.add(MessagepointRestrictions.eq("serviceType", SERVICE_TYPE_API));
			else if (servicesFilter.equals(ApplicationUtil.getMessage("page.label.services.application")))
				critList.add(MessagepointRestrictions.eq("serviceType", SERVICE_TYPE_APPLICATION));
			else if (servicesFilter.equals(ApplicationUtil.getMessage("page.label.services.translation")))
				critList.add(MessagepointRestrictions.eq("serviceType", SERVICE_TYPE_TRANSLATION));
		}
		List<Services> servicesList =  HibernateUtil.getManager().getObjectsAdvanced(Services.class, critList);
		return servicesList;
	}

	public static List<Services> findAllIncludingParents(String servicesFilter){
		ArrayList<Services> services = new ArrayList<>();

		try {
			List<Branch> branches = new ArrayList<>();

			Branch current = Node.getCurrentBranch();
			branches.add(current);
			while (current != null && current.getParentBranch() != null) {
				current = BranchUtil.getParentBranch(current);
				branches.add(current);
			}

			for (Branch branch : branches) {
				SessionHolder mainSessionHolder = null;

				try {
					mainSessionHolder = HibernateUtil.getManager().openTemporarySession(branch.getDcsSchemaName());
					List<Services> servicesFromBranch = findServicesByFilter(servicesFilter);
					services.addAll(servicesFromBranch);
				} catch(Exception e) {
					LogUtil.getLog(Services.class).error(e);
				} finally {
					if (mainSessionHolder != null) {
						HibernateUtil.getManager().restoreSession(mainSessionHolder);
					}
				}
			}
		} catch (Exception e) {
			LogUtil.getLog(Services.class).error(e);
		}

		return services;
	}

	public static Services findByGuid(String guid) {
		Services service;

		if (guid == null || guid.isEmpty()) {
			return null;
		}

		try {
			Branch branch = Node.getCurrentBranch();

			service = findByGuid(guid, branch);

			if (service != null) {
				return service;
			} else {
				while (branch != null && branch.getParentBranch() != null) {
					branch = BranchUtil.getParentBranch(branch);
					service = findByGuid(guid, branch);

					if (service != null) {
						return service;
					}
				}
			}
		} catch (Exception e) {
			LogUtil.getLog(Services.class).error(e);
		}

		return null;
	}

	private static Services findByGuid(String guid, Branch branch) {
		SessionHolder mainSessionHolder = null;

		try {
			mainSessionHolder = HibernateUtil.getManager().openTemporarySession(branch.getDcsSchemaName());
			return HibernateUtil.getManager().getObjectByGuid(Services.class, guid);
		} finally {
			if (mainSessionHolder != null)
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}
	}

	public static Services findById(long id) {
		return HibernateUtil.getManager().getObject(Services.class, id);
	}

	public static Services findById(long id, Node node) {
		if(node == null){
			return findById(id);
		}

		SessionHolder mainSessionHolder = null;
		try{
			mainSessionHolder = HibernateUtil.getManager().openTemporarySession(node.getSchemaName());
			return findById(id);
		}
		finally {
			if(mainSessionHolder != null){
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
			}
		}
	}

	public static Services findServiceByClientId(String clientID) {
		if (clientID == null || clientID.isEmpty()) {
			return null;
		}

		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("clientID", clientID));
		List<Services> servicesList =  HibernateUtil.getManager().getObjectsAdvanced(Services.class, critList);
		if (!servicesList.isEmpty()) {
			return servicesList.get(0);
		}

		return null;
	}

	public static Services findServiceByAPIIdentifier(String apiIdentifier) {
		if (apiIdentifier == null || apiIdentifier.isEmpty()) {
			return null;
		}

		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("apiIdentifier", apiIdentifier));
		List<Services> servicesList =  HibernateUtil.getManager().getObjectsAdvanced(Services.class, critList);
		if (!servicesList.isEmpty()) {
			return servicesList.get(0);
		}

		return null;
	}

	public List<Node> getReferencedNodes(){
		List<Node> usedInNodes = new ArrayList<>();
		try {
			for (Branch branch : BranchUtil.getAllBranches()) {
				SessionHolder mainSessionHolder = null;

				for(Node node: branch.getAllAccessibleNodes(true)){
					try {
						mainSessionHolder = HibernateUtil.getManager().openTemporarySession(node.getSchemaName());
						List<ConfigurableWorkflowStep> steps = ConfigurableWorkflowStep.findAllByService(this.getGuid());
						if(steps != null && steps.stream().anyMatch(step -> !step.getConfigurableWorkflowInstance().isHistorical())){
							usedInNodes.add(node);
						}
					} catch(Exception e) {
						LogUtil.getLog(Services.class).error(e);
					} finally {
						if (mainSessionHolder != null) {
							HibernateUtil.getManager().restoreSession(mainSessionHolder);
						}
					}
				}

			}
		} catch (Exception e) {
			LogUtil.getLog(Services.class).error(e);
		}

		return usedInNodes;
	}

}