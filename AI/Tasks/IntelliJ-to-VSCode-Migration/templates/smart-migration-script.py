#!/usr/bin/env python3
"""
Smart IntelliJ to VS Code Migration Script
Discovers developer environment and provides confidence-based migration
"""

import json
import xml.etree.ElementTree as ET
import os
import sys
import socket
import subprocess
import configparser
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class ConfidenceLevel(Enum):
    HIGH = "🟢 HIGH CONFIDENCE"
    MEDIUM = "🟡 MEDIUM CONFIDENCE" 
    LOW = "🔴 LOW CONFIDENCE"

@dataclass
class DiscoveryResult:
    component: str
    status: str
    confidence: ConfidenceLevel
    details: str
    action_required: Optional[str] = None

class SmartMigrationScript:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.vscode_dir = self.project_root / ".vscode"
        self.idea_dir = self.project_root / ".idea"
        self.discovery_results: List[DiscoveryResult] = []
        
        # Ensure .vscode directory exists
        self.vscode_dir.mkdir(exist_ok=True)
    
    def run_discovery(self):
        """Run complete environment discovery with confidence assessment"""
        print("🔍 STARTING SMART ENVIRONMENT DISCOVERY")
        print("=" * 50)
        
        # Discover all components
        self.discover_java()
        self.discover_jboss()
        self.discover_database()
        self.discover_properties()
        self.discover_ssl_nginx()
        
        # Assess overall confidence and provide feedback
        self.provide_confidence_assessment()
        
        # If confidence is sufficient, proceed with configuration
        if self.can_proceed_automatically():
            self.generate_vscode_configuration()
        else:
            self.provide_remediation_steps()
    
    def discover_java(self):
        """Discover Java installation and configuration"""
        java_locations = [
            "/Library/Java/JavaVirtualMachines/amazon-corretto-17.jdk/Contents/Home",
            "/Library/Java/JavaVirtualMachines/amazon-corretto-11.jdk/Contents/Home",
            "/Library/Java/JavaVirtualMachines/openjdk-17.jdk/Contents/Home",
        ]
        
        found_java = []
        for location in java_locations:
            if Path(location).exists():
                found_java.append(location)
        
        if len(found_java) == 1 and "17" in found_java[0]:
            self.discovery_results.append(DiscoveryResult(
                component="Java",
                status="✅ Java 17 found",
                confidence=ConfidenceLevel.HIGH,
                details=f"Location: {found_java[0]}"
            ))
        elif len(found_java) > 1:
            self.discovery_results.append(DiscoveryResult(
                component="Java",
                status="❓ Multiple Java versions found",
                confidence=ConfidenceLevel.MEDIUM,
                details=f"Found: {', '.join(found_java)}",
                action_required="Please confirm which Java version to use"
            ))
        else:
            self.discovery_results.append(DiscoveryResult(
                component="Java",
                status="❌ No Java 17 installation found",
                confidence=ConfidenceLevel.LOW,
                details="Expected Java 17 at standard locations",
                action_required="Install Amazon Corretto 17 JDK"
            ))
    
    def discover_jboss(self):
        """Discover JBoss installation and configuration"""
        jboss_locations = [
            "/Users/<USER>/JBoss/jboss-eap-7.4",
            "/Users/<USER>/JBoss/jboss-eap-7.2", 
            f"{os.path.expanduser('~')}/JBoss/jboss-eap-7.4",
            f"{os.path.expanduser('~')}/JBoss/jboss-eap-7.2",
        ]
        
        found_jboss = []
        for location in jboss_locations:
            jboss_path = Path(location)
            if jboss_path.exists() and (jboss_path / "bin" / "standalone.sh").exists():
                found_jboss.append(location)
        
        if len(found_jboss) == 1:
            version = "7.4" if "7.4" in found_jboss[0] else "7.2"
            self.discovery_results.append(DiscoveryResult(
                component="JBoss",
                status=f"✅ JBoss EAP {version} found",
                confidence=ConfidenceLevel.HIGH,
                details=f"Location: {found_jboss[0]}"
            ))
        elif len(found_jboss) > 1:
            # Prefer 7.4 but ask for confirmation
            preferred = next((j for j in found_jboss if "7.4" in j), found_jboss[0])
            self.discovery_results.append(DiscoveryResult(
                component="JBoss",
                status="❓ Multiple JBoss installations found",
                confidence=ConfidenceLevel.MEDIUM,
                details=f"Found: {', '.join(found_jboss)}\nPreferred: {preferred}",
                action_required="Confirm which JBoss version to use (default: latest)"
            ))
        else:
            self.discovery_results.append(DiscoveryResult(
                component="JBoss",
                status="❌ No JBoss installation found",
                confidence=ConfidenceLevel.LOW,
                details="Checked standard locations",
                action_required="Install JBoss EAP 7.4 or provide custom location"
            ))
    
    def discover_database(self):
        """Discover database configuration and connectivity"""
        # Read database settings from local.properties
        db_config = self.read_database_properties()
        
        if db_config:
            host = db_config.get('host', 'prlvm.messagepoint.app')
            port = db_config.get('port', 5432)
            
            # Test database connectivity
            if self.test_database_connection(host, port):
                self.discovery_results.append(DiscoveryResult(
                    component="Database",
                    status="✅ Database connection verified",
                    confidence=ConfidenceLevel.HIGH,
                    details=f"PostgreSQL at {host}:{port}"
                ))
            else:
                self.discovery_results.append(DiscoveryResult(
                    component="Database",
                    status="⚠️ Database configured but not accessible",
                    confidence=ConfidenceLevel.MEDIUM,
                    details=f"Config found: {host}:{port}",
                    action_required="Start Parallels VM or check database server"
                ))
        else:
            self.discovery_results.append(DiscoveryResult(
                component="Database",
                status="❌ No database configuration found",
                confidence=ConfidenceLevel.LOW,
                details="Could not read database settings from local.properties",
                action_required="Configure database connection in local.properties"
            ))
    
    def discover_properties(self):
        """Discover and validate local.properties configuration"""
        local_props = self.project_root / "local.properties"
        
        if local_props.exists():
            try:
                with open(local_props, 'r') as f:
                    content = f.read()
                
                # Check for key properties
                required_props = [
                    'app.messagepoint.fileroot',
                    'app.messagepoint.job.fileroot',
                    'app.compositionEngines.fileroot'
                ]
                
                missing_props = [prop for prop in required_props if prop not in content]
                
                if not missing_props:
                    self.discovery_results.append(DiscoveryResult(
                        component="Properties",
                        status="✅ Local properties configured",
                        confidence=ConfidenceLevel.HIGH,
                        details="All required properties found"
                    ))
                else:
                    self.discovery_results.append(DiscoveryResult(
                        component="Properties",
                        status="⚠️ Some properties missing",
                        confidence=ConfidenceLevel.MEDIUM,
                        details=f"Missing: {', '.join(missing_props)}",
                        action_required="Review local.properties configuration"
                    ))
            except Exception as e:
                self.discovery_results.append(DiscoveryResult(
                    component="Properties",
                    status="❌ Error reading local.properties",
                    confidence=ConfidenceLevel.LOW,
                    details=f"Error: {str(e)}",
                    action_required="Fix local.properties file"
                ))
        else:
            self.discovery_results.append(DiscoveryResult(
                component="Properties",
                status="❌ local.properties not found",
                confidence=ConfidenceLevel.LOW,
                details="File does not exist in project root",
                action_required="Create local.properties file"
            ))
    
    def discover_ssl_nginx(self):
        """Discover SSL certificates and NGINX configuration"""
        ssl_path = Path("/Users/<USER>/SSL")
        nginx_configs = [
            ssl_path / "master_messagepoint_page.conf",
            ssl_path / "developer_messagepoint_page.conf"
        ]
        
        if ssl_path.exists():
            found_configs = [conf for conf in nginx_configs if conf.exists()]
            if found_configs:
                self.discovery_results.append(DiscoveryResult(
                    component="SSL/NGINX",
                    status="✅ SSL certificates and NGINX configs found",
                    confidence=ConfidenceLevel.HIGH,
                    details=f"Found {len(found_configs)} configuration files"
                ))
            else:
                self.discovery_results.append(DiscoveryResult(
                    component="SSL/NGINX",
                    status="⚠️ SSL directory exists but configs missing",
                    confidence=ConfidenceLevel.MEDIUM,
                    details="SSL directory found but configuration files missing",
                    action_required="Install NGINX configuration files"
                ))
        else:
            self.discovery_results.append(DiscoveryResult(
                component="SSL/NGINX",
                status="❌ SSL certificates not found",
                confidence=ConfidenceLevel.LOW,
                details="No SSL directory at /Users/<USER>/SSL",
                action_required="Install SSL certificates and NGINX configuration"
            ))
    
    def read_database_properties(self) -> Dict[str, Any]:
        """Read database configuration from local.properties"""
        local_props = self.project_root / "local.properties"
        if not local_props.exists():
            return {}
        
        try:
            with open(local_props, 'r') as f:
                content = f.read()
            
            # Extract database-related properties
            db_config = {}
            for line in content.split('\n'):
                if 'db.messagepoint.host' in line and '=' in line:
                    db_config['host'] = line.split('=')[1].strip()
                elif 'db.messagepoint.port' in line and '=' in line:
                    db_config['port'] = int(line.split('=')[1].strip())
            
            # Default values if not found
            if 'host' not in db_config:
                db_config['host'] = 'prlvm.messagepoint.app'
            if 'port' not in db_config:
                db_config['port'] = 5432
                
            return db_config
        except Exception:
            return {}
    
    def test_database_connection(self, host: str, port: int) -> bool:
        """Test if database server is accessible"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((host, port))
            sock.close()
            return result == 0
        except Exception:
            return False

    def provide_confidence_assessment(self):
        """Analyze discovery results and provide confidence-based feedback"""
        print("\n" + "=" * 50)
        print("📊 DISCOVERY RESULTS")
        print("=" * 50)

        high_confidence = [r for r in self.discovery_results if r.confidence == ConfidenceLevel.HIGH]
        medium_confidence = [r for r in self.discovery_results if r.confidence == ConfidenceLevel.MEDIUM]
        low_confidence = [r for r in self.discovery_results if r.confidence == ConfidenceLevel.LOW]

        # Display results by confidence level
        for result in self.discovery_results:
            print(f"\n{result.confidence.value}")
            print(f"🔧 {result.component}: {result.status}")
            print(f"   {result.details}")
            if result.action_required:
                print(f"   ⚡ ACTION: {result.action_required}")

        # Overall assessment
        print("\n" + "=" * 50)
        if len(low_confidence) == 0 and len(medium_confidence) <= 1:
            print("🟢 OVERALL ASSESSMENT: READY FOR AUTOMATIC CONFIGURATION")
            print("✅ All critical components discovered successfully")
            if medium_confidence:
                print("ℹ️  Some components need confirmation but can proceed")
        elif len(low_confidence) == 0:
            print("🟡 OVERALL ASSESSMENT: NEEDS INPUT BEFORE PROCEEDING")
            print("❓ Multiple components need confirmation")
            print("💡 Can proceed after answering questions")
        else:
            print("🔴 OVERALL ASSESSMENT: MANUAL REMEDIATION REQUIRED")
            print("❌ Critical components missing or misconfigured")
            print("🛠️  Please resolve issues before running migration")

        print("=" * 50)

    def can_proceed_automatically(self) -> bool:
        """Determine if we can proceed with automatic configuration"""
        low_confidence = [r for r in self.discovery_results if r.confidence == ConfidenceLevel.LOW]
        medium_confidence = [r for r in self.discovery_results if r.confidence == ConfidenceLevel.MEDIUM]

        # Can proceed if no low confidence issues and at most 1 medium confidence issue
        return len(low_confidence) == 0 and len(medium_confidence) <= 1

    def handle_medium_confidence_questions(self):
        """Handle questions for medium confidence items"""
        medium_items = [r for r in self.discovery_results if r.confidence == ConfidenceLevel.MEDIUM]

        responses = {}
        for item in medium_items:
            if "Multiple JBoss" in item.status:
                print(f"\n❓ {item.action_required}")
                print(item.details)
                # For now, use the preferred (latest) version
                # In a real implementation, you'd prompt for user input
                responses['jboss_path'] = self.get_preferred_jboss_path()
            elif "Multiple Java" in item.status:
                print(f"\n❓ {item.action_required}")
                print(item.details)
                # Use Java 17 if available
                responses['java_path'] = self.get_preferred_java_path()

        return responses

    def get_preferred_jboss_path(self) -> str:
        """Get preferred JBoss installation path"""
        jboss_locations = [
            "/Users/<USER>/JBoss/jboss-eap-7.4",
            "/Users/<USER>/JBoss/jboss-eap-7.2",
        ]

        for location in jboss_locations:
            if Path(location).exists():
                return location
        return ""

    def get_preferred_java_path(self) -> str:
        """Get preferred Java installation path"""
        java_locations = [
            "/Library/Java/JavaVirtualMachines/amazon-corretto-17.jdk/Contents/Home",
            "/Library/Java/JavaVirtualMachines/openjdk-17.jdk/Contents/Home",
        ]

        for location in java_locations:
            if Path(location).exists():
                return location
        return ""

    def generate_vscode_configuration(self):
        """Generate VS Code configuration files based on discovery"""
        print("\n🔧 GENERATING VS CODE CONFIGURATION...")

        # Handle any medium confidence questions first
        user_choices = self.handle_medium_confidence_questions()

        # Extract configuration from discovery results
        java_path = user_choices.get('java_path', self.get_preferred_java_path())
        jboss_path = user_choices.get('jboss_path', self.get_preferred_jboss_path())
        db_config = self.read_database_properties()

        # Generate all VS Code configuration files
        self.generate_settings_json(java_path)
        self.generate_extensions_json()
        self.generate_tasks_json()
        self.generate_launch_json()
        self.generate_server_config(jboss_path)

        print("✅ VS Code configuration generated successfully!")
        print(f"📁 Configuration files created in: {self.vscode_dir}")

        # Provide next steps
        self.provide_next_steps()

    def provide_remediation_steps(self):
        """Provide specific remediation steps for low confidence items"""
        print("\n🛠️  REMEDIATION STEPS REQUIRED")
        print("=" * 50)

        low_confidence = [r for r in self.discovery_results if r.confidence == ConfidenceLevel.LOW]

        for i, item in enumerate(low_confidence, 1):
            print(f"\n{i}. {item.component}: {item.status}")
            print(f"   🔧 {item.action_required}")
            print(f"   💡 {item.details}")

        print(f"\n📋 NEXT STEPS:")
        print("1. Resolve the issues listed above")
        print("2. Re-run this script: python3 smart-migration-script.py")
        print("3. Script will re-discover your environment")

    def provide_next_steps(self):
        """Provide next steps after successful configuration"""
        print("\n📋 NEXT STEPS:")
        print("1. Open VS Code")
        print("2. Open this project folder in VS Code")
        print("3. VS Code will prompt to install recommended extensions → Click 'Install'")
        print("4. Wait for Java extension to activate (check status bar)")
        print("5. Test build: Cmd+Shift+P → 'Tasks: Run Task' → 'gradle-build'")
        print("6. Configure JBoss server using Community Server Connectors")
        print("7. Set up database connection using SQLTools")
        print("\n🎉 Your VS Code environment is ready for MessagePoint development!")

    def generate_settings_json(self, java_path: str):
        """Generate VS Code settings.json with discovered configuration"""
        settings = {
            "java.home": java_path,
            "java.configuration.runtimes": [
                {
                    "name": "JavaSE-17",
                    "path": java_path
                }
            ],
            "gradle.nestedProjects": True,
            "java.compile.nullAnalysis.mode": "automatic",
            "typescript.preferences.includePackageJsonAutoImports": "auto",
            "typescript.suggest.autoImports": True,
            "files.associations": {
                "*.jsp": "html",
                "*.hbs": "handlebars"
            },
            "editor.tabSize": 4,
            "editor.insertSpaces": True,
            "editor.detectIndentation": True,
            "terminal.integrated.defaultProfile.osx": "bash"
        }

        settings_file = self.vscode_dir / "settings.json"
        with open(settings_file, 'w') as f:
            json.dump(settings, f, indent=2)

        print(f"✅ Generated: {settings_file}")

    def generate_extensions_json(self):
        """Generate VS Code extensions.json"""
        extensions = {
            "recommendations": [
                "vscjava.vscode-java-pack",
                "vscjava.vscode-gradle",
                "redhat.vscode-community-server-connector",
                "mtxr.sqltools",
                "mtxr.sqltools-driver-pg",
                "ms-vscode.vscode-typescript-next",
                "eamodio.gitlens"
            ]
        }

        extensions_file = self.vscode_dir / "extensions.json"
        with open(extensions_file, 'w') as f:
            json.dump(extensions, f, indent=2)

        print(f"✅ Generated: {extensions_file}")

    def generate_tasks_json(self):
        """Generate VS Code tasks.json"""
        tasks = {
            "version": "2.0.0",
            "tasks": [
                {
                    "label": "gradle-build",
                    "type": "shell",
                    "command": "./gradlew",
                    "args": ["build"],
                    "group": {
                        "kind": "build",
                        "isDefault": True
                    },
                    "presentation": {
                        "echo": True,
                        "reveal": "always",
                        "focus": False,
                        "panel": "shared"
                    },
                    "problemMatcher": ["$gradle"]
                },
                {
                    "label": "gradle-explodedWar",
                    "type": "shell",
                    "command": "./gradlew",
                    "args": ["explodedWar"],
                    "group": "build",
                    "dependsOn": "gradle-build"
                },
                {
                    "label": "npm-install-typescript",
                    "type": "shell",
                    "command": "npm",
                    "args": ["install", "--omit=optional", "--legacy-peer-deps"],
                    "options": {
                        "cwd": "${workspaceFolder}/src/typescript"
                    },
                    "group": "build"
                },
                {
                    "label": "npm-install-web-includes",
                    "type": "shell",
                    "command": "npm",
                    "args": ["install", "--omit=optional", "--legacy-peer-deps"],
                    "options": {
                        "cwd": "${workspaceFolder}/src/web/includes"
                    },
                    "group": "build"
                },
                {
                    "label": "full-build",
                    "dependsOrder": "sequence",
                    "dependsOn": [
                        "npm-install-typescript",
                        "npm-install-web-includes",
                        "gradle-explodedWar"
                    ],
                    "group": {
                        "kind": "build",
                        "isDefault": False
                    }
                }
            ]
        }

        tasks_file = self.vscode_dir / "tasks.json"
        with open(tasks_file, 'w') as f:
            json.dump(tasks, f, indent=2)

        print(f"✅ Generated: {tasks_file}")

    def generate_launch_json(self):
        """Generate VS Code launch.json"""
        launch = {
            "version": "0.2.0",
            "configurations": [
                {
                    "type": "java",
                    "name": "Debug MessagePoint",
                    "request": "attach",
                    "hostName": "localhost",
                    "port": 8787,
                    "timeout": 30000
                },
                {
                    "name": "Debug Gradle Task",
                    "type": "java",
                    "request": "launch",
                    "mainClass": "org.gradle.wrapper.GradleWrapperMain",
                    "args": ["build", "--debug-jvm"],
                    "console": "internalConsole"
                }
            ]
        }

        launch_file = self.vscode_dir / "launch.json"
        with open(launch_file, 'w') as f:
            json.dump(launch, f, indent=2)

        print(f"✅ Generated: {launch_file}")

    def generate_server_config(self, jboss_path: str):
        """Generate server configuration information"""
        if jboss_path:
            server_info = {
                "jboss_path": jboss_path,
                "startup_script": f"{jboss_path}/bin/standalone.sh",
                "config_file": "standaloneMessagepoint.xml",
                "startup_args": "--server-config=standaloneMessagepoint.xml -b 0.0.0.0",
                "deployment_path": "build/libs/exploded/mp.war"
            }

            server_file = self.vscode_dir / "server-config.json"
            with open(server_file, 'w') as f:
                json.dump(server_info, f, indent=2)

            print(f"✅ Generated: {server_file}")

def main():
    """Main entry point"""
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = os.getcwd()

    print("🚀 SMART MESSAGEPOINT VS CODE MIGRATION")
    print(f"📁 Project: {project_root}")
    print("=" * 50)

    migrator = SmartMigrationScript(project_root)
    migrator.run_discovery()

if __name__ == "__main__":
    main()
