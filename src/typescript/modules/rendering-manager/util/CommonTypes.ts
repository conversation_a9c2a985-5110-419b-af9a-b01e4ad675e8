/*TYPE DEFINITIONS*/

import {ContentEditorIF} from "../editor/ContentEditorIF";

/**
 * @typedef {Object} RenderingDataSection the touchpoint data needed for the page to be rendered
 * @property {number} id the id of the touchpoint
 * @property {Margins} margins
 * @property {string} headerHeight
 * @property {string} footerHeight
 * @property {string} regionLeftWidth
 * @property {string}regionRightWidth
 * @property {Dimensions} dimensions
 * @property {RenderingDataZone[]} zones the page zones
 */

interface RenderingDataSection {
    guid?: string;
    donotcountpage?: boolean;
    startsfrontfacing?: boolean;
    resetpagecount?: boolean;
    margins: Margins;
    headerHeight: string;
    footerHeight: string;
    regionLeftWidth: string;
    regionRightWidth: string;
    dimensions: Dimensions;
    zones: RenderingDataZone[];
}

/**
 * @typedef {Object} RenderingDataZone the rendering data for a zone
 * @property {number} id
 * @property {Position} position
 * @property {Dimensions} dimensions
 * @property {string} zoneType
 * @property {string} content
 */

interface RenderingDataZone {
    id: number;
    sectionGuid: string;
    position: Position;
    dimensions: Dimensions;
    zoneType: string;
    interactive: boolean;
    name: string;
    contentType: ZoneContentType;
    zoneGraphicType: string;
    communicationContentEditing: boolean;
    enabled: boolean;
    originalHeight: number;
    //it is passed when zones are split
    zoneEditMode: string;
    stylesDefault: ZoneDefaultStyles;
    defaultCommunicationTemplateImage: string;
    starterTextStyle: string;
    textStyleData: any;
    paragraphStyleData: any;
    listStyleData: any;
    restrictSharedAssets: any;
    contentLibraryAssets: any;
    defaultCommunicationTemplateEmbeddedContent: any;
    parts: RenderingDataZonePart[];
    embeddedContentAssets: any;
    zIndex: number;
    sequence: number;
    freeform: boolean;
    flowZone?: boolean;
    flowZoneFixedHeight?: boolean;
    flowTo?: number;
    flowToFixedHeight?: boolean;
    rotation?: number;
}

interface RenderingDataZonePart {
    id: number;
    sectionGuid: string;
    name: string;
    description: string;
    sequence: number;
    contentType: ZoneContentType;
    position: Position;
    dimensions: Dimensions;
}

export interface ZoneDefaultStyles {
    textStyleDefaultGUID: string;
    paragraphStyleDefaultGUID: string;
    listStyleDefaultGUID: string;
}

export interface DocumentDefaultStyles {
    textStyleDefaultGUID: string;
    paragraphStyleDefaultGUID: string;
    listStyleDefaultGUID: string;
}

enum ZoneContentType {
    GRAPHIC = "GRAPHIC",
    TEXT = "TEXT",
    MULTIPART = "MULTIPART",
    FREE_FORM = "SHARED_FREEFORM"
}

class BindingContentTypeId {
    public static TEXT: number = 1;
    public static GRAPHIC: number = 2;
    public static MULTIPART: number = 4
}

enum RecipientContentType {
    IMG = "Img",
    TEXT = "TEXT"
}

enum EDIT_ACTIONS_TYPE {
    ALL = "All",
    MANDATORY = "Mandatory",
    OPTIONAL = "Optional",
    INCOMPLETE = "Incomplete"
}
/**
 * @typedef {Object} Margins the margins for a touchpoint
 * @property {string} top
 * @property {string} right
 * @property {string} bottom
 * @property {string} left
 */

interface Margins {
    top: string;
    right: string;
    bottom: string;
    left: string;
}

/**
 * @typedef {Object} Dimensions
 * @property {string} width
 * @property {string} height
 */

interface Dimensions {
    width: string;
    height: string;
}

/**
 * @typedef {Object} Position
 * @property {string} x
 * @property {string} y
 */

interface Position {
    x: string;
    y: string;
}

interface ContentView {
    indexInContentsArray: number;
    content: Content;
}

interface Content {
    Connected: boolean;
    ContentID: number;
    Type: string;
    Value: string;
    MessageID: number;
    FlowType?: number;
    Guid?: string;
    Name?: string;
    FileName?: string;
    FromContentLibrary?: boolean;
    Width? : number;
    Height?: number;
    Extra?: Array<{Value: string, Guid?: string, Width?: number, Height?: number}>;
}

enum MessageFlowType {
    FIRST_OR_ONLY = "FIRST_OR_ONLY",
    ANY = "ANY",
    NOT_FIRST_OR_LAST = "NOT_FIRST_OR_LAST",
    LAST = "LAST",
    ONLY = "ONLY",
    NOT_FIRST = "NOT_FIRST",
    NOT_LAST = "NOT_LAST",
    FIRST = "FIRST",
    LAST_OR_ONLY = "LAST_OR_ONLY",
    LAST_ONLY = "LAST_ONLY", //SEFAS
}

interface ZoneContent {
    PartsContent: ZoneContent[];
    Content: Content[];
    ZoneID: number;
}

interface DEWSContent {
    base: RecipientContents;
    persisted: PersistedContent;
    localImages: LocalImagesContent;
}

interface PersistedContent {
    [zoneId: number]: string;
}

interface LocalImagesContent {
    [zoneId: number]: LocalImageContent;
}

interface LocalImageContent {
    Name: string;
    GUID: string;
    Value: string;
}

interface RecipientContents {
    RecipientID: string;
    ZoneContents: ZoneContent[];
}

interface PostEditorInitCallback {
    callback: (editor: ContentEditorIF) => any;
    startEditTime: number;
}

enum ServerResponseStatusType {
    SUCCESS = "success",
    ERROR = "error",
    DONE = "done",
    IN_PROGRESS = "in_progress"
}

enum ContentInitializerComponentType {
    EDIT_ACTION = "EDIT_ACTION",
    EDIT_CONTENT = "EDIT_CONTENT"
}

export {
    Position,
    Dimensions,
    Margins,
    RenderingDataZone,
    RenderingDataZonePart,
    RenderingDataSection,
    Content,
    ContentView,
    MessageFlowType,
    ZoneContent,
    RecipientContents,
    PostEditorInitCallback,
    DEWSContent,
    PersistedContent,
    ZoneContentType,
    RecipientContentType,
    LocalImagesContent,
    ServerResponseStatusType,
    EDIT_ACTIONS_TYPE,
    BindingContentTypeId,
    ContentInitializerComponentType
};