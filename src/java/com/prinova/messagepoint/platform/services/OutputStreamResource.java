/**
 * 
 */
package com.prinova.messagepoint.platform.services;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;

public class OutputStreamResource {
	private File file;
	private BufferedOutputStream outputStream;

	public File getFile() {
		return file;
	}

	public void setFile(File file) {
		this.file = file;
	}

	public BufferedOutputStream getOutputStream() {
		return outputStream;
	}

	public void setOutputStream(BufferedOutputStream outputStream) {
	
		if(this.outputStream!=null){
			try {
				this.outputStream.close();
			} catch (IOException e) {
				//do nothing here 
			}
		}
		this.outputStream = outputStream;
	}

}