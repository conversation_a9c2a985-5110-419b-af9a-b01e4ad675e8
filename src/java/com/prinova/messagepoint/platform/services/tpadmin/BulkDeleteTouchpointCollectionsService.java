package com.prinova.messagepoint.platform.services.tpadmin;

import java.util.List;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;

public class BulkDeleteTouchpointCollectionsService extends AbstractService {
	public static final String SERVICE_NAME = "tpadmin.BulkDeleteTouchpointCollectionsService";
	private static final Log log = LogUtil.getLog(BulkDeleteTouchpointCollectionsService.class);
	
	public void execute(ServiceExecutionContext context) {
		BulkTouchpointCollectionsActionServiceRequest request = (BulkTouchpointCollectionsActionServiceRequest) context.getRequest();
		List<TouchpointCollection> tpCollectionsList = request.getTpCollectionsList();
		if(tpCollectionsList == null || tpCollectionsList.isEmpty()){
			return;
		}
		try {
			validate(context);
			if(hasValidationError(context)){
				return;
			}

			for(TouchpointCollection tpCollection: tpCollectionsList){
				ServiceExecutionContext context2 = DeleteTouchpointCollectionService.createContext(tpCollection.getId());
				Service service = MessagepointServiceFactory.getInstance()
						.lookupService(DeleteTouchpointCollectionService.SERVICE_NAME, DeleteTouchpointCollectionService.class);
				service.execute(context2);
				if(!context2.getResponse().isSuccessful()){
					context2.getResponse().mergeResultMessages(context2.getResponse());
				}
			}
		} catch (Exception e) {
			log.error(" unexpected exception when invoking DeleteTouchpointCollectionService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {
	}
	
	public static SimpleExecutionContext createContext(List<TouchpointCollection> tpCollectionsList) {
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		BulkTouchpointCollectionsActionServiceRequest request = new BulkTouchpointCollectionsActionServiceRequest();
		context.setRequest(request);
		
		request.setTpCollectionsList(tpCollectionsList);
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}	

}
