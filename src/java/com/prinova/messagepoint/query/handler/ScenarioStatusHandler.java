package com.prinova.messagepoint.query.handler;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import com.prinova.messagepoint.model.scenario.AbstractScenario;
import com.prinova.messagepoint.model.testing.TestScenario;
import com.prinova.messagepoint.query.DefaultPage;
import com.prinova.messagepoint.query.IPage;

public class ScenarioStatusHandler implements PostQueryHandler {

	private boolean isOrderAsc;
	
	public boolean isOrderAsc() {
		return isOrderAsc;
	}

	public void setOrderAsc(boolean isOrderAsc) {
		this.isOrderAsc = isOrderAsc;
	}
	
	@SuppressWarnings("unchecked")
	public IPage handle(IPage page) {
		List<AbstractScenario> tests = (List<AbstractScenario>)page.getList();
		
		Collections.sort(tests, new Comparator<>() {
            public int compare(AbstractScenario o1, AbstractScenario o2) {
                String status1 = getTestStatus(o1);
                String status2 = getTestStatus(o2);
                if (isOrderAsc()) {
                    return status1.compareTo(status2);
                } else {
                    return status2.compareTo(status1);
                }
            }
        });
		
        ((DefaultPage)page).setList(tests);
		return page;
	}

	private String getTestStatus(AbstractScenario scenario){
		if(scenario instanceof TestScenario){	// Test Scenario
			if(scenario.isError() || ( scenario.getDeliveryEvent() != null && scenario.getDeliveryEvent().isError()) ){
				return "Error";
			}
		}else{	// Report Scenario
			if(scenario.isError()){
				return "Error";
			}
		}
		if(scenario.isComplete()){
			return "Complete";
		}else{
			return "In process";
		}
	}
}
