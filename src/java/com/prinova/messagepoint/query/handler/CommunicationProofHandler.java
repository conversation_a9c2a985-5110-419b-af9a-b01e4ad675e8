package com.prinova.messagepoint.query.handler;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.communication.CommunicationProof;
import com.prinova.messagepoint.query.DefaultPage;
import com.prinova.messagepoint.query.IPage;
import com.prinova.messagepoint.util.ApplicationUtil;

public class CommunicationProofHandler implements PostQueryHandler {

	private boolean isOrderAsc;
	
	public boolean isOrderAsc() {
		return isOrderAsc;
	}

	public void setOrderAsc(boolean isOrderAsc) {
		this.isOrderAsc = isOrderAsc;
	}
	
	@SuppressWarnings("unchecked")
	public IPage handle(IPage page) {
		List<Communication> communications = (List<Communication>)page.getList();
		
		Collections.sort(communications, new Comparator<>() {
            public int compare(Communication o1, Communication o2) {
                String status1 = getProofStatus(o1.getLatestProof());
                String status2 = getProofStatus(o2.getLatestProof());
                if (isOrderAsc()) {
                    return status1.compareTo(status2);
                } else {
                    return status2.compareTo(status1);
                }
            }
        });
		
        ((DefaultPage)page).setList(communications);
		return page;
	}

	private String getProofStatus(CommunicationProof proof){
		if ( proof != null ) {
			if ( !proof.isComplete() && !proof.isError() ) {
				return ApplicationUtil.getMessage("page.text.in.process");
			} else if ( proof.isNoMatchingRecipientError() ) {
				return ApplicationUtil.getMessage("page.label.error");
			} else if ( proof.isNoProductionContentError() ) {
				return ApplicationUtil.getMessage("page.label.error");
			} else if ( !proof.isError() && proof.getOutputPath() == null && !proof.getCommunication().getDocument().isCommunicationWebServiceCompositionResultsEnabled() && !proof.getIsEmailOrWebPreview() ) {
				return ApplicationUtil.getMessage("page.label.error");
			} else if ( !proof.isError() ) {
				return ApplicationUtil.getMessage("page.label.complete");
			} else if ( proof.isError() ) {
				return ApplicationUtil.getMessage("page.label.error");
			}
		}
		return "None";
	}
}
