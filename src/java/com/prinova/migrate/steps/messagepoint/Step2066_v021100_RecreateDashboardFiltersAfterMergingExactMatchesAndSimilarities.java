package com.prinova.migrate.steps.messagepoint;

import com.prinova.messagepoint.model.filters.WordCountFilter;
import com.prinova.migrate.MigrationStep;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class Step2066_v021100_RecreateDashboardFiltersAfterMergingExactMatchesAndSimilarities extends MigrationStep {

    private int counter = 0;

    @Override
    public void migrate(Connection conn) throws Exception {
        if(tableExists(conn, "dashboard_filters")) {
            // delete all entries from table dashboard_filters
            executeUpdate(conn, "delete from dashboard_filters");

            // Insert default Rationalizer dashboard filters for wordCount
            createRationalizerApplicationsDefaultsForWordCount(conn);

            // Insert default Messagepoint dashboard filters for wordCount
            createMessagepointDefaultsForWordCount(conn);
        }
    }

    private void createMessagepointDefaultsForWordCount(Connection conn) throws Exception {

        // Exact matches and Similarities section
        messagepointInsertWordCountKeywordDefaultsForSection(conn, "exact_matches_similarities");
        // Readability
        messagepointInsertWordCountKeywordDefaultsForSection(conn, "readability");
        // Sentiment
        messagepointInsertWordCountKeywordDefaultsForSection(conn, "sentiment");
    }

    private void messagepointInsertWordCountKeywordDefaultsForSection(Connection conn, String section) throws Exception{
        String insertQuery = "INSERT INTO dashboard_filters (id, applies_to, object_id, section, keyword, type, operation, value) " +
                "values (" + (++counter) + ", 'MP', '-1', '" + section + "', 'wordCount', 'integer', 'gt', '" + getDefaultValue("gt", section) + "')";
        executeUpdate(conn, insertQuery);
        insertQuery = "INSERT INTO dashboard_filters (id, applies_to, object_id, section, keyword, type, operation, value) " +
                "values (" + (++counter) + ", 'MP', '-1', '" + section + "', 'wordCount', 'integer', 'lt', '" + getDefaultValue("lt", section) + "')";
        executeUpdate(conn, insertQuery);
    }

    /**
     * Insert preconfigured values for wordCount filters for every Rationalizer application.
     */
    private void createRationalizerApplicationsDefaultsForWordCount(Connection conn) throws Exception {
        PreparedStatement fetchStatement = conn.prepareStatement("SELECT id FROM rationalizer_application");
        ResultSet rs = fetchStatement.executeQuery();
        while (rs.next()) {
            // Exact matches and Similarities section
            rationalizerInsertWordCountKeywordDefaultsForSection(conn, rs, "exact_matches_similarities");
            // Readability
            rationalizerInsertWordCountKeywordDefaultsForSection(conn, rs, "readability");
            // Sentiment
            rationalizerInsertWordCountKeywordDefaultsForSection(conn, rs, "sentiment");
        }
        rs.close();

    }

    public void rationalizerInsertWordCountKeywordDefaultsForSection(Connection conn, ResultSet rs, String section) throws Exception {
        String insertQuery = "INSERT INTO dashboard_filters (id, applies_to, object_id, section, keyword, type, operation, value) " +
                "values (" + (++counter) + ", 'RT', " + rs.getInt("id") + ", '" + section + "', 'wordCount', 'integer', 'gt', '" + getDefaultValue("gt", section) + "')";
        executeUpdate(conn, insertQuery);
        insertQuery = "INSERT INTO dashboard_filters (id, applies_to, object_id, section, keyword, type, operation, value) " +
                "values (" + (++counter) + ", 'RT', " + rs.getInt("id") + ", '" + section + "', 'wordCount', 'integer', 'lt', '" + getDefaultValue("lt", section) + "')";
        executeUpdate(conn, insertQuery);
    }

    private String getDefaultValue(String operator, String section) {
        if(operator.equalsIgnoreCase("gt")) {
            if(section.equalsIgnoreCase("exact_matches_similarities")) {
                return WordCountFilter.EXACT_MATCHES_SIMILARITIES_GT_DEFAULT_VALUE;
            } else if(section.equalsIgnoreCase("readability")) {
                return WordCountFilter.READABILITY_GT_DEFAULT_VALUE;
            } else if(section.equalsIgnoreCase("sentiment")) {
                return WordCountFilter.SENTIMENT_GT_DEFAULT_VALUE;
            }
        } else if(operator.equalsIgnoreCase("lt")) {
            if(section.equalsIgnoreCase("exact_matches")) {
                return WordCountFilter.EXACT_MATCHES_SIMILARITIES_LT_DEFAULT_VALUE;
            } else if(section.equalsIgnoreCase("readability")) {
                return WordCountFilter.READABILITY_LT_DEFAULT_VALUE;
            } else if(section.equalsIgnoreCase("sentiment")) {
                return WordCountFilter.SENTIMENT_LT_DEFAULT_VALUE;
            }
        }
        return "-1";
    }
}
