package com.prinova.messagepoint.reports.model.report.insert;

import java.io.Serializable;

public class JobInsertScheduleStatistic implements Serializable {
	private static final long serialVersionUID = 5845107755291689728L;

	private long id;
	private BatchStatistics batchStatistics;
	private long insertScheduleId;
	private long qualified;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getJobId() {
		return batchStatistics.getJobId();
	}

	public long getBatchId() {
		return batchStatistics.getBatchId();
	}

	public long getInsertScheduleId() {
		return insertScheduleId;
	}

	public void setInsertScheduleId(long insertScheduleId) {
		this.insertScheduleId = insertScheduleId;
	}

	public long getQualified() {
		return qualified;
	}

	public void setQualified(long qualified) {
		this.qualified = qualified;
	}

	public void setBatchStatistics( BatchStatistics item )
	{
		batchStatistics = item;
	}
	public BatchStatistics getBatchStatistics()
	{
		return batchStatistics;
	}
	
	public String toString() {
		StringBuilder sb = new StringBuilder(1000);
		sb.append("InsertSchedule ").append(insertScheduleId);
		sb.append(" qualified: ").append(qualified);

		return sb.toString();
	}
}
