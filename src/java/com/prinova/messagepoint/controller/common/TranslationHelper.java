package com.prinova.messagepoint.controller.common;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONObject;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Properties;

public class TranslationHelper {
    private static final Log LOGGER = LogUtil.getLog(TranslationHelper.class);

    public static  JSONObject loadTranslationsMessages(final String language, final List<String> propertiesList) {
        JSONObject jsonTranslation = new JSONObject();
        String defaultTranslationFile = "WEB-INF/i18n/messages.properties";
        StringBuilder fileName = new StringBuilder();
        fileName.append("WEB-INF/i18n/messages");
        if (!language.equals("en")) {
            fileName.append("_");
            fileName.append(language);
        }
        fileName.append(".properties");
        InputStream inputStream = TranslationHelper.class.getClassLoader().getResourceAsStream(fileName.toString());
        InputStream defaultInputStream = TranslationHelper.class.getClassLoader().getResourceAsStream(defaultTranslationFile);
        if (inputStream != null && defaultInputStream != null) {
            try {
                Properties propMessages = new Properties();
                propMessages.load(inputStream);
                Properties defaultPropMessages = new Properties();
                defaultPropMessages.load(defaultInputStream);
                propertiesList.forEach(property -> {
                    String translation = propMessages.getProperty(property);
                    if (translation == null || translation.isEmpty()) {
                        translation = defaultPropMessages.getProperty(property);
                    }
                    jsonTranslation.put(property, translation);
                });
            } catch (IOException e) {
                LOGGER.error("Error - Unable to load properties file: " + e.getMessage(), e);
            }
        } else {
            if (inputStream == null && defaultInputStream != null) {
                try {
                    Properties defaultPropMessages = new Properties();

                    defaultPropMessages.load(defaultInputStream);
                    propertiesList.forEach(property -> {
                        String translation = defaultPropMessages.getProperty(property);
                        jsonTranslation.put(property, translation);
                    });
                } catch (IOException e) {
                    LOGGER.error("Error - Unable to load from default properties file: " + e.getMessage(), e);
                }
            }
        }

        return jsonTranslation;
    }
}
