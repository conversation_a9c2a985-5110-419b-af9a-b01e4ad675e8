package com.prinova.messagepoint.controller.communication.connected;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.StaticTypeIdCustomEditor;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.beans.propertyeditors.CustomNumberEditor;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class ConnectedTouchpointInterviewSetupController extends MessagepointController {
    private static final Log log = LogUtil.getLog(ConnectedTouchpointInterviewSetupController.class);

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
        Map<String, Object> referenceData = new HashMap<String, Object>();
        return referenceData;
    }

    @Override
    protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {
        return super.showForm(request, response, errors);
    }

    @Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.setAutoGrowCollectionLimit(1024);
        binder.registerCustomEditor(String.class, new StringXSSEditor());
        binder.registerCustomEditor(Integer.class, new CustomNumberEditor(Integer.class, true));
        binder.registerCustomEditor(Date.class, DateUtil.getCustomDateEditor());
        binder.registerCustomEditor(AbstractDataElement.class, new IdCustomEditor<AbstractDataElement>(AbstractDataElement.class));
        binder.registerCustomEditor(MetadataFormItemType.class, new StaticTypeIdCustomEditor<MetadataFormItemType>(MetadataFormItemType.class));
        binder.registerCustomEditor(TouchpointSelection.class, new IdCustomEditor<TouchpointSelection>(TouchpointSelection.class));
        binder.registerCustomEditor(DataSource.class, new IdCustomEditor<DataSource>(DataSource.class));
    }

    @Override
    protected Object formBackingObject(HttpServletRequest request) throws Exception {
        return new ConnectedTouchpointInterviewSetupWrapper();
    }
}