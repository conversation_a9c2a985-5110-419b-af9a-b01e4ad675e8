package com.prinova.messagepoint.model.version;

import java.util.Date;
import persistent.Persistentable;


public interface ModelVersionMapping extends Persistentable{

	public ModelVersionMappingId getId();

	public void setId(ModelVersionMappingId id);

	public <E extends VersionedModel> E getModel();

	public <E extends VersionedModel> void setModel(E model);

	public <E extends VersionedInstance> E getModelInstance();

	public <E extends VersionedInstance> void setModelInstance(E modelInstance);
	
	public void setVersionInfo(VersionInfo versionInfo);
	
	public void setStatus(VersionStatus status);
	public void setEffectStartDate(Date date);
	public void setEffectEndDate(Date date);
	public void setLockedFor(Long lockedFor);
	public void setCreationUserNote(String creationUserNote);
	public void setCreationReason(VersionActivityReason creationReason);
	public void setExpiryUserNote(String expiryUserNote);
	public void setExpiryReason(VersionActivityReason expiryReason);
	public void setLatestArchived(boolean latestArchived);
		
	public VersionStatus getStatus();
	public Date getEffectStartDate();
	public Date getEffectEndDate();
	public Long getLockedFor();
	public boolean isLatestArchived() ;
	public String getCreationUserNote();
	public VersionActivityReason getCreationReason();
	public String getExpiryUserNote();
	public VersionActivityReason getExpiryReason();
}