package com.prinova.messagepoint.platform.services.admin;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.web.multipart.MultipartFile;

import com.prinova.messagepoint.model.BackgroundTheme;
import com.prinova.messagepoint.model.SystemTheme;
import com.prinova.messagepoint.model.tenant.TenantThemeInfo;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleResourceAwareServiceExeContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.theme.ThemeManager;
import com.prinova.messagepoint.util.HibernateUtil;

public class UpdateThemeService extends AbstractService {

	public static final String SERVICE_NAME = "admin.UpdateThemeService";
	private static final Log log = LogUtil.getLog(UpdateThemeService.class);

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			UpdateThemeServiceRequest request = (UpdateThemeServiceRequest) context.getRequest();

			TenantThemeInfo themeInfo = HibernateUtil.getManager().getObject(TenantThemeInfo.class, request.getThemeInfoId());
			
			if(request.isResetHeaderLogo()){
				themeInfo.resetHeaderLogo();
			}else if(request.isResetProviderLogo()){
				themeInfo.resetProviderLog();
			}else{
				MultipartFile corpFile = request.getCorporateLogo();
				if(corpFile != null && !corpFile.isEmpty()){
					String location = ThemeManager.addCorporateLogo(themeInfo, corpFile.getOriginalFilename(), corpFile.getBytes());
					themeInfo.setLogoLocation(location);
				}
				
				MultipartFile providerFile = request.getProviderLogo();
				if(providerFile != null && !providerFile.isEmpty()){
					String location = ThemeManager.addProviderLogo(themeInfo, providerFile.getOriginalFilename(), providerFile.getBytes());
					themeInfo.setProviderLogoLocation(location);
				}		
				
				themeInfo.setHeaderText(request.getHeadertext());
				themeInfo.setProviderText(request.getProvidertext());
				themeInfo.setBackgroundTheme(request.getBackgroundTheme());
				themeInfo.setSystemTheme(request.getSystemTheme());
				themeInfo.setHeaderThemeTypeId(request.getHeaderThemeTypeId());
				themeInfo.setHeaderThemeColor(request.getHeaderThemeColor());
			}
			HibernateUtil.getManager().saveObject(themeInfo);

			UpdateThemeServiceResponse response = (UpdateThemeServiceResponse) context.getResponse();
			response.setOwnerId(themeInfo.getOwner().getId());

		} catch (Exception e) {
			log.error(" unexpected exception when invoking UpdateLocaleSettingsService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e.toString(),
				context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub

	}

	public static ServiceExecutionContext createContext(Long themeInfoId,	
														MultipartFile corporateLogo,
														MultipartFile providerLogo,
														String headertext,
														String providertext,
														SystemTheme systemTheme,
														BackgroundTheme backgroundTheme,
														int headerThemeTypeId,
														String themeColor) {

		SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
		context.setServiceName(SERVICE_NAME);

		UpdateThemeServiceRequest request = new UpdateThemeServiceRequest();
		context.setRequest(request);

		request.setBackgroundTheme(backgroundTheme);
		request.setCorporateLogo(corporateLogo);
		request.setProviderLogo(providerLogo);
		request.setHeadertext(headertext);
		request.setProvidertext(providertext);
		request.setSystemTheme(systemTheme);
		request.setThemeInfoId(themeInfoId);
		request.setHeaderThemeTypeId(headerThemeTypeId);
		request.setHeaderThemeColor(themeColor);

		UpdateThemeServiceResponse serviceResp = new UpdateThemeServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}
	
	public static ServiceExecutionContext createContextForResetHeaderLogo(Long themeInfoId){
		SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
		context.setServiceName(SERVICE_NAME);

		UpdateThemeServiceRequest request = new UpdateThemeServiceRequest();
		context.setRequest(request);

		request.setResetHeaderLogo(true);
		request.setThemeInfoId(themeInfoId);
		
		UpdateThemeServiceResponse serviceResp = new UpdateThemeServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;		
	}
	
	public static ServiceExecutionContext createContextForResetProviderLogo(Long themeInfoId){
		SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
		context.setServiceName(SERVICE_NAME);

		UpdateThemeServiceRequest request = new UpdateThemeServiceRequest();
		context.setRequest(request);

		request.setResetProviderLogo(true);
		request.setThemeInfoId(themeInfoId);

		UpdateThemeServiceResponse serviceResp = new UpdateThemeServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;				
	}
}