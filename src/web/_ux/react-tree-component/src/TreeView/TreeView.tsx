import React, { Component } from 'react';
import { TreeViewNode } from './TreeViewNode'
import { ITreeData } from '.'
import _ from 'lodash';

interface TreeViewProps {
    data: ITreeData[];
    selectedNode?: number;
    treeUpdateSubscriber(update: (data: ITreeData[]) => void): void;
}

interface TreeViewState {
    treeData: ITreeData[],
    selectedNodeId?: number,

}

interface TreeIdMap<TMappedType> {
    [key: number]: TMappedType;
}

export class TreeView extends React.Component<TreeViewProps, TreeViewState> {

    private treeDataMap: TreeIdMap<ITreeData> = {}

    private treeUpdateSubscribers: TreeIdMap<TreeViewNode> = {};

    constructor(props: TreeViewProps) {
        super(props);
        this.state = {
            treeData: props.data,
            selectedNodeId: props.selectedNode        
        }

        this.receiveUpdatedTree = this.receiveUpdatedTree.bind(this);

        props.treeUpdateSubscriber(this.receiveUpdatedTree);
        props.data.forEach(x => this.normalizeTree(x));
    }

    receiveUpdatedTree(update: ITreeData[]) {
        console.log('the tree was updated');
        
        if (_.isArray(update)) {
            this.treeDataMap = {};
            update.forEach(x => this.normalizeTree(x));
        }


        Object.keys(this.treeDataMap).forEach(x => {

            let key = parseInt(x);

            if (_.has(this.treeUpdateSubscribers, key)) {
                this.treeUpdateSubscribers[key].receiveUpdatedNode(this.treeDataMap[key])
            }
        });
        

    }

    addTreeUpdateSubscriber(node: TreeViewNode) {
        console.log('add sub: ' + node.state.node.id);
        this.treeUpdateSubscribers[node.state.node.id] = node;
    }

    removeTreeUpdateSubscriber(node: TreeViewNode) {
        console.log('rm sub: ' + node.state.node.id);
        delete this.treeUpdateSubscribers[node.state.node.id]
    }

    render() {
        return (<TreeViewNode treeView={this} node={this.state.treeData[0]} level={0} expanded={true} key={this.state.treeData[0].id} />)
    }

    normalizeTree(data: ITreeData): void {
        console.log(data);
        this.treeDataMap[data.id] = data;


        if (_.isArray(data.children)) {
            data.children.forEach(x => this.normalizeTree(x));
        }
    }

    isSelected(node: ITreeData) {
        return this.state.selectedNodeId === node.id;
    }

    hasSelectedChild(node: ITreeData): boolean {

        if (_.isArray(node.children)) {
            if (node.children.filter(x => this.state.selectedNodeId == x.id).length > 0) {
                return true;
            }
        }

        if (_.isArray(node.children)) {
            return node.children.filter(x => this.hasSelectedChild(x)).length > 0
        }

        return false;
    }

    handleNodeSelection(node: ITreeData) {
        this.setState(state => ({
            selectedNodeId: node.id
        }));
    }
}

export default TreeView;