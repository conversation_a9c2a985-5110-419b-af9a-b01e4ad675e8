package com.prinova.messagepoint.model.rationalizer;

import ai.mpr.marcie.content.rationalizer.misc.JsonArrayBuilder;
import ai.mpr.marcie.content.rationalizer.misc.JsonObjectBuilder;
import ai.mpr.marcie.reindex.common.record.idandhashes.ContentIdAndHashesDTO;
import ai.mpr.marcie.reindex.common.record.idandhashes.ContentIdAndHashesList;
import com.google.gson.JsonArray;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.mapper.RationalizerDocumentToJsonMapper;
import com.prinova.messagepoint.model.metadata.HistoricalMetadataForm;
import com.prinova.messagepoint.model.metadata.MetadataForm;
import com.prinova.messagepoint.model.metadata.MetadataFormItem;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchHandler;
import com.prinova.messagepoint.platform.services.elasticsearch.action.duplicates.ContentWorkQueueWithFirstId;
import com.prinova.messagepoint.platform.services.rationalizer.RationalizerIndexingScheduler;
import com.prinova.messagepoint.platform.services.rationalizer.dto.RationalizerContentCommandDeleteDto;
import com.prinova.messagepoint.platform.services.rationalizer.dto.RationalizerContentCommandIndexDto;
import com.prinova.messagepoint.platform.services.rationalizer.dto.RationalizerMarcieFieldsDocumentContentUpdateCommandDto;
import com.prinova.messagepoint.platform.services.rationalizer.dto.RationalizerMarcieFieldsMultipleDeleteCommandsDto;
import com.prinova.messagepoint.platform.services.rationalizer.dto.RationalizerMarcieFieldsSharedContentUpdateCommandDto;
import com.prinova.messagepoint.platform.services.rationalizer.dto.RationalizerSharedContentCommandIndexDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.*;
import java.util.stream.Collectors;

import static com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent.MESSAGE_NAME;
import static com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent.ORDER;
import static com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent.ZONE_CONNECTOR;
import static com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent.buildDocumentContentGuidForElasticSearchGuid;
import static com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent.buildSharedContentGuidForElasticSearchGuid;
import static com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils.calculateContentHash;
import static com.prinova.messagepoint.platform.services.rationalizer.removeduplicates.RemoveDuplicatesNormalizeHtml.normalizeHtmlText;

public interface RationalizerContent {

    Log log = LogUtil.getLog(RationalizerContent.class);
    String EMPTY_MARKUP_CONTENT = "<p></p>";


    static RationalizerContent findByFormItemId(long formItemId) {
        RationalizerContent rationalizerContent = RationalizerDocumentContent.findDocumentContentByFormItemId(formItemId);
        if (rationalizerContent != null) {
            return rationalizerContent;
        }

        rationalizerContent = RationalizerSharedContent.findSharedContentByFormItemId(formItemId);
        return rationalizerContent;
    }

    long getId();
    String getName();
    String getDescription();
    String getMetatags();
    void setMetatags(String metatags);
    String getTextContent();
    void setTextContent(String textContent);
    String getMarkupContent();
    default String computeNormalizedMarkupContent() {
        return normalizeHtmlText(getMarkupContent());
    }

    void setMarkupContent(String markupContent);
    void setMarkupContent(String markupContent, boolean normalizeHtml);
    String buildDisplayedTextContent();
    String buildDisplayedMarkupContent();
    MetadataForm getParsedContentForm();
    void setParsedContentForm(MetadataForm parsedContentForm);
    Set<HistoricalMetadataForm> getHistMetadataContentForms();
    String getGuid();
    String getHashCode();
    String buildElasticSearchGuid();
    String findMetadataValueByDefinitionItem(MetadataFormItemDefinition itemDefinition);
    MetadataFormItem findFirstFormItemByItemDefinition(MetadataFormItemDefinition itemDefinition);
    String findMetadataValueByItemDefinitionPrimaryConnector(String itemDefinitionPrimaryConnector);
    void save();

    default void setLastAction(RationalizerActionTypeEnum lastActionOnContent) {
    }

    static boolean contentExistsInDatabase(
            String elasticSearchGuid,
            RationalizerApplication rationalizerApplication
    ) {
        final List<RationalizerContent> contentsInDatabase = findRationalizerContentsByElasticSearchGuidsAndApplication(Collections.singletonList(elasticSearchGuid), rationalizerApplication);
        return !contentsInDatabase.isEmpty();
    }

    static List<RationalizerContent> findFirstRationalizerContentsByHashes(
            RationalizerElasticSearchHandler rationalizerElasticSearchHandler,
            Collection<String> hashes
    ) {
        if (CollectionUtils.isEmpty(hashes)) {
            return new LinkedList<>();
        }

        final Map<String, ContentWorkQueueWithFirstId.CountAndFirstGuid> hashToCountAndFirstGuidMap = rationalizerElasticSearchHandler.computeHashToCountAndFirstGuidMapForQuery(
                new JsonObjectBuilder()
                        .add("terms", new JsonObjectBuilder()
                                .add("hash", new JsonArrayBuilder()
                                        .addAll(
                                                hashes
                                        )
                                        .build())
                                .build())
                        .build(),
                null,
                1
        );

        final Set<String> elasticSearchGuids = hashes.stream()
                .map(hash -> {
                    final ContentWorkQueueWithFirstId.CountAndFirstGuid countAndFirstGuid = hashToCountAndFirstGuidMap.get(hash);
                    if (countAndFirstGuid == null) {
                        return null;
                    }

                    return countAndFirstGuid.getFirstGuid();
                })
                .filter(item -> item != null)
                .collect(Collectors.toCollection(LinkedHashSet::new));

        return RationalizerContent.findRationalizerContentsByElasticSearchGuidsAndApplication(elasticSearchGuids, rationalizerElasticSearchHandler.getRationalizerApplication());
    }

    static RationalizerContent findRationalizerContentByElasticSearchGuid(
            String elasticSearchGuid
    ) {
        if (StringUtils.isEmpty(elasticSearchGuid)) {
            return null;
        }

        final List<RationalizerContent> contents = findRationalizerContentsByElasticSearchGuids(List.of(elasticSearchGuid));
        if (CollectionUtils.isEmpty(contents)) {
            return null;
        }

        return contents.get(0);
    }

    static List<RationalizerContent> findRationalizerContentsByElasticSearchGuids(
            Collection<String> elasticSearchGuids
    ) {
        if (CollectionUtils.isEmpty(elasticSearchGuids)) {
            return new LinkedList<>();
        }

        final LinkedHashSet<String> sharedContentsGuids = elasticSearchGuids.stream()
                .filter(elasticSearchGuid -> elasticSearchGuid != null)
                .filter(elasticSearchGuid -> RationalizerSharedContent.isElasticSearchGuidForSharedContent(elasticSearchGuid))
                .map(elasticSearchGuid -> buildSharedContentGuidForElasticSearchGuid(elasticSearchGuid))
                .collect(Collectors.toCollection(LinkedHashSet::new));

        final LinkedHashSet<String> documentContentsGuids = elasticSearchGuids.stream()
                .filter(elasticSearchGuid -> elasticSearchGuid != null)
                .filter(elasticSearchGuid -> !RationalizerSharedContent.isElasticSearchGuidForSharedContent(elasticSearchGuid))
                .map(elasticSearchGuid -> buildDocumentContentGuidForElasticSearchGuid(elasticSearchGuid))
                .collect(Collectors.toCollection(LinkedHashSet::new));

        final LinkedHashMap<String, RationalizerDocumentContent> documentContentsMap = RationalizerDocumentContent.findContentsByGuids(
                documentContentsGuids
        ).stream()
                .map(documentContent -> Pair.of(documentContent.buildElasticSearchGuid(), documentContent))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, LinkedHashMap::new));

        final LinkedHashMap<String, RationalizerSharedContent> sharedContentsMap = RationalizerSharedContent.findSharedContentsBySharedContentsGuids(
                sharedContentsGuids
        ).stream()
                .map(sharedContent -> Pair.of(sharedContent.buildElasticSearchGuid(), sharedContent))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, LinkedHashMap::new));

        final LinkedList<RationalizerContent> rationalizerContents = elasticSearchGuids.stream()
                .map(elasticSearchGuid -> {
                    RationalizerContent rationalizerContent;
                    boolean isSharedContent = RationalizerSharedContent.isElasticSearchGuidForSharedContent(elasticSearchGuid);
                    if (isSharedContent) {
                        rationalizerContent = sharedContentsMap.get(elasticSearchGuid);
                    } else {
                        rationalizerContent = documentContentsMap.get(elasticSearchGuid);
                    }

                    return rationalizerContent;
                })
                .filter(content -> content != null)
                .collect(Collectors.toCollection(LinkedList::new));

        return rationalizerContents;
    }

    static List<RationalizerContent> findRationalizerContentsByElasticSearchGuidsAndApplication(
            Collection<String> elasticSearchGuids,
            RationalizerApplication rationalizerApplication
    ) {
        if (CollectionUtils.isEmpty(elasticSearchGuids)) {
            return new LinkedList<>();
        }

        final LinkedHashSet<String> sharedContentsGuids = elasticSearchGuids.stream()
                .filter(elasticSearchGuid -> elasticSearchGuid != null)
                .filter(elasticSearchGuid -> RationalizerSharedContent.isElasticSearchGuidForSharedContent(elasticSearchGuid))
                .map(elasticSearchGuid -> buildSharedContentGuidForElasticSearchGuid(elasticSearchGuid))
                .collect(Collectors.toCollection(LinkedHashSet::new));

        final LinkedHashSet<String> documentContentsGuids = elasticSearchGuids.stream()
                .filter(elasticSearchGuid -> elasticSearchGuid != null)
                .filter(elasticSearchGuid -> !RationalizerSharedContent.isElasticSearchGuidForSharedContent(elasticSearchGuid))
                .map(elasticSearchGuid -> buildDocumentContentGuidForElasticSearchGuid(elasticSearchGuid))
                .collect(Collectors.toCollection(LinkedHashSet::new));

        final LinkedHashMap<String, RationalizerDocumentContent> documentContentsMap = RationalizerDocumentContent.findContentsByGuidsAndApplication(
                documentContentsGuids,
                rationalizerApplication
        ).stream()
                .map(documentContent -> Pair.of(documentContent.buildElasticSearchGuid(), documentContent))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, LinkedHashMap::new));

        final LinkedHashMap<String, RationalizerSharedContent> sharedContentsMap = RationalizerSharedContent.findSharedContentsBySharedContentsGuidsAndApplication(
                sharedContentsGuids,
                rationalizerApplication
        ).stream()
                .map(sharedContent -> Pair.of(sharedContent.buildElasticSearchGuid(), sharedContent))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, LinkedHashMap::new));

        final LinkedList<RationalizerContent> rationalizerContents = elasticSearchGuids.stream()
                .map(elasticSearchGuid -> {
                    RationalizerContent rationalizerContent;
                    boolean isSharedContent = RationalizerSharedContent.isElasticSearchGuidForSharedContent(elasticSearchGuid);
                    if (isSharedContent) {
                        rationalizerContent = sharedContentsMap.get(elasticSearchGuid);
                    } else {
                        rationalizerContent = documentContentsMap.get(elasticSearchGuid);
                    }

                    return rationalizerContent;
                })
                .filter(content -> content != null)
                .collect(Collectors.toCollection(LinkedList::new));

        return rationalizerContents;
    }

    RationalizerSharedContent computeRationalizerSharedContent();

    default RationalizerApplication computeRationalizerApplication() {
        if (this instanceof RationalizerSharedContent) {
            return ((RationalizerSharedContent) this).getRationalizerApplication();
        }

        return ((RationalizerDocumentContent) this).getRationalizerDocument().getRationalizerApplication();
    }

    static void sendContentsToContentIdAndHashesList(
            Collection<? extends RationalizerContent> contents,
            String workflowId,
            ContentIdAndHashesList contentIdAndHashesList
    ) {
        try {
            for (RationalizerContent crtContent : contents) {
                if (crtContent == null) {
                    continue;
                }
                if (crtContent instanceof RationalizerDocumentContent) {
                    RationalizerDocumentContent documentContent = (RationalizerDocumentContent) crtContent;
                    if (documentContent.getRationalizerSharedContent() != null) {
                        // Document Contents that are part of a shared content are not sent to ElasticSearch.
                        continue;
                    }
                }

                contentIdAndHashesList.put(ContentIdAndHashesDTO.builder()
                        .workflowId(workflowId)
                        .contentId(crtContent.buildElasticSearchGuid())
                        .textHash(calculateContentHash(crtContent.getTextContent()))
                        .htmlHash(calculateContentHash(crtContent.computeNormalizedMarkupContent()))
                        .build()
                );
            }
        } catch (InterruptedException e) {
            log.error("RationalizerContent - contentIdAndHashesList error", e);
        }
    }

    static void sendRationalizerContentsToElasticSearch(
            User principalUser,
            RationalizerElasticSearchHandler rationalizerElasticSearchHandler,
            Collection<String> elasticSearchGuids,
            Map<String, String> elasticSearchGuidToPreviousTextMap,
            Map<String, String> elasticSearchGuidToNewTextMap
    ) {

        if (CollectionUtils.isEmpty(elasticSearchGuids)) {
            return;
        }

        final LinkedHashSet<String> sharedContentsGuids = elasticSearchGuids.stream()
                .filter(elasticSearchGuid -> elasticSearchGuid != null)
                .filter(elasticSearchGuid -> RationalizerSharedContent.isElasticSearchGuidForSharedContent(elasticSearchGuid))
                .map(elasticSearchGuid -> buildSharedContentGuidForElasticSearchGuid(elasticSearchGuid))
                .collect(Collectors.toCollection(LinkedHashSet::new));

        final LinkedHashSet<String> documentContentsGuids = elasticSearchGuids.stream()
                .filter(elasticSearchGuid -> elasticSearchGuid != null)
                .filter(elasticSearchGuid -> !RationalizerSharedContent.isElasticSearchGuidForSharedContent(elasticSearchGuid))
                .map(elasticSearchGuid -> buildDocumentContentGuidForElasticSearchGuid(elasticSearchGuid))
                .collect(Collectors.toCollection(LinkedHashSet::new));

        final RationalizerApplication rationalizerApplication = rationalizerElasticSearchHandler.getRationalizerApplication();

        if (CollectionUtils.isNotEmpty(documentContentsGuids)) {
            final List<RationalizerDocumentContent> rationalizerDocumentContents = RationalizerDocumentContent.findContentsByGuidsAndApplication(documentContentsGuids, rationalizerApplication);
            JsonArray bulkData = new RationalizerDocumentToJsonMapper(rationalizerElasticSearchHandler).mapRationalizerDocumentContents(rationalizerDocumentContents);
            rationalizerElasticSearchHandler.sendBulkData(bulkData);

            if (rationalizerDocumentContents.size() < documentContentsGuids.size()) {
                // In this case some of the RationalizerContent objects were deleted from db meanwhile.
                // So in this case we must delete them and their dependencies from ElasticSearch.
                final LinkedHashSet<String> existingContentsGuids = rationalizerDocumentContents.stream()
                        .map(content -> content.getGuid())
                        .collect(Collectors.toCollection(LinkedHashSet::new));
                final LinkedHashSet<String> elasticSearchGuidsToDelete = documentContentsGuids.stream()
                        .filter(contentGuid -> !existingContentsGuids.contains(contentGuid))
                        .map(contentGuid -> RationalizerDocumentContent.buildElasticSearchGuidForDocumentContentGuid(contentGuid))
                        .collect(Collectors.toCollection(LinkedHashSet::new));
                rationalizerElasticSearchHandler.deleteContents(elasticSearchGuidsToDelete);
                rationalizerElasticSearchHandler.deleteMatchesSimilarityEntriesForContents(elasticSearchGuidsToDelete);
            }

            documentContentsGuids.forEach(documentContentGuid -> {
                RationalizerMarcieFieldsDocumentContentUpdateCommandDto updateCommandDto =
                        new RationalizerMarcieFieldsDocumentContentUpdateCommandDto() {{
                            setUser(principalUser);
                            setSchemaName(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier());
                            setRationalizerApplicationId(rationalizerApplication.getId());
                            setElasticApplicationId(rationalizerElasticSearchHandler.getElasticAppId());
                            setContentGuid(documentContentGuid);
                            setPreviousString(elasticSearchGuidToPreviousTextMap.get(documentContentGuid));
                            setNewString(elasticSearchGuidToNewTextMap.get(documentContentGuid));
                        }};
                RationalizerIndexingScheduler.addMarcieCommand(updateCommandDto);
            });
        }

        if (CollectionUtils.isNotEmpty(sharedContentsGuids)) {
            final List<RationalizerSharedContent> rationalizerSharedContents = RationalizerSharedContent.findSharedContentsBySharedContentsGuidsAndApplication(sharedContentsGuids, rationalizerApplication);
            JsonArray bulkData = new RationalizerDocumentToJsonMapper(rationalizerElasticSearchHandler).mapRationalizerSharedContents(rationalizerSharedContents);
            rationalizerElasticSearchHandler.sendBulkData(bulkData);

            if (rationalizerSharedContents.size() < sharedContentsGuids.size()) {
                // In this case some of the RationalizerSharedContent objects were deleted from db meanwhile.
                // This is possible because the code is written to delete an RationalizerSharedContent object if it has no RationalizerDocumentContent in their contents list.
                // So in this case we must delete them and their dependencies from ElasticSearch.
                final LinkedHashSet<String> existingSharedContentsGuids = rationalizerSharedContents.stream()
                        .map(sharedContent -> sharedContent.getGuid())
                        .collect(Collectors.toCollection(LinkedHashSet::new));
                final LinkedHashSet<String> elasticSearchGuidsToDelete = sharedContentsGuids.stream()
                        .filter(sharedContentGuid -> !existingSharedContentsGuids.contains(sharedContentGuid))
                        .map(sharedContentGuid -> RationalizerSharedContent.buildElasticSearchGuidForSharedContentGuid(sharedContentGuid))
                        .collect(Collectors.toCollection(LinkedHashSet::new));
                rationalizerElasticSearchHandler.deleteContents(elasticSearchGuidsToDelete);
                rationalizerElasticSearchHandler.deleteMatchesSimilarityEntriesForContents(elasticSearchGuidsToDelete);
            }

            sharedContentsGuids.forEach(sharedContentGuid -> {
                RationalizerMarcieFieldsSharedContentUpdateCommandDto updateCommandDto =
                        new RationalizerMarcieFieldsSharedContentUpdateCommandDto() {{
                            setUser(principalUser);
                            setSchemaName(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier());
                            setRationalizerApplicationId(rationalizerApplication.getId());
                            setElasticApplicationId(rationalizerElasticSearchHandler.getElasticAppId());
                            setSharedContentGuid(sharedContentGuid);
                            setPreviousString(elasticSearchGuidToPreviousTextMap.get(sharedContentGuid));
                            setNewString(elasticSearchGuidToNewTextMap.get(sharedContentGuid));
                        }};
                RationalizerIndexingScheduler.addMarcieCommand(updateCommandDto);
            });
        }
    }

    static void sendRationalizerContentsToElasticSearchDelayed(
            User principalUser,
            RationalizerElasticSearchHandler rationalizerElasticSearchHandler,
            Collection<String> elasticSearchGuids,
            Map<String, String> elasticSearchGuidToPreviousTextMap,
            Map<String, String> elasticSearchGuidToNewTextMap
    ) {

        if (CollectionUtils.isEmpty(elasticSearchGuids)) {
            return;
        }

        final LinkedHashSet<String> sharedContentsGuids = elasticSearchGuids.stream()
                .filter(elasticSearchGuid -> elasticSearchGuid != null)
                .filter(elasticSearchGuid -> RationalizerSharedContent.isElasticSearchGuidForSharedContent(elasticSearchGuid))
                .map(elasticSearchGuid -> buildSharedContentGuidForElasticSearchGuid(elasticSearchGuid))
                .collect(Collectors.toCollection(LinkedHashSet::new));

        final LinkedHashSet<String> documentContentsGuids = elasticSearchGuids.stream()
                .filter(elasticSearchGuid -> elasticSearchGuid != null)
                .filter(elasticSearchGuid -> !RationalizerSharedContent.isElasticSearchGuidForSharedContent(elasticSearchGuid))
                .map(elasticSearchGuid -> buildDocumentContentGuidForElasticSearchGuid(elasticSearchGuid))
                .collect(Collectors.toCollection(LinkedHashSet::new));

        final RationalizerApplication rationalizerApplication = rationalizerElasticSearchHandler.getRationalizerApplication();
        final String tenantIdentifier = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();

        if (CollectionUtils.isNotEmpty(documentContentsGuids)) {
            RationalizerContentCommandIndexDto documentContentCommandIndexDto = new RationalizerContentCommandIndexDto() {{
                setSchemaName(tenantIdentifier);
                setRationalizerApplicationId(rationalizerApplication.getId());
                setContentGuids(documentContentsGuids);
            }};
            RationalizerIndexingScheduler.addContentCommand(documentContentCommandIndexDto);

            documentContentsGuids.forEach(documentContentGuid -> {
                RationalizerMarcieFieldsDocumentContentUpdateCommandDto updateCommandDto =
                        new RationalizerMarcieFieldsDocumentContentUpdateCommandDto() {{
                            setUser(principalUser);
                            setSchemaName(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier());
                            setRationalizerApplicationId(rationalizerApplication.getId());
                            setElasticApplicationId(rationalizerElasticSearchHandler.getElasticAppId());
                            setContentGuid(documentContentGuid);
                            setPreviousString(elasticSearchGuidToPreviousTextMap.get(documentContentGuid));
                            setNewString(elasticSearchGuidToNewTextMap.get(documentContentGuid));
                        }};
                RationalizerIndexingScheduler.addMarcieCommand(updateCommandDto);
            });
        }

        if (CollectionUtils.isNotEmpty(sharedContentsGuids)) {
            RationalizerSharedContentCommandIndexDto sharedContentCommandIndexDto = new RationalizerSharedContentCommandIndexDto() {{
                setSchemaName(tenantIdentifier);
                setRationalizerApplicationId(rationalizerApplication.getId());
                setSharedContentsGuids(sharedContentsGuids);
            }};
            RationalizerIndexingScheduler.addContentCommand(sharedContentCommandIndexDto);

            sharedContentsGuids.forEach(sharedContentGuid -> {
                RationalizerMarcieFieldsSharedContentUpdateCommandDto updateCommandDto =
                        new RationalizerMarcieFieldsSharedContentUpdateCommandDto() {{
                            setUser(principalUser);
                            setSchemaName(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier());
                            setRationalizerApplicationId(rationalizerApplication.getId());
                            setElasticApplicationId(rationalizerElasticSearchHandler.getElasticAppId());
                            setSharedContentGuid(sharedContentGuid);
                            setPreviousString(elasticSearchGuidToPreviousTextMap.get(sharedContentGuid));
                            setNewString(elasticSearchGuidToNewTextMap.get(sharedContentGuid));
                        }};
                RationalizerIndexingScheduler.addMarcieCommand(updateCommandDto);
            });
        }
    }

    static void deleteRationalizerContentsFromElasticSearch(
            User principalUser,
            RationalizerElasticSearchHandler rationalizerElasticSearchHandler,
            Collection<String> elasticSearchGuids
    ) {
        if (CollectionUtils.isEmpty(elasticSearchGuids)) {
            return;
        }

        rationalizerElasticSearchHandler.deleteContents(elasticSearchGuids);

        final RationalizerApplication rationalizerApplication = rationalizerElasticSearchHandler.getRationalizerApplication();

        RationalizerMarcieFieldsMultipleDeleteCommandsDto multipleDeleteCommandsDto = new RationalizerMarcieFieldsMultipleDeleteCommandsDto() {{
            setUser(principalUser);
            setSchemaName(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier());
            setRationalizerApplicationId(rationalizerApplication.getId());
            setElasticApplicationId( rationalizerElasticSearchHandler.getElasticAppId());
            setElasticSearchGuids(elasticSearchGuids);
        }};

        RationalizerIndexingScheduler.addMarcieCommand(multipleDeleteCommandsDto);
    }

    static void deleteRationalizerContentsFromElasticSearchDelayed(
            User principalUser,
            RationalizerElasticSearchHandler rationalizerElasticSearchHandler,
            Collection<String> elasticSearchGuids
    ) {
        if (CollectionUtils.isEmpty(elasticSearchGuids)) {
            return;
        }

        final RationalizerApplication rationalizerApplication = rationalizerElasticSearchHandler.getRationalizerApplication();
        final String tenantIdentifier = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();

        RationalizerContentCommandDeleteDto contentCommandDeleteDto = new RationalizerContentCommandDeleteDto() {{
            setSchemaName(tenantIdentifier);
            setRationalizerApplicationId(rationalizerApplication.getId());
            setContentGuids(elasticSearchGuids);
        }};
        RationalizerIndexingScheduler.addContentCommand(contentCommandDeleteDto);

        RationalizerMarcieFieldsMultipleDeleteCommandsDto multipleDeleteCommandsDto = new RationalizerMarcieFieldsMultipleDeleteCommandsDto() {{
            setUser(principalUser);
            setSchemaName(tenantIdentifier);
            setRationalizerApplicationId(rationalizerApplication.getId());
            setElasticApplicationId( rationalizerElasticSearchHandler.getElasticAppId());
            setElasticSearchGuids(elasticSearchGuids);
        }};
        RationalizerIndexingScheduler.addMarcieCommand(multipleDeleteCommandsDto);
    }

    static Set<String> buildElasticSearchGuids(Collection<? extends RationalizerContent> rationalizerContents) {
        if (CollectionUtils.isEmpty(rationalizerContents)) {
            return new LinkedHashSet<>();
        }

        return rationalizerContents.stream()
                .map(content -> content.buildElasticSearchGuid())
                .collect(Collectors.toCollection(LinkedHashSet::new));
    }

    default void setContentMetadataFormItemValue(MetadataFormItem currentFormItem, String newFormItemValue) {
        if (currentFormItem == null) {
            return;
        }

        currentFormItem.setValue(newFormItemValue);
        currentFormItem.save();

        if (this instanceof RationalizerSharedContent) {
            return;
        }

        RationalizerDocumentContent documentContent = (RationalizerDocumentContent) this;

        final MetadataFormItemDefinition itemDefinition = currentFormItem.getItemDefinition();
        if (itemDefinition == null) {
            return;
        }

        final String primaryConnector = itemDefinition.getPrimaryConnector();
        if (StringUtils.isEmpty(primaryConnector)) {
            return;
        }

        if (StringUtils.equalsIgnoreCase(ORDER, primaryConnector)) {
            try {
                final int orderValue = Integer.parseInt(newFormItemValue);
                documentContent.setOrder(orderValue);
                documentContent.save();
            } catch (NumberFormatException ex) {
                log.error(ex.getMessage(), ex);
            }

            return;
        }

        if (StringUtils.equalsIgnoreCase(ZONE_CONNECTOR, primaryConnector)) {
            documentContent.setZoneConnector(newFormItemValue);
            documentContent.save();
            return;
        }

        if (StringUtils.equalsIgnoreCase(MESSAGE_NAME, primaryConnector)) {
            documentContent.setMessageName(newFormItemValue);
            documentContent.save();
            return;
        }
    }
}
