package com.prinova.messagepoint.platform.services.rationalizer;

import ai.mpr.marcie.content.rationalizer.ApplicationException;
import ai.mpr.marcie.content.rationalizer.ApplicationService;
import ai.mpr.marcie.reindex.client.main.contentreindex.AwsProcessorContentReindex;
import ai.mpr.marcie.reindex.common.model.AwsExecutionImportInfo;
import ai.mpr.marcie.reindex.common.model.AwsExecutionResult;
import ai.mpr.marcie.reindex.common.model.AwsExecutionStatus;
import ai.mpr.marcie.reindex.common.model.AwsWorkflowExecutedByEnum;
import ai.mpr.marcie.reindex.common.model.AwsWorkflowHandlerElasticAppMeta;
import ai.mpr.marcie.reindex.common.model.AwsWorkflowInfo;
import ai.mpr.marcie.reindex.common.record.idandhashes.ContentIdAndHashesDTO;
import ai.mpr.marcie.reindex.common.record.idandhashes.ContentIdAndHashesList;
import ai.mpr.marcie.reindex.common.stats.GlobalMarcieStats;
import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.model.filters.WordCountFilter;
import com.prinova.messagepoint.model.rationalizer.HistoricalRationalizerSharedContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplicationSyncStatus;
import com.prinova.messagepoint.model.rationalizer.RationalizerContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocument;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.aws.util.ContentReindexGUIDProviderImpl;
import com.prinova.messagepoint.platform.services.aws.util.ContentReindexMetadataProviderImpl;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchHandler;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.RationalizerUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.hibernate.Session;

import java.io.File;
import java.net.InetAddress;
import java.text.MessageFormat;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static ai.mpr.marcie.reindex.client.main.contentreindex.AwsProcessorContentReindex.CONTENT_REINDEX_ACTION_NAME;
import static ai.mpr.marcie.reindex.common.elasticsearch.ElasticsearchUtil.buildElasticsearchScallingService;
import static ai.mpr.marcie.reindex.common.stats.ContentImportStatsUtils.writeMarcieStatsToApplicationMeta;
import static com.prinova.messagepoint.model.rationalizer.RationalizerDocument.sendDocumentsToElasticSearchWithoutRecomputingMarcieFields;
import static com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent.processSharedContentsGuidsForBackgroundTask;
import static com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils.calculateContentHash;
import static com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils.createElasticSearchApplicationMetadata;
import static com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils.isLargeApplication;

public class CloneRationalizerAppBackgroundTask extends MessagePointRunnable {

    private static final Log log = LogUtil.getLog(CloneRationalizerAppBackgroundTask.class);
    private static final int SHARED_CONTENT_BATCH_SIZE = 100;
    private static final int DOCUMENT_CONTENT_BATCH_SIZE = 1000;
    private final Long rationalizerAppId;
    private final User requestor;
    private final String cloneName;
    private final StatusPollingBackgroundTask statusPollingBackgroundTask;
    private RationalizerApplication clonedRationalizerApp;

    public CloneRationalizerAppBackgroundTask(Long rationalizerAppId, User requestor, String cloneName) {
        this.rationalizerAppId = rationalizerAppId;
        this.requestor = requestor;
        this.cloneName = cloneName;
        this.statusPollingBackgroundTask = new StatusPollingBackgroundTask();
        this.statusPollingBackgroundTask.setCreatedBy(requestor.getId());
    }

    @Override
    public void performMainProcessing() {
        RationalizerApplication rationalizerApp = RationalizerApplication.findById(rationalizerAppId);
        if (rationalizerApp == null) {
            return;
        }

        String workflowId = new ContentReindexGUIDProviderImpl().getRawGUID();
        String rationalizerAppName = rationalizerApp.getName();
        LogUtil.getLog(this.getClass()).info(MessageFormat.format("[workflowId={0}] [rationalizerApplicationId={1}]  Starting CloneRationalizerAppBackgroundTask for application with name \"{2}\".",
                        workflowId,
                        Long.toString(rationalizerAppId),
                        rationalizerAppName
                )
        );
        Session session = HibernateUtil.getManager().getSession();
        try {
            RationalizerIndexingScheduler.increaseRationalizerApplicationLockCount(
                    MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier(),
                    rationalizerAppId
            );


            this.statusPollingBackgroundTask.setType(StatusPollingBackgroundTask.TYPE_RATIONALIZER_APP_CLONE);
            this.statusPollingBackgroundTask.setName(StatusPollingBackgroundTask.SUB_TYPE_RATIONALIZER_APP_CLONE);
            this.statusPollingBackgroundTask.setDescription(rationalizerApp.getName());
            this.statusPollingBackgroundTask.setTargetObjectId(rationalizerAppId);
            this.statusPollingBackgroundTask.setRequestDate(new Date());
            this.statusPollingBackgroundTask.setBackgroundThread(this.getOwningThread());
            this.statusPollingBackgroundTask.setBackgroundThreadId(this.getOwningThread().getId());
            this.statusPollingBackgroundTask.setProgressInPercentInThread(0);
            this.statusPollingBackgroundTask.setActive(true);
            this.statusPollingBackgroundTask.save();

            // Clone the Rationalizer Application
            clonedRationalizerApp = (RationalizerApplication) rationalizerApp.clone();
            clonedRationalizerApp.setName(cloneName);

            clonedRationalizerApp.setAppSyncStatus(RationalizerApplicationSyncStatus.SYNCHRONIZATION_IN_PROGRESS.getStatusValue());

            session.save(clonedRationalizerApp);

            List<DashboardFilter> originalDashboardFilters = rationalizerApp.getDashboardFilters();
            List<DashboardFilter> dashboardFilters = CollectionUtils.isEmpty(originalDashboardFilters) ?
                    WordCountFilter.getRationalizerApplicationDefaultWordCountFilters(clonedRationalizerApp.getId()) :  originalDashboardFilters;
            if(CollectionUtils.isNotEmpty(dashboardFilters)) {
                for (DashboardFilter dashboardFilter : dashboardFilters) {
                    DashboardFilter newDashboardFilter = new WordCountFilter(DashboardFilter.AppliesTo.RT, clonedRationalizerApp.getId(),
                            DashboardFilter.Section.valueOf(dashboardFilter.getSection()),
                            DashboardFilter.Operator.valueOf(dashboardFilter.getOperation()),
                            dashboardFilter.getNormalizedValue()
                    );
                    session.save(newDashboardFilter);
                }
            }

            int contentCount = clonedRationalizerApp.computeContentCount();

            RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
            RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(clonedRationalizerApp);

            if (!rationalizerElasticSearchHandler.createIndex(createElasticSearchApplicationMetadata(rationalizerApp), isLargeApplication(contentCount))) {
                throw new ApplicationException(
                        MessageFormat.format("ElasticSearch index {0} could not be created!",
                                rationalizerElasticSearchHandler.getElasticAppId())
                );
            }

            String clonedApplicationGuid = rationalizerElasticSearchHandler.getElasticAppId();
            LogUtil.getLog(this.getClass()).info(MessageFormat.format("[workflowId={0}][cloneRationalizerApplicationId={1}][elasticAppIdForClone={2}] Computed Elastic application id for cloned application.",
                    workflowId,
                    clonedRationalizerApp.getId(),
                    clonedApplicationGuid)
            );

            this.statusPollingBackgroundTask.setProgressInPercentInThread(10);

            String awsExternalElasticUri = new ContentReindexMetadataProviderImpl().getAwsElasticUri();
            ApplicationService applicationService = buildElasticsearchScallingService(awsExternalElasticUri, null);

            AwsExecutionImportInfo awsExecutionImportInfo = new AwsExecutionImportInfo();
            ContentIdAndHashesList contentIdAndHashesList = new ContentIdAndHashesList();
            try {
                // GlobalMarcieStats instance must be created in the same try block
                // in which RationalizerReindexBackgroundTask is created
                // because RationalizerReindexBackgroundTask will release the instance.
                GlobalMarcieStats.createInstance(workflowId);


                AwsWorkflowHandlerElasticAppMeta awsWorkflowHandler = new AwsWorkflowHandlerElasticAppMeta(awsExternalElasticUri);
                AwsWorkflowInfo awsWorkflowInfo = new AwsWorkflowInfo(awsWorkflowHandler) {{
                    setWorkflowId(workflowId);
                    setElasticAppId(clonedApplicationGuid);
                    setClientIP(InetAddress.getLocalHost().getHostAddress());
                    setExecutedBy(AwsWorkflowExecutedByEnum.CLIENT);
                }};
                awsWorkflowInfo.write();
                log.debug(awsWorkflowInfo);

                awsExecutionImportInfo.setActionName(CONTENT_REINDEX_ACTION_NAME);
                awsExecutionImportInfo.setElasticAppId(clonedApplicationGuid);
                awsExecutionImportInfo.setWorkflowId(workflowId);
                awsExecutionImportInfo.setStartDateTime(Instant.now());
                awsExecutionImportInfo.setAwsExecutionStatus(AwsExecutionStatus.IN_PROGRESS);
                awsExecutionImportInfo.getApplicationStatsToImportInfoMap().putAll(
                        AwsExecutionImportInfo.buildApplicationStatsToImportInfoMap(new AwsExecutionResult(awsWorkflowInfo) {{
                            setChangesElasticRecordsRawData(true);
                            getContentReindexActions().addAll(AwsProcessorContentReindex.CONTENT_REINDEX_ACTIONS_LIST);
                        }})
                );
                writeMarcieStatsToApplicationMeta(applicationService, awsExecutionImportInfo);

                RationalizerReindexBackgroundTask rationalizerReindexBackgroundTask = new RationalizerReindexBackgroundTask(
                        clonedRationalizerApp,
                        workflowId,
                        contentIdAndHashesList,
                        requestor,
                        RationalizerReindexType.COMPLETE_REINDEX
                );
                MessagePointRunnableUtil.startThread(rationalizerReindexBackgroundTask, Thread.MAX_PRIORITY);

                Set<String> newSharedContentGuids = new LinkedHashSet<>();

                final Map<Long, Integer> documentIdToContentsCountMap = rationalizerApp.computeDocumentIdToContentsCountMap();
                final LinkedList<Pair<Long, Integer>> documentIdToContentsCountPairs = documentIdToContentsCountMap.entrySet().stream()
                        .map(entry -> Pair.of(entry.getKey(), entry.getValue()))
                        .collect(Collectors.toCollection(LinkedList::new));
                final double totalContents = documentIdToContentsCountPairs.stream()
                        .mapToDouble(pair -> pair.getValue().doubleValue())
                        .sum();
                double processedContents = 0;

                int crtBatchSize = DOCUMENT_CONTENT_BATCH_SIZE;
                Set<Long> tmpDocumentIds = new LinkedHashSet<>();
                do {

                    if (!documentIdToContentsCountPairs.isEmpty()) {
                        final Pair<Long, Integer> tmpDocumentIdToContentsCount = documentIdToContentsCountPairs.remove(0);
                        final Long tmpDocumentId = tmpDocumentIdToContentsCount.getKey();
                        tmpDocumentIds.add(tmpDocumentId);

                        final Integer tmpContentsCount = tmpDocumentIdToContentsCount.getValue();
                        crtBatchSize-= tmpContentsCount;

                        processedContents += tmpContentsCount;
                    }

                    boolean processNextSlice = false;
                    if (crtBatchSize <= 0) {
                        crtBatchSize = DOCUMENT_CONTENT_BATCH_SIZE;

                        processNextSlice = true;
                    }
                    processNextSlice = processNextSlice || documentIdToContentsCountPairs.isEmpty();

                    if (processNextSlice) {
                        List<RationalizerDocument> rationalizerDocList =
                                RationalizerApplication.findDocumentsByApplicationAndDocumentIdsEagerly(rationalizerApp, tmpDocumentIds);
                        tmpDocumentIds = new LinkedHashSet<>();
                        List<RationalizerDocument> clonedDocList = new ArrayList<>();
                        for (RationalizerDocument rationalizerDoc : rationalizerDocList) {
                            RationalizerDocument clonedRationalizerDoc = rationalizerDoc.clone(clonedRationalizerApp);
                            clonedDocList.add(clonedRationalizerDoc);
                        }

                        saveSharedObjectsToDatabase(clonedDocList);

                        collectSharedContentGuidsForDocuments(clonedDocList, newSharedContentGuids);

                        processClonedDocuments(workflowId, rationalizerElasticSearchHandler, contentIdAndHashesList, clonedDocList);

                        deleteContentsWithSharedFromElastic(rationalizerElasticSearchHandler, clonedDocList);

                        double percentInDouble = 10 +  (processedContents / totalContents)  * 70;
                        this.statusPollingBackgroundTask.setProgressInPercentInThread((int) percentInDouble);
                    }
                } while (!documentIdToContentsCountPairs.isEmpty());

                processSharedContentsGuidsForBackgroundTask(rationalizerElasticSearchHandler, workflowId, contentIdAndHashesList, newSharedContentGuids, SHARED_CONTENT_BATCH_SIZE);

                rationalizerElasticSearchHandler.refreshIndex();

                this.statusPollingBackgroundTask.setProgressInPercentInThread(80);

                awsExecutionImportInfo.setAwsExecutionStatus(AwsExecutionStatus.SUCCESS);
            } catch (Exception ex) {
                awsExecutionImportInfo.setAwsExecutionStatus(AwsExecutionStatus.ERROR);
                awsExecutionImportInfo.setErrorMessage(ex.getMessage());
                awsExecutionImportInfo.setException(ex);

                throw ex;
            } finally {
                try {
                    awsExecutionImportInfo.setEndDateTime(Instant.now());
                    writeMarcieStatsToApplicationMeta(applicationService, awsExecutionImportInfo);
                    log.debug(awsExecutionImportInfo);

                    // We do not release the GlobalMarcieStats instance here.
                    // It will be released from RationalizerReindexBackgroundTask.
                } finally {
                    // The last DTO is the marker that all the items were added to the list.
                    contentIdAndHashesList.put(ContentIdAndHashesList.buildLastDTO(workflowId));
                }
            }

            this.statusPollingBackgroundTask.setProgressInPercentInThread(100);
            this.statusPollingBackgroundTask.setCompletedDate(new Date());
            this.statusPollingBackgroundTask.setComplete(true);
            if (clonedRationalizerApp.getAppSyncStatus() == RationalizerApplicationSyncStatus.SYNCHRONIZATION_IN_PROGRESS.getStatusValue()) {
                clonedRationalizerApp.setAppSyncStatus(RationalizerApplicationSyncStatus.SYNCHRONIZED.getStatusValue());
                session.save(clonedRationalizerApp);
            }
            copyDocumentFiles(rationalizerApp, clonedRationalizerApp, workflowId);

        } catch (Exception e) {
            log.error(MessageFormat.format("[workflowId={0}] Caught exception.",
                    workflowId), e
            );

            this.statusPollingBackgroundTask.setError(true);
            clonedRationalizerApp.setAppSyncStatus(RationalizerApplicationSyncStatus.NOT_SYNCHRONIZED.getStatusValue());
            session.save(clonedRationalizerApp);
        } finally {
            RationalizerIndexingScheduler.decreaseRationalizerApplicationLockCount(
                    MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier(),
                    rationalizerAppId
            );
            CloneHelper.clearResources();

            this.statusPollingBackgroundTask.setActive(false);
            this.statusPollingBackgroundTask.setRefreshPage(false);
            this.statusPollingBackgroundTask.save();

            LogUtil.getLog(this.getClass()).info(MessageFormat.format("[workflowId={0}] End CloneRationalizerAppBackgroundTask Rationalizer application = {1}",
                    workflowId,
                    rationalizerAppName)
            );
        }
    }

    private void collectSharedContentGuidsForDocuments(List<RationalizerDocument> processedDocs, Set<String> sharedContentsCollector) {
        processedDocs.stream()
                .flatMap(doc -> doc.shallowCopyOfContents().stream())
                .filter(content -> content.computeRationalizerSharedContent() != null)
                .forEach(content ->  {
                            final RationalizerSharedContent rationalizerSharedContent = content.computeRationalizerSharedContent();
                            sharedContentsCollector.add(rationalizerSharedContent.getGuid());
                        }
                );
    }

    private void deleteContentsWithSharedFromElastic(RationalizerElasticSearchHandler rationalizerElasticSearchHandler, List<RationalizerDocument> clonedDocList) {
        Collection<String> elasticSearchContentGuids = clonedDocList.stream()
                .flatMap(doc -> doc.shallowCopyOfContents().stream())
                .filter(content -> content.computeRationalizerSharedContent() != null)
                .map(RationalizerDocumentContent::buildElasticSearchGuid)
                .collect(Collectors.toCollection(LinkedHashSet::new));

        RationalizerContent.deleteRationalizerContentsFromElasticSearch(
                UserUtil.getPrincipalUser(),
                rationalizerElasticSearchHandler,
                elasticSearchContentGuids
        );
    }

    private void processClonedDocuments(String workflowId, RationalizerElasticSearchHandler rationalizerElasticSearchHandler, ContentIdAndHashesList contentIdAndHashesList, List<RationalizerDocument> clonedDocList) {
        sendDocumentsToElasticSearchWithoutRecomputingMarcieFields(rationalizerElasticSearchHandler, clonedDocList);
        sendClonedContentIdAndHashesList(workflowId, contentIdAndHashesList, clonedDocList);
    }

    private void sendClonedContentIdAndHashesList(String workflowId, ContentIdAndHashesList contentIdAndHashesList, List<RationalizerDocument> clonedDocList) {
        clonedDocList.stream()
                .map(RationalizerDocument::shallowCopyOfContents)
                .forEach(documentContents -> {
                    for (RationalizerDocumentContent crtContent : documentContents) {
                        try {
                            if(crtContent.computeRationalizerSharedContent() == null) {
                                contentIdAndHashesList.put(ContentIdAndHashesDTO.builder()
                                        .workflowId(workflowId)
                                        .contentId(crtContent.buildElasticSearchGuid())
                                        .textHash(calculateContentHash(crtContent.getTextContent()))
                                        .htmlHash(calculateContentHash(crtContent.getMarkupContent()))
                                        .build()
                                );
                            }
                        } catch (InterruptedException e) {
                            log.warn("Clone error in sendClonedContentIdAndHashesList " + e.getMessage());
                        }
                    }
                });
    }

    private void saveSharedObjectsToDatabase(List<RationalizerDocument> clonedDocList) {

        final Map<String, RationalizerSharedContent> sharedContentsForOriginalContentsOfClones = computeSharedContentsForOriginalContentsOfClones(clonedDocList);

        Map<String, RationalizerSharedContent> oldSharedContentGuidToNewSharedContentMap = new HashMap<>();
        for (RationalizerSharedContent oldSharedContent : sharedContentsForOriginalContentsOfClones.values()) {
            RationalizerSharedContent clonedSharedContent = saveClonedSharedToDb(oldSharedContent);
            oldSharedContentGuidToNewSharedContentMap.put(oldSharedContent.getGuid(), clonedSharedContent);
        }

        updateSharedContentToClonedContents(clonedDocList, oldSharedContentGuidToNewSharedContentMap);
        HibernateUtil.getManager().getSession().flush();
    }

    private void updateSharedContentToClonedContents(List<RationalizerDocument> clonedDocList, Map<String, RationalizerSharedContent> oldSharedContentGuidToNewSharedContentMap) {

        for (RationalizerDocument doc : clonedDocList) {
            Set<RationalizerDocumentContent> clonedContents = doc.getRationalizerDocumentContents();
            for (RationalizerDocumentContent clonedContent : clonedContents) {
                RationalizerDocumentContent originalContent = (RationalizerDocumentContent)clonedContent.getParentObject();
                if (originalContent == null || originalContent.computeRationalizerSharedContent() == null) {
                    continue;
                }
                RationalizerSharedContent clonedSharedContent = oldSharedContentGuidToNewSharedContentMap.get(originalContent.computeRationalizerSharedContent().getGuid());
                clonedContent.setRationalizerSharedContent(clonedSharedContent);
                clonedContent.save();
            }
        }
    }

    private Map<String, RationalizerSharedContent> computeSharedContentsForOriginalContentsOfClones(List<RationalizerDocument> clonedDocList) {
        Set<String> sharedContentGuidsForOriginalContents = new LinkedHashSet<>();
        for (RationalizerDocument doc : clonedDocList) {
            Set<RationalizerDocumentContent> clonedContents = doc.getRationalizerDocumentContents();
            for (RationalizerDocumentContent clonedContent : clonedContents) {
                RationalizerDocumentContent originalContent = (RationalizerDocumentContent) clonedContent.getParentObject();
                if (originalContent == null || originalContent.computeRationalizerSharedContent() == null) {
                    continue;
                }

                sharedContentGuidsForOriginalContents.add(originalContent.computeRationalizerSharedContent().getGuid());
            }
        }

        final List<RationalizerSharedContent> sharedContents = RationalizerSharedContent.findSharedContentsBySharedContentsGuids(sharedContentGuidsForOriginalContents);

        final LinkedHashMap<String, RationalizerSharedContent> guidToSharedContentMap = sharedContents.stream()
                .map(sharedContent -> Pair.of(sharedContent.getGuid(), sharedContent))
                .collect(Collectors.toMap(Pair::getKey, Pair::getValue,
                        (oldValue, newValue) -> oldValue, LinkedHashMap::new)
                );

        return guidToSharedContentMap;
    }

    private RationalizerSharedContent saveClonedSharedToDb(RationalizerSharedContent originalSharedContent) {
        RationalizerSharedContent clonedSharedContent = new RationalizerSharedContent();
        clonedSharedContent.setGuid(clonedSharedContent.buildElasticSearchGuid());
        clonedSharedContent.setName(originalSharedContent.getName());
        clonedSharedContent.setTextContent(originalSharedContent.getTextContent());
        clonedSharedContent.setMarkupContent(originalSharedContent.getMarkupContent());
        clonedSharedContent.setRationalizerApplication(clonedRationalizerApp);
        Set<HistoricalRationalizerSharedContent> historyList = cloneSharedHistory(originalSharedContent, clonedSharedContent);
        clonedSharedContent.setHistRationalizerSharedContents(historyList);
        clonedSharedContent.setCreatedBy(originalSharedContent.getCreatedBy());
        clonedSharedContent.setCreated(originalSharedContent.getCreated());
        clonedSharedContent.setUpdatedBy(originalSharedContent.getCreatedBy());
        clonedSharedContent.setUpdated(originalSharedContent.getCreated());

        clonedSharedContent.save();
        return clonedSharedContent;
    }

    private Set<HistoricalRationalizerSharedContent> cloneSharedHistory(RationalizerSharedContent originalSharedContent, RationalizerSharedContent sharedContent) {
        Set<HistoricalRationalizerSharedContent> historyList = new LinkedHashSet<>();

        for (HistoricalRationalizerSharedContent crtHistoryDto : originalSharedContent.getHistRationalizerSharedContents()) {
            HistoricalRationalizerSharedContent sharedHistory = new HistoricalRationalizerSharedContent();
            sharedHistory.setName(crtHistoryDto.getName());
            sharedHistory.setTextContent(crtHistoryDto.getTextContent());
            sharedHistory.setMarkupContent(crtHistoryDto.getMarkupContent());
            sharedHistory.setRationalizerSharedContent(sharedContent);
            sharedHistory.setLastActionName(crtHistoryDto.getLastActionName());
            sharedHistory.setLastActionBy(crtHistoryDto.getLastActionBy());
            sharedHistory.setUpdatedBy(crtHistoryDto.getUpdatedBy());
            sharedHistory.setUpdated(crtHistoryDto.getUpdated());
            sharedHistory.setCreatedBy(crtHistoryDto.getCreatedBy());
            sharedHistory.setCreated(crtHistoryDto.getCreated());
            historyList.add(sharedHistory);
        }
        return historyList;
    }

    private void copyDocumentFiles(RationalizerApplication cloneFromApplication, RationalizerApplication cloneToApplication, String workflowId) {
        String fromPathName = RationalizerUtil.getFilerootTemplatesBasePath(cloneFromApplication.getId());
        String toPathName = RationalizerUtil.getFilerootTemplatesBasePath(cloneToApplication.getId());
        try {
            File fromDir = new File(fromPathName);
            File toDir = new File(toPathName);
            if (!fromDir.exists() || !fromDir.isDirectory()) {
                return;
            }

            toDir.mkdirs();
            FileUtil.copyDirContents(fromDir, toDir);
        } catch (Exception e) {
            log.error(MessageFormat.format("[workflowId={0}] Error copying original files" +
                            " when cloning rationalizer application {1} to application {2}.",
                    workflowId,
                    cloneFromApplication.getName(),
                    cloneToApplication.getName()), e
            );
        }
    }
}
