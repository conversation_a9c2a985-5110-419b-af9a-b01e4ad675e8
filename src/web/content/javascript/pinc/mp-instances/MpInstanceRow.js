var MpInstanceRow = function (props) {
    var rowData = props.rowData;
    var pod = rowData.metadata !== undefined && rowData.metadata.mpdc !== undefined && rowData.metadata.mpdc.pod !== undefined
        ? rowData.metadata.mpdc.pod
        : { name: '', username: '' };
    var setEntity = React.useContext(MpInstancesContext).setEntity;
    var _a = React.useContext(ListPageContext), handleSelectionChange = _a.handleSelectionChange, isRowSelected = _a.isRowSelected;
    return (React.createElement(React.Fragment, null,
        React.createElement("tr", null,
            React.createElement(AsyncJsonTableRowSelectCell, { rowId: rowData.id, selected: isRowSelected(rowData.id), selectChangeHandler: function (rowId, selected) { return handleSelectionChange(rowId, selected, rowData); } }),
            React.createElement("td", null,
                React.createElement("div", null,
                    React.createElement("a", { style: { cursor: 'pointer' }, onClick: function () { return setEntity(rowData); } }, rowData.name))),
            React.createElement("td", null,
                React.createElement("div", null, pod.name)),
            React.createElement("td", null,
                React.createElement("div", null, pod.username)),
            React.createElement("td", null,
                React.createElement("div", null, pincFormatDate(rowData.createdAt))),
            React.createElement("td", null,
                React.createElement("div", null, pincFormatDate(rowData.updatedAt))))));
};
