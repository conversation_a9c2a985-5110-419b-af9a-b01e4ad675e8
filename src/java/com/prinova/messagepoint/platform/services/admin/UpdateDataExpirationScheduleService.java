package com.prinova.messagepoint.platform.services.admin;

import com.prinova.messagepoint.model.DataExpirationSchedule;
import com.prinova.messagepoint.model.ExpiryDurationType;
import com.prinova.messagepoint.integrator.MessagepointCronTriggerFactoryBean;
import com.prinova.messagepoint.integrator.MessagepointQuartzUtils;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;


public class UpdateDataExpirationScheduleService extends AbstractService {
    public static final String SERVICE_NAME="admin.UpdateDataExpirationScheduleService";
    private static final Log log = LogUtil.getLog(UpdateDataExpirationScheduleService.class);

    @Override
    public void execute(ServiceExecutionContext context) {
        try {
            validate(context);
            if (hasValidationError(context)) {
                return;
            }

            UpdateDataExpirationScheduleServiceRequest request = (UpdateDataExpirationScheduleServiceRequest) context.getRequest();
            if(request != null){
                DataExpirationSchedule schedule = updateSchedule(request.getDataExpirationScheduleId(), request.isEnabled(), request.getDuration());
                scheduleCronJob(schedule);
            }
        }
        catch(Exception ex){
            log.error(" unexpected exception when invoking UpdateDataPurgeScheduleService execute method", ex);
            this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + ex,
                    context.getLocale());
            throw new RuntimeException(ex.getMessage());
        }
    }

    @Override
    public void validate(ServiceExecutionContext context) {

    }

    private DataExpirationSchedule updateSchedule(long scheduleId, Boolean enabled, ExpiryDurationType duration){
        DataExpirationSchedule dataExpirationScheduleTarget = DataExpirationSchedule.findById(scheduleId);
        if(dataExpirationScheduleTarget != null){
            dataExpirationScheduleTarget.setDuration(duration.getId());
            dataExpirationScheduleTarget.setEnabled(enabled);
            dataExpirationScheduleTarget.save();
            HibernateUtil.getManager().getSession().flush();
        }
        return dataExpirationScheduleTarget;
    }

    private void scheduleCronJob(DataExpirationSchedule schedule){
        if(schedule.getEnabled()){
            MessagepointCronTriggerFactoryBean triggerBean = new MessagepointCronTriggerFactoryBean();
            String cronExpr = "0 0 0 * * ?";
            triggerBean.setCronExpression(cronExpr);
            MessagepointQuartzUtils.rescheduleJob(SyncDataExpirationScheduler.class, triggerBean);
        }
        else{
            MessagepointQuartzUtils.deleteJob(SyncDataExpirationScheduler.class);
        }
    }

    public static ServiceExecutionContext createContext(long id, Boolean enabled, ExpiryDurationType duration){
        SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
        context.setServiceName(SERVICE_NAME);

        UpdateDataExpirationScheduleServiceRequest request = new UpdateDataExpirationScheduleServiceRequest(id, enabled, duration);
        context.setRequest(request);

        SimpleServiceResponse serviceResp = new SimpleServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }

    public static ServiceExecutionContext createContext(DataExpirationSchedule schedule){
        SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
        context.setServiceName(SERVICE_NAME);

        UpdateDataExpirationScheduleServiceRequest request = new UpdateDataExpirationScheduleServiceRequest(schedule.getId(), schedule.getEnabled(), new ExpiryDurationType(schedule.getDuration()));
        context.setRequest(request);

        SimpleServiceResponse serviceResp = new SimpleServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }

}
