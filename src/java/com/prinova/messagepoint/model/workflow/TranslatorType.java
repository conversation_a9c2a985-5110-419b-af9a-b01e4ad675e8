package com.prinova.messagepoint.model.workflow;

import com.prinova.messagepoint.model.common.StaticType;
import com.prinova.messagepoint.util.ApplicationUtil;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

public class TranslatorType extends StaticType {
    @Serial
    private static final long serialVersionUID = -1937260932893524925L;

    public static final int ID_USER                             = 1;
    public static final int ID_SERVICE                          = 2;

    public static final String MESSAGE_CODE_USER                = "page.label.translator.type.user";
    public static final String MESSAGE_CODE_SERVICE             = "page.label.translator.type.service";

    public TranslatorType(Integer id) {
        super();
        switch (id) {
            case ID_USER:
                this.setId(ID_USER);
                this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_USER));
                this.setDisplayMessageCode(MESSAGE_CODE_USER);
                break;
            case ID_SERVICE:
                this.setId(ID_SERVICE);
                this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_SERVICE));
                this.setDisplayMessageCode(MESSAGE_CODE_SERVICE);
                break;
        }
    }

    public TranslatorType(String name) {
        super();
        if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_USER))) {
            this.setId(ID_USER);
            this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_USER));
            this.setDisplayMessageCode(MESSAGE_CODE_USER);
        } else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_SERVICE))) {
            this.setId(ID_SERVICE);
            this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_SERVICE));
            this.setDisplayMessageCode(MESSAGE_CODE_SERVICE);
        }
    }

    public static List<TranslatorType> listAll() {
        List<TranslatorType> allTypes = new ArrayList<>();

        TranslatorType listFilterType = null;

        listFilterType = new TranslatorType(ID_USER);
        allTypes.add(listFilterType);

        listFilterType = new TranslatorType(ID_SERVICE);
        allTypes.add(listFilterType);

        return allTypes;
    }
}
