package com.prinova.messagepoint.redis.ops;

import com.prinova.messagepoint.platform.services.backgroundtask.DeleteDBSchemaBackgroundTask;
import com.prinova.messagepoint.redis.RedisMode;
import com.prinova.messagepoint.redis.client.MessagepointRedisConnection;
import com.prinova.messagepoint.redis.client.MessagepointRedisPubSubConnection;
import com.prinova.messagepoint.redis.config.MessagepointRedisConfiguration;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.RedisUtil;
import com.prinova.messagepoint.util.redis.RedisNode;
import io.lettuce.core.api.StatefulRedisConnection;
import io.lettuce.core.cluster.api.StatefulRedisClusterConnection;
import io.lettuce.core.cluster.models.partitions.RedisClusterNode;
import io.lettuce.core.cluster.pubsub.RedisClusterPubSubAdapter;
import io.lettuce.core.cluster.pubsub.RedisClusterPubSubListener;
import io.lettuce.core.cluster.pubsub.StatefulRedisClusterPubSubConnection;
import io.lettuce.core.cluster.pubsub.api.sync.RedisClusterPubSubCommands;
import io.lettuce.core.masterreplica.StatefulRedisMasterReplicaConnection;
import io.lettuce.core.pubsub.RedisPubSubAdapter;
import io.lettuce.core.pubsub.RedisPubSubListener;
import io.lettuce.core.pubsub.StatefulRedisPubSubConnection;
import io.lettuce.core.pubsub.api.sync.RedisPubSubCommands;
import org.apache.commons.logging.Log;
import org.hibernate.internal.util.SerializationHelper;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.io.IOException;
import java.io.Serializable;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Configuration
public class MessagepointRedisOperations {

    private static final Log logger = LogUtil.getLog(MessagepointRedisOperations.class);

    private final String CHANNEL_KEY_SCHEMA_DELETE;

    private MessagepointRedisConfiguration config;

    private MessagepointRedisConnection messagepointRedisConnection;

    private MessagepointRedisPubSubConnection messagepointRedisPubSubConnection;

    public MessagepointRedisOperations(@Autowired MessagepointRedisConfiguration config) {
        setConfig(config);
        CHANNEL_KEY_SCHEMA_DELETE = channelKey(DeleteDBSchemaBackgroundTask.class.getSimpleName());
    }

    private String redisNamespace;

    private String getRedisNamespace() {
        return redisNamespace;
    }

    @Autowired
    public void setRedisNamespace(String redisNamespace) {
        this.redisNamespace = redisNamespace;
    }

    @Autowired
    public void setConfig(MessagepointRedisConfiguration config) {
        this.config = config;
        this.redisNamespace = config.getRedisNamespace();
    }

    @Autowired
    public void setMessagepointRedisConnection(MessagepointRedisConnection messagepointRedisConnection) {
        this.messagepointRedisConnection = messagepointRedisConnection;
    }

    @Autowired
    public void setMessagepointRedisPubSubConnection(MessagepointRedisPubSubConnection messagepointRedisPubSubConnection) {
        this.messagepointRedisPubSubConnection = messagepointRedisPubSubConnection;
    }

    private StatefulRedisConnection<byte[], byte[]> getRedisStandaloneConnection() {
        return (StatefulRedisConnection<byte[], byte[]>) messagepointRedisConnection.getRedisConnection();
    }

    private StatefulRedisClusterConnection<byte[], byte[]> getRedisClusterConnection() {
        return (StatefulRedisClusterConnection<byte[], byte[]>) messagepointRedisConnection.getRedisConnection();
    }

    private StatefulRedisMasterReplicaConnection<byte[], byte[]> getRedisMasterReplicaConnection() {
        return (StatefulRedisMasterReplicaConnection<byte[], byte[]>) messagepointRedisConnection.getRedisConnection();
    }

    private StatefulRedisPubSubConnection<String, String> getRedisStandalonePubSubConnection() {
        return messagepointRedisPubSubConnection.getRedisPubSubConnection();
    }

    private StatefulRedisClusterPubSubConnection<String, String> getRedisClusterPubSubConnection() {
        return (StatefulRedisClusterPubSubConnection<String, String>) messagepointRedisPubSubConnection.getRedisPubSubConnection();
    }

    private byte[] createKey(String key) {
        return MessageFormat.format("{0}:{1}", getRedisNamespace(), key).getBytes();
    }

    public boolean containsKey(String key) {
        long start = System.currentTimeMillis();
        try {
            if (config.getMode() == RedisMode.STANDALONE) {
                return getRedisStandaloneConnection().sync().exists(createKey(key)) > 0;
            }

            if (config.getMode() == RedisMode.MASTER_REPLICA) {
                return getRedisMasterReplicaConnection().sync().exists(createKey(key)) > 0;
            }

            if (config.getMode() == RedisMode.CLUSTER) {
                return getRedisClusterConnection().sync().exists(createKey(key)) > 0;
            }

            return false;
        } finally {
            logger.info("MessagepointRedisOperations.containsKey - " + channelKey(key) + " took " + (System.currentTimeMillis() - start) + " ms");
        }
    }

    public boolean delete(String key) {
        long start = System.currentTimeMillis();
        try {
            if (config.getMode() == RedisMode.STANDALONE) {
                return getRedisStandaloneConnection().sync().del(createKey(key)) > 0;
            }

            if (config.getMode() == RedisMode.MASTER_REPLICA) {
                return getRedisMasterReplicaConnection().sync().del(createKey(key)) > 0;
            }

            if (config.getMode() == RedisMode.CLUSTER) {
                return getRedisClusterConnection().sync().del(createKey(key)) > 0;
            }

            return false;
        } finally {
            logger.info("MessagepointRedisOperations.delete - " + channelKey(key) + " took " + (System.currentTimeMillis() - start) + " ms");
        }
    }

    public byte[] getBytesValue(String key) {
        long start = System.currentTimeMillis();
        try {
            if (config.getMode() == RedisMode.STANDALONE) {
                return getRedisStandaloneConnection().sync().get(createKey(key));
            }

            if (config.getMode() == RedisMode.MASTER_REPLICA) {
                return getRedisMasterReplicaConnection().sync().get(createKey(key));
            }

            if (config.getMode() == RedisMode.CLUSTER) {
                return getRedisClusterConnection().sync().get(createKey(key));
            }

            return null;
        } finally {
            logger.info("MessagepointRedisOperations.getBytesValue - " + channelKey(key) + " took " + (System.currentTimeMillis() - start) + " ms");
        }
    }

    public <TValue> TValue getValue(String key, Class<TValue> retValueType) {
        long start = System.currentTimeMillis();
        try {
            Object result = null;

            byte[] value = getBytesValue(key);

            if (value != null) {
                result = SerializationHelper.deserialize(value);
            }

            return (TValue) result;
        } finally {
            logger.info("MessagepointRedisOperations.getValue - " + channelKey(key) + " took " + (System.currentTimeMillis() - start) + " ms");
        }
    }

    public boolean putValue(String key, byte[] value) {
        long start = System.currentTimeMillis();
        try {
            String result = null;

            if (config.getMode() == RedisMode.STANDALONE) {
                result = getRedisStandaloneConnection().sync().set(createKey(key), value);
            }

            if (config.getMode() == RedisMode.MASTER_REPLICA) {
                result = getRedisMasterReplicaConnection().sync().set(createKey(key), value);
            }

            if (config.getMode() == RedisMode.CLUSTER) {
                result = getRedisClusterConnection().sync().set(createKey(key), value);
            }

            return result != null && result.equalsIgnoreCase("OK");
        } finally {
            logger.info("MessagepointRedisOperations.putValue - " + channelKey(key) + " - value size: " + (value != null ? value.length : 0) + " bytes - took " + (System.currentTimeMillis() - start) + " ms");
        }
    }

    public boolean putValue(String key, Serializable value) {
        return putValue(key, SerializationHelper.serialize(value));
    }

    /**
     * Set key to hold the value and set key to timeout after a given number of seconds.
     * @param key cache key
     * @param value value
     * @param seconds TTL - time to live of a key in Redis
     * @return true if successful, false otherwise
     */
    public boolean putValue(String key, byte[] value, long seconds) {
        long start = System.currentTimeMillis();
        try {
            String result = null;

            if (config.getMode() == RedisMode.STANDALONE) {
                result = getRedisStandaloneConnection().sync().setex(createKey(key), seconds, value);
            }

            if (config.getMode() == RedisMode.MASTER_REPLICA) {
                result = getRedisMasterReplicaConnection().sync().setex(createKey(key), seconds, value);
            }

            if (config.getMode() == RedisMode.CLUSTER) {
                result = getRedisClusterConnection().sync().setex(createKey(key), seconds, value);
            }

            return result != null && result.equalsIgnoreCase("OK");
        } finally {
            logger.info("MessagepointRedisOperations.putValue - " + channelKey(key) + " - value size: " + (value != null ? value.length : 0) + " bytes - took " + (System.currentTimeMillis() - start) + " ms");
        }
    }

    /**
     * Set key to hold the value and set key to timeout after a given number of seconds.
     * @param key key
     * @param value value
     * @param seconds TTL - time to live of a key in Redis
     * @return true if successful, false otherwise
     */
    public boolean putValue(String key, Serializable value, long seconds) {
        return putValue(key, SerializationHelper.serialize(value), seconds);
    }

    public Long increment(String key) {
        long start = System.currentTimeMillis();
        try {
            if (config.getMode() == RedisMode.STANDALONE) {
                return getRedisStandaloneConnection().sync().incr(createKey(key));
            }

            if (config.getMode() == RedisMode.MASTER_REPLICA) {
                return getRedisMasterReplicaConnection().sync().incr(createKey(key));
            }

            if (config.getMode() == RedisMode.CLUSTER) {
                return getRedisClusterConnection().sync().incr(createKey(key));
            }

            return null;
        } finally {
            logger.info("MessagepointRedisOperations.increment - " + channelKey(key) + " took " + (System.currentTimeMillis() - start) + " ms");
        }
    }

    public Long decrement(String key) {
        long start = System.currentTimeMillis();
        try {
            if (config.getMode() == RedisMode.STANDALONE) {
                return getRedisStandaloneConnection().sync().decr(createKey(key));
            }

            if (config.getMode() == RedisMode.MASTER_REPLICA) {
                return getRedisMasterReplicaConnection().sync().decr(createKey(key));
            }

            if (config.getMode() == RedisMode.CLUSTER) {
                return getRedisClusterConnection().sync().decr(createKey(key));
            }

            return null;
        } finally {
            logger.info("MessagepointRedisOperations.decrement - " + channelKey(key) + " took " + (System.currentTimeMillis() - start) + " ms");
        }
    }

    public boolean putNewValue(String key, Serializable value) {
        long start = System.currentTimeMillis();
        byte[] valueBytes;
        int valueLength = 0;
        try {
            if (config.getMode() == RedisMode.STANDALONE) {
                valueBytes = SerializationHelper.serialize(value);
                valueLength = valueBytes.length;
                return getRedisStandaloneConnection().sync().setnx(createKey(key), valueBytes);
            }

            if (config.getMode() == RedisMode.MASTER_REPLICA) {
                valueBytes = SerializationHelper.serialize(value);
                valueLength = valueBytes.length;
                return getRedisMasterReplicaConnection().sync().setnx(createKey(key), valueBytes);
            }

            if (config.getMode() == RedisMode.CLUSTER) {
                valueBytes = SerializationHelper.serialize(value);
                valueLength = valueBytes.length;
                return getRedisClusterConnection().sync().setnx(createKey(key), valueBytes);
            }

            return false;
        } finally {
            logger.info("MessagepointRedisOperations.putNewValue - " + channelKey(key) + " - value size: " + valueLength + " bytes - took " + (System.currentTimeMillis() - start) + " ms");
        }
    }

    public Long lpush(String key, Serializable value) {
        long start = System.currentTimeMillis();
        byte[] valueBytes;
        int valueLength = 0;
        try {
            if (config.getMode() == RedisMode.STANDALONE) {
                valueBytes = SerializationHelper.serialize(value);
                valueLength = valueBytes.length;
                return getRedisStandaloneConnection().sync().lpush(createKey(key), valueBytes);
            }

            if (config.getMode() == RedisMode.MASTER_REPLICA) {
                valueBytes = SerializationHelper.serialize(value);
                valueLength = valueBytes.length;
                return getRedisMasterReplicaConnection().sync().lpush(createKey(key), valueBytes);
            }

            if (config.getMode() == RedisMode.CLUSTER) {
                valueBytes = SerializationHelper.serialize(value);
                valueLength = valueBytes.length;
                return getRedisClusterConnection().sync().lpush(createKey(key), valueBytes);
            }

            return null;
        } finally {
            logger.info("MessagepointRedisOperations.lpush - " + channelKey(key) + " - value size: " + valueLength + " bytes - took " + (System.currentTimeMillis() - start) + " ms");
        }
    }

    public List<Object> rpop(String key) {
        long start = System.currentTimeMillis();
        try {
            List<byte[]> values = new ArrayList<>();

            if (config.getMode() == RedisMode.STANDALONE) {
                values = getRedisStandaloneConnection().sync().rpop(createKey(key), 1);
            }

            if (config.getMode() == RedisMode.MASTER_REPLICA) {
                values = getRedisMasterReplicaConnection().sync().rpop(createKey(key), 1);
            }

            if (config.getMode() == RedisMode.CLUSTER) {
                values = getRedisClusterConnection().sync().rpop(createKey(key), 1);
            }

            return values.stream().map(va -> va == null ? null : SerializationHelper.deserialize(va)).collect(Collectors.toList());
        } finally {
            logger.info("MessagepointRedisOperations.rpop - " + channelKey(key) + " took " + (System.currentTimeMillis() - start) + " ms");
        }
    }

    public Boolean expire(String key, long seconds) {
        long start = System.currentTimeMillis();
        try {
            if (config.getMode() == RedisMode.STANDALONE) {
                return getRedisStandaloneConnection().sync().expire(createKey(key), seconds);
            }

            if (config.getMode() == RedisMode.MASTER_REPLICA) {
                return getRedisMasterReplicaConnection().sync().expire(createKey(key), seconds);
            }

            if (config.getMode() == RedisMode.CLUSTER) {
                return getRedisClusterConnection().sync().expire(createKey(key), seconds);
            }

            return Boolean.FALSE;
        } finally {
            logger.info("MessagepointRedisOperations.expire - " + channelKey(key) + " took " + (System.currentTimeMillis() - start) + " ms");
        }
    }

    private String channelKey(String key) {
        return MessageFormat.format("{0}:{1}", getRedisNamespace(), key);
    }

    public void close() throws IOException {
        if (messagepointRedisConnection != null && messagepointRedisConnection.getRedisConnection() != null)
            messagepointRedisConnection.getRedisConnection().close();
    }

    public void subscribe() {
        long start = System.currentTimeMillis();
        try {
            final String nodeId = RedisNode.getId();

            if (config.getPubSubMode() == RedisMode.STANDALONE) {
                StatefulRedisPubSubConnection<String, String> redisSubscribeConnection = getRedisStandalonePubSubConnection();

                RedisPubSubListener<String, String> listener = new RedisPubSubAdapter<>() {

                    @Override
                    public void message(String channel, String message) {
                        new Thread(() -> {
                            LogUtil.getLog(RedisUtil.class).info(MessageFormat.format("Channel: {0}, Message: {1}, {2}", channel, message, nodeId));

                            JSONObject messageWrapper = new JSONObject(message);

                            if (!messageWrapper.getString("sourceNode").equals(nodeId)) {
                                if (channel.equals(CHANNEL_KEY_SCHEMA_DELETE)) {
                                    String schema = messageWrapper.getString("message");
                                    LogUtil.getLog(RedisUtil.class).info(MessageFormat.format("Handle: {0}, message: {1}, {2}", channel, messageWrapper, nodeId));
                                    DeleteDBSchemaBackgroundTask.closeDatabaseConnections(schema, schema);
                                }
                            }
                        }).start();
                    }
                };

                redisSubscribeConnection.addListener(listener);
                RedisPubSubCommands<String, String> sync = redisSubscribeConnection.sync();
                sync.subscribe(CHANNEL_KEY_SCHEMA_DELETE);
            }

            if (config.getPubSubMode() == RedisMode.CLUSTER) {
                StatefulRedisClusterPubSubConnection<String, String> redisSubscribeConnection = getRedisClusterPubSubConnection();

                RedisClusterPubSubListener<String, String> listener = new RedisClusterPubSubAdapter<>() {

                    @Override
                    public void message(RedisClusterNode clusterNode, String channel, String message) {
                        new Thread(() -> {
                            LogUtil.getLog(RedisUtil.class).info(MessageFormat.format("Channel: {0}, Message: {1}, {2}", channel, message, nodeId));

                            JSONObject messageWrapper = new JSONObject(message);

                            if (!messageWrapper.getString("sourceNode").equals(nodeId)) {
                                if (channel.equals(CHANNEL_KEY_SCHEMA_DELETE)) {
                                    String schema = messageWrapper.getString("message");
                                    LogUtil.getLog(RedisUtil.class).info(MessageFormat.format("Handle: {0}, message: {1}, {2}", channel, messageWrapper, nodeId));
                                    DeleteDBSchemaBackgroundTask.closeDatabaseConnections(schema, schema);
                                }
                            }
                        }).start();
                    }
                };

                redisSubscribeConnection.addListener(listener);
                RedisClusterPubSubCommands<String, String> sync = redisSubscribeConnection.sync();
                sync.subscribe(CHANNEL_KEY_SCHEMA_DELETE);
            }

        } finally {
            logger.info("MessagepointRedisOperations.subscribe - " + CHANNEL_KEY_SCHEMA_DELETE + " took " + (System.currentTimeMillis() - start) + " ms");
        }
    }

    public void publish(String channel, String message) {
        long start = System.currentTimeMillis();
        try {
            JSONObject messageWrapper = new JSONObject();
            messageWrapper.put("sourceNode", RedisNode.getId());
            messageWrapper.put("message", message);

            channel = channelKey(channel);

            logger.info(MessageFormat.format("publish({0},{1})", channel, messageWrapper.toString()));

            if (config.getPubSubMode() == RedisMode.STANDALONE)
                getRedisStandalonePubSubConnection().sync().publish(channel, messageWrapper.toString());

            if (config.getPubSubMode() == RedisMode.CLUSTER)
                getRedisClusterPubSubConnection().sync().publish(channel, messageWrapper.toString());
        } finally {
            logger.info("MessagepointRedisOperations.publish - " + channel + " - message length: " + (message != null ? message.length() : 0) + " bytes - took " + (System.currentTimeMillis() - start) + " ms");
        }
    }

    @Bean
    public RedisTemplate<?, ?> redisTemplate(LettuceConnectionFactory redisConnectionFactory) {
        RedisTemplate<?, ?> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        template.afterPropertiesSet();
        return template;
    }

    @Bean
    public StringRedisTemplate stringRedisTemplate(LettuceConnectionFactory redisConnectionFactory) {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setConnectionFactory(redisConnectionFactory);
        template.afterPropertiesSet();
        return template;
    }

}
