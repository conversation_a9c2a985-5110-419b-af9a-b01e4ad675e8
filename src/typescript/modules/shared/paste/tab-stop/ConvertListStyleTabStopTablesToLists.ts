import {ComputeFirstTableColumnIncrementalValueMetadata} from "./ComputeFirstTableColumnIncrementalValueMetadata";
import {TableColumnIncrementalValueMetadata} from "./TableColumnIncrementalValueMetadata";
import {BuildListElement} from "../util/BuildListElement";
import {ArrayUtil} from "../../util/ArrayUtil";
import {NodeUtil} from "../../util/NodeUtil";
import {SharedConstants} from "../../util/SharedConstants";
import {EditorContext} from "../EditorContext";
import {PasteUtil} from "../util/PasteUtil";

export class ConvertListStyleTabStopTablesToLists {

    public constructor(
        private targetJQuery: JQuery,
        private editorContext: EditorContext,
        private rootStyle: string,
        private ownerSpanClass: string
    ) {
    }

    public execute() {
        let tabStopTables: Element[] = [];
        this.targetJQuery
            .find(`[${SharedConstants.MP_PASTE_TAB_STOP_TABLE}]`)
            .each((index, element) => {
                tabStopTables.push(element);
            });

        try {
            for (let tabStopTable of tabStopTables) {
                this.processTabStopTable(tabStopTable);
            }
        } catch (e) {
            console.error(e);
        }
    }

    private processTabStopTable(tabStopTable: Element) {
        let columnWidths = tabStopTable.getAttribute(SharedConstants.MP_PASTE_TABLE_COLUMN_WIDTHS);
        if (!columnWidths || columnWidths.length === 0) {
            return;
        }
        if (!this.containsOnlyTwoColumns(columnWidths)) {
            return;
        }

        let tableMargins = PasteUtil.computeTableMargins(tabStopTable);
        let trElements: Element[] = ComputeFirstTableColumnIncrementalValueMetadata.collectTrElements(tabStopTable);
        if (trElements.length === 0) {
            tabStopTable.remove();

            return;
        }

        let computeFirstTableColumnMetadata: ComputeFirstTableColumnIncrementalValueMetadata = null;
        let firstTableColumnMetadata: TableColumnIncrementalValueMetadata = null;

        let tabbedTableProcessingEnum = TabbedTableProcessingEnum.FIRST_N_ROWS;
        let crtTrElements = ArrayUtil.cloneArray(trElements);

        while (crtTrElements.length  > 0 && firstTableColumnMetadata === null) {
            computeFirstTableColumnMetadata = new ComputeFirstTableColumnIncrementalValueMetadata(
                crtTrElements,
                columnWidths,
                tableMargins
            );
            firstTableColumnMetadata = computeFirstTableColumnMetadata.execute();
            if (firstTableColumnMetadata !== null) {
                break;
            }
            crtTrElements.splice(crtTrElements.length - 1, 1);
        }

        if (firstTableColumnMetadata === null) {
            tabbedTableProcessingEnum = TabbedTableProcessingEnum.LAST_N_ROWS;
            crtTrElements = ArrayUtil.cloneArray(trElements);
            // The case with entire array was tested above, so remove first elem from beginning.
            crtTrElements.splice(0, 1);

            while (crtTrElements.length  > 0 && firstTableColumnMetadata === null) {
                computeFirstTableColumnMetadata = new ComputeFirstTableColumnIncrementalValueMetadata(
                    crtTrElements,
                    columnWidths,
                    tableMargins
                );
                firstTableColumnMetadata = computeFirstTableColumnMetadata.execute();
                if (firstTableColumnMetadata !== null) {
                    break;
                }
                crtTrElements.splice(0, 1);
            }
        }

        if (firstTableColumnMetadata === null) {
            return;
        }

        let secondColumnTdElements = computeFirstTableColumnMetadata.getSecondColumnTdElements();
        let firstColumnTdElements = computeFirstTableColumnMetadata.getFirstColumnTdElements();

        let listElement = new BuildListElement(firstTableColumnMetadata, firstColumnTdElements, secondColumnTdElements, this.editorContext, this.rootStyle, this.ownerSpanClass).execute();
        if (!listElement) {
            return;
        }

        if (!tabStopTable.parentElement) {
            return;
        }
        if (tabbedTableProcessingEnum === TabbedTableProcessingEnum.FIRST_N_ROWS) {
            tabStopTable.parentElement.insertBefore(listElement, tabStopTable);

            if (crtTrElements.length !== trElements.length) {
                this.removeFirstNRowsFromTable(tabStopTable, crtTrElements.length);
                this.processTabStopTable(tabStopTable);
                return;
            }
            tabStopTable.remove();
        } else if (tabbedTableProcessingEnum === TabbedTableProcessingEnum.LAST_N_ROWS) {
            NodeUtil.insertAfterNode(tabStopTable, listElement);

            if (crtTrElements.length !== trElements.length) {
                this.removeLastNRowsFromTable(tabStopTable, crtTrElements.length);
                this.processTabStopTable(tabStopTable);
                return;
            }
            tabStopTable.remove();
        }
    }

    private removeFirstNRowsFromTable(tableElement: Element, removeCount: number) {
        let removedItemsCount = 0;
        let childNodes = Array.from(tableElement.childNodes);
        for (let childNode of childNodes) {
            if (removedItemsCount >= removeCount) {
                break;
            }

            if (childNode.nodeType !== Node.ELEMENT_NODE) {
                continue;
            }
            let childElement = childNode as Element;
            let childElementNameLowerCase = childElement.nodeName.toLowerCase();
            if (['tr', 'th', 'thead', 'tfoot'].indexOf(childElementNameLowerCase) !== -1) {
                removedItemsCount++;
                childElement.remove();
            }
        }
    }

    private removeLastNRowsFromTable(tableElement: Element, removeCount: number) {
        let removedItemsCount = 0;
        let childNodes = Array.from(tableElement.childNodes);
        for (let i = childNodes.length - 1; i >= 0; i--) {
            let childNode = childNodes[i];
            if (removedItemsCount >= removeCount) {
                break;
            }

            if (childNode.nodeType !== Node.ELEMENT_NODE) {
                continue;
            }
            let childElement = childNode as Element;
            let childElementNameLowerCase = childElement.nodeName.toLowerCase();
            if (['tr', 'th', 'thead', 'tfoot'].indexOf(childElementNameLowerCase) !== -1) {
                removedItemsCount++;
                childElement.remove();
            }
        }
    }

    private containsOnlyTwoColumns(columnWidths: string) {
        return columnWidths.split(';').length === 2
    }
}


enum TabbedTableProcessingEnum {
    FIRST_N_ROWS = 'FIRST_N_ROWS',
    LAST_N_ROWS = 'LAST_N_ROWS',
}