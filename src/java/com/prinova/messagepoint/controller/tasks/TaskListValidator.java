package com.prinova.messagepoint.controller.tasks;

import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.model.wrapper.AsyncTasksListVO;
import com.prinova.messagepoint.model.wrapper.AsyncTasksListWrapper;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.StringUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidationUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.validation.Errors;

import java.util.List;
import java.util.function.Predicate;

public class TaskListValidator extends MessagepointInputValidator{
	@Override
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		TaskListWrapper command = (TaskListWrapper)commandObj;
			
		int action = -1;
		if (command.getActionValue() != null && !command.getActionValue().trim().isEmpty()) {
			action = Integer.valueOf(command.getActionValue()).intValue();
		}

		switch (action) {
			case TaskListController.ACTION_EDIT:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncTasksListVO.TasksListVOFlags::isCanUpdate);
				break;
			case TaskListController.ACTION_DELETE:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncTasksListVO.TasksListVOFlags::isCanRemove);
				break;
			case TaskListController.ACTION_MARK_COMPLETE:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncTasksListVO.TasksListVOFlags::isCanMarkComplete);
				break;
			case TaskListController.ACTION_REOPEN:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncTasksListVO.TasksListVOFlags::isCanReopen);
				break;
			case TaskListController.ACTION_REASSIGN:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncTasksListVO.TasksListVOFlags::isCanReassign);
				break;
			case TaskListController.ACTION_RELEASE_FOR_APPROVAL:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncTasksListVO.TasksListVOFlags::isCanReleaseForApproval);
				break;
			case TaskListController.ACTION_ACTIVATE:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncTasksListVO.TasksListVOFlags::isCanActivate);
				break;
			case TaskListController.ACTION_APPROVE:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncTasksListVO.TasksListVOFlags::isCanApprove);
				break;
			case TaskListController.ACTION_RELEASE_FROM_TRANSLATION:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncTasksListVO.TasksListVOFlags::isCanReleaseFromTranslation);
				break;
			case TaskListController.ACTION_REJECT:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncTasksListVO.TasksListVOFlags::isCanReject);
				break;
			case TaskListController.ACTION_ABORT:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncTasksListVO.TasksListVOFlags::isCanAbort);
				break;
			case TaskListController.ACTION_REJECT_BACK_ONE:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncTasksListVO.TasksListVOFlags::isCanReject);
				break;
			case TaskListController.ACTION_ADD_APPLICATION:
				break; // No validation needed
			default:
				break;
		}

		// Additional validation logic for specific actions
		if (action == TaskListController.ACTION_DELETE) {
			validateDeleteAction(command, errors);
		}

		if (action == TaskListController.ACTION_ADD_APPLICATION) {
			validateAddApplicationAction(command, errors);
		}
	}

	private void validateActionPermission(List<Task> tasks, Errors errors, String errorMessage, Predicate<AsyncTasksListVO.TasksListVOFlags> flagChecker) {

		// Admin bypass - Feature flag hidden toggle
		if ( !Boolean.parseBoolean(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.WebAppSecurity.SERVER_SIDE_ACTION_VALIDATION)) )
			return;

		if ( tasks.isEmpty() )
			errors.reject(errorMessage);

		for (Task task : tasks) {
			AsyncTasksListVO vo = new AsyncTasksListVO();
			vo.setTask(task);

			AsyncTasksListVO.TasksListVOFlags flags = new AsyncTasksListVO.TasksListVOFlags();
			AsyncTasksListWrapper.setActionFlags(task, flags);
			if (!flagChecker.test(flags)) {
				errors.reject(errorMessage);
				break;
			}
		}
	}

	private void validateDeleteAction(TaskListWrapper command, Errors errors) {
		List<Task> selectedTasks = command.getSelectedList();
		StringBuilder referencedTaskNames = new StringBuilder();
		StringBuilder inMidWorkflowTaskNames = new StringBuilder();
		boolean referenced = false, inMidWorkflow = false;

		for (Task currentTask : selectedTasks) {
			if (currentTask.isReferenced()) {
				referenced = true;
				referencedTaskNames.append(currentTask.getItemName()).append(" ");
			}

			if (currentTask.isAssetInMidWorkflow()) {
				inMidWorkflow = true;
				inMidWorkflowTaskNames.append(currentTask.getItemName()).append(" ");
			}
		}

		if (referenced) {
			errors.reject("error.message.cannot.delete.referenced.tasks",
					new String[]{referencedTaskNames.toString()},
					"The following task(s) are referenced and cannot be deleted: " + referencedTaskNames);
		}

		if (inMidWorkflow) {
			errors.reject("error.message.cannot.delete.workflow.engaged.tasks",
					new String[]{inMidWorkflowTaskNames.toString()},
					"The following task(s) are engaged in workflow(s) and cannot be deleted: " + inMidWorkflowTaskNames);
		}
	}

	private void validateAddApplicationAction(TaskListWrapper command, Errors errors) {
		if (command.getApplicationName() == null || command.getApplicationName().trim().isEmpty()) {
			errors.reject("error.input.mandatory", new String[]{ApplicationUtil.getMessage("page.label.name")}, "");
			return;
		}
		// check if application name is unique
		List<RationalizerApplication> applicationList = RationalizerApplication.findAll();
		if (CollectionUtils.isNotEmpty(applicationList)) {
			for (RationalizerApplication rationalizerApplication : applicationList) {
				if (!StringUtil.isEmptyOrNull(rationalizerApplication.getName())) {
					if (rationalizerApplication.getName().equalsIgnoreCase(command.getApplicationName())) {
						errors.reject("error.message.rationalizer.name.unique", new String[]{}, "");
						break;
					}
				}
			}
		}
		String label = ApplicationUtil.getMessage("page.label.name");
		MessagepointInputValidationUtil.validateStringValue(label, command.getApplicationName() == null ? command.getApplicationName() :
				command.getApplicationName().trim(), true, 1, 255, ApplicationUtil.getMessage("page.text.validator.alphanum.space.dash.underscore.apos"), StringUtil.injectI18Nchars("^[A-Za-z0-9\\p{L}\\s_\\-']*+"), errors);
	}
}
