package com.prinova.messagepoint.query.handler;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import com.prinova.messagepoint.model.insert.Insert;
import com.prinova.messagepoint.query.DefaultPage;
import com.prinova.messagepoint.query.IPage;

public class InsertTargetingHandler implements PostQueryHandler {

	private boolean isOrderAsc;
	
	public boolean isOrderAsc() {
		return isOrderAsc;
	}

	public void setOrderAsc(boolean isOrderAsc) {
		this.isOrderAsc = isOrderAsc;
	}
	
	@SuppressWarnings("unchecked")
	public IPage handle(IPage page) {
		List<Insert> items = (List<Insert>)page.getList();
		
		Collections.sort(items, new Comparator<>() {
            public int compare(Insert o1, Insert o2) {
                if (o1.getHasTarget() && !o2.getHasTarget()) {
                    if (isOrderAsc()) {
                        return 1;
                    } else {
                        return -1;
                    }
                } else if (o2.getHasTarget() && !o1.getHasTarget()) {
                    if (isOrderAsc()) {
                        return -1;
                    } else {
                        return 1;
                    }
                } else {
                    return 0;
                }
            }
        });
		
        ((DefaultPage)page).setList(items);
		return page;
	}
}
