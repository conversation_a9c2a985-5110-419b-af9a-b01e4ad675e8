package com.prinova.messagepoint.platform.services.backgroundtask;

import java.io.ByteArrayInputStream;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;

import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.content.BulkCheckoutContentObjectService;
import com.prinova.messagepoint.platform.services.workflow.WorkflowReleaseForApprovalService;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.controller.content.ContentObjectSftpImageUploadController;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.content.Content;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.content.ContentObjectBulkImageCreateService;
import com.prinova.messagepoint.platform.sftprepo.SftpRepoClient;
import com.prinova.messagepoint.platform.sftprepo.SftpRepoClient.SftpRepoImage;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;

public class SyncSftpRepoContentObjectBackgroundTask extends MessagePointRunnable
{
	private static final Log log = LogUtil.getLog(SyncSftpRepoContentObjectBackgroundTask.class);

	private final User requestor;
	private final String folder;
	private final String metatags;
	private boolean variableContentEnabled;
	private final boolean activate;
	private final StatusPollingBackgroundTask statusPollingBackgroundTask;
	
	public SyncSftpRepoContentObjectBackgroundTask( User requestor, String folder, String metatags, boolean variableContentEnabled, boolean activate )
	{
		this.requestor = requestor;
		this.folder = folder;
		this.metatags = metatags;
		this.variableContentEnabled = variableContentEnabled;
		this.activate = activate;
		this.statusPollingBackgroundTask = new StatusPollingBackgroundTask();
		this.statusPollingBackgroundTask.setCreatedBy(requestor.getId());
	}
	
	public String getBackgroundTaskId() 
	{
		return statusPollingBackgroundTask.getGuid();
	}

	@Override
	public void performMainProcessing()
	{
		log.info("Starting SyncSftpRepoContentObjectBackgroundTask");
		
		SftpRepoClient sftp = null;
		try {
			this.statusPollingBackgroundTask.setType(StatusPollingBackgroundTask.TYPE_OBJECT_IMPORT);
			this.statusPollingBackgroundTask.setName(StatusPollingBackgroundTask.SUB_TYPE_SFTP_REPO_CONTENT_LIB_IMPORT);

			this.statusPollingBackgroundTask.setDescription(ApplicationUtil.getMessage("page.label.importing") + " " + this.folder);
			this.statusPollingBackgroundTask.setRequestDate(new Date(System.currentTimeMillis()));
			this.statusPollingBackgroundTask.setBackgroundThread(this.getOwningThread());
			this.statusPollingBackgroundTask.setBackgroundThreadId(this.getOwningThread().getId());
			this.statusPollingBackgroundTask.setProgressInPercentInThread(0);
			this.statusPollingBackgroundTask.setActive(true);
			this.statusPollingBackgroundTask.save();

			if ( folder == null )
				throw new Exception("Invalid SFTP Folder in SyncSftpRepoContentObjectBackgroundTask");

			sftp = new SftpRepoClient();
			if ( !sftp.isValid() )
				throw new Exception("No SFTP Repository settings found for this instance in SyncSftpRepoContentObjectBackgroundTask");
			
			// for security, check if the requested folder is within the configured folder.
			if ( folder.isEmpty() || !folder.startsWith(sftp.getFolder()) )
				throw new Exception("Provided folder doesn't match with the base folder configured for this instance in SyncSftpRepoContentObjectBackgroundTask");
			sftp.setFolder(folder);
			
			List<SftpRepoImage> remoteFiles = sftp.listImages(false);
			if ( remoteFiles == null )
				throw new Exception("Error requesting images from SFTP Repository in SyncSftpRepoContentObjectBackgroundTask");

			int percentCount = 0;
			double progressIncrementPerFile = 100.0 / (double) remoteFiles.size();
			double currentProgress = 0;			

			boolean firstConnectRequest = true;
			boolean hasError = false;
			int updatedContentCount = 0;
			int updatedImagesCount = 0;
			
			Set<Long> docIds = new HashSet<>();
			if ( variableContentEnabled ) {
				for( Document doc : Document.findAllDocumentsAndProjectsVisible() )
					docIds.add( doc.getId() );
				
				if (docIds.isEmpty())
					variableContentEnabled = false;
			}
			
			int newImagesCount = 0;
			int bulkCount = 0;
			int bulkSize = 0;
			int sftpRepoBulkSize = SystemPropertyManager.getInstance().getIntegerSystemProperty(SystemPropertyKeys.SftpRepoSettings.KEY_SftpRepoBulkSize, 10);
			List<MultipartFile> fileList = new ArrayList<>();
			List<String> fileNameList = new ArrayList<>();
			List<SftpRepoImage> sftpRepoImageList = new ArrayList<>();
			
			for ( SftpRepoImage sftpImage : remoteFiles ) {
				boolean newImage = true;
				boolean updatedContent = false;
				byte[] imageData = null;

				List<ContentObjectAssociation> contentObjectAssoc = ContentObjectAssociation.findAllByAssetId(sftpImage.getId());

				for (ContentObjectAssociation contentObjAssoc : contentObjectAssoc) {

					Content content = contentObjAssoc.getContent();

					// There can be more than one content ID of the same image if it's referenced by active dynamic message
					if (content != null && content.getAssetId() != null && content.getAssetURL() == null)
					{
						newImage = false;
						
						// check if it should be upgraded
						Date contentTimestamp = content.getAssetLastUpdate();
						Date sftpImageTimestamp = sftpImage.getUpdatedDate();
						
						if ( sftpImageTimestamp == null ) {
							log.error( "Failed to retrieve image date from SFTP repository for image: " + sftpImage.getFilename() + " Server Date: " + sftpImage.getUpdatedDate());
							hasError = true;
						}
						else if ( contentTimestamp == null || !DateUtil.equalsWithErrorTolerants(contentTimestamp, sftpImageTimestamp, 0) ) 
						{
							// only update files with timestamp different than last one
							
							if (imageData == null)
							{
								if (firstConnectRequest)
								{
									if ( !sftp.connect() )
										throw new Exception("Error connecting to SFTP Repository in SyncSftpRepoContentObjectBackgroundTask");
									firstConnectRequest = false;
								}
								
								imageData = sftp.downloadFile(sftpImage.getFilePath());
							}

							ContentObject contentObject = contentObjAssoc.getContentObject();

							if (!contentObject.hasWorkingData()) {
								List<ContentObject> target = new ArrayList<>();
								target.add(contentObjAssoc.getContentObject());
								ServiceExecutionContext context = BulkCheckoutContentObjectService.createContext(target, requestor, MessageFormat.format("SFTP Sync: {0}", sftpImage.getId()));
								Service service = MessagepointServiceFactory.getInstance().lookupService(BulkCheckoutContentObjectService.SERVICE_NAME,
										BulkCheckoutContentObjectService.class);
								service.execute(context);
								ServiceResponse serviceResponse = context.getResponse();

								if (!serviceResponse.isSuccessful()) {
									throw new Exception("Could not create working copy of content object: " + serviceResponse.getMessagesToString());
								}

								contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
								contentObjAssoc = contentObject
										.getContentObjectDataWorkingCentric()
										.getContentObjectAssociations()
										.stream()
										.filter(x -> x.getContent() != null &&
												x.getContent().getAssetId() != null &&
												x.getContent().getAssetId().equals(sftpImage.getId()))
										.findFirst()
										.orElse(null);

								if (contentObjAssoc == null) {
									throw new Exception("Could not create working copy of content object: " + contentObject.getGuid());
								}

								content = contentObjAssoc.getContent();
							}

							
							if ( imageData != null && contentObjAssoc.getDataType() == ContentObject.DATA_TYPE_WORKING ) {
								updatedContent = true;
								updatedContentCount++;
								ContentObjectSftpImageUploadController.saveFileFromByteArray(content.getImageLocation(), imageData);
								content.setAssetLastUpdate(sftpImageTimestamp);
								content.setAssetLastSync(DateUtil.now());
								content.save();
							} else if (imageData == null) {
								log.error("Failed to download file " + sftpImage.getFilePath() + " from SFTP Repo.");
								hasError = true;
							}

							if (activate) {
								ServiceExecutionContext context = WorkflowReleaseForApprovalService.createContextWithAction(requestor, MessageFormat.format("SFTP Sync: {0}", sftpImage.getId()), true, contentObject);
								Service service = MessagepointServiceFactory.getInstance()
										.lookupService(WorkflowReleaseForApprovalService.SERVICE_NAME, WorkflowReleaseForApprovalService.class);
								service.execute(context);
								ServiceResponse serviceResponse = context.getResponse();
							}
						}
						else
						{
							// This can be disabled to get faster response if there is no modification of remote file
							content.setAssetLastSync(DateUtil.now());
							content.save();
						}
					}
				}
				
				if (newImage)
				{
					// new file added to remote folder
					newImagesCount++;

					if (firstConnectRequest)
					{
						if ( !sftp.connect() )
							throw new Exception("Error connecting to SFTP Repository in SyncSftpRepoContentObjectBackgroundTask");
						firstConnectRequest = false;
					}
					
					byte[] bytes = sftp.downloadFile(sftpImage.getFilePath());
					if ( bytes != null ) {
						ByteArrayInputStream in = new ByteArrayInputStream(bytes);
						MockMultipartFile imageFile = new MockMultipartFile(sftpImage.getFilename(), sftpImage.getFilename(), null, in);
						in.close();
	
						// Content Library Instance creation
						bulkCount++;
						bulkSize += sftpImage.getSize();
						fileList.add(imageFile);
						fileNameList.add(sftpImage.getName());
						sftpRepoImageList.add(sftpImage);
						
						// upload new content in bulk of [configured bulk size] files or [configured bulk size]MB 
						if ( bulkCount == sftpRepoBulkSize || bulkSize > 1024*1024*sftpRepoBulkSize ) {
							ServiceExecutionContext createContenContext = ContentObjectBulkImageCreateService.createContextSftpRepo(requestor, fileList, fileNameList, metatags, docIds, variableContentEnabled, activate, sftpRepoImageList);
							Service service = MessagepointServiceFactory.getInstance().lookupService(ContentObjectBulkImageCreateService.SERVICE_NAME, ContentObjectBulkImageCreateService.class);
							service.execute(createContenContext);
							// cleanup
							bulkCount = bulkSize = 0;
							fileList.clear();
							fileNameList.clear();
							sftpRepoImageList.clear();
						}
					} else {
						log.error("Failed to download file " + sftpImage.getFilePath() + " from SFTP Repo.");
						hasError = true;
					}
				}
				
				if (updatedContent)
					updatedImagesCount++;				
				
				currentProgress += progressIncrementPerFile;
				if ( percentCount < (int)currentProgress ) {
					percentCount = (int)currentProgress;
					this.statusPollingBackgroundTask.setProgressInPercentInThread(percentCount);
				}
				
			}			
			
			if ( bulkCount > 0 ) {
				ServiceExecutionContext createContenContext = ContentObjectBulkImageCreateService.createContextSftpRepo(requestor, fileList, fileNameList, metatags, docIds, variableContentEnabled, activate, sftpRepoImageList);
				Service service = MessagepointServiceFactory.getInstance().lookupService(ContentObjectBulkImageCreateService.SERVICE_NAME, ContentObjectBulkImageCreateService.class);
				service.execute(createContenContext);
			}

			log.info(String.format("%d new files uploaded and %d files updated in %d contents of Content Library Images from remote SFTP Repository.", 
			         newImagesCount, updatedImagesCount, updatedContentCount));
			this.statusPollingBackgroundTask.setProgressInPercentInThread(100);
			this.statusPollingBackgroundTask.setCompletedDate(new Date(System.currentTimeMillis()));
			if ( !hasError )
				this.statusPollingBackgroundTask.setComplete(true);
			else
				this.statusPollingBackgroundTask.setError(true);
			this.statusPollingBackgroundTask.setActive(false);

			this.statusPollingBackgroundTask.save();
		} catch (Exception e) {
			log.error("Unexpected exception when invoking in SyncSftpRepoContentObjectBackgroundTask", e);
			this.statusPollingBackgroundTask.setError(true);
			this.statusPollingBackgroundTask.setActive(false);
			this.statusPollingBackgroundTask.save();
		} finally {
			if ( sftp != null )
				sftp.disconnect();
		}
	}

	public static Date parseDateStr(String date, String format) {
		Date rtnDate = null;
		try {
			SimpleDateFormat sdf = new SimpleDateFormat(format, Locale.US);
			sdf.setLenient(false);
			rtnDate = sdf.parse(date);
	    }catch (ParseException e) {
	    	return null;
	    }catch (IllegalArgumentException e) {
	    	return null;
	    }
	    return rtnDate;
	}	

}