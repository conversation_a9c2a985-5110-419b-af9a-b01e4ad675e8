package com.prinova.messagepoint.platform.services.rationalizer.dto;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;

public class RationalizerContentCommandUpdateFieldWithSameValue implements RationalizerContentCommand {

    private String schemaName;
    private long rationalizerApplicationId;
    private String dotSeparatedPathFieldName;
    private List<String> fieldValue;
    private List<String> elasticSearchGuids;

    public RationalizerContentCommandUpdateFieldWithSameValue() {
    }

    @Override
    public String getSchemaName() {
        return schemaName;
    }

    public void setSchemaName(String schemaName) {
        this.schemaName = schemaName;
    }

    @Override
    public long getRationalizerApplicationId() {
        return rationalizerApplicationId;
    }

    public void setRationalizerApplicationId(long rationalizerApplicationId) {
        this.rationalizerApplicationId = rationalizerApplicationId;
    }

    public String getDotSeparatedPathFieldName() {
        return dotSeparatedPathFieldName;
    }

    public void setDotSeparatedPathFieldName(String dotSeparatedPathFieldName) {
        this.dotSeparatedPathFieldName = dotSeparatedPathFieldName;
    }

    public List<String> getFieldValue() {
        return fieldValue;
    }

    public void setFieldValue(List<String> fieldValue) {
        this.fieldValue = new LinkedList<>(fieldValue);
    }

    public List<String> getElasticSearchGuids() {
        return elasticSearchGuids;
    }

    public void setElasticSearchGuids(Collection<String> elasticSearchGuids) {
        this.elasticSearchGuids = new LinkedList<>(elasticSearchGuids);
    }
}
