package com.prinova.messagepoint.controller.content;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.font.ParagraphStyle;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;

public class ParagraphStyleListWrapper {
	private List<Long>						selectedIds;
	private String							actionValue;
	private String 							cloneName;
	private String							whereUsedReportId 	= DateUtil.timeStamp();
	
	public ParagraphStyleListWrapper() {
		super();
		this.selectedIds 		= new ArrayList<>();
	}
	
	public String getActionValue() {
		return actionValue;
	}
	
	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}
	
	public String getCloneName() {
		return cloneName;
	}

	public void setCloneName(String cloneName) {
		this.cloneName = cloneName;
	}
	
	public List<Long> getSelectedIds() {
		return selectedIds;
	}

	public void setSelectedIds(List<Long> selectedIds) {
		this.selectedIds = selectedIds;
	}
	
	public List<ParagraphStyle> getSelectedList(){
		List<ParagraphStyle> selectedList = new ArrayList<>();
		for(Long selectedId : this.selectedIds){
			selectedList.add(HibernateUtil.getManager().getObject(ParagraphStyle.class, selectedId));
		}
		return selectedList;
	}

	public String getWhereUsedReportId() {
		return whereUsedReportId;
	}

	public void setWhereUsedReportId(String whereUsedReportId) {
		this.whereUsedReportId = whereUsedReportId;
	}
}
