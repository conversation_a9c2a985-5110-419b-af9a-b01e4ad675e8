package com.prinova.messagepoint.platform.services.admin;

import java.util.ArrayList;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.springframework.orm.hibernate5.SessionHolder;

import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.wrapper.UserSettingsWrapper;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleResourceAwareServiceExeContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.notification.CreateOrUpdateNotificationSettingsService;
import com.prinova.messagepoint.util.BranchUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.UserUtil;

public class UpdateUserSettingsService extends AbstractService {

	public static final String SERVICE_NAME = "admin.UpdateUserSettingsService";
	private static final Log log = LogUtil.getLog(UpdateUserSettingsService.class);

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			UpdateUserSettingsServiceRequest request = (UpdateUserSettingsServiceRequest) context.getRequest();
			UserSettingsWrapper userSettingsWrapper = request.getUserSettingsWrapper();
			User user = request.getUser();
			User dbUser = User.findById(user.getId());
			User homeDomainUser = dbUser.getHomeDcsUser();
			User localDomainUser = dbUser.getLocalDcsUser();
			boolean domainUserChange = false;
			boolean localDomainUserChange = false;
			int numChangesSaved = 0;
    		int resultCount = 0;

    		// Save the selected defaultNodeId for this User
    		if ( (userSettingsWrapper.getDefaultNodeId() >= 0) && (userSettingsWrapper.getDefaultNodeId() != user.getDefaultNodeId()) ) {
    			dbUser.setDefaultNodeId(userSettingsWrapper.getDefaultNodeId());
    			if (localDomainUser != null)
    			{
    				localDomainUser.setDefaultNodeId(userSettingsWrapper.getDefaultNodeId());
    				localDomainUserChange = true;
    			}
    			if (log.isDebugEnabled()) {
        	    	log.debug("Updated " + resultCount + " row for defaultNodeId=" + userSettingsWrapper.getDefaultNodeId() + ", where userId=" + userSettingsWrapper.getUserId() + " and user.email='" + userSettingsWrapper.getEmail() + "'...");
    			}
    			// Manually update the in-memory user object since this is controlled and managed by ACEGI Security and not us...
    			user.setDefaultNodeId(userSettingsWrapper.getDefaultNodeId());
    			numChangesSaved++;
    		}

    		// Save the selected defaultTabId for this User
    		if ( (userSettingsWrapper.getDefaultTabId() >= 0) && (userSettingsWrapper.getDefaultTabId() != user.getDefaultTabId()) ) {
    			dbUser.setDefaultTabId(userSettingsWrapper.getDefaultTabId());
    			if (log.isDebugEnabled()) {
        	    	log.debug("Updated " + resultCount + " row for defaultTabId=" + userSettingsWrapper.getDefaultTabId() + ", where userId=" + userSettingsWrapper.getUserId() + " and user.email='" + userSettingsWrapper.getEmail() + "'...");
    			}
    			// Manually update the in-memory user object since this is controlled and managed by ACEGI Security and not us...
    			user.setDefaultTabId(userSettingsWrapper.getDefaultTabId());
    			numChangesSaved++;
    		}

    		// Save the Email Notification flags
    		if (userSettingsWrapper.getReceiveDailySummaryEmail() != user.isEmailNotifyDaily()) {
    			dbUser.setEmailNotifyDaily(userSettingsWrapper.getReceiveDailySummaryEmail());
    			if (log.isDebugEnabled()) {
        	    	log.debug("Updated " + resultCount + " row for emailNotifyDaily=" + userSettingsWrapper.getReceiveDailySummaryEmail() + ", where userId=" + userSettingsWrapper.getUserId() + " and user.email='" + userSettingsWrapper.getEmail() + "'...");
    			}
    			// Manually update the in-memory user object since this is controlled and managed by ACEGI Security and not us...
    			user.setEmailNotifyDaily(userSettingsWrapper.getReceiveDailySummaryEmail());
    			numChangesSaved++;
    		}
    		if (userSettingsWrapper.getReceiveRealTimeEmail() != user.isEmailNotifyRealTime()) {
    			dbUser.setEmailNotifyRealTime(userSettingsWrapper.getReceiveRealTimeEmail());
    			if (log.isDebugEnabled()) {
        	    	log.debug("Updated " + resultCount + " row for emailNotifyRealTime=" + userSettingsWrapper.getReceiveRealTimeEmail() + ", where userId=" + userSettingsWrapper.getUserId() + " and user.email='" + userSettingsWrapper.getEmail() + "'...");
    			}
    			// Manually update the in-memory user object since this is controlled and managed by ACEGI Security and not us...
    			user.setEmailNotifyRealTime(userSettingsWrapper.getReceiveRealTimeEmail());
    			CreateOrUpdateNotificationSettingsService.syncBranchLevelNotificationSetting(user.getId(), userSettingsWrapper.getReceiveRealTimeEmail(), CreateOrUpdateNotificationSettingsService.TARGET_PROP_EMAIL_NOTIFICATION, 0);
    			numChangesSaved++;
    		}

    		// check if the user changed their password and update it
    		if ( (userSettingsWrapper.getNewPassword() != null) &&
    			 (!"".equals(userSettingsWrapper.getNewPassword())) && 
    			 ( (user.getPassword() != null) && (!userSettingsWrapper.getNewPassword().equals(user.getPassword())) ) ) 
    		{
    			if ( user.getSalt() == null )
    			{
    				user.createSalt();
    				dbUser.setSalt(user.getSalt());
    				if (homeDomainUser != null)
    					homeDomainUser.setSalt(user.getSalt());
    			}
    			
    			String encryptedPassword = UserUtil.hashAndSaltPassword(userSettingsWrapper.getNewPassword(), user);
    			
				dbUser.addPasswordHistory(encryptedPassword);
				dbUser.setPassword(encryptedPassword);
				dbUser.setPasswordLastUpdated(DateUtil.now());
				if (homeDomainUser != null)
				{
					homeDomainUser.addPasswordHistory(encryptedPassword);
					homeDomainUser.setPassword(encryptedPassword);
					homeDomainUser.setPasswordLastUpdated(DateUtil.now());
				}

				domainUserChange = true;
				
				if (user.isPrinovaUser() && userSettingsWrapper.isCascadePasswordChange())
				{
					Branch currentBranch = Node.getCurrentBranch();
					
					for(Branch branch: BranchUtil.getPossibleEnabledBranchesByRequestor(currentBranch, UserUtil.getPrincipalUser()))
					{
						if (branch.getId() == currentBranch.getId())
							continue;
						
						Node node = branch.getDcsNode();
						
						if (node.isAccessible())
						{
							SessionHolder localSessionHolder = HibernateUtil.getManager().openTemporarySession(node.getSchemaName());
					    	
							ArrayList<MessagepointCriterion> critList = new ArrayList<>();
							critList.add(MessagepointRestrictions.eq("id", user.getId()));
							critList.add(MessagepointRestrictions.eq("username", user.getUsername()));
							
							User nodeUser = HibernateUtil.getManager().getObjectUnique(User.class, critList);

							if (nodeUser != null)
							{
				    			if ( nodeUser.getSalt() == null )
				    			{
				    				nodeUser.createSalt();
				    			}
				    			
			        			encryptedPassword = UserUtil.hashAndSaltPassword(userSettingsWrapper.getNewPassword(), nodeUser);
			        			
			        			nodeUser.addPasswordHistory(encryptedPassword);
			        			nodeUser.setPassword(encryptedPassword);
			        			nodeUser.setPasswordLastUpdated(DateUtil.now());
			        			nodeUser.save();
							}
							
							HibernateUtil.getManager().restoreSession(localSessionHolder);
						}
					}
				}
				
				if(user.isEmailNotifyRealTime()){
					UserUtil.sendPasswordChangedEmail(user, userSettingsWrapper.getNewPassword());
				}
				// Manually update the in-memory user object since this is controlled and managed by ACEGI Security and not us...
        	    user.setPassword(encryptedPassword);
        	    numChangesSaved++;
        	    if (log.isDebugEnabled()) {
        	    	log.debug("Updated " + resultCount + " row for password='*********', where userId=" + userSettingsWrapper.getUserId() + " and user.email='" + userSettingsWrapper.getEmail() + "'...");
    			}
        	    // Audit (Password change from "My Settings")
        	    AuditEventUtil.push(AuditEventType.ID_USER, AuditObjectType.ID_USER, user.getUsername(), user.getId(), AuditActionType.ID_PASSWORD_CHANGE, null);
    		}

    		// check if the user changed their email and update it
    		if ( (userSettingsWrapper.getEmail() != null) && (!userSettingsWrapper.getEmail().isEmpty()) && (!userSettingsWrapper.getEmail().equals(user.getEmail())) ) {
    			dbUser.setEmail(userSettingsWrapper.getEmail());
    			if (log.isDebugEnabled()) {
        	    	log.debug("Updated " + resultCount + " row for email='', where userId=" + userSettingsWrapper.getEmail() + "...");
    			}
    			// Manually update the in-memory user object since this is controlled and managed by ACEGI Security and not us...
    			user.setEmail(userSettingsWrapper.getEmail());
    			numChangesSaved++;
    		}
    		if(numChangesSaved > 0){
    			// save any changes
    			HibernateUtil.getManager().saveObject(dbUser);
				
    			if (homeDomainUser != null && domainUserChange)
				{
			    	SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(homeDomainUser.getSchemaOfThisUser());
			    	homeDomainUser.save();
					HibernateUtil.getManager().restoreSession(mainSessionHolder);
				}
				
				if (localDomainUser != null && localDomainUserChange)
				{
			    	SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(localDomainUser.getSchemaOfThisUser());
			    	localDomainUser.save();
					HibernateUtil.getManager().restoreSession(mainSessionHolder);
				}
    		}
    		UpdateUserSettingsServiceResponse response = (UpdateUserSettingsServiceResponse) context.getResponse();
    		response.setNumChangesSaved(numChangesSaved);

		} catch (Exception e) {
			log.error(" unexpected exception when invoking UpdateUserSettingsService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
				context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
	}

	public static ServiceExecutionContext createContext(UserSettingsWrapper userSettingsWrapper,
														User user) {

		SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
		context.setServiceName(SERVICE_NAME);

		UpdateUserSettingsServiceRequest request = new UpdateUserSettingsServiceRequest();
		context.setRequest(request);

		request.setUserSettingsWrapper(userSettingsWrapper);
		request.setUser(user);

		UpdateUserSettingsServiceResponse serviceResp = new UpdateUserSettingsServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

}
