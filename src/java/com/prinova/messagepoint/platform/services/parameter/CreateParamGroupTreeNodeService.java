package com.prinova.messagepoint.platform.services.parameter;

import java.util.*;

import com.prinova.messagepoint.model.admin.ParameterGroupInstance;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.admin.ParameterGroup;
import com.prinova.messagepoint.model.admin.ParameterGroupInstanceCollection;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;

public class CreateParamGroupTreeNodeService extends AbstractService{

	private static final Log log = LogUtil.getLog(CreateParamGroupTreeNodeService.class);
	public static final String SERVICE_NAME = "parameter.CreateParamGroupTreeNodeService";

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}	
			//
			CreateParamGroupTreeNodeServiceRequest request = (CreateParamGroupTreeNodeServiceRequest)context.getRequest();
			if(request!=null){
				ParameterGroupInstanceCollection pgInstanceCollection = null;
				ParameterGroupTreeNode pgTreeNode = new ParameterGroupTreeNode();
				long parentNodeId = request.getParentNodeId();
				if(parentNodeId>0){
					ParameterGroupTreeNode parentNode = ParameterGroupTreeNode.findById(parentNodeId);
					if(parentNode!=null){
						pgTreeNode.setParentNode(parentNode);
						
					}
				}
				if(request.getPgtnDna() != null) {
				    pgTreeNode.setDna(request.getPgtnDna());
				}
				long paramGroupId = request.getParameterGroupId();
				ParameterGroup pg = ParameterGroup.findById(paramGroupId);
				pgTreeNode.setParameterGroup(pg);
				
				if(request.getPgInstanceCollectionId()>0){
					//use shared collection
					pgInstanceCollection = ParameterGroupInstanceCollection.findById(request.getPgInstanceCollectionId());
					pgTreeNode.setName(pgInstanceCollection.getName());
				}else{
					//create new collection
					pgTreeNode.setName(request.getName());
					List<String> itemValues = new ArrayList<>(Arrays.asList(request.getValues()));

					pgInstanceCollection = new ParameterGroupInstanceCollection();
					pgInstanceCollection.setName(request.getName());
					pgInstanceCollection.setShared(request.isSharedFlag());
					if (request.getPgInstanceCollectionGuid() != null)
						pgInstanceCollection.setGuid(request.getPgInstanceCollectionGuid());

					Set<ParameterGroupInstance> parameterGroupInstances = new HashSet<>();

					ParameterGroupInstance.removeEmptyValuesFromEnd(itemValues);

					List<List<String>> flatValues = ParameterGroupInstance.flattenValue(itemValues);

					for (List<String> flatValue : flatValues) {
						if (!flatValue.isEmpty())
						{
							ParameterGroupInstance parameterGroupInstance = ParameterGroupInstance.createParameterGroupInstance(request.getParameterGroupId(), flatValue);
							parameterGroupInstance.setParameterGroupInstanceCollection(pgInstanceCollection);
							parameterGroupInstances.add(parameterGroupInstance);
						}
					}

					pgInstanceCollection.replaceParameterGroupInstances(parameterGroupInstances);

					pgInstanceCollection.save();
				}
			
				if(pgInstanceCollection!=null){
					pgTreeNode.setParameterGroupInstanceCollection(pgInstanceCollection);
					if(request.getName()==null || request.getName().trim().isEmpty()){
						pgTreeNode.setName(pgInstanceCollection.getName());
					} 
				}
			
				pgTreeNode.save(true);
				context.getResponse().setResultValueBean(pgTreeNode);
			}
		} catch (Exception e) {
				String logInfo = "Unexpected exception has occured in CreateParamGroupTreeNodeService.execute(ServiceExecutionContext)...";
				log.error(logInfo, e);
				getResponse(context).addErrorMessage(getErrorMessageKey(context),
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					context.getServiceName() + e, context.getLocale());
				throw new RuntimeException(e.getMessage());
			}
		
	}

	public void validate(ServiceExecutionContext context) {
	
		
	}
	
    public static ServiceExecutionContext createContext(String name, String[] values, boolean isShared, long parameterGroupId, long parentNodeId, long pgInstanceCollectionId, String pgInstanceCollectionGuid) {
        return createContext(name, values, isShared, parameterGroupId, parentNodeId, pgInstanceCollectionId, pgInstanceCollectionGuid, null);
    }
    
	public static ServiceExecutionContext createContext(String name, String[] values, boolean isShared, long parameterGroupId, long parentNodeId, long pgInstanceCollectionId, String pgInstanceCollectionGuid, String pgtnDna) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		CreateParamGroupTreeNodeServiceRequest request = new CreateParamGroupTreeNodeServiceRequest();
		context.setRequest(request);

		request.setName(name);
	
		request.setParameterGroupId(parameterGroupId);
		request.setParentNodeId(parentNodeId);
		request.setPgInstanceCollectionId(pgInstanceCollectionId);
		request.setPgInstanceCollectionGuid(pgInstanceCollectionGuid);
		request.setSharedFlag(isShared);
		request.setValues(values);
		request.setPgtnDna(pgtnDna);
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

}
