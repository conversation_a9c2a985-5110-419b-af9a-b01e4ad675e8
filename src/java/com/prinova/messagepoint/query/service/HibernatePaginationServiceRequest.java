package com.prinova.messagepoint.query.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import javax.persistence.criteria.JoinType;

import com.prinova.messagepoint.query.IConverter;
import com.prinova.messagepoint.query.QueryBuilder;
import com.prinova.messagepoint.query.handler.PostQueryHandler;

public class HibernatePaginationServiceRequest extends AbstractPaginationServiceRequest {

	private static final long serialVersionUID = -712573171757783635L;
	
	private List<Long> objectIds = new ArrayList<>();
	
	private List<MessagepointCriterion> firstLevelCriterionList = new ArrayList<>();
	private List<MessagepointCriterion> secondLevelCriterionList = new ArrayList<>();
	
	private Map<String, String> firstLevelJoinAlias = new HashMap<>();
	private Map<String, String> secondLevelJoinAlias = new HashMap<>();
	
	private Map<String, JoinType> secondLevelJoinType = new HashMap<>();
	
	private IConverter converter = null;
	private PostQueryHandler handler = null;

	public List<Long> getObjectIds() {
		return objectIds;
	}

	public void setObjectIds(List<Long> objectIds) {
		this.objectIds = objectIds;
	}

	public IConverter getConverter() {
		return converter;
	}

	public void setConverter(IConverter converter) {
		this.converter = converter;
	}

	public PostQueryHandler getHandler() {
		return handler;
	}

	public void setHandler(PostQueryHandler handler) {
		this.handler = handler;
	}

	public HibernatePaginationServiceRequest() {
		this.queryType = QueryBuilder.HIBERNATE;
	}
	
	public List<MessagepointCriterion> getFirstLevelCriterionList() {
		return firstLevelCriterionList;
	}

	public void setFirstLevelCriterionList(List<MessagepointCriterion> firstLevelCriterionList) {
		this.firstLevelCriterionList = firstLevelCriterionList;
	}

	public List<MessagepointCriterion> getSecondLevelCriterionList() {
		return secondLevelCriterionList;
	}

	public void setSecondLevelCriterionList(List<MessagepointCriterion> secondLevelCriterionList) {
		this.secondLevelCriterionList = secondLevelCriterionList;
	}

	public Map<String, String> getFirstLevelJoinAlias() {
		return firstLevelJoinAlias;
	}

	public void setFirstLevelJoinAlias(Map<String, String> firstLevelJoinAlias) {
		this.firstLevelJoinAlias = firstLevelJoinAlias;
	}

	public Map<String, String> getSecondLevelJoinAlias() {
		return secondLevelJoinAlias;
	}

	public void setSecondLevelJoinAlias(Map<String, String> secondLevelJoinAlias) {
		this.secondLevelJoinAlias = secondLevelJoinAlias;
	}

	public Map<String, JoinType> getSecondLevelJoinType() {
		return secondLevelJoinType;
	}

	public void setSecondLevelJoinType(Map<String, JoinType> secondLevelJoinType) {
		this.secondLevelJoinType = secondLevelJoinType;
	}	
}
