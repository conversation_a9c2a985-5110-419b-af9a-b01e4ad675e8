package com.prinova.migrate.steps.messagepoint;

import java.sql.Connection;
import com.prinova.migrate.MigrationStep;
import com.prinova.migrate.models.ColumnDefinition;

public class Step1306_v017200_AddRefreshPageToBackgroundTask extends MigrationStep {
	@Override
	public void migrate(Connection conn) throws Exception {
		if ( !columnExists( conn, "status_polling_background_task", "refresh_page" ) ){
			addColumn(conn, "status_polling_background_task", ColumnDefinition.createBoolean("refresh_page", true));
		}
	}
}
