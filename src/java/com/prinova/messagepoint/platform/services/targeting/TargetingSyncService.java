package com.prinova.messagepoint.platform.services.targeting;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.prinova.messagepoint.model.admin.SyncHistory;
import com.prinova.messagepoint.model.content.ContentObjectData;
import org.apache.commons.logging.Log;
import org.hibernate.Session;
import org.springframework.orm.hibernate5.SessionHolder;

import com.google.common.collect.Multimap;
import com.google.common.io.Files;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.ContentTargeting;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.common.SyncObjectType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.model.targeting.ConditionItem;
import com.prinova.messagepoint.model.targeting.ConditionItemValue;
import com.prinova.messagepoint.model.targeting.ConditionSubelement;
import com.prinova.messagepoint.model.targeting.FilterCondition;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.targeting.TargetGroupInstance;
import com.prinova.messagepoint.model.util.SyncTouchpointUtil;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleResourceAwareServiceExeContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.ConditionElementUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.wtu.ReferencableObject;

public class TargetingSyncService extends AbstractService {
    public static final int ACTION_SYNC_TARGET_RULES = 1;
    public static final int ACTION_SYNC_TARGET_GROUPS = 2;
    public static final int ACTION_DUPLICATE_TARGET_RULES = 3;
    public static final int ACTION_DUPLICATE_TARGET_GROUPS = 4;

	public static final String SERVICE_NAME = "targeting.TargetingSyncService";
    private static final Log log = LogUtil.getLog(TargetingSyncService.class);

    public static ThreadLocal<Session> saveSessionInThread = new ThreadLocal<>();
    public static ThreadLocal<Session> sourceSessionInThread = new ThreadLocal<>();
    
	public void execute(ServiceExecutionContext context) {
        SessionHolder podMasterSessionHolder = null;
        SessionHolder mainSessionHolder = null;
        SessionHolder saveSessionHolder = null;
        boolean sessionSwitched = false;
        
		try {
            log.info("TargetingSyncService starts");
            
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			TargetingSyncServiceRequest request = (TargetingSyncServiceRequest) context.getRequest();

            boolean syncFromOther = request.isSyncUpdate();
            
            String currentSchema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
            String requestSchema = request.getSourceSchema();
            
            String sourceSchema = syncFromOther ? requestSchema : currentSchema;
            String targetSchema = syncFromOther ? currentSchema : requestSchema;

            User requestor = request.getRequestor();
            
            if(syncFromOther) {
                if (sourceSchema != null && !MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(sourceSchema)) {
                    Session saveSession = HibernateUtil.getManager().getSession();
                    podMasterSessionHolder = HibernateUtil.getManager().openTemporarySession(null);
                    Session podMasterSession = HibernateUtil.getManager().getSession();
                    mainSessionHolder = HibernateUtil.getManager().openTemporarySession(sourceSchema);
                    Session sourceSession = HibernateUtil.getManager().getSession();
                    CloneHelper.startCrossInstanceClone();
                    CloneHelper.addSession(null, podMasterSession);
                    CloneHelper.addSession(targetSchema, saveSession);
                    CloneHelper.addSession(sourceSchema, sourceSession);
                    sessionSwitched = true;
                    saveSessionInThread.set(saveSession);
                    sourceSessionInThread.set(sourceSession);
                    
                }
            } else {
                if (targetSchema != null && !MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(targetSchema)) {
                    Node targetNode = Node.findBySchema(targetSchema);
                    podMasterSessionHolder = HibernateUtil.getManager().openTemporarySession(null);
                    Session podMasterSession = HibernateUtil.getManager().getSession();
                    mainSessionHolder = HibernateUtil.getManager().openTemporarySession(targetSchema);
                    Session saveSession = HibernateUtil.getManager().getSession();
                    saveSessionHolder = HibernateUtil.getManager().openTemporarySession(sourceSchema);
                    Session sourceSession = HibernateUtil.getManager().getSession();
                    CloneHelper.startCrossInstanceClone();
                    CloneHelper.addSession(null, podMasterSession);
                    CloneHelper.addSession(targetSchema, saveSession);
                    CloneHelper.addSession(sourceSchema, sourceSession);
                    sessionSwitched = true;
                    saveSessionInThread.set(saveSession);
                    sourceSessionInThread.set(sourceSession);
                    
                    
                    User targetUser = requestor.getNodeUser(targetNode);
                    if(targetUser != null) {
                        requestor = targetUser; 
                    }
                }
            }
            
            request.setRequestor(requestor);
            CloneHelper.setRequestor(requestor);
            
			if ( request.getActionId() == ACTION_SYNC_TARGET_RULES ) {
				syncTargetRules( context, request, false );
			} else if ( request.getActionId() == ACTION_DUPLICATE_TARGET_RULES ) {
				syncTargetRules( context, request, true );
			} else if ( request.getActionId() == ACTION_SYNC_TARGET_GROUPS ) {
				syncTargetGroups( context, request, false );
			} else if ( request.getActionId() == ACTION_DUPLICATE_TARGET_GROUPS ) {
				syncTargetGroups( context, request, true );
			}

            context.getResponse().setResultValueBean( 1L );
            log.info("TargetingSyncService ends");
		} catch (Exception e) {
			log.error(" unexpected exception when invoking TargetingSyncService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
				context.getLocale());
			throw new RuntimeException(e.getMessage());
		} finally {
            if (sessionSwitched) {
                CloneHelper.stopCrossInstanceClone();
                
                if(saveSessionHolder != null)
                    HibernateUtil.getManager().restoreSession(saveSessionHolder);
                if(mainSessionHolder != null)
                    HibernateUtil.getManager().restoreSession(mainSessionHolder);
                if(podMasterSessionHolder != null)
                    HibernateUtil.getManager().restoreSession(podMasterSessionHolder);
            }
		}
	}
	
	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
	
	}

	private void syncTargetRules ( ServiceExecutionContext context, TargetingSyncServiceRequest request, boolean isClone ) {
        User requestor = request.getRequestor();
        boolean syncFromOther = request.isSyncUpdate();
        Long sourceDocumentId = request.getSourceDocumentId();
        Long targetDocumentId = request.getTargetDocumentId();
        boolean logDifferences = request.isLogDifferences();
        boolean hideUntilNextChange = request.isHideUntilNextChange();
        Document targetDocument = CloneHelper.queryInSaveSession(()->Document.findById(targetDocumentId));
        Multimap<Long, Map<Long, Long>> syncObjectMap = request.getSyncObjectMap();
        List<Map<Long, Long>> targetRuleStatusMapList = (List<Map<Long, Long>>) syncObjectMap.get((long) SyncObjectType.ID_TARGET_RULE);
        Map<ConditionElement, ConditionElement> clonedTargetRulesMap = new HashMap<>();
        for(Map<Long, Long> syncObjectStatusMap : targetRuleStatusMapList) {
        	for(Long objectIdx10 : syncObjectStatusMap.keySet()) {
                long objectId = objectIdx10 / 0x10;
                long objectStatus = syncObjectStatusMap.get(objectIdx10);
                boolean objectIsReferenced = (objectStatus & 0x100000) > 0;
                boolean objectIsFromOther = (objectStatus & 0x10000) > 0;
                objectStatus = objectStatus & 0xFFFF;
                if (syncFromOther != objectIsFromOther)
                    continue;
                
                ConditionElement sourceConditionElement = objectIsFromOther ? ConditionElement.findById(objectId) : CloneHelper.queryInSaveSession(()->ConditionElement.findById(objectId));
                if(sourceConditionElement != null) {
                	String ruleName = sourceConditionElement.getName();
                    
                	String guid =  objectIsFromOther ? sourceConditionElement.getGuid() : CloneHelper.queryInSaveSession(()->sourceConditionElement.getGuid());
                    ConditionElement targetConditionElement = objectIsFromOther ? CloneHelper.queryInSaveSession(()->ConditionElement.findByGuid(guid)) : ConditionElement.findByGuid(guid);

                    if(targetConditionElement == null) {
                        for(ConditionSubelement sourceConditionSubelement : sourceConditionElement.getSubElements()) {
                            String conditionSubelementGuid = sourceConditionSubelement.getGuid();
                            targetConditionElement = CloneHelper.queryInSaveSession(() -> {
                                ConditionSubelement targetConditionSubelement = ConditionSubelement.findByGuid(conditionSubelementGuid);
                                return targetConditionSubelement == null ? null : targetConditionSubelement.getElement();
                            });
                            if(targetConditionElement != null) break;
                        }
                    }

                    if(hideUntilNextChange && targetConditionElement != null) {
                        log.info("Hide target rule source id = (" + sourceConditionElement.getId() + "), name = \"" + ruleName + "\"");
                    } else if(isClone) {
                        log.info("Clone target rule source id = (" + sourceConditionElement.getId() + "), name = \"" + ruleName + "\"");
                	} else {
                        log.info("Sync " + (objectIsReferenced ? "referenced" : "selected") + " target rule source id = (" + sourceConditionElement.getId() + "), name = \"" + ruleName + "\"");
                        if(logDifferences) {
            	            SyncTouchpointUtil.logSyncReason(log, sourceConditionElement, targetConditionElement);
                        }
                	}
                	
                    
                    if(targetConditionElement == null) {
                    	targetConditionElement = CloneHelper.clone(sourceConditionElement);
                    } else if(! hideUntilNextChange){
                    	syncConditionElement(sourceConditionElement, targetConditionElement);
                    }
                    ConditionElement targetConditionElementFinal = targetConditionElement;
                    CloneHelper.execInSaveSession(()->targetConditionElementFinal.save());
                    clonedTargetRulesMap.put(targetConditionElement, sourceConditionElement);
                    doHibernateUtilFlush();
                    doHibernateUtilClear();
                }
        	}
        }

        Map<Long, Long> newClonedMap = CloneHelper.getNewClonedTargetRuleMap();
        for(Long sourceId : newClonedMap.keySet()) {
            Long targetId = newClonedMap.get(sourceId);
            if(! clonedTargetRulesMap.keySet().stream().anyMatch(targetRule->targetRule.getId() == targetId.longValue())) {
                ConditionElement sourceConditionElement = ConditionElement.findById(sourceId);
                ConditionElement targetConditionElement = CloneHelper.queryInSaveSession(()->ConditionElement.findById(targetId));
                if(targetConditionElement != null) {
                    clonedTargetRulesMap.put(targetConditionElement, sourceConditionElement);
                }
            }
        }

        String sourceNodeGuid = Node.getCurrentNode().getGuid();
        String targetNodeGuid = CloneHelper.queryInSaveSession(() -> Node.getCurrentNode().getGuid());

        SyncTouchpointUtil.updateModelSyncHistory(isClone ? SyncHistory.SYNC_TYPE_CLONING : SyncHistory.SYNC_TYPE_SYNCHRONIZING, SyncObjectType.ID_TARGET_RULE, clonedTargetRulesMap, sourceDocumentId, targetDocumentId, requestor.getId(), sourceNodeGuid, targetNodeGuid, hideUntilNextChange);

        doHibernateUtilFlush();
        doHibernateUtilClear();
	}
	
	private void syncConditionElement(ConditionElement sourceConditionElement, ConditionElement targetConditionElement) {
    	targetConditionElement.setName(sourceConditionElement.getName());
    	targetConditionElement.setFormName(sourceConditionElement.getFormName());
    	
    	targetConditionElement.setConditionType(CloneHelper.assignConstObject(sourceConditionElement.getConditionType()));
    	
    	ConditionElement targetConditionElementFinal = targetConditionElement;

    	List<ConditionSubelement> targetSubElementsList = new ArrayList<>();
    	for(ConditionSubelement sourceConditionSubelement : sourceConditionElement.getSubElements()) {
    		String conditionSubelementGuid = sourceConditionSubelement.getGuid();
    		ConditionSubelement targetConditionSubelement = 
    			CloneHelper.queryInSaveSession(()->targetConditionElementFinal.getSubElements())
    				.stream()
    				.filter(cse->{
    					return cse != null && cse.getGuid().equals(conditionSubelementGuid);
    				})
    				.findFirst()
    				.orElse(null);
    		if(targetConditionSubelement == null) {
    			targetConditionSubelement =  CloneHelper.clone(sourceConditionSubelement, o->o.clone(targetConditionElementFinal));
    			ConditionSubelement targetConditionSubelementFinal = targetConditionSubelement;
    			CloneHelper.execInSaveSession(()->{
    				targetConditionSubelementFinal.save();
    			});
    			targetSubElementsList.add(targetConditionSubelement);
    		} else {
    		    ConditionSubelement targetConditionSubelementFinal = targetConditionSubelement;
    		    CloneHelper.execInSaveSession(()->targetConditionElementFinal.getSubElements().remove(targetConditionSubelementFinal));
    			targetSubElementsList.add(targetConditionSubelement);
    			
    			targetConditionSubelement.setName          			 (sourceConditionSubelement.getName());
    			targetConditionSubelement.setConditionType 			 (CloneHelper.assignConstObject(sourceConditionSubelement.getConditionType()));
    			targetConditionSubelement.setDataElementVariable	 (CloneHelper.assign(sourceConditionSubelement.getDataElementVariable()));
    			targetConditionSubelement.setDataElementComparisonId (sourceConditionSubelement.getDataElementComparisonId());
    			targetConditionSubelement.setParameterized           (sourceConditionSubelement.isParameterized());
    			targetConditionSubelement.setConditionOperator       (CloneHelper.assignConstObject(sourceConditionSubelement.getConditionOperator()));
    			Map<String, String> sourceConditionAttributes = sourceConditionSubelement.getConditionAttributes();
    			Map<String, String> targetConditionAttributes = new LinkedHashMap<>();
    		    if(sourceConditionAttributes != null) {
    		        for(String attributeName : sourceConditionAttributes.keySet()) {
    		            String attributeValue = sourceConditionAttributes.get(attributeName);
    		            if(attributeName.equals(ConditionElementUtil.VARIABLE_ID) || 
    		              attributeName.equals(ConditionElementUtil.VARIABLE_ID2)) {
    		                long variableId = Long.parseLong(attributeValue);
    		                DataElementVariable sourceDataElementVariable = DataElementVariable.findById(variableId);
    		                if(sourceDataElementVariable != null) {
    		                    DataElementVariable targetDataElementVariable = CloneHelper.assign(sourceDataElementVariable);
    		                    if(targetDataElementVariable != null) {
    		                        attributeValue = "" + targetDataElementVariable.getId();
    		                    }
    		                }
    		            }
    		            targetConditionAttributes.put(attributeName, attributeValue);
    		        }
    		    }
    		    targetConditionSubelement.setConditionAttributes(targetConditionAttributes);
    		    
    		    FilterCondition sourceFilterCondition = sourceConditionSubelement.getFilterCondition();
		    	FilterCondition targetFilterCondition = CloneHelper.queryInSaveSession(()->targetConditionSubelementFinal.getFilterCondition());
    		    if(sourceFilterCondition == null) {
    		    	if(targetFilterCondition != null) {
    		    		FilterCondition targetFilterConditionFinal = targetFilterCondition;
    		    		CloneHelper.execInSaveSession(()->targetFilterConditionFinal.delete());
    		    	}
    		    	targetConditionSubelement.setFilterCondition(null);
    		    } else {
    		    	targetFilterCondition = CloneHelper.queryInSaveSession(()->targetConditionSubelementFinal.getFilterCondition());
    		    	if(targetFilterCondition == null) {
    		    		targetFilterCondition = CloneHelper.clone(sourceFilterCondition, o->o.clone(targetConditionSubelementFinal));
    		    		FilterCondition targetFilterConditionFinal = targetFilterCondition;
    		    		CloneHelper.execInSaveSession(()->{
    		    			targetFilterConditionFinal.save();
    		    		});
        		    	targetConditionSubelement.setFilterCondition(targetFilterConditionFinal);
    		    	} else {
    		    		targetFilterCondition.setDataElementVariable(CloneHelper.assign(sourceFilterCondition.getDataElementVariable()));
    		    		targetFilterCondition.setDataElementComparisonId(sourceFilterCondition.getDataElementComparisonId());
    		    		
    		    		FilterCondition targetFilterConditionFinal = targetFilterCondition;
    		    		String targetDataFilePath = copyDataFile(sourceFilterCondition.getDataFilePath(), 
    		    				CloneHelper.queryInSaveSession(()->targetFilterConditionFinal.getDataFilePath()), 
    		    				CloneHelper.queryInSaveSession(()->targetFilterConditionFinal.getDataFileDirectoryPath()));
    		    		targetFilterCondition.setDataFilePath(targetDataFilePath);
    		    		
    		    		Map<String, String> sourceConditionValueMap = sourceFilterCondition.getConditionValueMap();
    		    		Map<String, String> targetConditionValueMap = new HashMap<>();
    		    		if(sourceConditionValueMap != null) {
    		    			targetConditionValueMap.putAll(sourceConditionValueMap);
    		    		}
    		    		targetFilterCondition.setConditionValueMap(targetConditionValueMap);
    		    		CloneHelper.execInSaveSession(()->targetFilterConditionFinal.save());
    		    	}
    		    }
    			
	    		String targetDataFilePath = copyDataFile(sourceConditionSubelement.getDataFilePath(), 
	    				CloneHelper.queryInSaveSession(()->targetConditionSubelementFinal.getDataFilePath()), 
	    				CloneHelper.queryInSaveSession(()->targetConditionSubelementFinal.getDataFileDirectoryPath()));
	    		
	    		targetConditionSubelement.setDataFilePath(targetDataFilePath);
    		    CloneHelper.execInSaveSession(()->targetConditionSubelementFinal.save());
    		}
    	}
    	
		CloneHelper.execInSaveSession(()->{
            Set<Long> deletedConditionSubelements =
                    targetConditionElementFinal.getSubElements().stream().map(se->se.getId()).collect(Collectors.toSet());

            deletedConditionSubelements.remove(targetSubElementsList.stream().map(se->se.getId()).collect(Collectors.toSet()));

            for(Long conditionSubelementID : deletedConditionSubelements) {
                ConditionSubelement conditionSubelement = ConditionSubelement.findById(conditionSubelementID);
                List<ConditionItemValue> referencingConditionItemValues = conditionSubelement.getReferencingConditionItemValues();
                for(ConditionItemValue civ : referencingConditionItemValues) {
                    ConditionItem ci = civ.getConditionItem();
                    ci.getConditionItemValues().remove(civ);
                    HibernateUtil.getManager().deleteObject(civ);
                }
            }

            targetConditionElementFinal.getSubElements().clear();
            targetConditionElementFinal.getSubElements().addAll(targetSubElementsList);
        });
		
    	targetConditionElement.setDefaultValue    ( sourceConditionElement.isDefaultValue() );
    	targetConditionElement.setUseDefaultValue ( sourceConditionElement.isUseDefaultValue() );
    	targetConditionElement.setMetatags        ( sourceConditionElement.getMetatags() );
    	targetConditionElement.setSearchValue     ( sourceConditionElement.getSearchValue() );
    	
    	CloneHelper.execInSaveSession(()->{
    		targetConditionElement.save();
    	});
	}

	private void syncTargetGroups ( ServiceExecutionContext context, TargetingSyncServiceRequest request, boolean isClone ) {
        User requestor = request.getRequestor();
        boolean syncFromOther = request.isSyncUpdate();
        Long sourceDocumentId = request.getSourceDocumentId();
        Long targetDocumentId = request.getTargetDocumentId();
        boolean logDifferences = request.isLogDifferences();
        boolean hideUntilNextChange = request.isHideUntilNextChange();
        Document sourceDocument = Document.findById(sourceDocumentId);
        Document targetDocument = CloneHelper.queryInSaveSession(()->Document.findById(targetDocumentId));
        Multimap<Long, Map<Long, Long>> syncObjectMap = request.getSyncObjectMap();
        List<Map<Long, Long>> targetGroupStatusMapList = (List<Map<Long, Long>>) syncObjectMap.get((long) SyncObjectType.ID_TARGET_GROUP);
        Set<Long> targetGroupIDsUpdated = new HashSet<>();
        Map<TargetGroup, TargetGroup> clonedTargetGroupsMap = new HashMap<>();
        for(Map<Long, Long> syncObjectStatusMap : targetGroupStatusMapList) {
        	for(Long objectIdx10 : syncObjectStatusMap.keySet()) {
                long objectId = objectIdx10 / 0x10;
                long objectStatus = syncObjectStatusMap.get(objectIdx10);
                boolean objectIsReferenced = (objectStatus & 0x100000) > 0;
                boolean objectIsFromOther = (objectStatus & 0x10000) > 0;
                objectStatus = objectStatus & 0xFFFF;
                if (syncFromOther != objectIsFromOther)
                    continue;
                
                TargetGroup sourceTargetGroup = objectIsFromOther ? TargetGroup.findById(objectId) : CloneHelper.queryInSaveSession(()->TargetGroup.findById(objectId));
                if(sourceTargetGroup != null) {
                	String targetGroupName = sourceTargetGroup.getName();
                    
                	String targetGroupDna = sourceTargetGroup.getDna();
                	TargetGroup targetTargetGroup = objectIsFromOther ? CloneHelper.queryInSaveSession(()->TargetGroup.findByDna(targetGroupDna)) : TargetGroup.findByDna(targetGroupDna);

                	if(hideUntilNextChange && targetTargetGroup != null) {
                        log.info("Hide target group source id = (" + sourceTargetGroup.getId() + "), name = \"" + targetGroupName + "\"");
                    } else if(isClone) {
	                    log.info("Clone target group source id = (" + sourceTargetGroup.getId() + "), name = \"" + targetGroupName + "\"");
                	} else {
	                    log.info("Sync " + (objectIsReferenced ? "referenced" : "selected") + " target group source id = (" + sourceTargetGroup.getId() + "), name = \"" + targetGroupName + "\"");
	                    if(logDifferences) {
	        	            SyncTouchpointUtil.logSyncReason(log, sourceTargetGroup, targetTargetGroup);
	                    }
                	}
                	
                	if(targetTargetGroup == null) {
                		targetTargetGroup = CloneHelper.clone(sourceTargetGroup, o->o.clone(null, null, targetDocument));
                		targetTargetGroup.save();
                	} else if(! hideUntilNextChange){
                		syncTargetGroup(targetDocument, sourceTargetGroup, targetTargetGroup);
                	}
                	TargetGroup targetTargetGroupFinal = targetTargetGroup;
                	CloneHelper.execInSaveSession(()->{
	                	targetTargetGroupFinal.getDocuments().add(targetDocument);
	                	targetTargetGroupFinal.save();
                	});
                    clonedTargetGroupsMap.put(targetTargetGroup, sourceTargetGroup);
                	targetGroupIDsUpdated.add(targetTargetGroup.getId());
                }
        	}
        }
        
		TargetGroup.findAllByDocument(sourceDocument, null, false, 0, false).forEach(tg->{
			String dna = tg.getDna();
			CloneHelper.execInSaveSession(()->{
				TargetGroup targetTargetGroup = TargetGroup.findByDna(dna);
				if(targetTargetGroup != null) {
					targetTargetGroup.getDocuments().add(targetDocument);
				}
			});
		});

        Map<Long, Long> newClonedMap = CloneHelper.getNewClonedTargetGroupMap();
        for(Long sourceId : newClonedMap.keySet()) {
            Long targetId = newClonedMap.get(sourceId);
            if(! clonedTargetGroupsMap.keySet().stream().anyMatch(targetGroup->targetGroup.getId() == targetId.longValue())) {
                TargetGroup sourceTargetGroup = TargetGroup.findById(sourceId);
                TargetGroup targetTargetGroup = CloneHelper.queryInSaveSession(()->TargetGroup.findById(targetId));
                if(targetTargetGroup != null) {
                    clonedTargetGroupsMap.put(targetTargetGroup, sourceTargetGroup);
                }
            }
        }

        String sourceNodeGuid = Node.getCurrentNode().getGuid();
        String targetNodeGuid = CloneHelper.queryInSaveSession(() -> Node.getCurrentNode().getGuid());

        SyncTouchpointUtil.updateModelSyncHistory(isClone ? SyncHistory.SYNC_TYPE_CLONING : SyncHistory.SYNC_TYPE_SYNCHRONIZING, SyncObjectType.ID_TARGET_GROUP, clonedTargetGroupsMap, sourceDocumentId, targetDocumentId, requestor.getId(), sourceNodeGuid, targetNodeGuid, hideUntilNextChange);

        doHibernateUtilFlush();
        doHibernateUtilClear();
/*        
        for(Long targetGroupId : targetGroupIDsUpdated) {
        	updateTargetGroupAffectedObjects(targetGroupId);
        }
        
        doHibernateUtilFlush();
*/        
	}

	private void updateTargetGroupAffectedObjects(Long targetGroupId) {
		CloneHelper.execInSaveSession(()->{
			TargetGroup targetGroup = TargetGroup.findById(targetGroupId);
			List<ReferencableObject> directReferences = targetGroup.getDirectReferences();
			for(ReferencableObject referencableObject : directReferences) {
				Object targetObjct = referencableObject.getTargetObject();
				if(targetObjct instanceof ContentObjectData) {
					ContentObjectData targetMessageInstance = (ContentObjectData) targetObjct;
					targetMessageInstance.save();
					targetMessageInstance.getModel().save();
				} else if(targetObjct instanceof ContentTargeting) {
					ContentTargeting targetContentTargeting = (ContentTargeting) targetObjct;
					IdentifiableMessagePointModel targetInstance = targetContentTargeting.getAssetInstance();
					if(targetInstance instanceof ContentObjectData) {
						ContentObjectData targetMessageInstance = (ContentObjectData) targetInstance;
						targetMessageInstance.save();
						targetMessageInstance.getModel().save();
					}
				}
			}
		});
	}
	
	private void syncTargetGroup(Document targetDocument, TargetGroup sourceTargetGroup, TargetGroup targetTargetGroup) {
		targetTargetGroup.setName          				(sourceTargetGroup.getName());
		targetTargetGroup.setMetatags      				(sourceTargetGroup.getMetatags());
		targetTargetGroup.setConditionRelationship      (sourceTargetGroup.getConditionRelationship());
		targetTargetGroup.setSegmentationData           (sourceTargetGroup.getSegmentationData());

		{
			TargetGroupInstance sourceTargetInstance = sourceTargetGroup.getInstance();
			TargetGroupInstance targetTargetInstance = CloneHelper.clone(sourceTargetInstance);
			CloneHelper.execInSaveSession(()->{
				targetTargetInstance.save();
				TargetGroupInstance existingTargetGroupInstance = targetTargetGroup.getInstance();
				if(existingTargetGroupInstance != null) {
				    Set<ConditionItem> conditionItemsToDelete = new HashSet<>(existingTargetGroupInstance.getConditionItems());
                    existingTargetGroupInstance.getConditionItems().clear();

                    conditionItemsToDelete.forEach(ci->{
                        Set<ConditionItemValue> conditionItemValuesToDelete = new HashSet<>(ci.getConditionItemValues());
				        ci.getConditionItemValues().clear();
				        conditionItemValuesToDelete.forEach(civ->{
				            HibernateUtil.getManager().deleteObject(civ);
                        });
				        ci.delete();
                    });

				    existingTargetGroupInstance.delete();
                }
				targetTargetGroup.setInstance(targetTargetInstance);
			});

/*			
			TargetGroupInstance targetTargetInstance = CloneHelper.queryInSaveSession(()->targetTargetGroup.getInstance());
			
			syncTargetGroupInstance(sourceTargetGroup.getConditionRelationship(), sourceTargetInstance, targetTargetInstance);
*/			
		}

/*
		for(Long sourceMessageInstanceId : sourceTargetGroup.getParameterizedMap().keySet()) {
			TargetGroupInstance sourceTargetInstance  = sourceTargetGroup.getParameterizedMap().get(sourceMessageInstanceId);
			MessageInstance sourceMessageInstance = MessageInstance.findById(sourceMessageInstanceId);
			TargetGroupInstance targetTargetInstance = null;
			if(sourceMessageInstance != null) {
				Message sourceMessage = sourceMessageInstance.getModel();
				if(sourceMessage != null) {
					String dna = sourceMessage.getDna();
					Message targetMessage = CloneHelper.queryInSaveSession(()->Message.findByDnaAndDocument(dna, targetDocument));
					if(targetMessage != null) {
						if(sourceMessageInstance.isActive()) {
							MessageInstance targetMessageInstance = CloneHelper.queryInSaveSession(()->(MessageInstance) targetMessage.getActiveCopy());
							if(targetMessageInstance != null) {
								targetTargetInstance = CloneHelper.queryInSaveSession(()->targetTargetGroup.getParameterizedMap().get(targetMessageInstance.getId()));
							}
						} else if(sourceMessageInstance.isWorkingCopy()) {
							MessageInstance targetMessageInstance = CloneHelper.queryInSaveSession(()->(MessageInstance) targetMessage.getWorkingCopy());
							if(targetMessageInstance != null) {
								targetTargetInstance = CloneHelper.queryInSaveSession(()->targetTargetGroup.getParameterizedMap().get(targetMessageInstance.getId()));
							}
						}
					}
				}
				
				if(targetTargetInstance != null) {
					syncTargetGroupInstance(sourceTargetGroup.getConditionRelationship(), sourceTargetInstance, targetTargetInstance);
				}
			}
		}
		
		for(Long sourceEmbeddedContentInstanceId : sourceTargetGroup.getEmbeddedContentTargetGroupsMap().keySet()) {
			TargetGroupInstance sourceTargetInstance  = sourceTargetGroup.getEmbeddedContentTargetGroupsMap().get(sourceEmbeddedContentInstanceId);
			EmbeddedContentInstance sourceEmbeddedContentInstance = EmbeddedContentInstance.findById(sourceEmbeddedContentInstanceId);
			TargetGroupInstance targetTargetInstance = null;
			if(sourceEmbeddedContentInstance != null) {
				EmbeddedContent sourceEmbeddedContent = sourceEmbeddedContentInstance.getModel();
				if(sourceEmbeddedContent != null) {
					String dna = sourceEmbeddedContent.getDna();
					EmbeddedContent targetEmbeddedContent = CloneHelper.queryInSaveSession(()->EmbeddedContent.findByDna(dna).stream().findFirst().orElse(null));
					if(targetEmbeddedContent != null) {
						if(sourceEmbeddedContentInstance.isActive()) {
							EmbeddedContentInstance targetEmbeddedContentInstance = CloneHelper.queryInSaveSession(()->(EmbeddedContentInstance) targetEmbeddedContent.getActiveCopy());
							if(targetEmbeddedContentInstance != null) {
								targetTargetInstance = CloneHelper.queryInSaveSession(()->targetTargetGroup.getEmbeddedContentTargetGroupsMap().get(targetEmbeddedContentInstance.getId()));
							}
						} else if(sourceEmbeddedContentInstance.isWorkingCopy()) {
							EmbeddedContentInstance targetEmbeddedContentInstance = CloneHelper.queryInSaveSession(()->(EmbeddedContentInstance) targetEmbeddedContent.getWorkingCopy());
							if(targetEmbeddedContentInstance != null) {
								targetTargetInstance = CloneHelper.queryInSaveSession(()->targetTargetGroup.getEmbeddedContentTargetGroupsMap().get(targetEmbeddedContentInstance.getId()));
							}
						}
					}
				}
				
				if(targetTargetInstance != null) {
					syncTargetGroupInstance(sourceTargetGroup.getConditionRelationship(), sourceTargetInstance, targetTargetInstance);
				}
			}
		}
		
		for(Long sourceContentTargetingId : sourceTargetGroup.getContentTargetGroupsMap().keySet()) {
			ContentTargeting sourceContentTargeting = ContentTargeting.findById(sourceContentTargetingId);
			if(sourceContentTargeting != null) {
				Content sourceContent = Content.findByTargeting(sourceContentTargetingId);
				if(sourceContent != null) {
					ContentAssociation sourceContentAssociation = ContentAssociation.findByOwningContent(sourceContent.getId());
					ProductionContentAssociation sourceProdictionContentAssociation = ProductionContentAssociation.findByOwningContent(sourceContent.getId());
					
					IdentifiableMessagePointModel sourceAssetInstance = sourceContentTargeting.findAssetInstance();
					if(sourceAssetInstance != null) {
						if(sourceAssetInstance instanceof MessageInstance) {
							MessageInstance sourceMessageInstance = (MessageInstance) sourceAssetInstance;
							Message sourceMessage = sourceMessageInstance.getModel();
							if(sourceMessage != null) {
								String messageDna = sourceMessage.getDna();
								Message targetMessage = CloneHelper.queryInSaveSession(()->Message.findByDnaAndDocument(messageDna, targetDocument));
							}
						}
					}
				}
				
				HistoricalContent sourceHistoricalContent = HistoricalContent.findByTargeting(sourceContentTargetingId);
				if(sourceHistoricalContent != null) {
					
				}
			}
		}
*/		
		CloneHelper.execInSaveSession(()->targetTargetGroup.save());
	}
	
	private void syncTargetGroupInstance(int conditionRelationship, TargetGroupInstance sourceTargetGroupInstance, TargetGroupInstance targetTargetGroupInstance) {
		Set<ConditionItem> sourceCondiitonItems = sourceTargetGroupInstance.getConditionItems();
		Set<ConditionItem> targetConditionItems = CloneHelper.queryInSaveSession(()->targetTargetGroupInstance.getConditionItems());
		
		Map<String, ConditionItem> guidToTargetConditionItem = CloneHelper.queryInSaveSession(
			()->targetConditionItems.stream()
				.filter(ci->ci.getConditionElement() != null)
				.collect(Collectors.toMap(ci->ci.getConditionElement().getGuid(), Function.identity()))
		);
		
		for(ConditionItem sourceConditionItem : sourceCondiitonItems) {
			ConditionElement sourceConditionElement = sourceConditionItem.getConditionElement();
			String conditionElementGuid = sourceConditionElement.getGuid();
			ConditionItem targetConditionItem = null;
			if(! guidToTargetConditionItem.containsKey(conditionElementGuid)) {
				targetConditionItem = CloneHelper.clone(sourceConditionItem, o->{
					ConditionItem clonedConditionItem = o.clone(targetTargetGroupInstance);
					clonedConditionItem.save();
					targetTargetGroupInstance.getConditionItems().add(clonedConditionItem);
					return clonedConditionItem;
				});
			} else {
				targetConditionItem = guidToTargetConditionItem.get(conditionElementGuid);
				syncTargetConditionItem(sourceConditionItem, targetConditionItem);
			}
			
			guidToTargetConditionItem.remove(conditionElementGuid);
			
			ConditionItem targetConditionItemFinal = targetConditionItem;
			if(sourceTargetGroupInstance.getConditionParamMap().containsKey(sourceConditionItem)) {
				Boolean conditionParam = sourceTargetGroupInstance.getConditionParamMap().get(sourceConditionItem);
				CloneHelper.execInSaveSession(()->{
					targetTargetGroupInstance.getConditionParamMap().put(targetConditionItemFinal, conditionParam);
				});
			}
		}
		
		CloneHelper.execInSaveSession(()->{
			for(String conditionElementGuid : guidToTargetConditionItem.keySet()) {
				ConditionItem targetConditionItem = guidToTargetConditionItem.get(conditionElementGuid);
                Set<ConditionItemValue> conditionItemValuesToDelete = new HashSet<>(targetConditionItem.getConditionItemValues());
                targetConditionItem.getConditionItemValues().clear();
                conditionItemValuesToDelete.forEach(civ->{
                    HibernateUtil.getManager().deleteObject(civ);
                });
				targetConditionItem.delete();
				targetTargetGroupInstance.getConditionItems().remove(targetConditionItem);
				targetTargetGroupInstance.getConditionParamMap().remove(targetConditionItem);
			}
		});
		
		CloneHelper.execInSaveSession(()->{
			targetTargetGroupInstance.buildSearchString(conditionRelationship);
			targetTargetGroupInstance.save();
		});
	}
	
	private void syncTargetConditionItem(ConditionItem sourceConditionItem, ConditionItem targetConditionItem) {
		Set<ConditionItemValue> sourceConditionItemValues = sourceConditionItem.getConditionItemValues();
		Set<ConditionItemValue> targetConditionItemValues = CloneHelper.queryInSaveSession(()->targetConditionItem.getConditionItemValues());
		
		Map<String, ConditionItemValue> guidToConditionItemValue = CloneHelper.queryInSaveSession(()->
			targetConditionItemValues.stream().filter(civ->civ.getConditionSubelement() != null).collect(Collectors.toMap(civ->civ.getConditionSubelement().getGuid(), Function.identity()))
		);
		
		for(ConditionItemValue sourceConditionItemValue : sourceConditionItemValues) {
			ConditionSubelement sourceConditionSubelement = sourceConditionItemValue.getConditionSubelement();
			String conditionSubelementGuid = sourceConditionSubelement.getGuid();
			ConditionItemValue targetConditionItemValue = null;
			if(! guidToConditionItemValue.containsKey(conditionSubelementGuid)) {
				targetConditionItemValue = CloneHelper.clone(sourceConditionItemValue, o->{
					ConditionItemValue clonedConditionItemValue = o.clone(targetConditionItem);
					clonedConditionItemValue.save(true);
					targetConditionItem.getConditionItemValues().add(clonedConditionItemValue);
					return clonedConditionItemValue;
				});
			} else {
				targetConditionItemValue = guidToConditionItemValue.get(conditionSubelementGuid);
				syncTargetConditionItemValue(sourceConditionItemValue, targetConditionItemValue);
				guidToConditionItemValue.remove(conditionSubelementGuid);
			}
			
		}

		CloneHelper.execInSaveSession(()->{
			for(String conditionSubelementGuid : guidToConditionItemValue.keySet()) {
				ConditionItemValue targetConditionItemValue = guidToConditionItemValue.get(conditionSubelementGuid);
                HibernateUtil.getManager().deleteObject(targetConditionItemValue);
				targetConditionItem.getConditionItemValues().remove(targetConditionItemValue);
			}
			
			targetConditionItem.save();
		});
	}
	
	private void syncTargetConditionItemValue(ConditionItemValue sourceConditionItemValue, ConditionItemValue targetConditionItemValue) {
	    Map<String, String> sourceValueMap = sourceConditionItemValue.getValueMap();
        Map<String, String> targetValueMap = new LinkedHashMap<>(sourceValueMap);
	    targetConditionItemValue.setValueMap(targetValueMap);
	    CloneHelper.execInSaveSession(()->{
	    	targetConditionItemValue.save(false);
	    });
	}
	
	private String copyDataFile(String sourceDataFilePath, String targetDataFilePath, String targetDataFileDirectory) {
		File sourceFile = (sourceDataFilePath == null || sourceDataFilePath.isEmpty()) ? null : new File(sourceDataFilePath);
		
		File targetFile = (targetDataFilePath == null || targetDataFilePath.isEmpty()) ? null : new File(targetDataFilePath);

		if(sourceDataFilePath == null || sourceDataFilePath.isEmpty()) {
			if(targetDataFilePath != null && ! targetDataFilePath.isEmpty()) {
//??             targetFile.delete();
			}
			targetDataFilePath = null;
		} else {
			if(targetFile != null && targetFile.exists()) {
//??             targetFile.delete();
			}
			String fileName = sourceFile.getName();

			File directory = new File(targetDataFileDirectory);
			directory.mkdirs();
			targetDataFilePath = targetDataFileDirectory + fileName;
			targetFile = new File(targetDataFilePath);
			try {
				Files.copy(sourceFile, targetFile);
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		return targetDataFilePath;
	}
	
	private void doHibernateUtilFlush() {
        long startTime = System.currentTimeMillis();
        if(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
            HibernateUtil.getManager().getSession().flush();
        } else {
            CloneHelper.getCloneSession().flush();
        }
		log.info("doHibernateUtilFlush; TIME TAKEN:<" + (System.currentTimeMillis() - startTime) + ">");
	}

    private void doHibernateUtilClear() {
        if(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
            HibernateUtil.getManager().getSession().clear();
        } else {
            CloneHelper.getCloneSession().clear();
        }
    }

    public static ServiceExecutionContext createContextForCloneRules(Long sourceDocumentId, Long targetDocumentId, boolean syncUpdate, boolean logDifferences, Multimap<Long, Map<Long, Long>> syncObjectMap, User requestor, String sourceSchema) {
    	return createContext(ACTION_DUPLICATE_TARGET_RULES, sourceDocumentId, targetDocumentId, syncUpdate, logDifferences, false, syncObjectMap, requestor, sourceSchema);
    }
    
    public static ServiceExecutionContext createContextForCloneGroups(Long sourceDocumentId, Long targetDocumentId, boolean syncUpdate, boolean logDifferences, Multimap<Long, Map<Long, Long>> syncObjectMap, User requestor, String sourceSchema) {
    	return createContext(ACTION_DUPLICATE_TARGET_GROUPS, sourceDocumentId, targetDocumentId, syncUpdate, logDifferences, false, syncObjectMap, requestor, sourceSchema);
    }
    
    public static ServiceExecutionContext createContextForSyncRules(Long sourceDocumentId, Long targetDocumentId, boolean syncUpdate, boolean logDifferences, boolean hideUntilNextChange, Multimap<Long, Map<Long, Long>> syncObjectMap, User requestor, String sourceSchema) {
    	return createContext(ACTION_SYNC_TARGET_RULES, sourceDocumentId, targetDocumentId, syncUpdate, logDifferences, hideUntilNextChange, syncObjectMap, requestor, sourceSchema);
    }
    
    public static ServiceExecutionContext createContextForSyncGroups(Long sourceDocumentId, Long targetDocumentId, boolean syncUpdate, boolean logDifferences, boolean hideUntilNextChange, Multimap<Long, Map<Long, Long>> syncObjectMap, User requestor, String sourceSchema) {
    	return createContext(ACTION_SYNC_TARGET_GROUPS, sourceDocumentId, targetDocumentId, syncUpdate, logDifferences, hideUntilNextChange, syncObjectMap, requestor, sourceSchema);
    }
    
    public static ServiceExecutionContext createContext(int actionId, Long sourceDocumentId, Long targetDocumentId, boolean syncUpdate, boolean logDifferences, boolean hideUntilNextChange, Multimap<Long, Map<Long, Long>> syncObjectMap, User requestor, String otherSchema) {
        SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
        context.setServiceName(SERVICE_NAME);

        TargetingSyncServiceRequest request = new TargetingSyncServiceRequest();
        context.setRequest(request);

        request.setActionId(actionId);
        request.setSourceDocumentId(sourceDocumentId);
        request.setTargetDocumentId(targetDocumentId);
        request.setLogDifferences(logDifferences);
        request.setHideUntilNextChange(hideUntilNextChange);
        request.setSyncUpdate(syncUpdate);
        request.setSyncObjectMap(syncObjectMap);
        request.setRequestor(requestor);
        request.setSourceSchema(otherSchema);

        SimpleServiceResponse serviceResp = new SimpleServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }

}
