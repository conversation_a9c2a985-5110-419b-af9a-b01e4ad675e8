<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC 
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="com.prinova.messagepoint.model.font.TextStyleCustomization" table="text_style_cust">
		<id name="id" column="id">
			<generator class="native" />
		</id>
		
		<property name="connectorName" 		column="connector_name" 	length="96" />
		<property name="color" 											length="255" />
		<property name="fontName" 			column="font_name" 			length="96" />
		<property name="webFontName" 		column="web_font_name" 		length="255" />
		<property name="pointSize" 			column="point_size" />
		<property name="bold" />
		<property name="underline" />		
		<property name="italic" />
		<property name="taggingOverride"	column="tagging_override"	length="2048" />
		
		<property name="toggleBold"			column="toggle_bold" 		not-null="true" />
		<property name="toggleUnderline"	column="toggle_underline" 	not-null="true" />
		<property name="toggleItalic"		column="toggle_italic" 		not-null="true" />
		<property name="togglePointSize"	column="toggle_point_size" 	not-null="true" />
		<property name="toggleColor"		column="toggle_color" 		not-null="true" />
		
		<set name="toggleColorValues" table="text_style_cust_colors">
			<key column="text_style_cust_id" />
			<element column="value" type="string" length="255" />
		</set>
		<set name="togglePointSizeValues" table="text_style_cust_point_sizes">
			<key column="text_style_cust_id" />
			<element column="value" type="string" length="255" />
		</set>
		
		<many-to-one name="textStyleFont" class="com.prinova.messagepoint.model.font.TextStyleFont" column="font_id" not-found="ignore" />
		<property name="applyTextStyleFont" column="apply_font"/>
				
		<many-to-one name="masterTextStyle" class="com.prinova.messagepoint.model.font.TextStyle" column="master_text_style_id" />
		
		<many-to-one name="originObject" 		column="origin_object_id"	class="com.prinova.messagepoint.model.font.TextStyleCustomization"  not-found="ignore" />
		
		<property 	name="checkoutTimestamp"	column="checkout_timestamp" />
		<property 	name="checkinTimestamp"		column="checkin_timestamp" />

		<property 	name="updated" 				column="updated" />
		<property 	name="updatedBy" 			column="updated_by_id" />
		<property 	name="created" 				column="created" />
		<property 	name="createdBy" 			column="created_by_id" />
	</class>
</hibernate-mapping>