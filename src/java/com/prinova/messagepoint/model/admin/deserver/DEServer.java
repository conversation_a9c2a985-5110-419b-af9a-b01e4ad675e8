package com.prinova.messagepoint.model.admin.deserver;

import com.github.jknack.handlebars.Handlebars;
import com.github.jknack.handlebars.Template;
import com.prinova.messagepoint.integrator.MessagepointMultiTenantConnectionProvider;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.EventType;
import com.prinova.messagepoint.model.common.StaticType;
import com.prinova.messagepoint.model.deliveryevent.DeliveryEvent;
import com.prinova.messagepoint.model.file.DatabaseFile;
import com.prinova.messagepoint.model.util.JobPackerUtil;
import com.prinova.messagepoint.util.*;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.orm.hibernate5.SessionHolder;

import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class DEServer extends IdentifiableMessagePointModel {

	private static final long serialVersionUID = -1967183504682619298L;
	private static final Log log = LogUtil.getLog(DEServer.class);

	private static final String BUNDLE_DELIVERY_VAR_POD_ID			= "{podId}";
	private static final String BUNDLE_DELIVERY_VAR_COMPANY			= "{company}";
	private static final String BUNDLE_DELIVERY_VAR_INSTANCE		= "{instance}";
	private static final String BUNDLE_DELIVERY_VAR_INSTANCE_TYPE	= "{instancetype}";
	private static final String BUNDLE_DELIVERY_VAR_TOUCHPOINT		= "{touchpoint}";
	private static final String BUNDLE_DELIVERY_VAR_JOB_ID			= "{jobId}";
	private static final String BUNDLE_DELIVERY_VAR_JOB_TYPE		= "{jobType}";
	private static final String BUNDLE_DELIVERY_VAR_DATE_YYYY		= "{date_yyyy}";
	private static final String BUNDLE_DELIVERY_VAR_DATE_YY			= "{date_yy}";
	private static final String BUNDLE_DELIVERY_VAR_DATE_MM			= "{date_mm}";
	private static final String BUNDLE_DELIVERY_VAR_DATE_DD			= "{date_dd}";
	private static final String BUNDLE_DELIVERY_VAR_TIME_HH			= "{time_hh}";
	private static final String BUNDLE_DELIVERY_VAR_TIME_MM			= "{time_mm}";
	private static final String BUNDLE_DELIVERY_VAR_TIME_SS			= "{time_ss}";
	
	private boolean		notifyErrorOnly;
	private boolean 	defaultServer = false;
	private DatabaseFile privateSshKeyFile;
	private DatabaseFile publicSshKeyFile;
	private DEServerBundleTypeState bundleTypeState;
	private DEServerCommunicationType communicationType;
	private String		filenamePattern	= null;
	private String		notificationEmails	= null;
	private String		postProcessScript = null;
	private String		url	= null;
	private String 		jobZipName = null;
	private int 		availability = DEServerAvailability.NOT_TESTED;

    public boolean isDefaultServer() {
		return defaultServer;
	}

	public void setDefaultServer(boolean defaultServer) {
		this.defaultServer = defaultServer;
	}

	public DEServerCommunicationType getCommunicationType() {
		return communicationType;
	}

	public void setCommunicationType(DEServerCommunicationType communicationType) {
		this.communicationType = communicationType;
	}

	public static String getBundleDeliveryVarInstanceType() {
		return BUNDLE_DELIVERY_VAR_INSTANCE_TYPE;
	}

	public String getCommunicationTypeDisplayName() {
		return ApplicationUtil.getMessage(communicationType.getName());
	}

	public String getFilenamePattern() {
		return filenamePattern;
	}

	public void setFilenamePattern(String filenamePattern) {
		this.filenamePattern = filenamePattern;
	}

	public String getNotificationEmails() {
		return notificationEmails;
	}

	public void setNotificationEmails(String notificationEmails) {
		this.notificationEmails = notificationEmails;
	}

	public boolean isNotifyErrorOnly() {
		return notifyErrorOnly;
	}

	public void setNotifyErrorOnly(boolean notifyErrorOnly) {
		this.notifyErrorOnly = notifyErrorOnly;
	}

	@Override
	public void preSave(Boolean isNew) {
		super.preSave(isNew);

		if (getPrivateSshKeyFile() != null) {
			SessionHolder mainSessionHolder = null;
			try {
				mainSessionHolder = HibernateUtil.getManager().openTemporarySession(Node.getCurrentBranch().getDcsSchemaName());
				getPrivateSshKeyFile().save();
			} finally {
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
			}
		}

		if (getPublicSshKeyFile() != null) {
			SessionHolder mainSessionHolder = null;
			try {
				mainSessionHolder = HibernateUtil.getManager().openTemporarySession(Node.getCurrentBranch().getDcsSchemaName());
				getPublicSshKeyFile().save();
			} finally {
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
			}
		}

		if (getBundleTypeState() != null) {
			SessionHolder mainSessionHolder = null;
			try {
				mainSessionHolder = HibernateUtil.getManager().openTemporarySession(Node.getCurrentBranch().getDcsSchemaName());
				getBundleTypeState().save();
			} finally {
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
			}
		}

	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + communicationType.getId();
		result = prime * result + (defaultServer ? 1231 : 1237);
		result = prime * result
				+ ((isGuidEmpty()) ? 0 : getGuid().hashCode());
		result = prime * result
				+ ((name == null) ? 0 : name.hashCode());
		result = prime * result
				+ ((description == null) ? 0 : description.hashCode());
		result = prime * result
				+ ((url == null) ? 0 : url.hashCode());
		result = prime * result
				+ ((filenamePattern == null) ? 0 : filenamePattern.hashCode());
		result = prime * result
				+ ((notificationEmails == null) ? 0 : notificationEmails.hashCode());
		return result;
	}
	
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DEServer other = (DEServer) obj;
		if (getGuid().equals(other.getGuid()))
			return false;
		if (!Objects.equals(name, other.name))
			return false;
		if (defaultServer != other.defaultServer)
			return false;
		if ((description == null && other.description != null) || !Objects.equals(description, other.description))
			return false;
		if ((url == null && other.url != null) || !Objects.equals(url, other.url))
			return false;
		if ((notificationEmails == null && other.notificationEmails != null) || !Objects.equals(notificationEmails, other.notificationEmails))
			return false;
		if ((filenamePattern == null && other.filenamePattern != null) || !Objects.equals(filenamePattern, other.filenamePattern))
			return false;
		return true;
	}

	public static DEServer findByGuid(String guid) {

    	DEServer result;

		if (guid == null || guid.isEmpty()) {
			return null;
		}

		try {
			Branch branch = Node.getCurrentBranch();

			result = findByGuid(guid, branch);

			if (result != null) {
				return result;
			} else {
				while (branch != null && branch.getParentBranch() != null) {
					branch = BranchUtil.getParentBranch(branch);
					result = findByGuid(guid, branch);

					if (result != null) {
						return result;
					}
				}
			}
		} catch (Exception e) {
			logError(e);
		}

    	return null;
	}

	private static DEServer findByGuid(String guid, Branch branch) {
    	DEServer result = null;

    	SessionHolder mainSessionHolder = null;

		try {
			mainSessionHolder = HibernateUtil.getManager().openTemporarySession(branch.getDcsSchemaName());
			result = HibernateUtil.getManager().getObjectByGuid(DEServer.class, guid);

			// bundleTypeState is set as lazy="false", but somehow it isn't loading correctly sometimes
			// when accessed outside this transaction
			if (result != null) {
				if (result.getBundleTypeState() == null) {
					logError("Bundle Delivery '" + result.getName() + "' Enabled/Default configurations returned null! guid: " + result.getGuid());
				};
			}
		} finally {
			if (mainSessionHolder != null)
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}

    	return result;
	}

	public static DEServer findById(long id) {
    	return findById(id, null);
	}
	
	public static DEServer findById(long id, Branch loadFrom) {
	    DEServer result = null;
		SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(loadFrom != null ? loadFrom.getDcsSchemaName() : Node.getCurrentBranch().getDcsSchemaName());

	    try {
			result = HibernateUtil.getManager().getObject(DEServer.class, id);
		} catch (Exception e) {
			logError(e);
		} finally {
			HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}

		return result;
	}

    public static DEServer findByDefaultEventTypeForCommunicationTypeFromBranch(int eventType, DEServerCommunicationType communicationType, Branch branch) {
        DEServer result = null;

        try {
            result = findByDefaultEventTypeForCommunicationType(eventType, communicationType, branch);

            if (result != null) {
                return result;
            } else {
                while (branch != null && branch.getParentBranch() != null) {
                    branch = BranchUtil.getParentBranch(branch);
                    result = findByDefaultEventTypeForCommunicationType(eventType, communicationType, branch);

                    if (result != null) {
                        return result;
                    }
                }
            }
        } catch (Exception e) {
            logError(e);
        }

        return result;
    }

	private static DEServer findByDefaultEventTypeForCommunicationType(int eventType, DEServerCommunicationType communicationType, Branch branch) {
		DEServer result = null;
		SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(branch != null ? branch.getDcsSchemaName() : null);

		try {
			StringBuilder hql = new StringBuilder("FROM DEServer de WHERE de.availability = 0 and ");

			switch (eventType) {
				case EventType.TYPE_PRODUCTION:
					hql.append(MessageFormat.format("de.communicationType.id = {0} and de.bundleTypeState.productionEnabled = true and de.bundleTypeState.productionDefault = true", communicationType.getId()));
					break;
				case EventType.TYPE_COMMUNICATION_PROOF:
					hql.append(MessageFormat.format("de.communicationType.id = {0} and de.bundleTypeState.communicationProofEnabled = true and de.bundleTypeState.communicationProofDefault = true", communicationType.getId()));
					break;
				case EventType.TYPE_TEST:
					hql.append(MessageFormat.format("de.communicationType.id = {0} and de.bundleTypeState.testEnabled = true and de.bundleTypeState.testDefault = true", communicationType.getId()));
					break;
				case EventType.TYPE_PROOF:
					hql.append(MessageFormat.format("de.communicationType.id = {0} and de.bundleTypeState.proofEnabled = true and de.bundleTypeState.proofDefault = true", communicationType.getId()));
					break;
				case EventType.TYPE_SIMULATION:
					hql.append(MessageFormat.format("de.communicationType.id = {0} and de.bundleTypeState.simulationEnabled = true and de.bundleTypeState.simulationDefault = true", communicationType.getId()));
					break;
				case EventType.TYPE_TEST_SUITE:
					hql.append(MessageFormat.format("de.communicationType.id = {0} and de.bundleTypeState.testSuiteEnabled = true and de.bundleTypeState.testSuiteDefault = true", communicationType.getId()));
					break;
				case EventType.TYPE_COMMUNICATION_PRODUCTION:
					hql.append(MessageFormat.format("de.communicationType.id = {0} and de.bundleTypeState.communicationProductionDefault = true and de.bundleTypeState.communicationProductionEnabled = true", communicationType.getId()));
					break;
				case EventType.TYPE_PREVIEW:
					hql.append(MessageFormat.format("de.communicationType.id = {0} and de.bundleTypeState.previewDefault = true and de.bundleTypeState.previewEnabled = true", communicationType.getId()));
					break;
				case EventType.TYPE_SEGMENTATION:
					hql.append(MessageFormat.format("de.communicationType.id = {0} and de.bundleTypeState.segmentationDefault = true and de.bundleTypeState.segmentationEnabled = true", communicationType.getId()));
					break;
				default:
					LogUtil.getLog(DEServer.class).warn(MessageFormat.format("Warning: DEServer load for unknown event type: {0}", eventType));
					return null;
			}

			List<DEServer> results = (List<DEServer>) HibernateUtil.getManager().getObjectsAdvanced(hql.toString());

			if (results != null && !results.isEmpty()) {
				result = results.get(0);
			}
		} catch (Exception e) {
			logError(e);
		} finally {
			HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}

		return result;
	}

	public static DEServer findByDefaultEventType(int eventType, Branch startingFrom) {
		DEServer result = null;

		try {
			Branch branch = startingFrom != null ? startingFrom : Node.getCurrentBranch();

			result = findByDefaultEventTypeForBranch(eventType, branch);

			if (result != null) {
				return result;
			} else {
				while (branch != null && branch.getParentBranch() != null) {
					branch = BranchUtil.getParentBranch(branch);
					result = findByDefaultEventTypeForBranch(eventType, branch);

					if (result != null) {
						return result;
					}
				}
			}
		} catch (Exception e) {
			logError(e);
		}

		return result;
	}

	public static DEServer findByDefaultEventType(int eventType) {

		DEServer result = null;

		try {
			Branch branch = Node.getCurrentBranch();

			result = findByDefaultEventType(eventType, branch);

			if (result != null) {
				return result;
			} else {
				while (branch != null && branch.getParentBranch() != null) {
					branch = BranchUtil.getParentBranch(branch);
					result = findByDefaultEventType(eventType, branch);

					if (result != null) {
						return result;
					}
				}
			}
		} catch (Exception e) {
			logError(e);
		}

		return result;
	}

	public static DEServer findByDefaultEventTypeForBranch(int eventType, Branch branch) {
		return DEServer.findByDefaultEventTypeForBranch(eventType, branch, true);
	}

	public static DEServer findByDefaultEventTypeForBranch(int eventType, Branch branch, boolean availableOnly) {
		DEServer result = null;
		SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(branch != null ? branch.getDcsSchemaName() : null);

		try {
			StringBuilder hql;

			if (availableOnly) {
				 hql = new StringBuilder("FROM DEServer de WHERE de.availability = 0 and ");
			} else {
				 hql = new StringBuilder("FROM DEServer de WHERE ");
			}

			switch (eventType) {
				case EventType.TYPE_PRODUCTION:
					hql.append("de.bundleTypeState.productionEnabled = true and de.bundleTypeState.productionDefault = true");
					break;
				case EventType.TYPE_COMMUNICATION_PROOF:
					hql.append("de.bundleTypeState.communicationProofEnabled = true and de.bundleTypeState.communicationProofDefault = true");
					break;
				case EventType.TYPE_TEST:
					hql.append("de.bundleTypeState.testEnabled = true and de.bundleTypeState.testDefault = true");
					break;
				case EventType.TYPE_PROOF:
					hql.append("de.bundleTypeState.proofEnabled = true and de.bundleTypeState.proofDefault = true");
					break;
				case EventType.TYPE_SIMULATION:
					hql.append("de.bundleTypeState.simulationEnabled = true and de.bundleTypeState.simulationDefault = true");
					break;
				case EventType.TYPE_TEST_SUITE:
					hql.append("de.bundleTypeState.testSuiteEnabled = true and de.bundleTypeState.testSuiteDefault = true");
					break;
				case EventType.TYPE_COMMUNICATION_PRODUCTION:
					hql.append("de.bundleTypeState.communicationProductionDefault = true and de.bundleTypeState.communicationProductionEnabled = true");
					break;
				case EventType.TYPE_PREVIEW:
					hql.append("de.bundleTypeState.previewDefault = true and de.bundleTypeState.previewEnabled = true");
					break;
				case EventType.TYPE_SEGMENTATION:
					hql.append("de.bundleTypeState.segmentationDefault = true and de.bundleTypeState.segmentationEnabled = true");
					break;
				default:
					LogUtil.getLog(DEServer.class).warn(MessageFormat.format("Warning: DEServer load for unknown event type: {0}", eventType));
					return null;
			}

			List<DEServer> results = (List<DEServer>) HibernateUtil.getManager().getObjectsAdvanced(hql.toString());

			if (results != null && !results.isEmpty()) {
				result = results.get(0);
			}
		} catch (Exception e) {
			logError(e);
		} finally {
			HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}

		return result;
	}

	public static List<DEServer> findEnabledByEventType(int eventTypeFilter) {

		List<DEServer> result = null;

		try {
			Branch branch = Node.getCurrentBranch();

			List<DEServer> servers = findEnabledByEventType(eventTypeFilter, branch);
			result = new ArrayList<>(servers);

			while (branch != null && branch.getParentBranch() != null) {
				branch = BranchUtil.getParentBranch(branch);
				servers = findEnabledByEventType(eventTypeFilter, branch);
				result.addAll(servers);
			}

			result.sort(Comparator.comparing(DEServer::getName));
		} catch (Exception ex) {
			logError(ex);
		}

		return result;
	}

	public static List<DEServer> findEnabledByEventType(int eventTypeFilter, Branch branch) {

		SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(branch != null ? branch.getDcsSchemaName() : null);
		List<?> results = null;

		try {
			StringBuilder hql = new StringBuilder("FROM DEServer de WHERE de.availability = 0 and ");

			switch (eventTypeFilter) {
				case EventType.TYPE_PRODUCTION:
					hql.append("de.bundleTypeState.productionEnabled = true");
					break;
				case EventType.TYPE_COMMUNICATION_PROOF:
					hql.append("de.bundleTypeState.communicationProofEnabled = true");
					break;
				case EventType.TYPE_TEST:
					hql.append("de.bundleTypeState.testEnabled = true");
					break;
				case EventType.TYPE_PROOF:
					hql.append("de.bundleTypeState.proofEnabled = true");
					break;
				case EventType.TYPE_SIMULATION:
					hql.append("de.bundleTypeState.simulationEnabled = true");
					break;
				case EventType.TYPE_TEST_SUITE:
					hql.append("de.bundleTypeState.testSuiteEnabled = true");
					break;
				case EventType.TYPE_COMMUNICATION_PRODUCTION:
					hql.append("de.bundleTypeState.communicationProductionEnabled = true");
					break;
				case EventType.TYPE_PREVIEW:
					hql.append("de.bundleTypeState.previewEnabled = true");
					break;
				case EventType.TYPE_SEGMENTATION:
					hql.append("de.bundleTypeState.segmentationEnabled = true");
					break;
				default:
					LogUtil.getLog(DEServer.class).warn(MessageFormat.format("Warning: DEServer load for unknown event type: {0}", eventTypeFilter));
					return null;
			}

			results = HibernateUtil.getManager().getObjectsAdvanced(hql.toString());
		} catch (Exception e) {
			logError(e);
		} finally {
			HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}

		return (List<DEServer>) results;
	}

	public static List<DEServer> findAll() {

    	ArrayList<DEServer> servers = new ArrayList<>();

    	try {
			List<Branch> branches = new ArrayList<>();

			Branch current = Node.getCurrentBranch();
			branches.add(current);
			while (current != null && current.getParentBranch() != null) {
				current = BranchUtil.getParentBranch(current);
				branches.add(current);
			}

			for (Branch branch : branches) {
				SessionHolder mainSessionHolder = null;

				try {
					mainSessionHolder = HibernateUtil.getManager().openTemporarySession(branch.getDcsSchemaName());
					List<DEServer> branchResult = HibernateUtil.getManager().getObjects(DEServer.class);
					servers.addAll(branchResult);
				} catch(Exception e) {
					logError(e);
				} finally {
					if (mainSessionHolder != null) {
						HibernateUtil.getManager().restoreSession(mainSessionHolder);
					}
				}
			}
		} catch (Exception e) {
    		logError(e);
		}

		return servers;
	}

	public JSONObject getNotificationEmailsAsJson() {
		JSONObject emails = new JSONObject();

		try {
			String existingEmails = getNotificationEmails();
			emails = existingEmails != null ? new JSONObject(existingEmails) : new JSONObject();

			if (!emails.has("to")) {
				emails.put("to", new JSONArray());
			}

			if (!emails.has("cc")) {
				emails.put("cc", new JSONArray());
			}

		} catch (Exception e) {
			try {
				emails.put("to", new JSONArray());
				emails.put("cc", new JSONArray());
			} catch (Exception ignored) {
			}
		}

		return emails;
	}

	public static List<String> splitEmails(String emails) {
		return Stream.of(emails.split("[,; ]"))
				.filter(x -> !StringUtils.isEmpty(x))
				.collect(Collectors.toList());
	}

	public DatabaseFile getPrivateSshKeyFile() {
		return privateSshKeyFile;
	}

	public void setPrivateSshKeyFile(DatabaseFile privateSshKeyFile) {
		this.privateSshKeyFile = privateSshKeyFile;
	}

	public DatabaseFile getPublicSshKeyFile() {
		return publicSshKeyFile;
	}

	public void setPublicSshKeyFile(DatabaseFile publicSshKeyFile) {
		this.publicSshKeyFile = publicSshKeyFile;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

    public boolean isDews() {
    	return url != null && url.toLowerCase().startsWith("http");
	}

	public boolean hasKeyPair() {
		return this.getPrivateSshKeyFile() != null && this.getPublicSshKeyFile() != null;
	}

	public DEServerBundleTypeState getBundleTypeState() {
		return bundleTypeState;
	}

	public void setBundleTypeState(DEServerBundleTypeState bundleTypeState) {
		this.bundleTypeState = bundleTypeState;
	}

	public String getJobZipFileName(DeliveryEvent deliveryEvent) {

		if (this.jobZipName == null) {
			String fullName;

			String branchName = Node.getCurrentBranchName();
			String nodeName = Node.getCurrentNodeName();
			String jobType = JobPackerUtil.getJobTypeString(deliveryEvent);
			String jobId = String.valueOf(deliveryEvent.getJob().getId());

			fullName = getFilenamePattern();

			if (deliveryEvent.getBundleNameOverride() != null && !deliveryEvent.getBundleNameOverride().isEmpty()) {
				fullName = deliveryEvent.getBundleNameOverride();
			}

			fullName = fullName.replace(BUNDLE_DELIVERY_VAR_POD_ID, MessagepointMultiTenantConnectionProvider.getPodMasterCode());
			fullName = fullName.replace(BUNDLE_DELIVERY_VAR_COMPANY, branchName);
			fullName = fullName.replace(BUNDLE_DELIVERY_VAR_INSTANCE, nodeName);
			Optional<StaticType> type;
			if ((type = Node.listAllNodeType().stream().filter(x -> x.getId() == Node.getCurrentNode().getNodeType()).findFirst()).isPresent()) {
				fullName = fullName.replace(BUNDLE_DELIVERY_VAR_INSTANCE_TYPE, type.get().getName().toLowerCase());
			}
			if (deliveryEvent.getItem() != null && deliveryEvent.getItem().getDocument() != null) {
				fullName = fullName.replace(BUNDLE_DELIVERY_VAR_TOUCHPOINT, deliveryEvent.getItem().getDocument().getName().replace(" ", "-"));
			}
			fullName = fullName.replace(BUNDLE_DELIVERY_VAR_JOB_ID, jobId);
			fullName = fullName.replace(BUNDLE_DELIVERY_VAR_JOB_TYPE, jobType);
			
			Date now = new Date();
			fullName = fullName.replace(BUNDLE_DELIVERY_VAR_DATE_YYYY, new SimpleDateFormat("yyyy").format(now));
			fullName = fullName.replace(BUNDLE_DELIVERY_VAR_DATE_YY, new SimpleDateFormat("yy").format(now));
			fullName = fullName.replace(BUNDLE_DELIVERY_VAR_DATE_MM, new SimpleDateFormat("MM").format(now));
			fullName = fullName.replace(BUNDLE_DELIVERY_VAR_DATE_DD, new SimpleDateFormat("dd").format(now));
			fullName = fullName.replace(BUNDLE_DELIVERY_VAR_TIME_HH, new SimpleDateFormat("hh").format(now));
			fullName = fullName.replace(BUNDLE_DELIVERY_VAR_TIME_MM, new SimpleDateFormat("mm").format(now));
			fullName = fullName.replace(BUNDLE_DELIVERY_VAR_TIME_SS, new SimpleDateFormat("ss").format(now));

			this.jobZipName = fullName.trim();
		}

		return this.jobZipName;
	}

	public String getPostProcessScript() {
		return postProcessScript;
	}

	public void setPostProcessScript(String postProcessScript) {
		this.postProcessScript = postProcessScript;
	}

	public String getUsername() {
		String username = "";

		if (getUrl() == null) {
		    return null;
        }

		URI uri = URI.create(getUrl());

		if (uri.getUserInfo() != null) {
			username = uri.getUserInfo().contains(":") ? uri.getUserInfo().split(":")[0] : uri.getUserInfo();
		}

		return username;
	}

	public String getPassword() {
		String password = null;
		if (getUrl() != null) {
			URI uri = URI.create(getUrl());
			String userInfo = uri.getUserInfo();

			if (userInfo != null && userInfo.contains(":")) {
				String username = userInfo.split(":")[0];
				// password could contain colon (:) character(s)
				password = userInfo.substring(username.length()+1);
			}
		}

		return password;
	}

	public String getHostname() {
		return getUrl() != null ? URI.create(getUrl()).getHost() : null;
	}

	public String getPort() {
		return getUrl() != null ? String.valueOf(URI.create(getUrl()).getPort()) : null;
	}

	public String getPath() {
		return getUrl() != null ? URI.create(getUrl()).getPath() : null;
	}

	public String getURLWithoutUserInfo() {
    	if (getUrl() != null) {
			URI url = URI.create(getUrl());
			return MessageFormat.format("{0}://{1}:{2,number,#}{3}", url.getScheme(), url.getHost(), url.getPort(), url.getPath());
		}

		return null;
	}

	public void sendErrorNotification(DeliveryEvent deliveryEvent, String errorInfo) {
		String emailContent;

		try {
			if (StringUtils.isEmpty(getNotificationEmails())) {
				return;
			}

			HashMap<Object, String> values = getEmailTemplateValues(deliveryEvent);

			if (errorInfo != null && !errorInfo.isEmpty()) {
				values.put("errorLog", errorInfo);
			}

			emailContent = loadEmailTemplate().apply(values);
			sendNotificationEmail("Error", emailContent);
		} catch (Exception ex) {
			log.error("error", ex);
		}
	}

	private HashMap<Object, String> getEmailTemplateValues(DeliveryEvent deliveryEvent) {
		HashMap<Object, String> values = new HashMap<>();

		values.put("bundleDeliveryName", getName());
		values.put("jobId", String.valueOf(deliveryEvent.getJob().getId()));
		values.put("jobType", EventType.findById(deliveryEvent.getEventTypeId()).getLocalizedName());
		values.put("dateTimeSent", DateUtil.formatDateTimeWithTimeZone(DateUtil.now()));

		if (deliveryEvent.getItem() != null && deliveryEvent.getItem().getDocument() != null) {
			values.put("deliveryType", ApplicationUtil.getMessage("page.label.touchpoint"));
			values.put("deliveryItem", deliveryEvent.getItem().getDocument().getName());
		} else if (deliveryEvent.getItem() != null && deliveryEvent.getItem().getTouchpointCollection() != null) {
			values.put("deliveryType", ApplicationUtil.getMessage("page.label.touchpoint.collection"));
			values.put("deliveryItem", deliveryEvent.getItem().getTouchpointCollection().getName());
		} else {
			values.put("deliveryType", StringUtils.EMPTY);
			values.put("deliveryItem", StringUtils.EMPTY);
		}

		values.put("pod",MessagepointMultiTenantConnectionProvider.getPodMasterCode());
		values.put("domain", Node.getCurrentBranchName());
		values.put("instance", Node.getCurrentNodeName());

		values.put("bundleFile", getJobZipFileName(deliveryEvent));
		values.put("destination", getDisplayUrl());

		return values;
	}

	public void sendSuccessNotification(DeliveryEvent deliveryEvent, String info) {

		try {
			if (StringUtils.isEmpty(getNotificationEmails())) {
				return;
			}

			HashMap<Object, String> values = getEmailTemplateValues(deliveryEvent);

			if (!StringUtils.isEmpty(info)) {
			    values.put("infoLog", info);
            }

			String emailContent = loadEmailTemplate().apply(values);
			sendNotificationEmail("Success", emailContent);
		} catch (Exception ex) {
			logError(ex);
		}
	}

	private void sendNotificationEmail(String notificationType, String emailContent) {
		if (emailContent != null) {

			JSONObject emails = getNotificationEmailsAsJson();

			List<String> toEmails = JSONUtils.getJSONArrayAsList(emails, "to", String.class);
			List<String> ccEmails = JSONUtils.getJSONArrayAsList(emails, "cc", String.class);
			List<String> bccEmails = getBccEmails();

			LogUtil.getLog(DEServer.class).info(MessageFormat.format("Sending bundle delivery {0} email notification", notificationType));

			EmailManager emailManager = EmailUtil.getEmailManagerBean();
			emailManager.sendMail(toEmails,
					emailContent,
					MessageFormat.format("Bundle Delivery Notification: {0}", notificationType),
					null,
					ccEmails,
					bccEmails);
		}
	}
	
	private List<String> getBccEmails() {

		String supportEmail = SystemPropertyManager.getInstance().getPodMasterSystemProperty(SystemPropertyKeys.Email.KEY_DeliveryEventSupportNotification);

		return !StringUtils.isEmpty(supportEmail) ? splitEmails(supportEmail) : new ArrayList<>();
	}

	private Template loadEmailTemplate() throws IOException {
		String templatePath = "/com/prinova/messagepoint/model/admin/deserver/resources/delivery_notification.hbs";
		InputStream stream = getClass().getClassLoader().getResourceAsStream(templatePath);

		String templateContent = null;

		if (stream != null) {
			templateContent = IOUtils.toString(stream);
		}

		return new Handlebars().compileInline(templateContent);
	}

	private String getDisplayUrl() {
    	if (getUrl() != null) {
			URI uri = URI.create(getUrl());
			return MessageFormat.format("{0}://{1}{2}{3}", uri.getScheme(), uri.getHost(), uri.getPort() != -1 ? ":" + uri.getPort() : "", uri.getPath());
		}

    	return StringUtils.EMPTY;
	}
	
	private static void logError(Exception e) {
    	LogUtil.getLog(DEServer.class).error("Bundle Delivery Exception", e);
	}

	private static void logError(String e) {
    	logError(new RuntimeException(e));
	}

	public int getAvailability() {
		return availability;
	}

	public void setAvailability(int availability) {
		this.availability = availability;
	}

	public boolean isEnabledForJobType(int eventTypeId) {
    	if (this.getBundleTypeState() == null) {
    		logError(MessageFormat.format("Bundle delivery enabled/default state for server with guid {0} is null", this.getGuid()));
    		return false;
		}

    	switch (eventTypeId) {
			case EventType.TYPE_PRODUCTION:
				return this.getBundleTypeState().isProductionEnabled();
			case EventType.TYPE_COMMUNICATION_PROOF:
				return this.getBundleTypeState().isCommunicationProofEnabled();
			case EventType.TYPE_TEST:
				return this.getBundleTypeState().isTestEnabled();
			case EventType.TYPE_PROOF:
				return this.getBundleTypeState().isProofEnabled();
			case EventType.TYPE_SIMULATION:
				return this.getBundleTypeState().isSimulationEnabled();
			case EventType.TYPE_TEST_SUITE:
				return this.getBundleTypeState().isTestSuiteEnabled();
			case EventType.TYPE_COMMUNICATION_PRODUCTION:
				return this.getBundleTypeState().isCommunicationProductionEnabled();
			case EventType.TYPE_COMMUNICATION_MINI_PRODUCTION:
				return this.getBundleTypeState().isCommunicationProductionEnabled();
			case EventType.TYPE_PREVIEW:
				return this.getBundleTypeState().isPreviewEnabled();
			case EventType.TYPE_SEGMENTATION:
				return this.getBundleTypeState().isSegmentationEnabled();
    		default:
    			logError(MessageFormat.format("Unrecognized event type id: {0}", eventTypeId));
    			return false;
		}
	}
}
