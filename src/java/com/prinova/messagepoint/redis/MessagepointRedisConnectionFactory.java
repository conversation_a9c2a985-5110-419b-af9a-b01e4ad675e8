package com.prinova.messagepoint.redis;

import com.prinova.messagepoint.redis.client.MessagepointRedisConnection;
import com.prinova.messagepoint.redis.client.MessagepointRedisContext;
import com.prinova.messagepoint.redis.client.MessagepointRedisPubSubConnection;
import com.prinova.messagepoint.redis.client.MessagepointRedisPubSubContext;
import com.prinova.messagepoint.redis.config.MessagepointRedisConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.BeanFactoryAnnotationUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.session.data.redis.config.annotation.SpringSessionRedisConnectionFactory;

@Configuration
public class MessagepointRedisConnectionFactory {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private MessagepointRedisConfiguration messagepointRedisConfiguration;

    private MessagepointRedisContext messagepointRedisContext;

    private MessagepointRedisPubSubContext messagepointRedisPubSubContext;

    @Autowired
    public void setMessagepointRedisContext(MessagepointRedisConfiguration messagepointRedisConfiguration) {
        String redisMode = messagepointRedisConfiguration.getRedisMode();

        this.messagepointRedisContext = BeanFactoryAnnotationUtils.qualifiedBeanOfType(
                applicationContext.getAutowireCapableBeanFactory(),
                MessagepointRedisContext.class, redisMode);
    }

    @Autowired
    public void setMessagepointRedisPubSubContext(MessagepointRedisConfiguration messagepointRedisConfiguration) {
        String redisMode = messagepointRedisConfiguration.getRedisPubSubMode();

        this.messagepointRedisPubSubContext = BeanFactoryAnnotationUtils.qualifiedBeanOfType(
                applicationContext.getAutowireCapableBeanFactory(),
                MessagepointRedisPubSubContext.class, redisMode);
    }

    @Bean("redisConnectionFactory")
    public LettuceConnectionFactory redisConnectionFactory() {
        return messagepointRedisContext.getLettuceConnectionFactory();
    }

    @Bean("mpPubSubConnectionFactory")
    public LettuceConnectionFactory mpPubSubConnectionFactory() {
        return messagepointRedisPubSubContext.getPubSubConnectionFactory();
    }

    @Bean("messagepointRedisConnection")
    public MessagepointRedisConnection messagepointRedisConnection() {
        return BeanFactoryAnnotationUtils.qualifiedBeanOfType(
                applicationContext.getAutowireCapableBeanFactory(),
                MessagepointRedisConnection.class,
                messagepointRedisConfiguration.getRedisMode());
    }

    @Bean("messagepointRedisPubSubConnection")
    public MessagepointRedisPubSubConnection messagepointRedisPubSubConnection() {
        return BeanFactoryAnnotationUtils.qualifiedBeanOfType(
                applicationContext.getAutowireCapableBeanFactory(),
                MessagepointRedisPubSubConnection.class,
                messagepointRedisConfiguration.getRedisPubSubMode());
    }

    @SpringSessionRedisConnectionFactory
    @Bean("springSessionRedisConnectionFactory")
    public RedisConnectionFactory springSessionRedisConnectionFactory() {
        return mpPubSubConnectionFactory();
    }

}
