
interface MpInstance {
    id?: string;
    name: string;
    companyId: string;
    metadata: MpInstanceMetadata;
    createdAt?: string;
    updatedAt?: string;
}

interface MpInstanceMetadata {
    mpdc?: {
        pod: {
            name: string;
            username: string;
            password: string;
        };
    };
}

// Doesn't require specific State, generic EntityState is sufficient

interface MpInstancesContextInterface extends EntityState, EntityContext {
    addMpInstance: (mpInstance: MpInstance) => void;
    updateMpInstance: (mpInstance: MpInstance) => void;
}

const MpInstancesContext = React.createContext<MpInstancesContextInterface>(null);


const MpInstancesState = props => {

    const { pincConfig, apiPost, apiPatch, bulkDelete } = React.useContext(PincApiContext);

    const initialState: EntityState = {
        apiEndpoint: pincConfig.apiServer + getApiEndpoint(ApiEndpoint.MP_INSTANCES),
        showDialog: false,
        actionType: null,
        refresh: 0,
        actionErrors: [],
        entity: null
    };

    const [state, dispatch] = React.useReducer(genericEntityReducer, initialState);

    const addMpInstance = (mpInstance: MpInstance) => {
        const payload: MpInstance = {
            name: mpInstance.name,
            companyId: mpInstance.companyId,
            metadata: mpInstance.metadata
        };

        apiPost(
            state.apiEndpoint,
            payload,
            () => dispatch({ type: ActionType.SUCCESS }),
            error => dispatch({ type: ActionType.ERROR, payload: [error] })
        );
    };

    const updateMpInstance = (mpInstance: MpInstance) => {
        const payload = {
            name: mpInstance.name,
            metadata: mpInstance.metadata
        };

        apiPatch(
            state.apiEndpoint + '/' + mpInstance.id,
            payload,
            () => dispatch({ type: ActionType.SUCCESS }),
            error => dispatch({ type: ActionType.ERROR, payload: [error] })
        );
    };

    const deleteMpInstances = async (ids: string[]) => {
        bulkDelete(
            ids,
            state.apiEndpoint,
            () => dispatch({ type: ActionType.SUCCESS }),
            errors => dispatch({ type: ActionType.ERROR, payload: errors })
        );
    };

    const openDialog = (actionType: ActionType) => dispatch({ type: actionType });

    const closeDialog = () => dispatch({ type: ActionType.CANCEL });

    const setEntity = (entity) => dispatch({ type: ActionType.SET, payload: entity });

    return (
        <MpInstancesContext.Provider
            value={{
                ...state,
                addMpInstance,
                updateMpInstance,
                deleteEntities: deleteMpInstances,
                openDialog,
                closeDialog,
                setEntity
            }}
            children={props.children}
        />
    );
};