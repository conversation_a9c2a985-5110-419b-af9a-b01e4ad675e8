package com.prinova.messagepoint.controller.tpadmin;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.util.HibernateUtil;

public class ZoneListWrapper  implements Serializable {
	private static final long serialVersionUID = 6558775873622781660L;
	
	private List<Long>				selectedIds;
	private String 					actionValue;
	private long					defaultStyle;
	
	public ZoneListWrapper(){
		super();
	}	
	
	public ZoneListWrapper(List<Zone> zones){
		super();
		this.selectedIds 	= new ArrayList<>();
	}

	public List<Long> getSelectedIds() {
		return selectedIds;
	}

	public void setSelectedIds(List<Long> selectedIds) {
		this.selectedIds = selectedIds;
	}	
	
	public String getActionValue() {
		return actionValue;
	}
	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}

	public long getDefaultStyle() {
		return defaultStyle;
	}

	public void setDefaultStyle(long defaultStyle) {
		this.defaultStyle = defaultStyle;
	}

	public List<Zone> getSelectedList(){
		List<Zone> selectedList = new ArrayList<>();
		for(Long selectedId : this.selectedIds){
			selectedList.add(HibernateUtil.getManager().getObject(Zone.class, selectedId));
		}
		return selectedList;
	}

	public static class ZonesVO{
		private boolean 	selectedForAction;		
		private Zone		zone;	
		
		public boolean isSelectedForAction() {
			return selectedForAction;
		}
		public void setSelectedForAction(boolean selectedForAction) {
			this.selectedForAction = selectedForAction;
		}
		public Zone getZone() {
			return zone;
		}
		public void setZone(Zone zone) {
			this.zone = zone;
		}				
	}

}
