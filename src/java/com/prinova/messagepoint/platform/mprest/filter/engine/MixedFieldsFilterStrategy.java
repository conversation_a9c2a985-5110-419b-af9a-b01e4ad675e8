package com.prinova.messagepoint.platform.mprest.filter.engine;

import com.prinova.messagepoint.platform.mprest.common.BadRequestException;
import com.prinova.messagepoint.platform.mprest.filter.FilterMappingInstance;
import com.prinova.messagepoint.platform.mprest.filter.FilterOperator;
import com.prinova.messagepoint.platform.mprest.filter.FilterRequest;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.ReflectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import javax.persistence.criteria.*;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class MixedFieldsFilterStrategy<T, V> implements FilterStrategy<T, V> {
    @Override
    public boolean canHandle(FilterContext<T, V> context) {
        // This strategy ONLY handles requests involving computed fields
        // (either mixed database + computed, or pure computed fields)
        return context.getFilterRequest().isComplexRequest() && context.hasComputedFields() && context.hasDatabaseFields();
    }

    @Override
    public int getPriority() {
        return 300;
    }

    @Override
    public FilterResult<T> filter(List<T> entities, FilterContext<T, V> context) {
        // Break down the filter request into logical AND and OR groups
        FilterRequest filterRequest = context.getFilterRequest();

        List<T> results = new ArrayList<>();

        boolean hasSubRequests = filterRequest.isComplexRequest();
        while(hasSubRequests){
            // Fetch data for the current filter request
            results.addAll(fetchDataForFilterRequest(context, filterRequest));

            hasSubRequests = filterRequest.hasSubRequests();
            if(hasSubRequests){

            }

            hasSubRequests = false;
        }

        // Return FilterResult with cursor information
        FilterResult<T> filterResult = new FilterResult<T>()
                .withData(results)
                .withTotalCount((long) results.size())
                .withCursor(context.getCursorParams());

        if (context.hasCursorPagination()) {
            filterResult.withCursor(context.getCursorParams());
        }

        return filterResult;
    }

    private List<T> fetchDataForFilterRequest(FilterContext<T, V> context, FilterRequest filterRequest){
        List<T> results = new ArrayList<>();
        int targetLimit = getEffectiveLimit(context);
        Long cursorPosition = context.getCursorPositionAsLong(); // Can be null for first request
        int totalProcessed = 0;

        // Initialize cursor position for first request (start from beginning)
        if (cursorPosition == null) {
            cursorPosition = 0L; // Start from the beginning for first request
        }
        int failStop = 0;
        // Adaptive batch processing loop
        while (results.size() < targetLimit) {
            failStop++;
            if(failStop > 100){
                break;
            }
            boolean hasDatabaseFields = FilterAnalyzer.containsDatabaseFields(filterRequest, context.getFilterMappings(), false),
                    hasComputedFields = FilterAnalyzer.containsComputedFields(filterRequest, context.getFilterMappings(), false),
                    isAnd = filterRequest.isAnd(),
                    isOr = filterRequest.isOr();


            // If it is an AND request then we can fetch database fields first and then filter by computed fields
            //***************** TOP LEVEL AND ****************/
            if (isAnd) {
                if (hasDatabaseFields) {
                    // Fetch next batch from database with cursor condition
                    List<T> databaseFieldsBatch = fetchDatabaseBatch(context, filterRequest, cursorPosition, context.getBatchSize(), null);

                    if (databaseFieldsBatch.isEmpty()) {
                        break; // No more data available
                    }

                    // Process each entity in the batch
                    for (T entity : databaseFieldsBatch) {
                        Long entityId = extractEntityId(entity);
                        totalProcessed++;

                        // Skip entities that were already processed in previous requests
                        if (entityId != null && entityId <= cursorPosition) {
                            continue;
                        }

                        // Update cursor position to the last processed entity
                        if (entityId != null) {
                            cursorPosition = entityId;
                        }

                        if (hasComputedFields) {
                            if (matchesComputedCriteriaForFilterRequest(entity, context, filterRequest)) {
                                results.add(entity);

                                // Stop when we have enough results
                                if (results.size() >= targetLimit) {
                                    break;
                                }
                            }
                        }
                        else {
                            results.add(entity);

                            // Stop when we have enough results
                            if (results.size() >= targetLimit) {
                                break;
                            }
                        }
                    }
                } else if (hasComputedFields) {
                    // Fetch next batch from database with cursor condition
                    List<T> computedFieldsBatch = fetchSelectAllBatch(context, cursorPosition, context.getBatchSize(), null);

                    if (computedFieldsBatch.isEmpty()) {
                        break; // No more data available
                    }

                    // Process each entity in the batch
                    for (T entity : computedFieldsBatch) {
                        Long entityId = extractEntityId(entity);
                        totalProcessed++;

                        // Skip entities that were already processed in previous requests
                        if (entityId != null && entityId <= cursorPosition) {
                            continue;
                        }

                        // Update cursor position to the last processed entity
                        if (entityId != null) {
                            cursorPosition = entityId;
                        }

                        if (matchesComputedCriteriaForFilterRequest(entity, context, filterRequest)) {
                            results.add(entity);

                            // Stop when we have enough results
                            if (results.size() >= targetLimit) {
                                break;
                            }
                        }
                    }
                } else {
                    break;
                }

                // Check for sub-requests
                //***************** SUB-REQUEST LEVEL AND ****************/
                if(filterRequest.hasSubRequests() && !results.isEmpty()){
                    List<FilterRequest> subRequests = filterRequest.getAnd().stream().filter(FilterRequest::isComplexRequest).toList();
                    for(FilterRequest subRequest : subRequests){
                        if (subRequest.isAnd()) {
                            if (FilterAnalyzer.containsDatabaseFields(subRequest, context.getFilterMappings(), false)) {
                                // Fetch next batch from database with cursor condition
                                results = fetchDatabaseBatch(context, subRequest, null, context.getBatchSize(), results.stream().map(this::extractEntityId).collect(Collectors.toList()));

                                if (results.isEmpty()) {
                                    break; // No more data available
                                }

                                // Process each entity in the batch
                                for (T entity : results) {
                                    if (FilterAnalyzer.containsComputedFields(subRequest, context.getFilterMappings(), false)) {
                                        if (!matchesComputedCriteriaForFilterRequest(entity, context, subRequest)) {
                                            results.remove(entity);
                                        }
                                    }
                                }
                            } else if (FilterAnalyzer.containsComputedFields(subRequest, context.getFilterMappings(), false)) {
                                // Process each entity in the batch
                                results.removeIf(entity -> !matchesComputedCriteriaForFilterRequest(entity, context, subRequest));
                            } else {
                                break;
                            }

                            // Check for sub-requests
                            // TODO
                        }
                        else if(subRequest.isOr()){
                            if (FilterAnalyzer.containsDatabaseFields(subRequest, context.getFilterMappings(), false)) {
                                // Fetch next batch from database with cursor condition
                                List<T> databaseFieldsSubRequestBatch = fetchDatabaseBatch(context, subRequest, null, context.getBatchSize(), results.stream().map(this::extractEntityId).collect(Collectors.toList()));

                                if (results.isEmpty()) {
                                    break; // No more data available
                                }

                                // Process each entity in the batch
                                for (T entity : results) {
                                    boolean matchesComputedCriteria = matchesComputedCriteriaForFilterRequest(entity, context, subRequest);
                                    if(!databaseFieldsSubRequestBatch.contains(entity) && !matchesComputedCriteria){
                                        results.remove(entity);
                                    }
                                }
                            } else if (FilterAnalyzer.containsComputedFields(subRequest, context.getFilterMappings(), false)) {
                                // Process each entity in the batch
                                results.removeIf(entity -> !matchesComputedCriteriaForFilterRequest(entity, context, subRequest));
                            } else {
                                break;
                            }
                        }
                    }
                }
            //***************** TOP LEVEL OR ****************/
            } else if (isOr) {
                if (hasDatabaseFields) {

                    // Fetch next batch from database with original cursor condition
                    List<T> databaseFieldsBatch = fetchDatabaseBatch(context, filterRequest, cursorPosition, context.getBatchSize(), null);

                    if (hasComputedFields) {
                        List<T> computedFieldsBatch = fetchSelectAllBatch(context, cursorPosition, context.getBatchSize(), null);

                        // Go through computed fields batch and extract and add to subRequestResults all database fields that have id less or equal to computed field
                        // if computed field matches criteria add to subRequestResults too
                        for (T entity : computedFieldsBatch) {
                            Long computedEntityId = ObjectUtils.firstNonNull(extractEntityId(entity), 0L);
                            // Add all database sub request results that have id less or equal to computedEntityId
                            for(T databaseEntity : databaseFieldsBatch){
                                Long databaseEntityId = extractEntityId(databaseEntity);
                                if (databaseEntityId <= computedEntityId && results.stream().noneMatch(e -> extractEntityId(e).equals(databaseEntityId))) {
                                    results.add(databaseEntity);
                                    cursorPosition = databaseEntityId;
                                }
                            }

                            if (results.size() >= targetLimit) {
                                break;
                            }

                            boolean alreadyInResults = results.stream().anyMatch(e -> extractEntityId(e).equals(computedEntityId));
                            if (!alreadyInResults && matchesComputedCriteriaForFilterRequest(entity, context, filterRequest)) {
                                results.add(entity);
                                cursorPosition = computedEntityId;
                            }

                            if (results.size() >= targetLimit) {
                                break;
                            }
                        }

                    /*

                    // Fetch next batch from database with cursor condition
                    List<T> databaseFieldsBatch = fetchDatabaseBatch(context, filterRequest, cursorPosition, context.getBatchSize(), null);

                    if(hasComputedFields){
                        List<T> computedFieldsBatch = fetchSelectAllBatch(context, cursorPosition, context.getBatchSize(), null);

                        // go through computedFieldsBatch and process each entity, check if there is an entity with id less or equal in database batch if so add to result
                        for (T entity : computedFieldsBatch) {
                            Long entityId = ObjectUtils.firstNonNull(extractEntityId(entity), 0L);

                            // Skip entities that were already processed in previous requests
                            if (entityId != null && entityId <= cursorPosition) {
                                continue;
                            }

                            // Update cursor position to the last processed entity
                            cursorPosition = entityId;

                            //For each entity in databaseFieldsBatch entity with id less or equal to entityId add to results
                            for (T databaseEntity : databaseFieldsBatch) {
                                Long databaseEntityId = extractEntityId(databaseEntity);
                                if (databaseEntityId != null && databaseEntityId <= entityId) {
                                    // check if results doesn't have same entityid already
                                    if (results.stream().noneMatch(e -> extractEntityId(e).equals(databaseEntityId))) {
                                        results.add(databaseEntity);
                                        cursorPosition = databaseEntityId;
                                    }

                                    // Stop when we have enough results
                                    if (results.size() >= targetLimit) {
                                        break;
                                    }
                                }
                            }

                            // Stop when we have enough results from database fields
                            if (results.size() >= targetLimit) {
                                break;
                            }

                            if (matchesComputedCriteriaForFilterRequest(entity, context, filterRequest)) {
                                if (results.stream().noneMatch(e -> extractEntityId(e).equals(entityId))) {
                                    results.add(entity);
                                    cursorPosition = entityId;
                                }

                                // Stop when we have enough results
                                if (results.size() >= targetLimit) {
                                    break;
                                }
                            }
                        } */
                    }
                    else {
                        if (databaseFieldsBatch.isEmpty()) {
                            break; // No more data available
                        }

                        // Process each entity in the batch
                        for (T entity : databaseFieldsBatch) {
                            Long entityId = extractEntityId(entity);
                            totalProcessed++;

                            // Skip entities that were already processed in previous requests
                            if (entityId != null && entityId <= cursorPosition) {
                                continue;
                            }

                            // Update cursor position to the last processed entity
                            if (entityId != null) {
                                cursorPosition = entityId;
                            }

                            results.add(entity);

                            // Stop when we have enough results
                            if (results.size() >= targetLimit) {
                                break;
                            }
                        }
                    }

                } else if (hasComputedFields) {
                    // Fetch next batch from database with cursor condition
                    List<T> computedFieldsBatch = fetchSelectAllBatch(context, cursorPosition, context.getBatchSize(), null);

                    if (computedFieldsBatch.isEmpty()) {
                        break; // No more data available
                    }

                    // Process each entity in the batch
                    for (T entity : computedFieldsBatch) {
                        Long entityId = extractEntityId(entity);
                        totalProcessed++;

                        // Skip entities that were already processed in previous requests
                        if (entityId != null && entityId <= cursorPosition) {
                            continue;
                        }

                        // Update cursor position to the last processed entity
                        if (entityId != null) {
                            cursorPosition = entityId;
                        }

                        if (matchesComputedCriteriaForFilterRequest(entity, context, filterRequest)) {
                            results.add(entity);

                            // Stop when we have enough results
                            if (results.size() >= targetLimit) {
                                break;
                            }
                        }
                    }
                } else {
                    break;
                }

                // Check for sub-requests
                //***************** SUB-REQUEST LEVEL OR ****************/
                if(filterRequest.hasSubRequests() && !results.isEmpty()){
                    Long originalCursorPosition = context.getCursorPositionAsLong();
                    List<FilterRequest> subRequests = filterRequest.getOr().stream().filter(FilterRequest::isComplexRequest).toList();
                    for(FilterRequest subRequest : subRequests){
                        List<T> subRequestResults = new ArrayList<>();
                        boolean subRequestHasDatabaseFields = FilterAnalyzer.containsDatabaseFields(subRequest, context.getFilterMappings(), false),
                                subRequestHasComputedFields = FilterAnalyzer.containsComputedFields(subRequest, context.getFilterMappings(), false);

                        if (subRequest.isAnd()) {
                            if (subRequestHasDatabaseFields) {
                                // Fetch next batch from database with original cursor condition
                                List<T> databaseFieldsSubRequestBatch = fetchDatabaseBatch(context, subRequest, originalCursorPosition, context.getBatchSize(), null);

                                if (databaseFieldsSubRequestBatch.isEmpty()) {
                                    break; // No more data available
                                }

                                // Process each entity in the batch
                                for (T entity : databaseFieldsSubRequestBatch) {
                                    if (subRequestHasComputedFields) {
                                        if (matchesComputedCriteriaForFilterRequest(entity, context, subRequest)) {
                                            subRequestResults.add(entity);

                                            // Stop when we have enough results
                                            if (subRequestResults.size() >= targetLimit) {
                                                break;
                                            }
                                        }
                                    }
                                    else {
                                        subRequestResults.add(entity);

                                        // Stop when we have enough results
                                        if (subRequestResults.size() >= targetLimit) {
                                            break;
                                        }
                                    }
                                }
                            }
                            else if(subRequestHasComputedFields){
                                List<T> computedFieldsBatch = fetchSelectAllBatch(context, originalCursorPosition, context.getBatchSize(), null);

                                if (computedFieldsBatch.isEmpty()) {
                                    break; // No more data available
                                }

                                // Process each entity in the batch
                                for (T entity : computedFieldsBatch) {
                                    if (matchesComputedCriteriaForFilterRequest(entity, context, subRequest)) {
                                        subRequestResults.add(entity);

                                        // Stop when we have enough results
                                        if (subRequestResults.size() >= targetLimit) {
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        else if(subRequest.isOr()){
                            if (subRequestHasDatabaseFields) {
                                // Fetch next batch from database with original cursor condition
                                List<T> databaseFieldsSubRequestBatch = fetchDatabaseBatch(context, subRequest, originalCursorPosition, context.getBatchSize(), null);

                                if (subRequestHasComputedFields) {
                                    List<T> computedFieldsBatch = fetchSelectAllBatch(context, originalCursorPosition, context.getBatchSize(), null);

                                    // Go through computed fields batch and extract and add to subRequestResults all database fields that have id less or equal to computed field
                                    // if computed field matches criteria add to subRequestResults too
                                    for (T entity : computedFieldsBatch) {
                                        Long computedEntityId = ObjectUtils.firstNonNull(extractEntityId(entity), 0L);
                                        // Add all database sub request results that have id less or equal to computedEntityId
                                        List<T> databaseFieldsEntitiesToAdd = databaseFieldsSubRequestBatch.stream()
                                                .filter(e -> extractEntityId(e) <= computedEntityId)
                                                .filter(e -> subRequestResults.stream().noneMatch(existing ->
                                                        extractEntityId(existing).equals(extractEntityId(e))))
                                                .toList();
                                        subRequestResults.addAll(databaseFieldsEntitiesToAdd);

                                        if (subRequestResults.size() >= targetLimit) {
                                            break;
                                        }

                                        if (matchesComputedCriteriaForFilterRequest(entity, context, subRequest) && !subRequestResults.contains(entity)) {
                                            subRequestResults.add(entity);
                                        }
                                    }
                                } else {
                                    subRequestResults.addAll(databaseFieldsSubRequestBatch);
                                }
                            }
                            else if (FilterAnalyzer.containsComputedFields(subRequest, context.getFilterMappings(), false)) {
                                List<T> computedFieldsBatch = fetchSelectAllBatch(context, originalCursorPosition, context.getBatchSize(), null);

                                for(T entity : computedFieldsBatch){
                                    if (matchesComputedCriteriaForFilterRequest(entity, context, subRequest)) {
                                        subRequestResults.add(entity);

                                        // Stop when we have enough results
                                        if (subRequestResults.size() >= targetLimit) {
                                            break;
                                        }
                                    }
                                }
                            } else {
                                break;
                            }
                        }

                        // Combine results with subRequestResults based on which id is less, update cursor position accordingly
                        List<T> finalResults = new ArrayList<>();
                        for (T subRequestResult : subRequestResults) {
                            Long subRequestEntityId = ObjectUtils.firstNonNull(extractEntityId(subRequestResult), 0L);

                            // Update cursor position to the last processed entity
                            //cursorPosition = entityId;

                            //Add to final results all results that have id less than subRequestEntityId
                            for (T result : results) {
                                Long resultId = extractEntityId(result);
                                if (resultId != null && resultId <= subRequestEntityId) {
                                    // check if results doesn't have same entityid already
                                    if (finalResults.stream().noneMatch(e -> extractEntityId(e).equals(resultId))) {
                                        finalResults.add(result);
                                        cursorPosition = resultId;
                                    }

                                    // Stop when we have enough results
                                    if (finalResults.size() >= targetLimit) {
                                        break;
                                    }
                                }
                            }

                            // Stop when we have enough results from database fields
                            if (finalResults.size() >= targetLimit) {
                                break;
                            }

                            if(finalResults.stream().noneMatch(e -> extractEntityId(e).equals(subRequestEntityId))){
                                finalResults.add(subRequestResult);
                                cursorPosition = subRequestEntityId;
                            }
                        }
                        results = finalResults;
                    }
                }
            }
        }

        // Generate response cursor parameters
        boolean hasMore = results.size() == targetLimit; // Simplified check
        if (context.getCursorParams() != null) {
            context.getCursorParams().generateResponseParams(cursorPosition, hasMore);
        }
        return results;
    }

    private List<T> fetchSelectAllBatch(FilterContext<T, V> context, Long cursorPosition, int batchSize, List<Long> entityIds){
        try {
            CriteriaQuery<T> query = createSelectAllQuery(context);

            // Add cursor condition and ordering
            CriteriaBuilder criteriaBuilder = HibernateUtil.getManager().getSession().getCriteriaBuilder();
            Root<T> root = (Root<T>) query.getRoots().iterator().next();

            // Add cursor condition: WHERE id > cursorPosition
            if (cursorPosition != null && cursorPosition > 0) {
                Predicate cursorPredicate = criteriaBuilder.greaterThan(root.get("id"), cursorPosition);
                if (query.getRestriction() != null) {
                    query.where(criteriaBuilder.and(query.getRestriction(), cursorPredicate));
                } else {
                    query.where(cursorPredicate);
                }
            }

            if(entityIds != null && !entityIds.isEmpty()){
                query.where(root.get("id").in(entityIds));
            }

            // Add ordering by ID for consistent cursor behavior
            query.orderBy(criteriaBuilder.asc(root.get("id")));

            // Execute query with batch size limit
            return HibernateUtil.getManager().getSession()
                    .createQuery(query)
                    .setMaxResults(batchSize)
                    .getResultList();

        } catch (Exception e) {
            throw new BadRequestException("Error fetching database batch: " + e.getMessage());
        }
    }

    /**
     * Fetch a batch of entities from the database with cursor-based ordering.
     */
    private List<T> fetchDatabaseBatch(FilterContext<T, V> context, FilterRequest filterRequest, Long cursorPosition, int batchSize, List<Long> entityIds) {
        try {
            FilterQueryBuilder<T, V> queryBuilder = new FilterQueryBuilder<>(
                    context.getEntityType(),
                    context.getDtoType(),
                    true,
                    entityIds
            );

            CriteriaQuery<T> query = queryBuilder.buildQuery(filterRequest);

            // Add cursor condition and ordering
            CriteriaBuilder criteriaBuilder = HibernateUtil.getManager().getSession().getCriteriaBuilder();
            Root<T> root = (Root<T>) query.getRoots().iterator().next();

            // Add cursor condition: WHERE id > cursorPosition
            if (cursorPosition != null && cursorPosition > 0) {
                Predicate cursorPredicate = criteriaBuilder.greaterThan(root.get("id"), cursorPosition);
                if (query.getRestriction() != null) {
                    query.where(criteriaBuilder.and(query.getRestriction(), cursorPredicate));
                } else {
                    query.where(cursorPredicate);
                }
            }

            // Add ordering by ID for consistent cursor behavior
            query.orderBy(criteriaBuilder.asc(root.get("id")));

            // Execute query with batch size limit
            return HibernateUtil.getManager().getSession()
                    .createQuery(query)
                    .setMaxResults(batchSize)
                    .getResultList();

        } catch (Exception e) {
            throw new BadRequestException("Error fetching database batch: " + e.getMessage());
        }
    }

    /**
     * Extract entity ID using reflection.
     */
    private Long extractEntityId(T entity) {
        try {
            Method getIdMethod = entity.getClass().getMethod("getId");
            Object idValue = getIdMethod.invoke(entity);

            if (idValue instanceof Long) {
                return (Long) idValue;
            } else if (idValue instanceof Number) {
                return ((Number) idValue).longValue();
            }
        } catch (Exception e) {
            // Could not extract ID
        }

        return null;
    }

    /**
     * Get the effective limit for the request.
     * For explicit cursor requests, use the cursor limit.
     * For automatic cursor optimization, use a default batch-friendly limit.
     */
    private int getEffectiveLimit(FilterContext<T, V> context) {
        if (context.hasCursorPagination()) {
            // Explicit cursor request - use the requested limit
            return context.getLimit();
        } else {
            // Automatic cursor optimization - use default limit if not specified
            // This ensures we don't process unlimited entities on first request
            return context.getLimit() > 0 ? context.getLimit() : 50; // Default to 50 for computed fields
        }
    }

    /**
     * Check if an entity matches all criteria (both database and computed fields).
     */
    private boolean matchesComputedCriteriaForFilterRequest(T entity, FilterContext<T, V> context, FilterRequest filterRequest) {
        return matchesComputedFieldCriteria(entity, filterRequest, context.getFilterMappings(), context.getEntityType(), 0);
    }

    /**
     * Check if an entity matches computed field criteria.
     */
    private boolean matchesComputedFieldCriteria(T entity, FilterRequest filterRequest,
                                                 Map<String, FilterMappingInstance> filterMappings,
                                                 Class<T> entityType,
                                                 int depth) {
        int nextDepth = depth + 1;
        if (filterRequest.getAnd() != null && depth == 0) {
            return filterRequest.getAnd().stream()
                    .filter(subRequest -> FilterAnalyzer.containsComputedFields(subRequest, filterMappings, false))
                    .allMatch(subRequest -> matchesComputedFieldCriteria(entity, subRequest, filterMappings, entityType, nextDepth));
        } else if (filterRequest.getOr() != null && depth == 0) {
            return filterRequest.getOr().stream()
                    .filter(subRequest -> FilterAnalyzer.containsComputedFields(subRequest, filterMappings, false))
                    .anyMatch(subRequest -> matchesComputedFieldCriteria(entity, subRequest, filterMappings, entityType, nextDepth));
        } else {
            return matchesSingleFieldCriteria(entity, filterRequest, filterMappings, entityType);
        }
    }

    /**
     * Check if an entity matches a single field criteria.
     */
    private boolean matchesSingleFieldCriteria(T entity, FilterRequest filterRequest,
                                               Map<String, FilterMappingInstance> filterMappings,
                                               Class<T> entityType) {
        String fieldName = filterRequest.getField();
        if (fieldName == null || fieldName.isEmpty()) {
            return false;
        }

        FilterMappingInstance mapping = filterMappings.get(fieldName);
        if (mapping == null) {
            return false; // Unknown field, skip
        }

        // Only process computed fields here (database fields already filtered)
        if (!mapping.isMethodBased()) {
            return true; // Database field, already handled
        }

        try {
            Method method = ReflectionUtils.findMethod(entityType, mapping.getMethodName(), (Class<?>) null);
            if (method == null) {
                throw new BadRequestException("Method '" + mapping.getMethodName() + "' not found on class " + entityType.getSimpleName());
            }

            Object computedValue = org.springframework.util.ReflectionUtils.invokeMethod(method, entity);

            FilterOperator operator = filterRequest.getOp() != null ?
                    FilterOperator.fromOperator(filterRequest.getOp()) :
                    (mapping.getOperator() != null ? mapping.getOperator() : FilterOperator.EQUALS);

            String expectedValue = filterRequest.getValue() != null ?
                    filterRequest.getValue() : mapping.getValue();

            return ValueComparator.compareValues(computedValue, expectedValue, operator);

        } catch (Exception e) {
            throw new BadRequestException("Error evaluating computed field '" + fieldName + "': " + e.getMessage());
        }
    }

    /**
     * Creates a simple SELECT ALL query without any WHERE clauses.
     * This is used when the FilterRequest contains only computed fields,
     * allowing FilterComponent to handle all filtering in memory.
     *
     * @return CriteriaQuery that selects all entities of the result type
     */
    private CriteriaQuery<T> createSelectAllQuery(FilterContext<T, V> context) {
        CriteriaBuilder cb = HibernateUtil.getManager().getSession().getCriteriaBuilder();
        CriteriaQuery<T> query = cb.createQuery(context.getEntityType());
        Root<T> root = query.from(context.getEntityType());
        query.select(root);
        return query;
    }

}
