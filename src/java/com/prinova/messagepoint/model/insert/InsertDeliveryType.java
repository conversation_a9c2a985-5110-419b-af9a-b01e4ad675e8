package com.prinova.messagepoint.model.insert;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.common.StaticType;
import com.prinova.messagepoint.util.ApplicationUtil;

public class InsertDeliveryType extends StaticType {

	private static final long serialVersionUID = -3666553978925945573L;

	public static final int ID_OPTIONAL = 1;
	public static final int ID_MANDATORY = 2;
	public static final int ID_NON_SELECTABLE = 3;
	
	public static final String MESSAGE_CODE_OPTIONAL = "page.label.insert.delivery.type.optional";
	public static final String MESSAGE_CODE_MANDATORY = "page.label.insert.delivery.type.mandatory";
	public static final String MESSAGE_CODE_NON_SELECTABLE = "page.label.insert.delivery.type.non.selectable";

	public InsertDeliveryType() {
		super();
	}
	
	public InsertDeliveryType(Integer id) {
		super();
		switch (id) {
		case ID_OPTIONAL:
			this.setId(ID_OPTIONAL);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_OPTIONAL));
			this.setDisplayMessageCode(MESSAGE_CODE_OPTIONAL);
			break;
		case ID_MANDATORY:
			this.setId(ID_MANDATORY);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_MANDATORY));
			this.setDisplayMessageCode(MESSAGE_CODE_MANDATORY);
			break;
		case ID_NON_SELECTABLE:
			this.setId(ID_NON_SELECTABLE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_NON_SELECTABLE));
			this.setDisplayMessageCode(MESSAGE_CODE_NON_SELECTABLE);
			break;
		default:
			break;
		}
	}

	public InsertDeliveryType(String name) {
		super();
		if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_OPTIONAL))) { 
			this.setId(ID_OPTIONAL);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_OPTIONAL));
			this.setDisplayMessageCode(MESSAGE_CODE_OPTIONAL);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_MANDATORY))) { 
			this.setId(ID_MANDATORY);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_MANDATORY));
			this.setDisplayMessageCode(MESSAGE_CODE_MANDATORY);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_NON_SELECTABLE))) { 
			this.setId(ID_NON_SELECTABLE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_NON_SELECTABLE));
			this.setDisplayMessageCode(MESSAGE_CODE_NON_SELECTABLE);
		}
	}
	
	public static List<InsertDeliveryType> listAll() {
		List<InsertDeliveryType> allInsertDeliveryTypes = new ArrayList<>();
		
		InsertDeliveryType insertDeliveryType = null;
		
		insertDeliveryType = new InsertDeliveryType(ID_OPTIONAL);
		allInsertDeliveryTypes.add(insertDeliveryType);
		
		insertDeliveryType = new InsertDeliveryType(ID_MANDATORY);
		allInsertDeliveryTypes.add(insertDeliveryType);
		
		insertDeliveryType = new InsertDeliveryType(ID_NON_SELECTABLE);
		allInsertDeliveryTypes.add(insertDeliveryType);
		
		return allInsertDeliveryTypes;
	}
}