<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="com.prinova.messagepoint.model.contentintelligence.ContentAssistant" table="content_assistant">

        <cache usage="read-write"/>
        <id name="id" column="id" >
            <generator class="native" />
        </id>

        <property name="name" length="255" />

        <property name="query" type="org.hibernate.type.TextType" column="query" />
        <property name="enabled" column="enabled" />
        <property name="triggerCriteria" column="trigger_criteria" />
        <property name="systemControlled" column="system_controlled" />
        <property name="contentSelector" column="content_selector" />
        <property name="batchThrottle" column="batch_throttle" />

        <property name="targetAllTouchpoints" column="target_all_touchpoints"  />
        <set name="targetTouchpoints" table="content_assistant_target_tps" cascade="none">
            <key column="content_assistant_id"/>
            <many-to-many column="document_id" class="com.prinova.messagepoint.model.Document" />
        </set>
        <property name="targetAllObjects" column="target_all_objects" />
        <property name="targetObjects" column="target_objects" />
        <property name="targetObjectsStatus" column="target_objects_status" />
        <property name="targetAllLocales" column="target_all_locales" />
        <set name="targetLocales" table="content_assistant_target_locales" cascade="none">
            <key column="content_assistant_id"/>
            <many-to-many column="locale_id" class="com.prinova.messagepoint.model.admin.MessagepointLocale" />
        </set>

    </class>
</hibernate-mapping>
