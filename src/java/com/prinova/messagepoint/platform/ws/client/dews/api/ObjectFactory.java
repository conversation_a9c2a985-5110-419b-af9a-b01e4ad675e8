
package com.prinova.messagepoint.platform.ws.client.dews.api;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.prinova.messagepoint.platform.ws.client.dews.api package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _TransactionResultFileResponse_QNAME = new QName("urn:dews", "transactionResultFileResponse");
    private final static QName _CreateConnectedOrderResponse_QNAME = new QName("urn:dews", "createConnectedOrderResponse");
    private final static QName _UploadJobBundleResponse_QNAME = new QName("urn:dews", "uploadJobBundleResponse");
    private final static QName _ProcessJobBundleAsyncResponse_QNAME = new QName("urn:dews", "processJobBundleAsyncResponse");
    private final static QName _UploadProcessJobBundleAsyncResponse_QNAME = new QName("urn:dews", "uploadProcessJobBundleAsyncResponse");
    private final static QName _ServerStatusResponse_QNAME = new QName("urn:dews", "serverStatusResponse");
    private final static QName _DownloadResultJobBundleResponse_QNAME = new QName("urn:dews", "downloadResultJobBundleResponse");
    private final static QName _ProcessResponse_QNAME = new QName("urn:dews", "processResponse");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.prinova.messagepoint.platform.ws.client.dews.api
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link CreateConnectedOrderResponse }
     * 
     */
    public CreateConnectedOrderResponse createCreateConnectedOrderResponse() {
        return new CreateConnectedOrderResponse();
    }

    /**
     * Create an instance of {@link DownloadResultFileByExternalId }
     * 
     */
    public DownloadResultFileByExternalId createDownloadResultFileByExternalId() {
        return new DownloadResultFileByExternalId();
    }

    /**
     * Create an instance of {@link ProcessTransactionDriverFileAsyncResponse }
     * 
     */
    public ProcessTransactionDriverFileAsyncResponse createProcessTransactionDriverFileAsyncResponse() {
        return new ProcessTransactionDriverFileAsyncResponse();
    }

    /**
     * Create an instance of {@link GetPerformanceMetricsResponse }
     * 
     */
    public GetPerformanceMetricsResponse createGetPerformanceMetricsResponse() {
        return new GetPerformanceMetricsResponse();
    }

    /**
     * Create an instance of {@link ValidateDownloadedFileResponse }
     * 
     */
    public ValidateDownloadedFileResponse createValidateDownloadedFileResponse() {
        return new ValidateDownloadedFileResponse();
    }

    /**
     * Create an instance of {@link DownloadConnectedResultFile }
     * 
     */
    public DownloadConnectedResultFile createDownloadConnectedResultFile() {
        return new DownloadConnectedResultFile();
    }

    /**
     * Create an instance of {@link DownloadResultFile }
     * 
     */
    public DownloadResultFile createDownloadResultFile() {
        return new DownloadResultFile();
    }

    /**
     * Create an instance of {@link ProcessJobBundleAsync }
     * 
     */
    public ProcessJobBundleAsync createProcessJobBundleAsync() {
        return new ProcessJobBundleAsync();
    }

    /**
     * Create an instance of {@link DownloadOrderBundleFile }
     * 
     */
    public DownloadOrderBundleFile createDownloadOrderBundleFile() {
        return new DownloadOrderBundleFile();
    }

    /**
     * Create an instance of {@link GetPerformanceMetrics }
     * 
     */
    public GetPerformanceMetrics createGetPerformanceMetrics() {
        return new GetPerformanceMetrics();
    }

    /**
     * Create an instance of {@link DeleteConnectedOrderResponse }
     * 
     */
    public DeleteConnectedOrderResponse createDeleteConnectedOrderResponse() {
        return new DeleteConnectedOrderResponse();
    }

    /**
     * Create an instance of {@link ProcessProductionConnectedOrdersAsyncResponse }
     * 
     */
    public ProcessProductionConnectedOrdersAsyncResponse createProcessProductionConnectedOrdersAsyncResponse() {
        return new ProcessProductionConnectedOrdersAsyncResponse();
    }

    /**
     * Create an instance of {@link ProcessResponse }
     * 
     */
    public ProcessResponse createProcessResponse() {
        return new ProcessResponse();
    }

    /**
     * Create an instance of {@link ProcessProductionTransactionDriverFileAsync }
     * 
     */
    public ProcessProductionTransactionDriverFileAsync createProcessProductionTransactionDriverFileAsync() {
        return new ProcessProductionTransactionDriverFileAsync();
    }

    /**
     * Create an instance of {@link DownloadSelectionsFile }
     * 
     */
    public DownloadSelectionsFile createDownloadSelectionsFile() {
        return new DownloadSelectionsFile();
    }

    /**
     * Create an instance of {@link DownloadOrderLogFile }
     * 
     */
    public DownloadOrderLogFile createDownloadOrderLogFile() {
        return new DownloadOrderLogFile();
    }

    /**
     * Create an instance of {@link ProcessTransactionDriverFileAsync }
     * 
     */
    public ProcessTransactionDriverFileAsync createProcessTransactionDriverFileAsync() {
        return new ProcessTransactionDriverFileAsync();
    }

    /**
     * Create an instance of {@link ProcessDataResourceJobBundleResponse }
     * 
     */
    public ProcessDataResourceJobBundleResponse createProcessDataResourceJobBundleResponse() {
        return new ProcessDataResourceJobBundleResponse();
    }

    /**
     * Create an instance of {@link ValidateUploadedFile }
     * 
     */
    public ValidateUploadedFile createValidateUploadedFile() {
        return new ValidateUploadedFile();
    }

    /**
     * Create an instance of {@link DownloadConnectedFontSrcFile }
     * 
     */
    public DownloadConnectedFontSrcFile createDownloadConnectedFontSrcFile() {
        return new DownloadConnectedFontSrcFile();
    }

    /**
     * Create an instance of {@link DownloadResultJobBundle }
     * 
     */
    public DownloadResultJobBundle createDownloadResultJobBundle() {
        return new DownloadResultJobBundle();
    }

    /**
     * Create an instance of {@link DownloadResultJobBundleResponse }
     * 
     */
    public DownloadResultJobBundleResponse createDownloadResultJobBundleResponse() {
        return new DownloadResultJobBundleResponse();
    }

    /**
     * Create an instance of {@link ProcessDataResourceJobBundle }
     * 
     */
    public ProcessDataResourceJobBundle createProcessDataResourceJobBundle() {
        return new ProcessDataResourceJobBundle();
    }

    /**
     * Create an instance of {@link ValidateDownloadedFile }
     * 
     */
    public ValidateDownloadedFile createValidateDownloadedFile() {
        return new ValidateDownloadedFile();
    }

    /**
     * Create an instance of {@link GetServerStatus }
     * 
     */
    public GetServerStatus createGetServerStatus() {
        return new GetServerStatus();
    }

    /**
     * Create an instance of {@link DownloadSelectableImagesFile }
     * 
     */
    public DownloadSelectableImagesFile createDownloadSelectableImagesFile() {
        return new DownloadSelectableImagesFile();
    }

    /**
     * Create an instance of {@link ProcessProductionTransactionDriverFileAsyncResponse }
     * 
     */
    public ProcessProductionTransactionDriverFileAsyncResponse createProcessProductionTransactionDriverFileAsyncResponse() {
        return new ProcessProductionTransactionDriverFileAsyncResponse();
    }

    /**
     * Create an instance of {@link DownloadVariableValuesFile }
     * 
     */
    public DownloadVariableValuesFile createDownloadVariableValuesFile() {
        return new DownloadVariableValuesFile();
    }

    /**
     * Create an instance of {@link DownloadProofLogFile }
     * 
     */
    public DownloadProofLogFile createDownloadProofLogFile() {
        return new DownloadProofLogFile();
    }

    /**
     * Create an instance of {@link UploadJobBundle }
     * 
     */
    public UploadJobBundle createUploadJobBundle() {
        return new UploadJobBundle();
    }

    /**
     * Create an instance of {@link ProcessJobBundleAsyncResponse }
     * 
     */
    public ProcessJobBundleAsyncResponse createProcessJobBundleAsyncResponse() {
        return new ProcessJobBundleAsyncResponse();
    }

    /**
     * Create an instance of {@link CreateConnectedOrder }
     * 
     */
    public CreateConnectedOrder createCreateConnectedOrder() {
        return new CreateConnectedOrder();
    }

    /**
     * Create an instance of {@link ProcessProductionConnectedOrdersAsync }
     * 
     */
    public ProcessProductionConnectedOrdersAsync createProcessProductionConnectedOrdersAsync() {
        return new ProcessProductionConnectedOrdersAsync();
    }

    /**
     * Create an instance of {@link IsInteractiveContentReady }
     * 
     */
    public IsInteractiveContentReady createIsInteractiveContentReady() {
        return new IsInteractiveContentReady();
    }

    /**
     * Create an instance of {@link TransactionResultFileResponse }
     * 
     */
    public TransactionResultFileResponse createTransactionResultFileResponse() {
        return new TransactionResultFileResponse();
    }

    /**
     * Create an instance of {@link DownloadBundleFile }
     * 
     */
    public DownloadBundleFile createDownloadBundleFile() {
        return new DownloadBundleFile();
    }

    /**
     * Create an instance of {@link UploadProcessJobBundleAsyncResponse }
     * 
     */
    public UploadProcessJobBundleAsyncResponse createUploadProcessJobBundleAsyncResponse() {
        return new UploadProcessJobBundleAsyncResponse();
    }

    /**
     * Create an instance of {@link ValidateUploadedFileResponse }
     * 
     */
    public ValidateUploadedFileResponse createValidateUploadedFileResponse() {
        return new ValidateUploadedFileResponse();
    }

    /**
     * Create an instance of {@link DownloadReferenceData }
     * 
     */
    public DownloadReferenceData createDownloadReferenceData() {
        return new DownloadReferenceData();
    }

    /**
     * Create an instance of {@link UploadReferenceData }
     * 
     */
    public UploadReferenceData createUploadReferenceData() {
        return new UploadReferenceData();
    }

    /**
     * Create an instance of {@link DeleteConnectedOrder }
     * 
     */
    public DeleteConnectedOrder createDeleteConnectedOrder() {
        return new DeleteConnectedOrder();
    }

    /**
     * Create an instance of {@link UploadJobBundleResponse }
     * 
     */
    public UploadJobBundleResponse createUploadJobBundleResponse() {
        return new UploadJobBundleResponse();
    }

    /**
     * Create an instance of {@link IsAsyncJobProcessCompleted }
     * 
     */
    public IsAsyncJobProcessCompleted createIsAsyncJobProcessCompleted() {
        return new IsAsyncJobProcessCompleted();
    }

    /**
     * Create an instance of {@link ProcessProductionTransactionDriverFile }
     * 
     */
    public ProcessProductionTransactionDriverFile createProcessProductionTransactionDriverFile() {
        return new ProcessProductionTransactionDriverFile();
    }

    /**
     * Create an instance of {@link UploadReferenceDataResponse }
     * 
     */
    public UploadReferenceDataResponse createUploadReferenceDataResponse() {
        return new UploadReferenceDataResponse();
    }

    /**
     * Create an instance of {@link DownloadRecipientContentFile }
     * 
     */
    public DownloadRecipientContentFile createDownloadRecipientContentFile() {
        return new DownloadRecipientContentFile();
    }

    /**
     * Create an instance of {@link UploadProcessJobBundleAsync }
     * 
     */
    public UploadProcessJobBundleAsync createUploadProcessJobBundleAsync() {
        return new UploadProcessJobBundleAsync();
    }

    /**
     * Create an instance of {@link ServerStatusResponse }
     * 
     */
    public ServerStatusResponse createServerStatusResponse() {
        return new ServerStatusResponse();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link TransactionResultFileResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "urn:dews", name = "transactionResultFileResponse")
    public JAXBElement<TransactionResultFileResponse> createTransactionResultFileResponse(TransactionResultFileResponse value) {
        return new JAXBElement<TransactionResultFileResponse>(_TransactionResultFileResponse_QNAME, TransactionResultFileResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link CreateConnectedOrderResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "urn:dews", name = "createConnectedOrderResponse")
    public JAXBElement<CreateConnectedOrderResponse> createCreateConnectedOrderResponse(CreateConnectedOrderResponse value) {
        return new JAXBElement<CreateConnectedOrderResponse>(_CreateConnectedOrderResponse_QNAME, CreateConnectedOrderResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link UploadJobBundleResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "urn:dews", name = "uploadJobBundleResponse")
    public JAXBElement<UploadJobBundleResponse> createUploadJobBundleResponse(UploadJobBundleResponse value) {
        return new JAXBElement<UploadJobBundleResponse>(_UploadJobBundleResponse_QNAME, UploadJobBundleResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ProcessJobBundleAsyncResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "urn:dews", name = "processJobBundleAsyncResponse")
    public JAXBElement<ProcessJobBundleAsyncResponse> createProcessJobBundleAsyncResponse(ProcessJobBundleAsyncResponse value) {
        return new JAXBElement<ProcessJobBundleAsyncResponse>(_ProcessJobBundleAsyncResponse_QNAME, ProcessJobBundleAsyncResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link UploadProcessJobBundleAsyncResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "urn:dews", name = "uploadProcessJobBundleAsyncResponse")
    public JAXBElement<UploadProcessJobBundleAsyncResponse> createUploadProcessJobBundleAsyncResponse(UploadProcessJobBundleAsyncResponse value) {
        return new JAXBElement<UploadProcessJobBundleAsyncResponse>(_UploadProcessJobBundleAsyncResponse_QNAME, UploadProcessJobBundleAsyncResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ServerStatusResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "urn:dews", name = "serverStatusResponse")
    public JAXBElement<ServerStatusResponse> createServerStatusResponse(ServerStatusResponse value) {
        return new JAXBElement<ServerStatusResponse>(_ServerStatusResponse_QNAME, ServerStatusResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DownloadResultJobBundleResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "urn:dews", name = "downloadResultJobBundleResponse")
    public JAXBElement<DownloadResultJobBundleResponse> createDownloadResultJobBundleResponse(DownloadResultJobBundleResponse value) {
        return new JAXBElement<DownloadResultJobBundleResponse>(_DownloadResultJobBundleResponse_QNAME, DownloadResultJobBundleResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ProcessResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "urn:dews", name = "processResponse")
    public JAXBElement<ProcessResponse> createProcessResponse(ProcessResponse value) {
        return new JAXBElement<ProcessResponse>(_ProcessResponse_QNAME, ProcessResponse.class, null, value);
    }

}
