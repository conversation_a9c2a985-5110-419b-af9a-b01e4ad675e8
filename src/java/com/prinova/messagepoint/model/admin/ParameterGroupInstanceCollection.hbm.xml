<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="com.prinova.messagepoint.model.admin.ParameterGroupInstanceCollection" table="pg_instance_collection">
        <id name="id" column="id">
         	<generator class="native"/>
        </id>

		<property 		name="guid" 					column="guid"						length="255" 	not-null="true" 	unique="true" />                                  
		<property 		name="name" 					column="name" 						length="96" 	not-null="true" />
		<property 		name="shared" 					column="shared" not-null="true" />
		<set name="parameterGroupInstances" inverse="false" cascade="all, delete-orphan">	
			<key column="pg_instance_collection_id"/>
			<one-to-many class="com.prinova.messagepoint.model.admin.ParameterGroupInstance"/>
		</set>

		<set name="parameterGroupTreeNodes">
			<cache usage="read-write" />
			<key column="pgi_collection_id" />
			<one-to-many class="com.prinova.messagepoint.model.admin.ParameterGroupTreeNode"/>
		</set>

		<property name="updated" />
		<property name="updatedBy" column="updated_by_id" />
		<property name="created" />
		<property name="createdBy" column="created_by_id" />

	</class>
	
</hibernate-mapping>
