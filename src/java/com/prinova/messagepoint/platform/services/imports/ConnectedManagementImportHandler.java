package com.prinova.messagepoint.platform.services.imports;

import java.util.*;
import java.util.stream.Collectors;

import com.prinova.messagepoint.controller.communication.connected.ConnectedReferenceDataSourceUtil;
import com.prinova.messagepoint.controller.metadata.MetadataFormItemDefinitionVO;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.communication.CommunicationOrderEntryItemDefinition;
import com.prinova.messagepoint.model.communication.RepeatingDataType;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.font.ParagraphStyle;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.webservice.WebServiceConfiguration;
import com.prinova.messagepoint.platform.services.imports.document.*;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.logging.Log;

public final class ConnectedManagementImportHandler extends MessagepointImportHandler {
    private static final Log log = LogUtil.getLog(ConnectedManagementImportHandler.class);
	private MessagepointImportHandler parent;
    private Long dataResourceRefId;
    private Long zoneMarkerStyleRefId;
    private boolean orderEntryEnabled;
    private int productionTypeId;
    private boolean externalValidationEnabled;
    private boolean appliesTagCloud;
    private boolean appliesCopiesInput;
    private Long multipleRecipientRefId;
    private boolean displayTouchpointThumbnail;
    private boolean resolveVariableValues;
    private boolean suppressNonEditableZones;
    
    private CompisitionResultImportHandler compisitionResultImportHandler;
    private ProductionStatusImportHandler productionStatusImportHandler;
    private DataFeedImportHandler dataFeedImportHandler;
	private NotificationImportHandler notificationImportHandler;

    private boolean communicationAppliesTouchpointSelection = false;
    private List<CommunicationOrderEntryImportHandler> orderEntries;

    private Boolean updateReferenceConnection = false;
    private Long primaryDataVariableRefId = 0L;
    private Long referenceDataVariableRefId = 0L;

	public ConnectedManagementImportHandler() {
		super(ConnectedManagementImportHandler.class);
		
		orderEntries = new ArrayList<>();

        setAttrMethod("ConnectedManagement", "updateReferenceConnection",   "setUpdateReferenceConnection", 	                Boolean.class);
        setAttrMethod("ConnectedManagement", "primaryDataVariableRefId",    "setPrimaryDataVariableRefId", 	        Long.class);
        setAttrMethod("ConnectedManagement", "referenceDataVariableRefId",    "setReferenceDataVariableRefId", 	    Long.class);

		setAttrMethod("DataResource",      "refid",          "setDataResourceRefId",         Long.class);
		setAttrMethod("ZoneMarkerStyle",   "refid", 		 "setZoneMarkerStyleRefId", 	 Long.class);
		setAttrMethod("OrderEntryEnabled", "", 			     "setOrderEntryEnabled", 		 Integer.class);
		
		setAttrMethod("ProductionType",    "refid",          "setProductionTypeRefId",       Integer.class);
		setAttrMethod("ExternalValidationEnabled", "", 		 "setExternalValidationEnabled", Integer.class);
		setAttrMethod("AppliesTagCloud",    "", 		     "setAppliesTagCloud",           Integer.class);
		setAttrMethod("AppliesCopiesInput", "", 		     "setAppliesCopiesInput",        Integer.class);
		
		setAttrMethod("DisplayTouchpointThumbnail", "", 	 "setDisplayTouchpointThumbnail",   Integer.class);
		setAttrMethod("ResolveVariableValues", "", 	 		 "setResolveVariableValues"		,   Integer.class);
		setAttrMethod("SuppressNonEditableZones", "", 	 	 "setSuppressNonEditableZones"	,   Integer.class);
		
		setAttrMethod("MultipleRecipient",  "refid",         "setMultipleRecipientRefId",    Long.class);

        setAttrMethod("VariantSelection",    "", 		     "setAppliesTouchpointSelection",           Integer.class);
	}

	@Override
	public void addChild(MessagepointImportHandler child) {
		if(child instanceof CompisitionResultImportHandler) {
			compisitionResultImportHandler = (CompisitionResultImportHandler) child; 
		} else if(child instanceof ProductionStatusImportHandler) {
			productionStatusImportHandler = (ProductionStatusImportHandler) child; 
		} else if(child instanceof DataFeedImportHandler) {
			dataFeedImportHandler = (DataFeedImportHandler) child; 
		} else if(child instanceof CommunicationOrderEntryImportHandler) {
			orderEntries.add((CommunicationOrderEntryImportHandler) child);
		} else if(child instanceof NotificationImportHandler) {
			notificationImportHandler = (NotificationImportHandler) child;
		}
	}

	@Override
	protected void doneProcessing() {
	}

	public void setParent(MessagepointImportHandler p) {
		parent = p;
	}

	public MessagepointImportHandler getParent() {
		return parent;
	}

    public void setUpdateReferenceConnection(Boolean update) {
        updateReferenceConnection = update;
    }

    public void setPrimaryDataVariableRefId(Long refId) {
        primaryDataVariableRefId = refId;
    }

    public void setReferenceDataVariableRefId(Long refId) {
        referenceDataVariableRefId = refId;
    }

	public void setDataResourceRefId(Long refId) {
		dataResourceRefId = refId;
	}
	
	public void setZoneMarkerStyleRefId(Long refId) {
		zoneMarkerStyleRefId = refId;
	}
	
	public void setOrderEntryEnabled(Integer orderEnabled) {
		orderEntryEnabled = (orderEnabled != null && orderEnabled.intValue() == 1);
	}

	public void setProductionTypeRefId(Integer refId) {
		if(refId != null && refId.intValue() > 0) {
			productionTypeId = refId;
		} else {
			productionTypeId = 0;
		}
	}

	public void setExternalValidationEnabled(Integer externalValidationEnabled) {
		this.externalValidationEnabled = (externalValidationEnabled != null && externalValidationEnabled.intValue() == 1);
	}

	public void setAppliesTagCloud(Integer appliesTagCloud) {
		this.appliesTagCloud = (appliesTagCloud != null && appliesTagCloud.intValue() == 1);
	}

	public void setAppliesCopiesInput(Integer appliesCopiesInput) {
		this.appliesCopiesInput = (appliesCopiesInput != null && appliesCopiesInput.intValue() == 1);
	}

	public void setDisplayTouchpointThumbnail(Integer displayTouchpointThumbnail) {
		this.displayTouchpointThumbnail = (displayTouchpointThumbnail != null && displayTouchpointThumbnail.intValue() == 1);
	}
	
	public void setResolveVariableValues(Integer resolveVariableValues) {
		this.resolveVariableValues = (resolveVariableValues != null && resolveVariableValues.intValue() == 1);
	}

	public void setSuppressNonEditableZones(Integer suppressNonEditableZones) {
		this.suppressNonEditableZones = (suppressNonEditableZones != null && suppressNonEditableZones.intValue() == 1);
	}

	public void setMultipleRecipientRefId(Long refId) {
		multipleRecipientRefId = refId;
	}

    public void setAppliesTouchpointSelection(Integer appliesTouchpointSelection) {
        this.communicationAppliesTouchpointSelection = (appliesTouchpointSelection != null && appliesTouchpointSelection.intValue() == 1);
    }

	public void createConnectedManagementData(Document doc, ExportImportHandler root) {
        Set<DataResourceImportHandler> dataResources = root.getDataResources();
        Set<ParagraphStyleImportHandler> styles = root.getParastyles().getParaStyles();
        Set<UserVariableImportHandler> variables = root.getDataVariables().getVariables();

        DataResource communicationsDataResource = (dataResources == null || dataResourceRefId == null) ? null :
            dataResources.stream()
                .filter(dih -> dih.getId() == dataResourceRefId)
                .map(dih -> dih.getDataResource()).findFirst().orElse(null);

        ParagraphStyle zoneMarkerStyle = (styles == null || zoneMarkerStyleRefId == null) ? null :
            styles.stream()
                .filter(s -> s.getId() == zoneMarkerStyleRefId)
                .map(s -> s.getParagraphStyle()).findFirst().orElse(null);

        DataElementVariable multiRecipientIdentifier = (variables == null || multipleRecipientRefId == null) ? null :
            variables.stream()
                .filter(v -> v.getId() == multipleRecipientRefId)
                .map(v -> v.getDataElementVariable()).findFirst().orElse(null);

        doc.setCommunicationsDataResource(communicationsDataResource);
        doc.setCommunicationZoneMarkerStyle(zoneMarkerStyle);
        doc.setCommunicationAppliesTagCloud(appliesTagCloud);
        doc.setCommunicationAppliesCopiesInput(appliesCopiesInput);
        doc.setCommunicationExternalValidationEnabled(externalValidationEnabled);
        doc.setCommunicationOrderEntryEnabled(orderEntryEnabled);
        doc.setCommunicationProductionTypeId(productionTypeId);
        doc.setCommunicationMultiRecipientIdentifier(multiRecipientIdentifier);
        doc.setCommunicationDisplayTouchpointThumbnail(displayTouchpointThumbnail);
        doc.setCommunicationResolveVariableValues(resolveVariableValues);
        doc.setCommunicationSuppressNonEditableZones(suppressNonEditableZones);
        doc.setCommunicationCompositionResultsWebService(compisitionResultImportHandler == null ? null : compisitionResultImportHandler.getWebServiceConfiguration());
        doc.setCommunicationProductionStatusWebService(productionStatusImportHandler == null ? null : productionStatusImportHandler.getWebServiceConfiguration());
        doc.setCommunicationDataFeedWebService(dataFeedImportHandler == null ? null : dataFeedImportHandler.getWebServiceConfiguration());
        doc.setCommunicationNotificationWebService(notificationImportHandler == null ? null : notificationImportHandler.getWebServiceConfiguration());
        doc.setCommunicationAppliesTouchpointSelection(communicationAppliesTouchpointSelection);

        if (orderEntries != null && !orderEntries.isEmpty()) {
            List<CommunicationOrderEntryItemDefinition> items = new ArrayList<>(doc.getCommunicationOrderEntryItemDefinitions());
            doc.getCommunicationOrderEntryItemDefinitions().clear();
            for (CommunicationOrderEntryItemDefinition item : items) {
                item.delete();
            }
            for (CommunicationOrderEntryImportHandler coeih : orderEntries) {
                coeih.createOrderEntry(doc, root);
            }
        }

        doc.save();
    }

    public void updateReferenceConnection(Document document, ExportImportHandler root) {
        if(document.getDataSourceAssociation() != null) {
            if(updateReferenceConnection != null && updateReferenceConnection.booleanValue()) {
                List<CommunicationOrderEntryItemDefinition> itemDefs = document.getCommunicationOrderEntryItemDefinitionsInOrder();

                try {
                    DataElementVariable primaryDataVariable = null;
                    DataElementVariable referenceDataVariable = null;
                    if(primaryDataVariableRefId != null && primaryDataVariableRefId.longValue() != 0) {
                        UserVariableImportHandler variable = root.getDataVariables().findVariableByRefId(primaryDataVariableRefId);
                        if(variable != null) {
                            primaryDataVariable = variable.getDataElementVariable();
                        }
                        if(primaryDataVariable == null) {
                            log.error("Primary variable not found: refid = " + primaryDataVariableRefId);
                        }
                    }

                    if(referenceDataVariableRefId != null && referenceDataVariableRefId.longValue() != 0) {
                        UserVariableImportHandler variable = root.getDataVariables().findVariableByRefId(referenceDataVariableRefId);
                        if(variable != null) {
                            referenceDataVariable = variable.getDataElementVariable();
                        }
                        if(referenceDataVariable == null) {
                            log.error("Reference variable not found: refid = " + referenceDataVariableRefId);
                        }
                    }

                    ConnectedReferenceDataSourceUtil.CreateDataSource(document, itemDefs.stream().sequential().map(itemDef -> {
                            try {
                                MetadataFormItemDefinitionVO newObject = new MetadataFormItemDefinitionVO(itemDef);
                                DataElementVariable var = itemDef.getDataElementVariable();
                                if (var != null) {
                                    AbstractDataElement ade = AbstractDataElement.findDataElementByVariableId(var.getId(), document);
                                    if (ade != null){
                                        newObject.setDataElementTypeId(ade.getDataSubtypeId());
                                        newObject.setDataElementInputTypeFormat(ade.getExternalFormatText());
                                    }
                                }
                                return newObject;
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        }).collect(Collectors.toList()),
                        primaryDataVariable == null ? 0L : primaryDataVariable.getId(),
                        referenceDataVariable == null ? 0L : referenceDataVariable.getId());
                } catch (Exception ex) {
                    throw ex;
                }
            } else {
                for(ReferenceConnection referenceConnection : document.getDataSourceAssociation().getReferenceConnections()) {
                    if(referenceConnection.getReferenceDataSource().getSourceType().getId() == SourceType.TYPE_REFERENCE_CONNECTED) {
                        referenceConnection.setConnectorParameter(null);
                        referenceConnection.setPrimaryVariable(null);
                        referenceConnection.setPrimaryCompoundKey(null);
                        referenceConnection.setReferenceVariable(null);
                        referenceConnection.setReferenceCompoundKey(null);
                        referenceConnection.save();
                    }
                }
            }
        }

        document.save();
	}
	
	public static abstract class WebServiceContainerImportHandler extends MessagepointImportHandler {
		private WebServiceImportHandler webServiceImportHandler;
		
		protected WebServiceContainerImportHandler(Class<? extends MessagepointImportHandler> subclazz) {
			super(subclazz);
			// TODO Auto-generated constructor stub
		}

		@Override
		public void addChild(MessagepointImportHandler child) {
			// TODO Auto-generated method stub
			if(child instanceof WebServiceImportHandler) {
				if(webServiceImportHandler == null) {
					webServiceImportHandler = (WebServiceImportHandler) child;
				}
			}
		}
		
		public WebServiceConfiguration getWebServiceConfiguration() {
			return  webServiceImportHandler == null ? null : webServiceImportHandler.getWebServiceConfiguration();
		}
		
	}
	
	public static final class CompisitionResultImportHandler extends WebServiceContainerImportHandler {
		public CompisitionResultImportHandler() {
			super(CompisitionResultImportHandler.class);
			// TODO Auto-generated constructor stub
		}
	}
	
	public static final class ProductionStatusImportHandler extends WebServiceContainerImportHandler {
		public ProductionStatusImportHandler() {
			super(ProductionStatusImportHandler.class);
			// TODO Auto-generated constructor stub
		}
	}
	public static final class NotificationImportHandler extends WebServiceContainerImportHandler {
		public NotificationImportHandler() {
			super(NotificationImportHandler.class);
			// TODO Auto-generated constructor stub
		}
	}

	public static final class DataFeedImportHandler extends WebServiceContainerImportHandler {
		public DataFeedImportHandler() {
			super(DataFeedImportHandler.class);
			// TODO Auto-generated constructor stub
		}
	}

	public static final class CommunicationOrderEntryImportHandler extends MessagepointImportHandler {
		private long id;
		private String description;
		private String name;
		private int    order;
		private int    typeId;
		private boolean isPrimaryDriverEntry;
		private boolean isIndicatorEntry;
		private int     dataPrivacyTypeId;
		private boolean mandatory;
		private boolean lockedForEdit;
		private int refreshOnValueChange;
		private Long dataElementRefId;
        private Long dataElementVariableRefId;
		private String menuValueItems;
		private String primaryConnector;
		private Integer parentItemOrder;
		private String displayTriggerValues;
		private String regexValidation;
		private String criteriaOperator;
		private String criteriaTriggerValuesJson;
		private int fieldSizeTypeId;
		private Integer fieldMaxLength;
		private Integer inputValidationTypeId;
		private String defaultInputValue;
		private boolean defaultToToday;
		private boolean uniqueValue;
		private Integer repeatingDataTypeId;
		
		private MessagepointImportHandler parent;
		
		private WebServiceImportHandler webServiceImportHandler;
		private ConnectorElementMapImportHandler connectorElementMapImportHandler;

        private Set<Long> appliedTouchpointSelectionIds = new HashSet<>();

		public CommunicationOrderEntryImportHandler() {
			super(CommunicationOrderEntryImportHandler.class);

			setAttrMethod("OrderEntry", "id",                   "setId",                       Long.class);
			setAttrMethod("OrderEntry", "guid",                 "setGuid",                     String.class);
			setAttrMethod("OrderEntry", "order",                "setOrder",                    Integer.class);
			setAttrMethod("OrderEntry", "type",                 "setTypeId",                   Integer.class);
			setAttrMethod("OrderEntry", "mandatory",            "setMandatory",                Integer.class);
			setAttrMethod("OrderEntry", "lockedForEdit",        "setLockedForEdit",            Integer.class);
			setAttrMethod("OrderEntry", "isprimary",            "setIsPrimaryDriverEntry",     Integer.class);
			setAttrMethod("OrderEntry", "isindicator",          "setIsIndicatorEntry",         Integer.class);
			setAttrMethod("OrderEntry", "dataprivacytypeid",    "setDataPrivacyTypeId",        Integer.class);
			setAttrMethod("OrderEntry", "refreshonvaluechange", "setRefreshOnValueChange",     Integer.class);
			setAttrMethod("OrderEntry", "dataelementrid",       "setDataElementRefId",         Long.class);
            setAttrMethod("OrderEntry", "dataElementVariableRid","setDataElementVariableRefId",         Long.class);
			setAttrMethod("OrderEntry", "parentitemorder",      "setParentItemOrder",          Integer.class);
			setAttrMethod("OrderEntry", "fieldsizetype",        "setFieldSizeTypeId",          Integer.class);
			setAttrMethod("OrderEntry", "fieldmaxlength",       "setFieldMaxLength",           Integer.class);
			setAttrMethod("OrderEntry", "inputvalidationtype",  "setInputValidationTypeId",    Integer.class);
			setAttrMethod("OrderEntry", "defaulttotoday",       "setDefaultToToday",           Integer.class);
			setAttrMethod("OrderEntry", "uniquevalue",          "setUniqueValue",              Integer.class);
			setAttrMethod("OrderEntry", "repeatingdatatypeid",  "setRepeatingDataTypeId",      Integer.class);

			setAttrMethod("Description",            "", "setDescription",                 String.class);
			setAttrMethod("Name",                   "", "setName",                        String.class);
			setAttrMethod("MenuValueItems",         "", "setMenuValueItems",              String.class);
			setAttrMethod("PrimaryConnector",       "", "setPrimaryConnector",            String.class);
			setAttrMethod("DisplayTriggerValues",   "", "setDisplayTriggerValues",        String.class);
			setAttrMethod("CriteriaTriggerValuesJson",   "", "setCriteriaTriggerValuesJson",        String.class);
			setAttrMethod("RegexValidation",        "", "setRegexValidation",             String.class);
			setAttrMethod("CriteriaOperator",       "","setCriteriaOperator",             String.class);
			setAttrMethod("DefaultInputValue",      "", "setDefaultInputValue",           String.class);

            setAttrMethod("ApplicableSelection", "refid", "setApplicableSelectionRefId", Long.class);
		}

		@Override
		public void addChild(MessagepointImportHandler child) {
			if(child instanceof WebServiceImportHandler) {
				setWebServiceImportHandler((WebServiceImportHandler) child);
			}
			if(child instanceof ConnectorElementMapImportHandler) {
				setConnectorElementMapImportHandler((ConnectorElementMapImportHandler) child);
			}
		}

		@Override
		protected void doneProcessing() {
		}

		public void setParent(MessagepointImportHandler p) {
			parent = p;
		}

		public MessagepointImportHandler getParent() {
			return parent;
		}

		public long getId() {
			return id;
		}

		public void setId(Long id) {
			this.id = id;
		}

		public void setOrder(Integer order) {
			this.order = order;
		}
		
		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}
		
		public void setDescription(String description) {
			this.description = description;
		}
		
		public void setTypeId(Integer typeId) {
			this.typeId = typeId;
		}
		
		public void setIsPrimaryDriverEntry(Integer isPrimaryDriverEntry) {
			this.isPrimaryDriverEntry = (isPrimaryDriverEntry != null && isPrimaryDriverEntry.intValue() != 0);
		}

		public void setIsIndicatorEntry(Integer isIndicatorEntry) {
			this.isIndicatorEntry = (isIndicatorEntry != null && isIndicatorEntry.intValue() != 0);
		}

		public void setRepeatingDataTypeId(Integer repeatingDataTypeId) {
			this.repeatingDataTypeId = repeatingDataTypeId;
		}
		public void setDataPrivacyTypeId(Integer dataPrivacyTypeId) {
			this.dataPrivacyTypeId = dataPrivacyTypeId;
		}
		
		public void setMandatory(Integer mandatory) {
			this.mandatory = (mandatory != null && mandatory.intValue() != 0);
		}

		public void setLockedForEdit(Integer lockedForEdit) {
			this.lockedForEdit = (lockedForEdit != null && lockedForEdit.intValue() != 0);
		}
		
		public void setRefreshOnValueChange(Integer refreshOnValueChange) {
			this.refreshOnValueChange = (refreshOnValueChange != null && refreshOnValueChange.intValue() != 0) ? refreshOnValueChange.intValue() : 1;
		}
		
		public void setApplicableSelectionRefId(Long refId) {
            appliedTouchpointSelectionIds.add(refId);
		}
		
		public void setParentItemOrder(Integer parentItemOrder) {
			if(parentItemOrder != null && parentItemOrder.intValue() > 0) {
				this.parentItemOrder = parentItemOrder;
			} else {
				this.parentItemOrder = null;
			}
		}
		
		public void setFieldSizeTypeId(Integer sizeTypeId) {
			this.fieldSizeTypeId = sizeTypeId;
		}
		
		public void setFieldMaxLength(Integer maxLength) {
			this.fieldMaxLength = maxLength;
		}
		
		public void setInputValidationTypeId(Integer inputValidationTypeId) {
			this.inputValidationTypeId = inputValidationTypeId;
		}
		
		public void setDefaultToToday(Integer defaultToToday) {
			this.defaultToToday = (defaultToToday != null && defaultToToday.intValue() != 0);
		}
		public void setUniqueValue(Integer uniqueValue) {
			this.uniqueValue = (uniqueValue != null && uniqueValue.intValue() != 0);
		}
		
		public void setMenuValueItems(String menuValueItems) {
			this.menuValueItems = menuValueItems;
		}

		public void setPrimaryConnector(String primaryConnector) {
			this.primaryConnector = primaryConnector;
		}

		public void setDisplayTriggerValues(String displayTriggerValues) {
			this.displayTriggerValues = displayTriggerValues;
		}

		public void setRegexValidation(String regexValidation) {
			this.regexValidation = regexValidation;
		}
		public void setCriteriaOperator(String criteriaOperator) {
			this.criteriaOperator = criteriaOperator;
		}
		public void setCriteriaTriggerValuesJson(String criteriaTriggerValuesJson) {
			this.criteriaTriggerValuesJson = criteriaTriggerValuesJson;
		}

		public void setDefaultInputValue(String defaultInputValue) {
			this.defaultInputValue = defaultInputValue;
		}

		public WebServiceImportHandler getWebServiceImportHandler() {
			return webServiceImportHandler;
		}

		public void setWebServiceImportHandler(WebServiceImportHandler webServiceImportHandler) {
			this.webServiceImportHandler = webServiceImportHandler;
		}

		public ConnectorElementMapImportHandler getConnectorElementMapImportHandler() {
			return connectorElementMapImportHandler;
		}

		public void setConnectorElementMapImportHandler(ConnectorElementMapImportHandler connectorElementMapImportHandler) {
			this.connectorElementMapImportHandler = connectorElementMapImportHandler;
		}

		private AbstractDataElement lookForDataElement(ExportImportHandler root, long refId) {
			AbstractDataElement dataElement = null;
			
			DataCollectionImportHandler dataCollection = root.getDataCollection();
			if(dataCollection != null) {
				Set<DataSourceImportHandler> dataSources = dataCollection.getDataSources();
				if(dataSources != null) {
					dataElement = dataSources.stream()
							.filter(ds->ds.getDataElements().containsKey(refId))
							.map(ds->ds.getDataElements().get(refId)).findFirst().orElse(null);
				}
			}
			
			return dataElement;
		}

        private DataElementVariable lookForDataElementVariable(ExportImportHandler root, Long dataElementRefId, Long dataElementVariableRefId) {
            UserVariableImportHandler dataElementVariableHandler = dataElementVariableRefId == null ? null : root.getDataVariables().findVariableByRefId(dataElementVariableRefId);
            DataElementVariable dataElementVariable = null;
            if(dataElementVariableHandler != null) {
                dataElementVariable = dataElementVariableHandler.getDataElementVariable();
            }

            if(dataElementVariable == null && dataElementRefId != null) {
                dataElementVariable = root.getDataElementRefId2VariableMap().get(dataElementRefId);
                if(dataElementVariable == null) {
                    AbstractDataElement dataElement = lookForDataElement(root, dataElementRefId);
                    if(dataElement != null) {
                        DataSource dataSource = dataElement.getDataSource();

                        Set<UserVariableImportHandler> variables = root.getDataVariables().getVariables()
                            .stream()
                            .filter(v->
                                v.getDataElementVariable().getTypeId() == DataElementVariableType.ID_DRIVER
                                    && v.getDataElementVariable().isReferenceVariable()
                                    && v.getDataElementVariable() != null
                                    && v.getDataElementVariable().getDataElementMap().entrySet().stream().anyMatch(
                                        e->e.getKey().longValue() == dataSource.getId()
                                            && e.getValue().getDataElement() != null
                                            && e.getValue().getDataElement().getId() == dataElement.getId()))
                            .collect(Collectors.toSet());

                        if(!variables.isEmpty()) {
                            dataElementVariable = variables.stream()
                                .map(vh->vh.getDataElementVariable())
                                .filter(v->v.isEnabledForConnectedInterview()).findFirst().orElse(null);

                            if(dataElementVariable == null) {
                                dataElementVariable = variables.stream()
                                    .map(vh->vh.getDataElementVariable())
                                    .findFirst().orElse(null);
                            }
                        }
/*
                        if(dataElementVariable == null) {
                            String name = dataElement.getName();

                            if (!name.startsWith("ImpDE-")) {
                                name = "ImpDE-" + name;
                            }

                            String newName = ApplicationUtil.getUnusedName(name, DataElementVariable.class);
                            int dataElementVariableType = DataElementVariableType.ID_DRIVER;
                            dataElementVariable = new DataElementVariable();
                            dataElementVariable.setName(newName);
                            dataElementVariable.setFriendlyName(newName);
                            dataElementVariable.setTypeId(dataElementVariableType);
                            dataElementVariable.setIsReferenceVariable(dataSource.isReferenceDataSource());
                            dataElementVariable.setEnabledForConnectedInterview(true);
                            VariableDataElementMap vdem = new VariableDataElementMap();
                            vdem.setDataElement(dataElement);
                            dataElementVariable.getDataElementMap().put(dataSource.getId(), vdem);
                            dataElementVariable.save();
                        }
*/
                        if(dataElementVariable != null) {
                            root.getDataElementRefId2VariableMap().put(dataElementRefId, dataElementVariable);
                        }
                    }
                }
            }

            return dataElementVariable;
        }

        public void setDataElementRefId(Long refId) {
            this.dataElementRefId = refId;
        }

        public void setDataElementVariableRefId(Long refId) {
            this.dataElementVariableRefId = refId;
        }

		public void createOrderEntry(Document doc, ExportImportHandler root) {
	    	boolean isNew = false;
	    	
			Date now = new Date(System.currentTimeMillis());
			User currentUser = UserUtil.getPrincipalUser();
			
			CommunicationOrderEntryItemDefinition itemDef = doc.getCommunicationOrderEntryItemDefinitions()
                    .stream()
                    .filter(codi->codi.getGuid().equals(getGuid()))
                    .findFirst()
                    .orElse(null)
                    ;

			if(itemDef == null) {
                itemDef = new CommunicationOrderEntryItemDefinition();
                itemDef.setGuid(getGuid());
                itemDef.setCreated(now);
                itemDef.setCreatedBy(currentUser.getId());
                doc.getCommunicationOrderEntryItemDefinitions().add(itemDef);
            }
			itemDef.setDescription(description);
			itemDef.setName(name == null ? "" : name);
			itemDef.setOrder(order);
			itemDef.setTypeId(typeId);
			itemDef.setIsManadatory(mandatory);
			itemDef.setIsLockedForEdit(lockedForEdit);
			itemDef.setIsPrimaryDriverEntry(isPrimaryDriverEntry);
			itemDef.setIsIndicatorEntry(isIndicatorEntry);
			itemDef.setDataPrivacyTypeId(dataPrivacyTypeId);
			itemDef.setWebServiceRefreshTypeId(refreshOnValueChange);
			itemDef.setMenuValueItems(menuValueItems);
			itemDef.setPrimaryConnector(primaryConnector);
			itemDef.setParentItemOrder(parentItemOrder);
			itemDef.setDisplayTriggerValues(displayTriggerValues);
			itemDef.setRegexValidation(regexValidation);
			itemDef.setCriteriaOperator(criteriaOperator);
			itemDef.setCriteriaTriggerValuesJson(criteriaTriggerValuesJson);
			itemDef.setFieldSizeTypeId((int) fieldSizeTypeId);
			itemDef.setFieldMaxLength(fieldMaxLength);
			itemDef.setInputValidationTypeId(inputValidationTypeId);
			itemDef.setDefaultInputValue(defaultInputValue);
			itemDef.setDefaultDateValueToTodaysDate(defaultToToday);
			itemDef.setUniqueValue(uniqueValue);
			itemDef.setRepeatingDataTypeId(repeatingDataTypeId == null ? RepeatingDataType.ID_NONE: repeatingDataTypeId);

            DataElementVariable dataElementVariable = lookForDataElementVariable(root, dataElementRefId, dataElementVariableRefId);

            if(dataElementVariable != null && !dataElementVariable.isEnabledForConnectedInterview()) {
                dataElementVariable.setEnabledForConnectedInterview(true);
            }

            itemDef.setDataElementVariable(dataElementVariable);

			itemDef.setDocument(doc);
			itemDef.setAutoGenerateOnImport(true);
			itemDef.setUpdated(now);
			itemDef.setUpdatedBy(currentUser.getId());
			
			if(connectorElementMapImportHandler != null) {
				List<ConnectorElementMapImportHandler.ItemImportHandler> items = connectorElementMapImportHandler.getItems();
				if(items!=null && !items.isEmpty()) {
					if(itemDef.getConnectorVariableMap() == null) {
						itemDef.setConnectorVariableMap(new HashMap<>());
					}
					itemDef.getConnectorVariableMap().clear();
					for(ConnectorElementMapImportHandler.ItemImportHandler iih : items) {
						String key = iih.getKey();
                        Long dataElementRefId = iih.getDataElementRefId();
						Long dataElementVariablerefId = iih.getDataElementVariableRefId();
						DataElementVariable mappedDataElementVariable = lookForDataElementVariable(root, dataElementRefId, dataElementVariablerefId);
						if(mappedDataElementVariable != null) {
                            if(!mappedDataElementVariable.isEnabledForConnectedInterview()) {
                                mappedDataElementVariable.setEnabledForConnectedInterview(true);
                            }
							itemDef.getConnectorVariableMap().put(key, mappedDataElementVariable);
						}
					}
				}
			}

            if(appliedTouchpointSelectionIds != null && ! appliedTouchpointSelectionIds.isEmpty()) {
                TPSelectionImportHandler tpRootSelectionHandler = root.getTouchpoint().getSelection();
                if(tpRootSelectionHandler != null) {
                    for(Long selectionRefId : appliedTouchpointSelectionIds) {
                        TPSelectionImportHandler tpSelectionHandler = tpRootSelectionHandler.findSelection(selectionRefId);
                        if(tpSelectionHandler != null) {
                            tpSelectionHandler.addOrderApplied(itemDef.getOrder());
                        }
                    }
                }
            }

			itemDef.save();
		}
		
		public static final class ConnectorElementMapImportHandler extends MessagepointImportHandler {
			private List<ItemImportHandler> items;
			
			public ConnectorElementMapImportHandler() {
				super(ConnectorElementMapImportHandler.class);
				// TODO Auto-generated constructor stub
				items = new ArrayList<>();
			}

			@Override
			public void addChild(MessagepointImportHandler child) {
				// TODO Auto-generated method stub
				if(child instanceof ItemImportHandler) {
					items.add((ItemImportHandler) child);
				}
			}

			public List<ItemImportHandler> getItems() {
				return items;
			}

			public static final class ItemImportHandler extends MessagepointImportHandler {
				private String key;
                private Long dataElementRefId;
				private Long dataElementVariableRefId;
				
				public ItemImportHandler() {
					super(ItemImportHandler.class);
					// TODO Auto-generated constructor stub
					setAttrMethod("Key",                    "",       "setKey",                        String.class);
                    setAttrMethod("DataElement",        "refid",  "setDataElementRefId",   Long.class);
					setAttrMethod("DataElementVariable",    "refid",  "setDataElementVariableRefId",   Long.class);

				}

				@Override
				public void addChild(MessagepointImportHandler child) {
					// TODO Auto-generated method stub

				}
				
				public void setKey(String key) {
					this.key = key;
				}

                public String getKey() {
                    return key;
                }

                public void setDataElementRefId(Long refId) {
                    this.dataElementRefId = refId;
                }

                public long getDataElementRefId() {
                    return dataElementRefId;
                }

                public void setDataElementVariableRefId(Long refId) {
					this.dataElementVariableRefId = refId;
				}

				public long getDataElementVariableRefId() {
					return dataElementVariableRefId;
				}

			}
		}
	}
}
