#!/usr/bin/env python3
"""
Lightweight Messagepoint Codebase Indexer
Memory-efficient version that processes files one at a time
"""

import os
import sys
import yaml
import json
import time
import hashlib
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
import requests
import weaviate
import weaviate.classes as wvc
from datetime import datetime

class LightweightIndexer:
    def __init__(self, config_path: str = None, openai_api_key: str = None):
        """Initialize lightweight indexer"""
        if config_path is None:
            config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'weaviate_config.yaml')
        
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        self.openai_api_key = openai_api_key or "********************************************************************************************************************************************************************"
        
        # Initialize Weaviate client
        self.client = weaviate.connect_to_custom(
            http_host=self.config['weaviate']['host'],
            http_port=self.config['weaviate']['port'],
            http_secure=False,
            grpc_host=self.config['weaviate']['host'],
            grpc_port=50051,
            grpc_secure=False,
            auth_credentials=wvc.init.Auth.api_key(self.config['weaviate']['api_key']),
            skip_init_checks=True
        )
        
        self.codebase_root = self._find_codebase_root()
        
        # Performance tracking
        self.total_files = 0
        self.total_chunks = 0
        self.total_cost = 0.0
        
    def _find_codebase_root(self) -> str:
        """Find the root of the Messagepoint codebase"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        while current_dir != '/':
            if os.path.exists(os.path.join(current_dir, 'src', 'java')):
                return current_dir
            current_dir = os.path.dirname(current_dir)
        
        return os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
    
    def get_embedding(self, text: str) -> List[float]:
        """Get embedding with minimal memory usage"""
        url = "https://api.openai.com/v1/embeddings"
        headers = {
            "Authorization": f"Bearer {self.openai_api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "input": text[:8000],  # Limit input size
            "model": "text-embedding-3-small"
        }
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            tokens = result['usage']['total_tokens']
            cost = tokens * 0.0001 / 1000
            self.total_cost += cost
            return result['data'][0]['embedding']
        else:
            raise Exception(f"API error: {response.status_code}")
    
    def should_index_file(self, file_path: str) -> bool:
        """Check if file should be indexed"""
        file_ext = Path(file_path).suffix
        
        if file_ext not in self.config['indexing']['file_extensions']:
            return False
        
        for exclude_dir in self.config['indexing']['exclude_dirs']:
            if exclude_dir in file_path:
                return False
        
        return True
    
    def classify_service_generation(self, content: str, file_path: str) -> str:
        """Simple classification"""
        content_lower = content.lower()
        file_path_lower = file_path.lower()
        
        if any(p.lower() in content_lower for p in ['ResponseDto', '@RestController']):
            return "clean"
        elif any(p.lower() in file_path_lower for p in ['legacy', 'old']):
            return "legacy"
        else:
            return "messy"
    
    def extract_business_domain(self, content: str, file_path: str) -> str:
        """Extract business domain"""
        content_lower = content.lower()
        file_path_lower = file_path.lower()
        
        domains = ['variable', 'user', 'touchpoint', 'task', 'company']
        for domain in domains:
            if domain in content_lower or domain in file_path_lower:
                return domain
        return "unknown"
    
    def process_single_file(self, file_path: str) -> int:
        """Process a single file with minimal memory usage"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            return 0
        
        # Get relative path
        rel_path = os.path.relpath(file_path, self.codebase_root)
        
        # Simple chunking - just split by lines
        lines = content.split('\n')
        chunk_size = 100  # Smaller chunks
        chunks_indexed = 0
        
        for i in range(0, len(lines), chunk_size):
            chunk_lines = lines[i:i + chunk_size]
            chunk_content = '\n'.join(chunk_lines)
            
            if not chunk_content.strip():
                continue
            
            try:
                # Get embedding
                embedding = self.get_embedding(chunk_content)
                
                # Prepare minimal data
                data_object = {
                    "content": chunk_content,
                    "file_path": rel_path,
                    "service_generation": self.classify_service_generation(chunk_content, rel_path),
                    "business_domain": self.extract_business_domain(chunk_content, rel_path),
                    "file_type": Path(file_path).suffix,
                    "chunk_hash": hashlib.md5(chunk_content.encode()).hexdigest()
                }
                
                # Add to Weaviate
                collection = self.client.collections.get("CodeChunk")
                collection.data.insert(
                    properties=data_object,
                    vector=embedding
                )
                
                chunks_indexed += 1
                
                # Rate limiting and memory cleanup
                time.sleep(0.5)  # Slower to reduce memory pressure
                
                if chunks_indexed % 10 == 0:
                    import gc
                    gc.collect()
                    print(f"  📦 {chunks_indexed} chunks indexed from {rel_path}")
                
            except Exception as e:
                print(f"❌ Error indexing chunk from {rel_path}: {e}")
                continue
        
        return chunks_indexed
    
    def create_simple_schema(self):
        """Create simplified schema"""
        try:
            self.client.collections.delete("CodeChunk")
            print("🗑️  Deleted existing collection")
        except:
            pass
        
        self.client.collections.create(
            name="CodeChunk",
            properties=[
                wvc.config.Property(name="content", data_type=wvc.config.DataType.TEXT),
                wvc.config.Property(name="file_path", data_type=wvc.config.DataType.TEXT),
                wvc.config.Property(name="service_generation", data_type=wvc.config.DataType.TEXT),
                wvc.config.Property(name="business_domain", data_type=wvc.config.DataType.TEXT),
                wvc.config.Property(name="file_type", data_type=wvc.config.DataType.TEXT),
                wvc.config.Property(name="chunk_hash", data_type=wvc.config.DataType.TEXT)
            ],
            vectorizer_config=wvc.config.Configure.Vectorizer.none()
        )
        print("✅ Created simplified schema")
    
    def index_incrementally(self, max_files: int = 50):
        """Index files incrementally to avoid memory issues"""
        print(f"🚀 Starting lightweight indexing (max {max_files} files)")
        
        self.create_simple_schema()
        
        start_time = time.time()
        files_processed = 0
        
        for root, dirs, files in os.walk(self.codebase_root):
            # Filter directories
            dirs[:] = [d for d in dirs if d not in self.config['indexing']['exclude_dirs']]
            
            for file in files:
                if files_processed >= max_files:
                    print(f"🛑 Reached max files limit ({max_files})")
                    break
                    
                file_path = os.path.join(root, file)
                
                if self.should_index_file(file_path):
                    print(f"📄 Processing: {os.path.relpath(file_path, self.codebase_root)}")
                    chunks = self.process_single_file(file_path)
                    
                    self.total_files += 1
                    self.total_chunks += chunks
                    files_processed += 1
                    
                    # Progress update
                    elapsed = time.time() - start_time
                    print(f"📊 Progress: {self.total_files} files, {self.total_chunks} chunks, ${self.total_cost:.4f} cost ({elapsed:.1f}s)")
                    
            if files_processed >= max_files:
                break
        
        elapsed = time.time() - start_time
        print(f"\n✅ Lightweight indexing complete!")
        print(f"📊 Files: {self.total_files}, Chunks: {self.total_chunks}")
        print(f"💰 Cost: ${self.total_cost:.4f}")
        print(f"⏱️  Time: {elapsed:.1f}s")
        
        self.client.close()

def main():
    indexer = LightweightIndexer()
    indexer.index_incrementally(max_files=20)  # Start with just 20 files

if __name__ == "__main__":
    main()
