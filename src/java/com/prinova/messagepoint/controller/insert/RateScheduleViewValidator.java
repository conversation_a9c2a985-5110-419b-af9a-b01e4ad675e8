package com.prinova.messagepoint.controller.insert;

import java.util.List;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.model.insert.RateSchedule;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class RateScheduleViewValidator extends MessagepointInputValidator {
	
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		RateScheduleViewWrapper command = (RateScheduleViewWrapper) commandObj;
		
		int action = -1; 
		if (command.getAction() != null && !command.getAction().trim().isEmpty()) {
			action = Integer.valueOf(command.getAction()).intValue();
		}
	
		RateSchedule rateSchedule = command.getRateSchedule();

		if (action == RateScheduleViewController.ACTION_DISCARD) {
			List<Document> documents = rateSchedule.getActivelyUsingDocuments();
			if (documents != null && !documents.isEmpty()) {
				String documentNames = InsertManagementValidationUtil.getDocumentNames(documents);
				errors.reject("error.rate.sheet.in.use.by.document", new String[] {documentNames}, "");
			}
			List<InsertSchedule> insertSchedules = rateSchedule.getUsingInsertSchedules();
			if (insertSchedules != null && !insertSchedules.isEmpty()) {
				String insertScheduleNames = InsertManagementValidationUtil.getInsertScheduleNames(insertSchedules);
				errors.reject("error.rate.sheet.in.use.by.insert.schedule", new String[] {insertScheduleNames}, "");
			}
		} else if (action == RateScheduleListController.ACTION_DISCONTINUE) {
			if (command.getRateSchedule().getStartDate().after(DateUtil.yesterdayWithZeroTime())) {
				errors.reject("error.rate.sheet.discontinue.start.date.in.future");
			}
			List<Document> documents = Document.findByRateScheduleCollection(rateSchedule.getRateScheduleCollection());
			if (documents != null && !documents.isEmpty()) {
				String documentNames = InsertManagementValidationUtil.getDocumentNames(documents);
				errors.reject("error.rate.sheet.in.use.by.document", new String[] {documentNames}, "");
			}
			List<InsertSchedule> insertSchedulesBeforeDiscontinue = rateSchedule.getUsingAsDefaultInsertSchedules();
			if (insertSchedulesBeforeDiscontinue != null && !insertSchedulesBeforeDiscontinue.isEmpty()) {
				rateSchedule.setEndDate(DateUtil.yesterdayWithZeroTime());
				List<InsertSchedule> insertSchedulesAfterDiscontinue = rateSchedule.getUsingInsertSchedules();
				if (insertSchedulesAfterDiscontinue == null || insertSchedulesAfterDiscontinue.size() != insertSchedulesBeforeDiscontinue.size()) {
					insertSchedulesBeforeDiscontinue.removeAll(insertSchedulesAfterDiscontinue);  
					String insertScheduleNames = InsertManagementValidationUtil.getInsertScheduleNames(insertSchedulesBeforeDiscontinue);
					errors.reject("error.rate.sheet.discontinue.in.use.by.insert.schedule", new String[] {insertScheduleNames}, "");
				}
				rateSchedule.setEndDate(null);
			}
		}
	}
}
