# 🛠️ REST API Implementation Guide

> **Purpose**: Practical guide for implementing REST APIs that meet Messagepoint standards, with code examples and best practices.

## 📁 FILE ORGANIZATION & PACKAGE STRUCTURE

### **Existing API Consultation Guidelines**:

#### **MANDATORY: Consult Existing APIs Before Generation**
Before generating any new API, analyze existing APIs in the codebase for established patterns:

**Reference APIs to Consult**:
```java
// Primary Reference APIs (Well-established patterns)
src/java/com/prinova/messagepoint/platform/mprest/Variables.java
src/java/com/prinova/messagepoint/platform/mprest/DataSources.java

// Additional Reference APIs
src/java/com/prinova/messagepoint/platform/mprest/BackgroundTasks.java
src/java/com/prinova/messagepoint/platform/mprest/TouchPoints.java
```

**Analysis Checklist**:
- ✅ **Class naming patterns** (Variables.java, DataSources.java - note: NOT plural)
- ✅ **URL patterns** (`@RequestMapping("/variables")`, `@RequestMapping("/data-sources")`)
- ✅ **Security annotations** (`@PreAuthorize` patterns)
- ✅ **Method signatures** (parameter patterns, return types)
- ✅ **Response patterns** (ResponseDto usage, error handling)
- ✅ **Documentation patterns** (`@Tag`, `@Operation`, `@Parameter`)

### **Class Naming Conventions** (Based on Existing APIs):

#### **REST Controllers** (Follow existing pattern - NOT plural):
```
Pattern: {EntityName}.java (singular, matching existing APIs)
Existing Examples:
- Variables.java (NOT VariablesController.java)
- DataSources.java (NOT DataSourcesController.java)
- BackgroundTasks.java (NOT BackgroundTasksController.java)

New Pattern:
- TextStyle entity → TextStyles.java (following Variables pattern)
- Document entity → Documents.java
- Zone entity → Zones.java
```

#### **DTOs** (Singular entity names):
```
Pattern: {EntityName}Dto.java
Examples:
- TextStyle entity → TextStyleDto.java
- Document entity → DocumentDto.java
- Zone entity → ZoneDto.java
```

#### **Integration Tests** (Follow existing pattern):
```
Pattern: {EntityName}ControllerIntegrationTest.java (matching existing APIs)
Existing Examples:
- Variables.java → VariablesControllerIntegrationTest.java
- DataSources.java → DataSourcesControllerIntegrationTest.java

New Pattern:
- TextStyles.java → TextStylesControllerIntegrationTest.java
- Documents.java → DocumentsControllerIntegrationTest.java
```

#### **Test Factories** (Singular entity names):
```
Pattern: {EntityName}TestFactory.java
Examples:
- TextStyle entity → TextStyleTestFactory.java
- Document entity → DocumentTestFactory.java
```

### **URL Patterns** (Based on Existing APIs):

#### **Base URL Patterns** (Follow existing conventions):
```java
// Existing API Patterns
@RequestMapping("/variables")        // Variables.java
@RequestMapping("/data-sources")     // DataSources.java
@RequestMapping("/background-tasks") // BackgroundTasks.java

// New API Pattern
@RequestMapping("/text-styles")      // TextStyles.java (kebab-case, plural)
@RequestMapping("/documents")        // Documents.java
@RequestMapping("/zones")           // Zones.java
```

**Note**: The `/mp/rest` prefix is handled at servlet level, not in controller annotations.

#### **Security Patterns** (Follow existing conventions):
```java
// Existing Pattern from Variables.java
@PreAuthorize(value = "hasAnyAuthority('data:admin') " +
        "or hasAnyRole('ROLE_TOUCHPOINT_DATA_LIST')")

// New Pattern for TextStyles
@PreAuthorize(value = "hasAnyAuthority('text_styles:read') " +
        "or hasAnyRole('ROLE_CONTENT_MANAGEMENT')")
```

### **Java Source Code Generation Locations**:

#### **Controllers** (REST API endpoints):
```
Package: com.prinova.messagepoint.platform.mprest
Location: src/java/com/prinova/messagepoint/platform/mprest/
Files: {EntityName}s.java (plural entity name, no Controller suffix)
Example: TextStyles.java
```

#### **DTOs** (Data Transfer Objects):
```
Package: com.prinova.messagepoint.platform.mprest.dto
Location: src/java/com/prinova/messagepoint/platform/mprest/dto/
Files: {EntityName}Dto.java
Example: TextStyleDto.java, TextStyleFontDto.java, ContentObjectDto.java

Required Patterns (follow VariableDto.java):
- Extend RestResourceDtoBase
- Include @JsonPropertyOrder annotation with all field names
- Include @JsonInclude(JsonInclude.Include.NON_NULL)
- Include @Schema description annotation
```

#### **Components** (Additional components if needed):
```
Package: com.prinova.messagepoint.platform.mprest.components
Location: src/main/java/com/prinova/messagepoint/platform/mprest/components/
Files: {EntityName}Component.java (if required)
```

### **Test Code Generation Locations**:

#### **Integration Tests**:
```
Package: com.prinova.messagepoint.platform.mprest.controller
Location: src/test/java/com/prinova/messagepoint/platform/mprest/controller/
Files: {EntityName}sControllerIntegrationTest.java (plural controller name)
```

#### **Test Factories**:
```
Package: com.prinova.messagepoint.test.factory
Location: src/test/java/com/prinova/messagepoint/test/factory/
Files: {EntityName}TestFactory.java
```

### **Generated Artifacts** (AI/Projects/REST-API-AI-Generation/Generated-APIs folder):

#### **Complete API Package** (Code + Documentation):
```
AI/Projects/REST-API-AI-Generation/Generated-APIs/{EntityName}/
├── {EntityName}sController.java       # REST controller implementation (plural)
├── {EntityName}Dto.java               # Data transfer object (singular)
├── {EntityName}sControllerIntegrationTest.java  # Integration tests (plural)
├── {EntityName}TestFactory.java       # Test data factory (singular)
├── generation-decisions.md            # Decision process documentation
├── generation-summary.md              # Generation results summary
├── validation-report.md               # Standards compliance validation
├── feedback-tracking.md               # Feedback collection for this API
└── lessons-learned.md                 # Insights for future generation
```

**Note**: The AI/Projects/REST-API-AI-Generation/Generated-APIs/{EntityName}/ folder contains ALL generated artifacts - both Java code and documentation - consolidated for easier management and organization.

## 🔧 COMPILATION REQUIREMENTS

### **MANDATORY: Ensure Generated Code Compiles**

After generating any Java classes, **ALWAYS verify compilation**:

#### **Compilation Verification Steps**:
1. **Deploy to correct codebase locations**:
   - Controllers → `frontend/src/java/com/prinova/messagepoint/platform/mprest/`
   - DTOs → `frontend/src/java/com/prinova/messagepoint/platform/mprest/dto/`

2. **Test compilation** using Gradle:
   ```bash
   ./gradlew compileJava
   ```

3. **Fix compilation errors** immediately:
   - **Missing methods**: Check actual entity class for available methods
   - **Wrong imports**: Use existing API imports as reference
   - **Type mismatches**: Verify DTO inheritance from RestResourceDtoBase
   - **Missing dependencies**: Ensure all required classes exist

#### **Common Compilation Issues**:
- **Entity methods**: Use `findByIdentifier()` not `findByGuid()` for TextStyle
- **DTO inheritance**: All DTOs must extend `RestResourceDtoBase`
- **Date types**: Use `Date` not `LocalDateTime` for entity timestamps
- **Include parameters**: Check if IncludeParams methods exist before using
- **Service methods**: Verify service class methods and parameters

#### **Quality Gate**:
**No generated code should be considered complete until it compiles successfully.**

## 🚀 **Quick Start Template**

### **Complete Controller Template** (Based on Existing APIs)
```java
package com.prinova.messagepoint.platform.mprest;

import com.prinova.messagepoint.platform.mprest.RestResourceBase;
import com.prinova.messagepoint.platform.mprest.dto.{EntityName}Dto;
// ... other imports

@Tag(name = "Resource Name", description = "Resource Management API")
@RestController
@RequestMapping("/resources")  // No /mp/rest prefix - handled at servlet level
public class Resources extends RestResourceBase {  // Singular class name like Variables.java
    
    private final ResourceComponent resourceComponent;
    
    public ResourceController(ResourceComponent resourceComponent) {
        this.resourceComponent = resourceComponent;
    }
    
    // GET collection with filtering and pagination
    @Operation(summary = "List resources", 
              description = "Get resources with filtering and pagination support",
              extensions = {@Extension(name = EXTENSION_RESPONSE_TYPE, 
                                     properties = {@ExtensionProperty(name = EXTENSION_RESPONSE_TYPE_IS_COLLECTION, 
                                                                    value = "true")})})
    @ApiResponses({
        @ApiResponse(responseCode = "200", 
                    description = RestApiConstants.ApiDocumenation.API_RESPONSE_MESSAGE_200_COLLECTION,
                    content = @Content(schema = @Schema(implementation = ResponseDto.class)))
    })
    @GetMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    @PreAuthorize(value = "hasAnyAuthority('resource:read', 'resource:admin') " +
            "or hasAnyRole('ROLE_RESOURCE_VIEW')")
    public ResponseEntity<String> getResources(
            @ParameterObject @ModelAttribute ResourceDto filterParams,
            @ParameterObject @ModelAttribute PaginationParams paginationParams,
            @ParameterObject @ModelAttribute PartialResponseParams partialResponseParams,
            @ParameterObject @ModelAttribute IncludeParams includeParams) {
        
        // 1. Fetch data
        List<ResourceEntity> entities = resourceComponent.findResources(filterParams);
        
        // 2. Apply pagination
        paginationParams.setTotalRecords(entities.size());
        entities = paginationParams.paginateStream(entities.stream()).toList();
        
        // 3. Convert to DTOs
        List<ResourceDto> dtos = new ArrayList<>();
        entities.forEach(entity -> dtos.add(new ResourceDto(entity, includeParams)));
        
        // 4. Build response
        CollectionDataDto dto = CollectionDataDto.newInstance()
                .items(dtos)
                .pagination(paginationParams)
                .count(dtos.size());
        
        return sendSuccessResponse(dto, RestResourceType.RESOURCE,
                "Resources retrieved successfully", dto.getCustomHeaders(), partialResponseParams);
    }
    
    // GET single item
    @Operation(summary = "Get resource", 
              description = "Get a single resource by GUID")
    @ApiResponses({
        @ApiResponse(responseCode = "200", 
                    description = RestApiConstants.ApiDocumenation.API_RESPONSE_MESSAGE_200_SINGLE,
                    content = @Content(schema = @Schema(implementation = ResponseDto.class)))
    })
    @GetMapping(value = "/{guid}", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    @PreAuthorize(value = "hasAnyAuthority('resource:read', 'resource:admin') " +
            "or hasAnyRole('ROLE_RESOURCE_VIEW')")
    public ResponseEntity<String> getResource(
            @Parameter(description = "GUID of the resource to find") @PathVariable String guid,
            @ParameterObject @ModelAttribute PartialResponseParams partialResponseParams) {
        
        // 1. Fetch data
        ResourceEntity entity = resourceComponent.findResourceByGuid(guid);
        
        if (entity == null) {
            return sendResourceNotFoundResponse("Resource with guid " + guid + " not found");
        }
        
        // 2. Convert to DTO
        ResourceDto dto = new ResourceDto(entity);
        
        return sendSuccessResponse(dto, RestResourceType.RESOURCE,
                "Resource retrieved successfully", dto.getTotalCountHeader(1L), partialResponseParams);
    }
    
    // POST create
    @Operation(summary = "Create resource", 
              description = "Create a new resource")
    @ApiResponses({
        @ApiResponse(responseCode = "200", 
                    description = "Resource created successfully",
                    content = @Content(schema = @Schema(implementation = ResponseDto.class))),
        @ApiResponse(responseCode = "422", 
                    description = "Unprocessable entity, validation errors")
    })
    @PostMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    @PreAuthorize(value = "hasAnyAuthority('resource:admin') " +
            "or hasAnyRole('ROLE_RESOURCE_EDIT')")
    @Transactional
    public ResponseEntity<String> createResource(
            @Parameter(description = "Resource data", 
                      content = @Content(schema = @Schema(implementation = ResourceDto.class))) 
            @RequestBody ResourceDto resourceParams,
            @ParameterObject @ModelAttribute PartialResponseParams partialResponseParams) {
        
        ResourceEntity entity = new ResourceEntity();
        
        if (resourceParams != null) {
            List<String> errors = new ArrayList<>();
            entity = resourceComponent.updateResource(entity, resourceParams, true, errors);
            
            if (!errors.isEmpty()) {
                return sendResourceProcessingFailedResponse(errors);
            }
        }
        
        ResourceDto dto = new ResourceDto(entity);
        
        return sendSuccessResponse(dto, RestResourceType.RESOURCE,
                "Resource created successfully", dto.getTotalCountHeader(1L), partialResponseParams);
    }
    
    // PATCH update
    @Operation(summary = "Update resource", 
              description = "Update an existing resource")
    @ApiResponses({
        @ApiResponse(responseCode = "200", 
                    description = "Resource updated successfully",
                    content = @Content(schema = @Schema(implementation = ResponseDto.class))),
        @ApiResponse(responseCode = "422", 
                    description = "Unprocessable entity, validation errors")
    })
    @PatchMapping(value = "/{guid}", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    @PreAuthorize(value = "hasAnyAuthority('resource:admin') " +
            "or hasAnyRole('ROLE_RESOURCE_EDIT')")
    @Transactional
    public ResponseEntity<String> updateResource(
            @Parameter(description = "GUID of the resource to update") @PathVariable String guid,
            @Parameter(description = "Resource data", 
                      content = @Content(schema = @Schema(implementation = ResourceDto.class))) 
            @RequestBody ResourceDto resourceParams,
            @ParameterObject @ModelAttribute PartialResponseParams partialResponseParams) {
        
        ResourceEntity entity = resourceComponent.findResourceByGuid(guid);
        
        if (entity == null) {
            return sendResourceNotFoundResponse("Resource with guid " + guid + " not found");
        }
        
        if (resourceParams != null) {
            List<String> errors = new ArrayList<>();
            resourceComponent.updateResource(entity, resourceParams, false, errors);
            
            if (!errors.isEmpty()) {
                return sendResourceProcessingFailedResponse(errors);
            }
        }
        
        ResourceDto dto = new ResourceDto(entity);
        
        return sendSuccessResponse(dto, RestResourceType.RESOURCE,
                "Resource updated successfully", dto.getTotalCountHeader(1L), partialResponseParams);
    }
    
    // DELETE
    @Operation(summary = "Delete resource", 
              description = "Delete a resource by GUID")
    @ApiResponses({
        @ApiResponse(responseCode = "200", 
                    description = "Resource deleted successfully"),
        @ApiResponse(responseCode = "422", 
                    description = "Unprocessable entity, cannot delete resource")
    })
    @DeleteMapping(value = "/{guid}", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    @PreAuthorize(value = "hasAnyAuthority('resource:admin') " +
            "or hasAnyRole('ROLE_RESOURCE_EDIT')")
    public ResponseEntity<String> deleteResource(
            @Parameter(description = "GUID of the resource to delete") @PathVariable String guid) {
        
        ResourceEntity entity = resourceComponent.findResourceByGuid(guid);
        
        if (entity == null) {
            return sendResourceNotFoundResponse("Resource with guid " + guid + " not found");
        }
        
        // Check if resource can be deleted
        if (entity.isReferenced()) {
            return sendResourceProcessingFailedResponse(
                "Resource with guid " + guid + " is referenced and cannot be deleted");
        }
        
        boolean result = resourceComponent.deleteResource(entity);
        String message = "Resource with guid " + guid + 
                " deleted " + (result ? "successfully" : "unsuccessfully");
        
        return sendSuccessResponseWithMessage(message);
    }
    
    // POST filter (advanced filtering)
    @Operation(summary = "Filter resources", 
              description = "Advanced filtering with complex criteria",
              extensions = {@Extension(name = EXTENSION_RESPONSE_TYPE, 
                                     properties = {@ExtensionProperty(name = EXTENSION_RESPONSE_TYPE_IS_COLLECTION, 
                                                                    value = "true")})})
    @ApiResponses({
        @ApiResponse(responseCode = "200", 
                    description = RestApiConstants.ApiDocumenation.API_RESPONSE_MESSAGE_200_COLLECTION,
                    content = @Content(schema = @Schema(implementation = ResponseDto.class)))
    })
    @PostMapping(value = "/filter", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    @PreAuthorize(value = "hasAnyAuthority('resource:read', 'resource:admin') " +
            "or hasAnyRole('ROLE_RESOURCE_VIEW')")
    public ResponseEntity<String> filterResources(
            @RequestBody FilterRequest filterRequest,
            @ParameterObject @ModelAttribute PaginationParams paginationParams,
            @ParameterObject @ModelAttribute CursorParams cursorParams,
            @ParameterObject @ModelAttribute PartialResponseParams partialResponseParams,
            @ParameterObject @ModelAttribute IncludeParams includeParams) {
        
        // Use filter engine for advanced filtering
        FilterResult<ResourceEntity> filterResult = resourceComponent.filterResources(
                filterRequest, paginationParams, cursorParams);
        List<ResourceEntity> entities = filterResult.getData();
        
        // Convert to DTOs
        List<ResourceDto> dtos = new ArrayList<>();
        entities.forEach(entity -> dtos.add(new ResourceDto(entity, includeParams)));
        
        // Build response
        CollectionDataDto dto = CollectionDataDto.newInstance()
                .items(dtos)
                .withPaginationOrCursor(paginationParams, cursorParams)
                .count(dtos.size());
        
        return sendSuccessResponse(dto, RestResourceType.RESOURCE,
                "Resources filtered successfully", dto.getCustomHeaders(), partialResponseParams);
    }
}
```

---

## 🔧 **Implementation Checklist**

### **Pre-Development Setup**
- [ ] Create component class for business logic
- [ ] Define DTO class with proper constructors
- [ ] Add RestResourceType enum entry
- [ ] Plan security scopes and roles
- [ ] Design filter criteria

### **Controller Implementation**
- [ ] Extend RestResourceBase
- [ ] Add @Tag, @RestController, @RequestMapping
- [ ] Implement all CRUD operations
- [ ] Add proper @PreAuthorize on all methods
- [ ] Use standard parameter types
- [ ] Implement proper error handling

### **Documentation**
- [ ] Add @Operation to all methods
- [ ] Add @ApiResponses for all status codes
- [ ] Add @Parameter descriptions
- [ ] Use @MessagepointApiResponses
- [ ] Add collection extensions where needed

### **Testing**
- [ ] Create integration test class
- [ ] Test all CRUD operations
- [ ] Test error scenarios
- [ ] Test authentication/authorization
- [ ] Test pagination and filtering

---

## 📚 **Common Patterns & Examples**

### **Error Handling Patterns**
```java
// Not found
if (entity == null) {
    return sendResourceNotFoundResponse("Resource with guid " + guid + " not found");
}

// Validation errors
if (!errors.isEmpty()) {
    return sendResourceProcessingFailedResponse(errors);
}

// Business rule violation
if (entity.isReferenced()) {
    return sendResourceProcessingFailedResponse("Resource is referenced and cannot be deleted");
}
```

### **Security Patterns**
```java
// Read operations
@PreAuthorize(value = "hasAnyAuthority('resource:read', 'resource:admin') " +
        "or hasAnyRole('ROLE_RESOURCE_VIEW')")

// Write operations  
@PreAuthorize(value = "hasAnyAuthority('resource:admin') " +
        "or hasAnyRole('ROLE_RESOURCE_EDIT')")
```

### **Response Patterns**
```java
// Collection response
CollectionDataDto dto = CollectionDataDto.newInstance()
        .items(dtos)
        .pagination(paginationParams)
        .count(dtos.size());

return sendSuccessResponse(dto, RestResourceType.RESOURCE,
        "Resources retrieved successfully", dto.getCustomHeaders(), partialResponseParams);

// Single item response
ResourceDto dto = new ResourceDto(entity);
return sendSuccessResponse(dto, RestResourceType.RESOURCE,
        "Resource retrieved successfully", dto.getTotalCountHeader(1L), partialResponseParams);
```

---

*This implementation guide provides the foundation for creating high-quality, standards-compliant REST APIs in the Messagepoint platform.*
