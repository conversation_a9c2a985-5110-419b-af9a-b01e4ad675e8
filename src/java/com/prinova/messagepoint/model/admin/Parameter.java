package com.prinova.messagepoint.model.admin;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.hibernate.query.NativeQuery;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.PrivateModelImpl;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.wtu.Referencable;
import com.prinova.messagepoint.wtu.ReferencableObject;
import com.prinova.messagepoint.wtu.services.DirectReferencesFetchService;

public class Parameter extends PrivateModelImpl implements Referencable {

	private static final long serialVersionUID = -7319552896292457244L;
	private DataElementVariable dataElementVariable;

	public Parameter() {
	    
	}
	
    protected Parameter(Parameter cloneFrom) {
        super(cloneFrom);
        setGuid(cloneFrom.getGuid());
        this.save();
        this.description         = cloneFrom.description;
        this.dataElementVariable = CloneHelper.assign(cloneFrom.dataElementVariable);
    }
    
    @Override
    public Object clone() {
        return new Parameter(this);
    }
	
	public DataElementVariable getDataElementVariable() {
		return dataElementVariable;
	}
	public void setDataElementVariable(DataElementVariable dataElementVariable) {
		this.dataElementVariable = dataElementVariable;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((getGuid() == null) ? 0 : getGuid().hashCode());
		result = prime * result + (int)(1 ^ (1 >>> 32));
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (!(obj instanceof Parameter))
			return false;
		final Parameter other = (Parameter) obj;
        if(!getObjectSchemaName().equals(other.getObjectSchemaName()))
            return false;
		if (getGuid() == null) {
			if (other.getGuid() != null)
				return false;
		}
		return true;
	}

	public String getVisibility() {
		return ApplicationUtil.getMessage("page.label.visibility.unrestricted");
	}
	
	public static Parameter findById(long id){
		return HibernateUtil.getManager().getObject(Parameter.class, id);
	}

	public static List<Parameter> findAll(){
		return HibernateUtil.getManager().getObjects(Parameter.class, MessagepointOrder.asc("name"));
	}
	
	public static List<Parameter> findByName(String name){
		String query = "SELECT p.id FROM parameter p, data_element_variable d WHERE p.data_element_variable_id = d.id AND p.name = '" + name + "'";
		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
		List<?> ids = sqlQuery.list();
		List<Parameter> parameters = new ArrayList<>();
		for(Object idObj : ids){
			long parameterId = ((BigInteger)idObj).longValue();
			parameters.add(Parameter.findById(parameterId));
		}
		return parameters;		
	}

	public static List<Parameter> findAllWithRestrictedVisibility() {
		List<MessagepointCriterion> criterionList = new ArrayList<>();
		criterionList.add(MessagepointRestrictions.eq("restrictedVisibility", true));
		List<Parameter> restrictedParameters = HibernateUtil.getManager().getObjectsAdvanced(Parameter.class, criterionList, MessagepointOrder.asc("name"));
		return restrictedParameters;
	}

	public static void delete(long id){
		List<ParameterGroup> parameterGroupList = ParameterGroup.findByParameter(id);
		for (ParameterGroup parameterGroup : parameterGroupList) {
			if (parameterGroup.isParameter()) {
				ParameterGroup.delete(parameterGroup.getId());	
			}
		}
		HibernateUtil.getManager().deleteObject(Parameter.class, id);
	}
	
	public ParameterGroup getCorrespondingParameterGroup() {
		List<ParameterGroup> parameterGroups = ParameterGroup.findByParameter(this.getId());
		for (ParameterGroup parameterGroup : parameterGroups) {
			if (parameterGroup.isParameter()) {
				return parameterGroup;
			}
		}
		return null;
	}

	public DataGroup getDataGroup(Document doc) {
		DataElementVariable dataElementVariable = this.getDataElementVariable();
		if ( dataElementVariable != null ) {
			return dataElementVariable.getDataGroup(doc);
		}
		return null;
	}
	
	@SuppressWarnings("unchecked")
	public static List<Parameter> findByVariable(long variableId){
		String query = "select p " +
		   			   "from Parameter as p " +
		   			   "where p.dataElementVariable.id = :variableId";

		Map<String, Object> params = new HashMap<>();
		params.put("variableId", variableId);
		return (List<Parameter>) HibernateUtil.getManager().getObjectsAdvanced(query, params);
	}
	
	public boolean hasLookupValue() {
		DataElementVariable dataElementVariable = getDataElementVariable();
		if (dataElementVariable != null) {
			if (!dataElementVariable.getLookupValues().isEmpty()) {
				return true;
			}
		}
		return false;
	}

	public boolean isVisibleToDocument(Document doc) {
		DataElementVariable dataElementVariable = this.getDataElementVariable();
		if (dataElementVariable != null) {
			return dataElementVariable.isVisibleToDocument(doc);
		}
		return true;
	}

	public Set<Document> getDocumentsWithVisibility() {
		List<Document> documents = Document.findAll();
		
		DataElementVariable dataElementVariable = this.getDataElementVariable();
		if (dataElementVariable != null) {
			Set<Document> dataElementDocuments = dataElementVariable.getDocumentsWithVisibility(); 
			documents.retainAll(dataElementDocuments);
		}
		return new HashSet<>(documents);
	}
	
	@SuppressWarnings("unchecked")
	public List<ReferencableObject> getDirectReferences() {
		ServiceExecutionContext context = DirectReferencesFetchService.createContext(getId(), Parameter.class);
		Service service = MessagepointServiceFactory.getInstance().lookupService(DirectReferencesFetchService.SERVICE_NAME, DirectReferencesFetchService.class);
		service.execute(context);
		if (!context.getResponse().isSuccessful()) {
			StringBuilder sb = new StringBuilder();
			sb.append(DirectReferencesFetchService.SERVICE_NAME);
			sb.append(" service call is not successful ");
			sb.append(" for object ").append(this.getClass().getName());
			sb.append(" id=").append(this.getId());
			throw new RuntimeException(sb.toString());
		}
		return (List<ReferencableObject>) context.getResponse().getResultValueBean();
	}

	public boolean isReferenced() {
		// If this variable is only used by Variable or ParameterGroup which has only one parameter
		// Don't treat it as referenced
		List<ReferencableObject> refs = this.getDirectReferences();
		for(ReferencableObject referenceObj: refs){	
			Object targetObj = referenceObj.getTargetObject();
			if(targetObj instanceof DataElementVariable){
				continue;
			}else if(targetObj instanceof ParameterGroup){
				ParameterGroup parameterGroup = (ParameterGroup)targetObj;
				if(!parameterGroup.isParameter())
					return true;
			}else{
				return true;
			}
		}
		return false;
	}
	
	/**
	 * Check whether this parameter is really referenced (Remove the single parameter group
	 * reference when those single parameter group is not referenced)
     */
	public boolean isApplied(){
		List<ReferencableObject> refs = this.getDirectReferences();
		if(refs != null && !refs.isEmpty()){
			for(ReferencableObject refObj : refs){
				if(!(refObj.getTargetObject() instanceof ParameterGroup)){
					return true;
				}else{
					ParameterGroup parmGrp = (ParameterGroup)refObj.getTargetObject();
					// Should be all single parameter selector and not referenced to continue					
					if(!parmGrp.isParameter() || parmGrp.isReferenced()){
						return true;
					}
				}
			}
		}
		return false;		
	}
	
	@SuppressWarnings("unchecked")
	public static Parameter findByGuid( String guid )
	{
	    if ( guid == null || guid.isEmpty())
	        return null;

	    String query = "select p " +
	    "from Parameter as p " +
	    "where guid = :guid";

	    Map<String, Object> params = new HashMap<>();
	    params.put("guid", guid);
	    List<Parameter> items = (List<Parameter>) HibernateUtil.getManager().getObjectsAdvanced(query, params);
	    if ( items != null && !items.isEmpty())
	        return items.get(0);
	    return null;

	}
	
	public boolean getIsDateParameter() {
		return dataElementVariable != null && dataElementVariable.getDataTypeId() == DataType.DATA_TYPE_DATE;
	}
	
	@Override
	public void save() {
	    if(dataElementVariable != null && dataElementVariable.getId() == 0) {
	        dataElementVariable.save();
	    }
	    super.save();
	}
}