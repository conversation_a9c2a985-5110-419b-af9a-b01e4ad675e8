package com.prinova.messagepoint.controller.admin;

import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.wrapper.AsyncDEServerListVO;
import com.prinova.messagepoint.model.wrapper.AsyncDEServerListWrapper;
import org.springframework.validation.Errors;

import com.prinova.messagepoint.validator.MessagepointInputValidator;

import java.util.function.Predicate;

public class DEServerListValidator extends MessagepointInputValidator {
	
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		DEServerListWrapper command = (DEServerListWrapper) commandObj;


		int action = -1;
		if (command.getActionValue() != null && !command.getActionValue().trim().isEmpty()) {
			action = Integer.parseInt(command.getActionValue());
		}

		switch(action){
			case DEServerListController.ACTION_UPDATE:{
				validateActionPermission(errors, "error.message.action.not.permitted", AsyncDEServerListVO.DEServerListVOFlags::isCanUpdate);
			}
			case DEServerListController.ACTION_DELETE:{
				validateActionPermission(errors, "error.message.action.not.permitted", AsyncDEServerListVO.DEServerListVOFlags::isCanDelete);
			}
			default:
				break;
		}
	}

	private void validateActionPermission(Errors errors, String errorMessage, Predicate<AsyncDEServerListVO.DEServerListVOFlags> flagChecker) {

		// Admin bypass - Feature flag hidden toggle
		if ( !Boolean.parseBoolean(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.WebAppSecurity.SERVER_SIDE_ACTION_VALIDATION)) )
			return;

		AsyncDEServerListVO vo = new AsyncDEServerListVO();
		AsyncDEServerListVO.DEServerListVOFlags flags = new AsyncDEServerListVO.DEServerListVOFlags();
		AsyncDEServerListWrapper.setActionFlags(flags);
		if (!flagChecker.test(flags)) {
			errors.reject(errorMessage);
		}
	}
}
