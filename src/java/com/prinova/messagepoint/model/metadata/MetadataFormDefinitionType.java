package com.prinova.messagepoint.model.metadata;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.common.StaticType;
import com.prinova.messagepoint.util.ApplicationUtil;

public class MetadataFormDefinitionType extends StaticType {

	private static final long serialVersionUID = -3489548088293106615L;

	public static final int ID_GENERAL					= 1;
	public static final int ID_TOUCHPOINT				= 2;
	public static final int ID_VARIANT					= 3;
	public static final int ID_TASK						= 4;
	public static final int ID_PROJECT					= 5;
	public static final int ID_RATIONALIZER_PARSED		= 7;
	public static final int ID_MESSAGE					= 8;
	public static final int ID_EMBEDDED_CONTENT			= 9;
	public static final int ID_CONTENT_LIBRARY			= 10;
	public static final int ID_COLLECTION				= 11;
	public static final int ID_CHANGE					= 12;

	public static final String MESSAGE_GENERAL 				= "page.label.general";
	public static final String MESSAGE_TOUCHPOINT			= "page.label.touchpoint";
	public static final String MESSAGE_VARIANT				= "page.label.variant";
	public static final String MESSAGE_TASK					= "page.label.task";
	public static final String MESSAGE_PROJECT				= "page.label.project";
	public static final String MESSAGE_RATIONALIZER_PARSED	= "page.label.rationalizer";
	public static final String MESSAGE_MESSAGE 				= "page.label.message";
	public static final String MESSAGE_EMBEDDED_CONTENT 	= "page.label.smart.object";
	public static final String MESSAGE_CONTENT_LIBRARY 		= "page.label.content.library";
	public static final String MESSAGE_COLLECTION			= "page.label.collection";

	public MetadataFormDefinitionType(Integer id) {
		super();
		switch (id) {
		case ID_GENERAL:
			this.setId(ID_GENERAL);
			this.setName(ApplicationUtil.getMessage(MESSAGE_GENERAL));
			this.setDisplayMessageCode(MESSAGE_GENERAL);
			break;		
		case ID_TOUCHPOINT:
			this.setId(ID_TOUCHPOINT);
			this.setName(ApplicationUtil.getMessage(MESSAGE_TOUCHPOINT));
			this.setDisplayMessageCode(MESSAGE_TOUCHPOINT);
			break;		
		case ID_VARIANT:
			this.setId(ID_VARIANT);
			this.setName(ApplicationUtil.getMessage(MESSAGE_VARIANT));
			this.setDisplayMessageCode(MESSAGE_VARIANT);
			break;
		case ID_TASK:
			this.setId(ID_TASK);
			this.setName(ApplicationUtil.getMessage(MESSAGE_TASK));
			this.setDisplayMessageCode(MESSAGE_TASK);
			break;
		case ID_PROJECT:
			this.setId(ID_PROJECT);
			this.setName(ApplicationUtil.getMessage(MESSAGE_PROJECT));
			this.setDisplayMessageCode(MESSAGE_PROJECT);
			break;
		case ID_RATIONALIZER_PARSED:
			this.setId(ID_RATIONALIZER_PARSED);
			this.setName(ApplicationUtil.getMessage(MESSAGE_RATIONALIZER_PARSED));
			this.setDisplayMessageCode(MESSAGE_RATIONALIZER_PARSED);
			break;
		case ID_MESSAGE:
			this.setId(ID_MESSAGE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_MESSAGE));
			this.setDisplayMessageCode(MESSAGE_MESSAGE);
			break;
		case ID_EMBEDDED_CONTENT:
			this.setId(ID_EMBEDDED_CONTENT);
			this.setName(ApplicationUtil.getMessage(MESSAGE_EMBEDDED_CONTENT));
			this.setDisplayMessageCode(MESSAGE_EMBEDDED_CONTENT);
			break;
		case ID_CONTENT_LIBRARY:
			this.setId(ID_CONTENT_LIBRARY);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CONTENT_LIBRARY));
			this.setDisplayMessageCode(MESSAGE_CONTENT_LIBRARY);
			break;
		case ID_COLLECTION:
			this.setId(ID_COLLECTION);
			this.setName(ApplicationUtil.getMessage(MESSAGE_COLLECTION));
			this.setDisplayMessageCode(MESSAGE_COLLECTION);
			break;
		default:
			break;
		}
	}

	public static List<MetadataFormDefinitionType> listAll() {
		List<MetadataFormDefinitionType> allMetadataFormDefinitionTypes = new ArrayList<>();

		MetadataFormDefinitionType metadataFormDefinitionType = null;

		metadataFormDefinitionType = new MetadataFormDefinitionType(ID_GENERAL);
		allMetadataFormDefinitionTypes.add(metadataFormDefinitionType);

		metadataFormDefinitionType = new MetadataFormDefinitionType(ID_TOUCHPOINT);
		allMetadataFormDefinitionTypes.add(metadataFormDefinitionType);
		
		metadataFormDefinitionType = new MetadataFormDefinitionType(ID_VARIANT);
		allMetadataFormDefinitionTypes.add(metadataFormDefinitionType);

		metadataFormDefinitionType = new MetadataFormDefinitionType(ID_TASK);
		allMetadataFormDefinitionTypes.add(metadataFormDefinitionType);

		//metadataFormDefinitionType = new MetadataFormDefinitionType(ID_PROJECT);
		//allMetadataFormDefinitionTypes.add(metadataFormDefinitionType);
		
		// ID_RATIONALIZER_PARSED: Not listed: System controlled
		
		metadataFormDefinitionType = new MetadataFormDefinitionType(ID_MESSAGE);
		allMetadataFormDefinitionTypes.add(metadataFormDefinitionType);
		
		metadataFormDefinitionType = new MetadataFormDefinitionType(ID_EMBEDDED_CONTENT);
		allMetadataFormDefinitionTypes.add(metadataFormDefinitionType);
		
		metadataFormDefinitionType = new MetadataFormDefinitionType(ID_CONTENT_LIBRARY);
		allMetadataFormDefinitionTypes.add(metadataFormDefinitionType);
		
		metadataFormDefinitionType = new MetadataFormDefinitionType(ID_COLLECTION);
		allMetadataFormDefinitionTypes.add(metadataFormDefinitionType);

		return allMetadataFormDefinitionTypes;
	}

}