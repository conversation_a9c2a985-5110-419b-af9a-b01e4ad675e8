package com.prinova.messagepoint.platform.services.imports.document;

public class BulkUploadUserUpdateInstanceRolesImportHandler extends MessagepointImportHandler {

    private Long id;
    private String name;

    public BulkUploadUserUpdateInstanceRolesImportHandler() {
        super(BulkUploadUserUpdateInstanceRolesImportHandler.class);

        setAttrMethod("Role", "id", "setId", Long.class);
        setAttrMethod("Role", "name", "setName", String.class);
    }

    @Override
    public void addChild(MessagepointImportHandler child) {

    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
