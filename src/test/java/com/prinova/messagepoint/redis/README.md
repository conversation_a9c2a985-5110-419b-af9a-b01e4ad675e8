# MessagepointRedisOperations Integration Tests and Reflection Utilities

This directory contains comprehensive integration tests for the `MessagepointRedisOperations` class and utilities for accessing the `client` property of `LettuceConnectionFactory` through reflection.

## Files Overview

### 1. `MessagepointRedisOperationTest.java`
Comprehensive integration test class that tests all public methods in `MessagepointRedisOperations`:

**Tested Methods:**
- `containsKey(String key)` - Check if a key exists in Redis
- `delete(String key)` - Delete a key from Redis
- `getBytesValue(String key)` - Get raw byte array value
- `getValue(String key, Class<TValue> retValueType)` - Get typed value
- `putValue(String key, byte[] value)` - Store byte array
- `putValue(String key, Serializable value)` - Store serializable object
- `putValue(String key, byte[] value, long seconds)` - Store with TTL (bytes)
- `putValue(String key, Serializable value, long seconds)` - Store with TTL (object)
- `increment(String key)` - Increment numeric value
- `decrement(String key)` - Decrement numeric value
- `putNewValue(String key, Serializable value)` - Store only if key doesn't exist (SETNX)
- `lpush(String key, Serializable value)` - Left push to list
- `rpop(String key)` - Right pop from list
- `expire(String key, long seconds)` - Set key expiration
- `close()` - Close Redis connections
- `subscribe()` - Subscribe to Redis pub/sub
- `publish(String channel, String message)` - Publish message
- `redisTemplate(LettuceConnectionFactory)` - Create RedisTemplate bean
- `stringRedisTemplate(LettuceConnectionFactory)` - Create StringRedisTemplate bean

**Test Features:**
- Tests all methods with various input scenarios (null, empty, valid)
- Error handling and edge case testing
- Integration test combining multiple operations
- Reflection demonstration for LettuceConnectionFactory client property

### 2. `LettuceConnectionFactoryReflectionUtil.java`
Utility class for accessing the `client` property of `LettuceConnectionFactory` through reflection:

**Key Methods:**
- `getClientPropertyValue(LettuceConnectionFactory)` - Main method to assign variable with client property value
- `findAndGetClientLikeField(LettuceConnectionFactory)` - Alternative approach to find client-like fields
- `demonstrateClientPropertyAccess(LettuceConnectionFactory)` - Demonstration method

**Features:**
- Handles class hierarchy traversal to find the client field
- Proper error handling for reflection operations
- Detailed logging of reflection process
- Support for finding fields with "client" in the name

### 3. `ReflectionDemonstration.java`
Demonstration class showing practical usage of the reflection utilities:

**Features:**
- Complete example of creating LettuceConnectionFactory and accessing client property
- Multiple demonstration methods for different scenarios
- Practical examples of using the reflection results
- Main method for standalone execution

## Usage Examples

### Running the Integration Tests

```bash
# Run all tests
./gradlew test --tests "com.prinova.messagepoint.redis.MessagepointRedisOperationTest"

# Run specific test method
./gradlew test --tests "com.prinova.messagepoint.redis.MessagepointRedisOperationTest.testContainsKey"

# Run integration tests only
./gradlew integrationTest
```

### Using the Reflection Utility

```java
// Create a LettuceConnectionFactory
LettuceConnectionFactory factory = new LettuceConnectionFactory(config);
factory.afterPropertiesSet();

// Assign a variable with the client property value through reflection
Object clientPropertyValue = LettuceConnectionFactoryReflectionUtil.getClientPropertyValue(factory);

if (clientPropertyValue != null) {
    System.out.println("Client type: " + clientPropertyValue.getClass().getName());
    // Use the client object as needed
}
```

### Running the Demonstration

```bash
# Compile and run the demonstration
./gradlew compileTestJava
java -cp build/classes/test:build/classes/main com.prinova.messagepoint.redis.ReflectionDemonstration
```

## Test Configuration

The tests use the following configuration:
- Test schema: `test_schema`
- Mock Redis configuration and connection factory
- Proper setup and teardown methods
- Integration with `TestApplicationContextUtils`

## Dependencies

The tests require:
- JUnit 4.12
- Mockito 3.4.0
- Spring Data Redis
- Lettuce Core 6.2.7.RELEASE
- Hibernate (for SerializationHelper)

## Notes

1. **Redis Availability**: Some tests may fail gracefully if Redis is not available, but they demonstrate the proper usage patterns.

2. **Reflection Security**: The reflection utilities handle security exceptions and provide appropriate error messages.

3. **Mock Objects**: The tests use mock objects where appropriate to isolate the testing of individual methods.

4. **Error Handling**: All tests include proper error handling and demonstrate both success and failure scenarios.

5. **Integration Testing**: The `testIntegratedOperations` method shows how multiple Redis operations work together in a realistic scenario.

## Extending the Tests

To add new test methods:

1. Follow the existing naming convention: `test[MethodName]`
2. Include tests for null inputs, empty inputs, and valid inputs
3. Add appropriate assertions and logging
4. Handle expected exceptions gracefully

## Troubleshooting

**Common Issues:**
- Redis connection failures: Check if Redis is running and accessible
- Reflection access denied: Ensure proper security permissions
- Serialization errors: Verify objects implement Serializable interface
- Mock setup issues: Check mock configuration in setUp() method

**Debug Tips:**
- Enable detailed logging to see reflection process
- Use the demonstration classes to verify functionality
- Check Redis logs for connection and operation details
- Verify Spring context initialization in test setup
