package com.prinova.messagepoint.theme;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.validation.Errors;

import com.prinova.messagepoint.controller.admin.HeaderThemeType;
import com.prinova.messagepoint.job.JobZipFile;
import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemTheme;
import com.prinova.messagepoint.model.font.DecimalValueUtil;
import com.prinova.messagepoint.model.tenant.TenantThemeInfo;
import com.prinova.messagepoint.model.tenant.TenantUtil;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.FileUtil;

public class MessagepointTheme {
	public static final String DEFAULT_HEADER_LOGO_NAME_DARK_THEME 	= "messagepoint-logo-dark-theme.svg";
	public static final String DEFAULT_HEADER_LOGO_NAME_LIGHT_THEME = "messagepoint-logo-light-theme.svg";

	private static final String THEME_PATH = "/includes/themes/";
	private static final String UPLOADED_THEME_PATH = "/dynamiccontent/themes/";
	private static final String BASE_THEME_NAME = "messagepoint";

	private TenantThemeInfo themeInfo = null;
	private Properties themeProperties = new Properties();
	private Properties baseProperties = new Properties();

	public MessagepointTheme(TenantThemeInfo themeInfo) throws IOException {

		if (themeInfo == null)
			themeInfo = TenantThemeInfo.getDefaultTheme();
		
		this.themeInfo = themeInfo;

		SystemTheme systemTheme = themeInfo.getSystemTheme();
		String themePath = systemTheme.isUploaded() ? UPLOADED_THEME_PATH : THEME_PATH;
		String themeName = systemTheme.isUploaded() ? systemTheme.getName() : systemTheme.getFolder();

		InputStream is = ApplicationUtil.getResourceAsStream(THEME_PATH + BASE_THEME_NAME + ".properties");
		if (is != null) {
			baseProperties.load(is);
			is.close();
		}
		is = ApplicationUtil.getResourceAsStream(themePath + "theme_monochrome" + "/" + themeName
				+ ".properties");
		if (is != null) {
			themeProperties.load(is);
			is.close();
		}
	}

	public String getProperty(String arg0) {
		String value = "monochrome.css";
		String themePath = themeInfo.getSystemTheme().isUploaded() ? UPLOADED_THEME_PATH : THEME_PATH;

		// values that have a leading slash are treated as absolute paths from
		// the web root. Otherwise they are considered relative paths.
		if (value != null) {
			if (!value.startsWith("/")) {
				value = ApplicationUtil.getWebRoot() + themePath + "theme_monochrome" + "/" + value;
			} else {
				value = ApplicationUtil.getWebRoot() + value;
			}
		} else {
			value = baseProperties.getProperty(arg0);
			if (value != null) {
				if (!value.startsWith("/")) {
					value = ApplicationUtil.getWebRoot() + THEME_PATH + value;
				} else {
					value = ApplicationUtil.getWebRoot() + value;
				}
			}
		}
		return value;
	}

	/**
	 * Returns the corporate logo location.
	 */
	public String getCorporateLogo() {
		return themeInfo.getLogoLocation();
	}

	/**
	 * Returns the provider logo location.
	 */
	public String getProviderLogo() {
		return themeInfo.getProviderLogoLocation();
	}
	
	/**
	 * Returns the header text.
	 */
	public String getHeaderText() {
		String headerText = themeInfo.getHeaderText();
		return (StringUtils.isBlank(headerText)) ? "" : headerText;
	}	

	/**
	 * Returns the provider text.
	 */
	public String getProviderText() {
		String providerText = themeInfo.getProviderText();
		return (StringUtils.isBlank(providerText)) ? "" : providerText;
	}

	/**
	 * Returns the provider logo location to be displayed. This provider logo is the parent's provider logo.
	 */
	public String getProviderLogoForFooter() {
		if (TenantUtil.isSystemMultiTenant()) {
			return TenantThemeInfo.getProviderThemeInfo(themeInfo).getProviderLogoLocation();

		} else {

			return themeInfo.getProviderLogoLocation();
		}
	}

	/**
	 * Returns the provider text to be displayed. This provider text is the parent's provider text.
	 */
	public String getProviderTextForFooter() {
		String text;

		if (TenantUtil.isSystemMultiTenant()) {
			text = TenantThemeInfo.getProviderThemeInfo(themeInfo).getProviderText();

		} else {

			text = themeInfo.getProviderText();
		}

		return (StringUtils.isBlank(text)) ? "" : text;
	}

	public String getBackgroundGradient() {
		return THEME_PATH + "backgrounds/" + themeInfo.getBackgroundTheme().getFilename();
	}

	public String getThemeName() {
		return themeInfo.getSystemTheme().getName();
	}

	public String getBackgroundThemeName() {
		return themeInfo.getBackgroundTheme().getName();
	}
	
	public String getHeaderThemeType(){
		return new HeaderThemeType(themeInfo.getHeaderThemeTypeId()).getName();
	}
	public HeaderThemeType getHeaderTheme(){
		return new HeaderThemeType(themeInfo.getHeaderThemeTypeId());
	}
	
	public String getHeaderThemeColor(){
		return themeInfo.getHeaderThemeColor();
	}	
	
	public boolean isDefaultHeaderLogo(){
		return themeInfo.getLogoLocation().endsWith(DEFAULT_HEADER_LOGO_NAME_DARK_THEME);
	}

	public static String saveTheme(InputStream is) throws IOException {
		String directory = "" + System.currentTimeMillis();
		File directoryFile = new File(ApplicationUtil.getRootPath() + UPLOADED_THEME_PATH + "/" + directory);
		directoryFile.mkdirs();

		ZipInputStream inputStream = new ZipInputStream(is);
		ZipEntry zipEntry = inputStream.getNextEntry();
		while (zipEntry != null) {
			String filename = ApplicationUtil.getRootPath() + UPLOADED_THEME_PATH + "/" + directory + "/"
					+ zipEntry.getName();
			if (!filename.isEmpty()) {
				File outputFile = new File(filename);
				JobZipFile.extractFile(inputStream, outputFile);
			}
			zipEntry = inputStream.getNextEntry();
		}
		inputStream.close();
		return directory;
	}

	public static boolean deleteThemeDirectory(String themeDirectory) {
		return FileUtil.deleteDir(new File(ApplicationUtil.getRootPath() + UPLOADED_THEME_PATH + themeDirectory));
	}

	public static boolean validateTheme(String themeName, String directory, Errors errors) {
		int initialErrorCount = errors.getAllErrors().size();
//		String themePath = true ? UPLOADED_THEME_PATH : THEME_PATH;
//		String themeName2 = true ? themeName : directory;
		String themePath = UPLOADED_THEME_PATH;
		String themeName2 = themeName;

		Properties themeProperties = new Properties();
		try {
			themeProperties.load(ApplicationUtil.getResourceAsStream(themePath + directory + "/" + themeName2
					+ ".properties"));
		} catch (Exception e) {
			errors.reject("error.theme.propertiesfilemissing",
					new Object[] { themeName2 + ".properties" },
					"Expecting a properties file with the name: " + themeName2 + ".properties");
		}

		String cssFilename = (String) themeProperties.get("css");
		File cssFile = new File(ApplicationUtil.getRootPath() + themePath + directory + "/" + cssFilename);
		if (!cssFile.exists())
			errors.reject("error.theme.cssfilemissing",
					new Object[] { cssFilename },
					"Expecting a CSS file with the name: " + cssFilename);

		return (initialErrorCount == errors.getAllErrors().size());
	}

	/**
	 * Returns the branch name from URL node.branch.pod.messagepoint.com
	 */
	public String getURLBranchName(HttpServletRequest request) {
		String urlBranchName = null;

		String requestedNodeGUID = request.getParameter("gd");
		if (requestedNodeGUID != null)
		{
			Branch branch = Branch.findByGuid(requestedNodeGUID);
			if (branch != null)
			{
				urlBranchName = branch.getName();
			}
			else
			{
				Node node = Node.findByGuid(requestedNodeGUID);
				if (node != null && node.getBranch() != null)
				{
					urlBranchName = node.getBranch().getName();
					// this covers email link with for external SSO users
					String requestedSSOHomeDomain = request.getParameter("ssohome");
					if (requestedSSOHomeDomain != null)
					{
						urlBranchName = urlBranchName.concat("#" + requestedSSOHomeDomain);
					}
				}
			}
		}

		if (urlBranchName == null)
		{
			String urlServerName = request.getServerName();
			String[] domainNames = urlServerName.split("\\.");
			
			if (domainNames.length > 3)
			{
				urlBranchName = domainNames[domainNames.length - 4].toLowerCase();	// 4th sub-domain (branch)
				if (domainNames.length == 4)
				{
					// if domainNames[0] is a number, the ServerName was an IP address
					if (DecimalValueUtil.validateDecimalValue(domainNames[0], 0, true))
					{
						urlBranchName = null;
					}
				}
			}
		}

		if (urlBranchName == null)
		{
			urlBranchName = request.getParameter("domain");
		}

		return urlBranchName;
	}
	
	/**
	 * Returns the node name from URL node.branch.pod.messagepoint.com
	 */
	public String getURLNodeName(HttpServletRequest request) {
		String urlNodeName = null;
		
		String requestedNodeGUID = request.getParameter("gd");
		if (requestedNodeGUID != null)
		{
			Node node = Node.findByGuid(requestedNodeGUID);
			if (node != null)
			{
				urlNodeName = node.getName();
			}
		}

		if (urlNodeName == null)
		{
			String urlServerName = request.getServerName();
			String[] domainNames = urlServerName.split("\\.");
			
			if (domainNames.length > 4)
			{
				urlNodeName = domainNames[domainNames.length - 5].toLowerCase(); // 5th sub-domain (node)
			}
		}
		
		return urlNodeName;
	}
	
	/**
	 * Returns the pod name from URL node.branch.pod.messagepoint.com
	 */
	public String getURLPodName(HttpServletRequest request) {
		String urlPodName = null;
		
		String urlServerName = request.getServerName();
		String[] domainNames = urlServerName.split("\\.");
		
		if (domainNames.length > 2)
		{
			urlPodName = domainNames[domainNames.length - 3].toLowerCase();	// 3rd sub-domain (pod)
			if (domainNames.length == 4)
			{
				// if domainNames[0] is a number, the ServerName was an IP address
				if (DecimalValueUtil.validateDecimalValue(domainNames[0], 0, true))
				{
					urlPodName = null;
				}
			}
		}
		
		return urlPodName;
	}
	
	/**
	 * Returns sign in marketing URL
	 */
	public String getSigninMarketingURL(HttpServletRequest request) {
		String signinMarketingURL = ApplicationUtil.getProperty(SystemPropertyKeys.UserInterface.KEY_SigninMarketingURL, null);
		
		if (signinMarketingURL == null)
			signinMarketingURL = "";
		else
			signinMarketingURL = signinMarketingURL.trim();
		
		if (!signinMarketingURL.isEmpty())
		{
			String urlPodName = getURLPodName(request);
			if (urlPodName != null && (urlPodName.contains("partner") || urlPodName.startsWith("pp")))
				signinMarketingURL = "";
		}
		
		return signinMarketingURL;
	}

    public SystemTheme getSystemTheme() {
        return this.themeInfo.getSystemTheme();
    }
}
