package com.prinova.messagepoint.platform.services.tpselection;

import com.prinova.messagepoint.controller.tpadmin.DocumentChannelEditWrapper;
import com.prinova.messagepoint.model.ComplexValue;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.admin.Connector;
import com.prinova.messagepoint.model.admin.ConnectorConfiguration;
import com.prinova.messagepoint.model.admin.GenericConnectorConfiguration;
import com.prinova.messagepoint.model.admin.QualificationOutput;
import com.prinova.messagepoint.model.clickatell.ClickatellConfiguration;
import com.prinova.messagepoint.model.dialogue.DialogueConfiguration;
import com.prinova.messagepoint.model.eMessaging.EMessagingConfiguration;
import com.prinova.messagepoint.model.exacttarget.ExactTargetConfiguration;
import com.prinova.messagepoint.model.file.CompositionFileSet;
import com.prinova.messagepoint.model.ftp.FtpConfiguration;
import com.prinova.messagepoint.model.gmc.GMCConfiguration;
import com.prinova.messagepoint.model.nativecomposition.NativeCompositionConfiguration;
import com.prinova.messagepoint.model.sefas.SefasConfiguration;
import com.prinova.messagepoint.model.sefas.MPHCSConfiguration;
import com.prinova.messagepoint.model.sendmail.SendmailConfiguration;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.model.wrapper.ConnecterConfigurationWrapper;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.HibernateDeproxyUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.lang.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.Date;
import java.util.List;

/**
 * 
 * Prinova Inc. 1998-2011
 * 678 Belmont Ave West, Unit 201, KW, ON N2M 1N6
 * All rights reserved.
 * 
 * UpdateTouchpointChannelService
 *
 * 2011-01-21
 * 11:19:50 AM
 * 
 * <AUTHOR>
 */
public class UpdateTouchpointChannelService extends AbstractService {

	public static final String SERVICE_NAME = "tpselection.UpdateTouchpointChannelService";

	private static final Log log = LogUtil.getLog(UpdateTouchpointChannelService.class);

	public void execute(ServiceExecutionContext context) {

		log.debug("UpdateTouchpointChannelService.execute()");

		try {
			
			validate(context);
			if ( hasValidationError(context) ) {
				return;
			}
			
			UpdateTouchpointChannelServiceRequest request = (UpdateTouchpointChannelServiceRequest) context.getRequest();
			DocumentChannelEditWrapper documentWrapper = request.getDocumentEditWrapper();
			saveDocument(documentWrapper);
			
			// If touchpoint belongs to an executable collection, update the channel information for other touchpoints in the same collection
			Document document = documentWrapper.getDocument();
			List<Document> familyDocuments = document.getAllPackagedCollectionFamilyDocuments();
			for ( Document collectionDoc : familyDocuments ) {
				Document channelContextCollectionDoc = UserUtil.getCurrentChannelDocumentContext(collectionDoc);
				if ( channelContextCollectionDoc.getId() != document.getId() ) {
					copyConfiguration(document, channelContextCollectionDoc);
					channelContextCollectionDoc.setProcessUsingCombinedContent(document.isProcessUsingCombinedContent());
					channelContextCollectionDoc.save();
				}
			}

		} catch (Exception e) {
			log.error(" unexpected exception when invoking UpdateTouchpointChannelService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION", ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e, context.getLocale());
			throw new RuntimeException(e);
		}
	}

	// if master and if there are Empty content associations, reject
	public void validate(ServiceExecutionContext context) {

	}
	
	public static ServiceExecutionContext createContext(DocumentChannelEditWrapper documentWrapper) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		UpdateTouchpointChannelServiceRequest request = new UpdateTouchpointChannelServiceRequest();
		request.setDocumentEditWrapper(documentWrapper);
		context.setRequest(request);

		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}

	private void saveDocument(DocumentChannelEditWrapper documentWrapper) throws Exception {
		// changed because of introduction of ConnectorConfiguration
		Document document = documentWrapper.getDocument();
		if ( documentWrapper.getDocument().getId() == 0 ) {
			// create configuration
			createConfiguration(documentWrapper);
			ConnectorConfiguration connConfiguration = document.getConnectorConfiguration();
			connConfiguration.setUpdatedBy(UserUtil.getPrincipalUserId());
			if ( documentWrapper.isOverrideRemote() ) {
				connConfiguration.setOverrideRemote(true);
				connConfiguration.setDEServerGuid(documentWrapper.getDEServerGuid());
			}
			
			HibernateUtil.getManager().saveObject(connConfiguration, true);		
		} else {
			Document dbDocument = Document.findById(document.getId());
			ConnectorConfiguration oldConnConfig = null;
			if ( dbDocument != null ) {
				oldConnConfig = dbDocument.getConnectorConfiguration();
			}
			processConnectorChange(documentWrapper);
			ConnectorConfiguration connConfiguration = document.getConnectorConfiguration();
			
			// update qulificationOutput
			QualificationOutput qualificationOutput = QualificationOutput.findById(documentWrapper.getQualificationOutputId());
			connConfiguration.setQualificationOutput(qualificationOutput);
			
			// Update inCloud settings
			connConfiguration.setExecuteInCloudPreview(documentWrapper.isExecuteInCloudPreview());
			connConfiguration.setExecuteInCloudProof(documentWrapper.isExecuteInCloudProof());
			connConfiguration.setExecuteInCloudTest(documentWrapper.isExecuteInCloudTest());
			connConfiguration.setExecuteInCloudSimulation(documentWrapper.isExecuteInCloudSimulation());
			
			// Update Dews scripts settings
			connConfiguration.setPreQualEngineScript(documentWrapper.getPreQualEngineScript());
			connConfiguration.setPostQualEngineScript(documentWrapper.getPostQualEngineScript());
			connConfiguration.setPostConnectorScript(documentWrapper.getPostConnectorScript());
			
			connConfiguration.setApplyFilenamesToBundledImages(documentWrapper.getApplyFilenamesToBundledImages());
			connConfiguration.setFilenameSeparatorForBundledImages(documentWrapper.getFilenameSeparatorForBundledImages());
			connConfiguration.setPlayMessageOnEmptyVar(documentWrapper.getPlayMessageOnEmptyVar());
			
			// Update override remote server settings
			if ( documentWrapper.isOverrideRemote() ) {
				connConfiguration.setOverrideRemote(true);
				connConfiguration.setRemoteServerIP(documentWrapper.getRemoteServerIP());
				connConfiguration.setRemoteServerPort(documentWrapper.getRemoteServerPort());
				connConfiguration.setRemoteServerUser(documentWrapper.getRemoteServerUser());

				String newPassword = documentWrapper.getRemoteServerPassword();
				if (newPassword == null || newPassword.isEmpty()) {
					connConfiguration.setRemoteServerPassword("");
				} else {
					String oldPassword = oldConnConfig.getRemoteServerPassword();
					if ((oldPassword == null) || (oldPassword != null && !oldPassword.equals(newPassword))) {
						connConfiguration.setRemoteServerPassword(newPassword);
					}
				}
			} else {
				connConfiguration.setOverrideRemote(false);
				connConfiguration.setRemoteServerIP("");
				connConfiguration.setRemoteServerPort("");
				connConfiguration.setRemoteServerUser("");
				connConfiguration.setRemoteServerPassword("");
			}

			if (documentWrapper.getDEServerGuid() == null || documentWrapper.getDEServerGuid().isEmpty()) {
				connConfiguration.setDEServerGuid(null);
				connConfiguration.setBundleNameOverride(null);
			} else {
				connConfiguration.setDEServerGuid(documentWrapper.getDEServerGuid());
				connConfiguration.setBundleNameOverride(documentWrapper.getBundleNameOverride());
			}

			ComplexValue outputFilename = connConfiguration.getOutputFilename();
			if ( connConfiguration.getOutputFilename() != null ) {
				connConfiguration.getOutputFilename().setEncodedValue(documentWrapper.getOutputFilename());
				outputFilename.save();
			} else if ( StringUtils.isNotBlank(documentWrapper.getOutputFilename() ) ) {
				outputFilename = new ComplexValue(documentWrapper.getOutputFilename(), UserUtil.getPrincipalUser());
				outputFilename.save();
				connConfiguration.setOutputFilename(outputFilename);
			}
			if ( connConfiguration.getOutputFilename() != null )
				connConfiguration.getOutputFilename().setEncodedValue(ContentObjectContentUtil.translateContentForPersistance(connConfiguration.getOutputFilename().getEncodedValue()));
			
			ComplexValue outputDocumentTitle = connConfiguration.getOutputDocumentTitle();
			if ( connConfiguration.getOutputDocumentTitle() != null ) {
				connConfiguration.getOutputDocumentTitle().setEncodedValue(documentWrapper.getOutputDocumentTitle());
				outputDocumentTitle.save();
			} else if ( StringUtils.isNotBlank(documentWrapper.getOutputDocumentTitle() ) ) {
				outputDocumentTitle = new ComplexValue(documentWrapper.getOutputDocumentTitle(), UserUtil.getPrincipalUser());
				outputDocumentTitle.save();
				connConfiguration.setOutputDocumentTitle(outputDocumentTitle);
			}
			if ( connConfiguration.getOutputDocumentTitle() != null )
				connConfiguration.getOutputDocumentTitle().setEncodedValue(ContentObjectContentUtil.translateContentForPersistance(connConfiguration.getOutputDocumentTitle().getEncodedValue()));
			
			connConfiguration.setValidateProductionBundle(documentWrapper.getValidateProductionBundle());
			connConfiguration.setEscapeTagsInDriverData(documentWrapper.getEscapeTagsInDriverData());
			connConfiguration.setConvertTableBorderPxToPts(documentWrapper.getConvertTableBorderPxToPts());
			connConfiguration.setEvalNotEqualOnMissingTag(documentWrapper.getEvalNotEqualOnMissingTag());
			connConfiguration.setPlayEmptyAggFirstLastVar(documentWrapper.getPlayEmptyAggFirstLastVar());
			connConfiguration.setRemoveZeroFromStyleConnector(documentWrapper.getRemoveZeroFromStyleConnector());
			connConfiguration.setCompTimeParentTagging(documentWrapper.getCompTimeParentTagging());
			connConfiguration.setDataGroupExpressionVarProc(documentWrapper.getDataGroupExpressionVarProc());
			connConfiguration.setScriptVarAppliesUndefined(documentWrapper.getScriptVarAppliesUndefined());
			connConfiguration.setCorrectParagraphTextStyles(documentWrapper.getCorrectParagraphTextStyles());
			connConfiguration.setFixInlineTargetingStyles(documentWrapper.getFixInlineTargetingStyles());
			connConfiguration.setPreserveDataWhitespace(documentWrapper.getPreserveDataWhitespace());
			connConfiguration.setNbspComposedAsSpace(documentWrapper.getNbspComposedAsSpace());
			connConfiguration.setNormalizeImageLibrary(documentWrapper.getNormalizeImageLibrary());
			connConfiguration.setNormalizeEmbeddedContent(documentWrapper.getNormalizeEmbeddedContent());
			connConfiguration.setListStyleControlType(documentWrapper.getListStyleControlType());
			connConfiguration.setColorOutputFormatType(documentWrapper.getColorOutputFormatType());
			connConfiguration.setGmcSpanToTTag(documentWrapper.getGmcSpanToTTag());
			connConfiguration.setBlueUnderlineLinks(documentWrapper.getBlueUnderlineLinks());
			connConfiguration.setUnalteredZonePDFPassthrough(documentWrapper.getUnalteredZonePDFPassthrough());
			connConfiguration.setUseDefaultImage(documentWrapper.getUseDefaultImage());
			
			// force hibernate to deproxy this object
			if ( connConfiguration != null ) {
				connConfiguration = HibernateDeproxyUtil.deproxy(connConfiguration, ConnectorConfiguration.class);

				if (connConfiguration instanceof DialogueConfiguration) {
					DialogueConfiguration dialogueConfig = (DialogueConfiguration) connConfiguration;
					dialogueConfig.setSupportsStyles(documentWrapper.isSupportsStyles());
					dialogueConfig.setLegacyDxfMode(documentWrapper.isLegacyDxfMode());
					dialogueConfig.setMixedDxfTaggedText(documentWrapper.isMixedDxfTaggedText());
					dialogueConfig.setRunTimeDxf(documentWrapper.isRunTimeDxf());
					dialogueConfig.setCompositionFileSet(documentWrapper.getCompositionFileSet());
					dialogueConfig.setCompositionVersion(documentWrapper.getCompositionVersion());
					if (dialogueConfig.getCachedApplicationReport() != null) {
						dialogueConfig.setReport(dialogueConfig.getName());
						dialogueConfig.setReportUpdated(new Date(System.currentTimeMillis()));
						dialogueConfig.saveFiles();
					}
				} else if (connConfiguration instanceof GMCConfiguration) {
					GMCConfiguration gmcConfig = (GMCConfiguration) connConfiguration;
					gmcConfig.setCompositionFileSet(documentWrapper.getCompositionFileSet());
					gmcConfig.setCompositionVersion(documentWrapper.getCompositionVersion());
					gmcConfig.setControlStyles(documentWrapper.getControlStyles());
					gmcConfig.setTextStyleCompositionType(documentWrapper.getTextStyleCompositionType());
					gmcConfig.setParagraphStyleCompositionType(documentWrapper.getParagraphStyleCompositionType());
					gmcConfig.setListStyleCompositionType(documentWrapper.getListStyleCompositionType());
					gmcConfig.setRestrictedQuotes(documentWrapper.getRestrictedQuotes());
				} else if (connConfiguration instanceof EMessagingConfiguration) {
					EMessagingConfiguration eMessagingConfig = (EMessagingConfiguration) connConfiguration;
					if (document.isSmsTouchpoint())
						eMessagingConfig.setCustomerPhoneNumberVariable( documentWrapper.getCustomerPhoneNumberVariable() );
					else if (document.isEmailTouchpoint())
						eMessagingConfig.setCustomerEmailAddressVariable( documentWrapper.getCustomerEmailAddressVariable() );
					
				} else if (connConfiguration instanceof SendmailConfiguration) {
					SendmailConfiguration sendmailConfig = (SendmailConfiguration) connConfiguration;
					sendmailConfig.setCustomerEmailAddressVariable( documentWrapper.getCustomerEmailAddressVariable() );
					sendmailConfig.setOverrideSMTP( documentWrapper.isOverrideSMTP() );
					sendmailConfig.setSmtpHost( documentWrapper.getSmtpHost() );
					sendmailConfig.setSmtpPort( documentWrapper.getSmtpPort() );
					sendmailConfig.setSmtpSecurity( documentWrapper.getSmtpSecurity() );
					sendmailConfig.setSmtpAccount( documentWrapper.getSmtpAccount() );
					sendmailConfig.setSmtpPassword( documentWrapper.getSmtpPassword() );
					sendmailConfig.setSmtpCustomHeader( documentWrapper.getSmtpCustomHeader() );
				} else if (connConfiguration instanceof ExactTargetConfiguration) {
					ExactTargetConfiguration exactTargetConfig = (ExactTargetConfiguration) connConfiguration;
					exactTargetConfig.setCustomerEmailAddressVariable( documentWrapper.getCustomerEmailAddressVariable() );
					exactTargetConfig.setCustomerKeyVariable( documentWrapper.getCustomerKeyVariable() );
				} else if (connConfiguration instanceof ClickatellConfiguration){
					ClickatellConfiguration clickatellConfig = (ClickatellConfiguration) connConfiguration;
					clickatellConfig.setCustomerPhoneNumberVariable(documentWrapper.getCustomerPhoneNumberVariable());
				} else if (connConfiguration instanceof FtpConfiguration){
					FtpConfiguration ftpConfig = (FtpConfiguration) connConfiguration;
					ComplexValue fileComplextValue = ftpConfig.getRecipientFileComplexValue();
					if(ftpConfig.getRecipientFileComplexValue() != null){
						ftpConfig.getRecipientFileComplexValue().setEncodedValue(documentWrapper.getRecipientFileName());
					} else if(StringUtils.isNotBlank(documentWrapper.getRecipientFileName() )){
						fileComplextValue = new ComplexValue(documentWrapper.getRecipientFileName(), UserUtil.getPrincipalUser());
						fileComplextValue.save();
						ftpConfig.setRecipientFileComplexValue(fileComplextValue);
					}
					ftpConfig.setServerId(documentWrapper.getServerId());
					ftpConfig.setWebURL(documentWrapper.getWebURL());
					ftpConfig.setRecipientFileLocation(documentWrapper.getRecipientFileLocation());
					ftpConfig.setIsEmbedded(documentWrapper.getIsEmbedded());
				} else if (connConfiguration instanceof NativeCompositionConfiguration) {
					NativeCompositionConfiguration nativeConfig = (NativeCompositionConfiguration) connConfiguration;
					nativeConfig.setAdvancedComposition(documentWrapper.isAdvancedComposition());
					nativeConfig.setApplyFillableForms(documentWrapper.isApplyFillableForms());
					nativeConfig.setOutputFileType(documentWrapper.getOutputFileTypeId());
					nativeConfig.setDuplexOutput(documentWrapper.getDuplexOutput());
					nativeConfig.setStartsOnOddPage(documentWrapper.getStartsOnOddPage());
				} else if (connConfiguration instanceof SefasConfiguration) {
					SefasConfiguration sefasConfig = (SefasConfiguration) connConfiguration;
					sefasConfig.setCompositionFileSet(documentWrapper.getCompositionFileSet());
					sefasConfig.setCompositionVersion(documentWrapper.getCompositionVersion());
					sefasConfig.setOutputFileType(documentWrapper.getOutputFileTypeId());
					sefasConfig.setDuplexOutput(documentWrapper.getDuplexOutput());
					sefasConfig.setStartsOnOddPage(documentWrapper.getStartsOnOddPage());
					sefasConfig.setTemplateControl(documentWrapper.getTemplateControl());
					sefasConfig.setAccessibility(documentWrapper.getAccessibility());
					sefasConfig.setLinespacePosition(documentWrapper.getLinespacePosition());
					sefasConfig.setTablePaddingInPts(documentWrapper.getTablePaddingInPts());
				} else if (connConfiguration instanceof MPHCSConfiguration) {
					MPHCSConfiguration mphcsConfig = (MPHCSConfiguration) connConfiguration;
					mphcsConfig.setCompositionFileSet(documentWrapper.getCompositionFileSet());
					mphcsConfig.setCompositionVersion(documentWrapper.getCompositionVersion());
					mphcsConfig.setOutputFileType(documentWrapper.getOutputFileTypeId());
					mphcsConfig.setDuplexOutput(documentWrapper.getDuplexOutput());
					mphcsConfig.setStartsOnOddPage(documentWrapper.getStartsOnOddPage());
					mphcsConfig.setTemplateControl(documentWrapper.getTemplateControl());
					mphcsConfig.setAccessibility(documentWrapper.getAccessibility());
					mphcsConfig.setLinespacePosition(documentWrapper.getLinespacePosition());
					mphcsConfig.setTablePaddingInPts(documentWrapper.getTablePaddingInPts());
				}
			}
			connConfiguration.setUpdatedBy(UserUtil.getPrincipalUserId());
			HibernateUtil.getManager().saveObject(connConfiguration, false);	
		}
	}

	private void copyConfiguration(Document fromDocument, Document toDocument) throws Exception {
		// The composition pack needs to maintain, the reference needs to be saved before the config to be deleted.
		ConnectorConfiguration toConfig = toDocument.getConnectorConfiguration();
		CompositionFileSet compPack = null;
		if ( toConfig instanceof DialogueConfiguration ) {
			DialogueConfiguration dialogueConfig = (DialogueConfiguration) toConfig;
			compPack = dialogueConfig.getCompositionFileSet();
		} else if ( toConfig instanceof GMCConfiguration ) {
			GMCConfiguration gmcConfig = (GMCConfiguration) toConfig;
			compPack = gmcConfig.getCompositionFileSet();
		} else if ( toConfig instanceof SefasConfiguration ) {
			SefasConfiguration sefasConfig = (SefasConfiguration) toConfig;
			compPack = sefasConfig.getCompositionFileSet();
		} else if ( toConfig instanceof MPHCSConfiguration ) {
			MPHCSConfiguration mphcsConfig = (MPHCSConfiguration) toConfig;
			compPack = mphcsConfig.getCompositionFileSet();
		}
		toConfig.delete();
		
		ConnectorConfiguration fromConfig = fromDocument.getConnectorConfiguration();
		Connector connector = Connector.findById(fromConfig.getConnector().getId());
		if ( connector != null ) {
			ConnectorConfiguration config = connector.createConfiguration();
			// create qulificationOutput
			QualificationOutput qualificationOutput = QualificationOutput.findById(fromConfig.getQualificationOutput().getId());
			config.setQualificationOutput(qualificationOutput);
			if ( config != null ) {
				config.setCustomerDriverInputFileName(fromConfig.getCustomerDriverInputFileName());
				config.setPubFile(fromConfig.getPubFile());
				config.setDocument(toDocument);
				config.setAppliedDeVersion(fromConfig.getAppliedDeVersion());
				config.setInputCharacterEncoding(fromConfig.getInputCharacterEncoding());
				config.setOutputCharacterEncoding(fromConfig.getOutputCharacterEncoding());
				
				config.setPreQualEngineScript(fromConfig.getPreQualEngineScript());
				config.setPostConnectorScript(fromConfig.getPostConnectorScript());
				config.setPostQualEngineScript(fromConfig.getPostQualEngineScript());
				
				config.setExecuteInCloudTest(fromConfig.isExecuteInCloudTest());
				config.setExecuteInCloudPreview(fromConfig.isExecuteInCloudPreview());
				config.setExecuteInCloudProof(fromConfig.isExecuteInCloudProof());
				config.setExecuteInCloudSimulation(fromConfig.isExecuteInCloudSimulation());
				config.setApplyFilenamesToBundledImages(fromConfig.getApplyFilenamesToBundledImages());
				config.setFilenameSeparatorForBundledImages(fromConfig.getFilenameSeparatorForBundledImages());
				config.setPlayMessageOnEmptyVar(fromConfig.getPlayMessageOnEmptyVar());
				config.setOverrideRemote(fromConfig.isOverrideRemote());
				config.setRemoteServerIP(fromConfig.getRemoteServerIP());
				config.setRemoteServerPort(fromConfig.getRemoteServerPort());
				config.setRemoteServerUser(fromConfig.getRemoteServerUser());
				config.setRemoteServerPassword(fromConfig.getRemoteServerPassword());
				config.setDEServerGuid(fromConfig.getDEServerGuid());
				config.setListStyleControlType(fromConfig.getListStyleControlType());
				config.setColorOutputFormatType(fromConfig.getColorOutputFormatType());
				
				ComplexValue outputFilenameClone = fromConfig.getOutputFilename() != null ? (ComplexValue)fromConfig.getOutputFilename().clone() : null;
				config.setOutputFilename(outputFilenameClone);
				ComplexValue outputDocumentTitleClone = fromConfig.getOutputDocumentTitle() != null ? (ComplexValue)fromConfig.getOutputDocumentTitle().clone() : null;
				config.setOutputDocumentTitle(outputDocumentTitleClone);

				if ( config instanceof DialogueConfiguration ) {
					DialogueConfiguration dialogueConfig = (DialogueConfiguration) config;
					DialogueConfiguration fromDialogueConfig = (DialogueConfiguration) fromConfig;
					dialogueConfig.setCompositionVersion(fromDialogueConfig.getCompositionVersion());
					dialogueConfig.setSupportsStyles(fromDialogueConfig.isSupportsStyles());
					dialogueConfig.setLegacyDxfMode(fromDialogueConfig.isLegacyDxfMode());
					dialogueConfig.setMixedDxfTaggedText(fromDialogueConfig.isMixedDxfTaggedText());
					dialogueConfig.setRunTimeDxf(fromDialogueConfig.isRunTimeDxf());
					dialogueConfig.setCompositionFileSet(compPack);
				} else if ( config instanceof GMCConfiguration ) {
					GMCConfiguration gmcConfig = (GMCConfiguration) config;
					GMCConfiguration fromGmcConfig = (GMCConfiguration) fromConfig;
					gmcConfig.setCompositionVersion(fromGmcConfig.getCompositionVersion());
					gmcConfig.setCompositionFileSet(compPack);
					gmcConfig.setControlStyles(fromGmcConfig.getControlStyles());
				} else if ( config instanceof NativeCompositionConfiguration ) {
					NativeCompositionConfiguration nativeConfiguration = (NativeCompositionConfiguration) config;
					NativeCompositionConfiguration fromNativeConfig = (NativeCompositionConfiguration) fromConfig;
					nativeConfiguration.setOutputFileType(fromNativeConfig.getOutputFileType());
					nativeConfiguration.setDuplexOutput(fromNativeConfig.getDuplexOutput());
					nativeConfiguration.setStartsOnOddPage(fromNativeConfig.getStartsOnOddPage());
				} else if ( config instanceof SefasConfiguration ) {
					SefasConfiguration sefasConfiguration = (SefasConfiguration) config;
					SefasConfiguration fromSefasConfig = (SefasConfiguration) fromConfig;
					sefasConfiguration.setCompositionVersion(fromSefasConfig.getCompositionVersion());
					sefasConfiguration.setCompositionFileSet(compPack);
					sefasConfiguration.setOutputFileType(fromSefasConfig.getOutputFileType());
					sefasConfiguration.setDuplexOutput(fromSefasConfig.getDuplexOutput());
					sefasConfiguration.setStartsOnOddPage(fromSefasConfig.getStartsOnOddPage());
					sefasConfiguration.setTemplateControl(fromSefasConfig.getTemplateControl());
				} else if ( config instanceof MPHCSConfiguration ) {
					MPHCSConfiguration mphcsConfiguration = (MPHCSConfiguration) config;
					MPHCSConfiguration fromMphcsConfig = (MPHCSConfiguration) fromConfig;
					mphcsConfiguration.setCompositionVersion(fromMphcsConfig.getCompositionVersion());
					mphcsConfiguration.setCompositionFileSet(compPack);
					mphcsConfiguration.setOutputFileType(fromMphcsConfig.getOutputFileType());
					mphcsConfiguration.setDuplexOutput(fromMphcsConfig.getDuplexOutput());
					mphcsConfiguration.setStartsOnOddPage(fromMphcsConfig.getStartsOnOddPage());
					mphcsConfiguration.setTemplateControl(fromMphcsConfig.getTemplateControl());
				}
				config.save(true);
				updateXSLTFile(toDocument, config);
				toDocument.setConnectorConfiguration(config);
			}
		}		
	}
	
	private void createConfiguration(DocumentChannelEditWrapper docChanEditWrapper) throws Exception {
		Document document = docChanEditWrapper.getDocument();
		if ( docChanEditWrapper.getConnectorId() > 0 ) {
			Connector connector = Connector.findById(docChanEditWrapper.getConnectorId());
			if ( connector != null ) {
				ConnectorConfiguration config = connector.createConfiguration();
				// create qulificationOutput
				QualificationOutput qualificationOutput = QualificationOutput.findById(docChanEditWrapper.getQualificationOutputId());
				config.setQualificationOutput(qualificationOutput);
				if ( config != null ) {
					config.setFileName(docChanEditWrapper.getXmlFileName());
					config.setFormatType(docChanEditWrapper.getFormatType());
					config.setCustomerDriverInputFileName(docChanEditWrapper.getCustomerDriverInputFilename());
					config.setPubFile(docChanEditWrapper.getPubFileName());
					config.setWorkflowFile(docChanEditWrapper.getWorkflowFileName());
					config.setDocument(document);
					config.setAppliedDeVersion(docChanEditWrapper.getAppliedDeVersion());
					config.setInputCharacterEncoding(docChanEditWrapper.getInputCharacterEncoding());
					config.setOutputCharacterEncoding(docChanEditWrapper.getOutputCharacterEncoding());
					config.save(true);
					updateXSLTFile(document, config);
					document.setConnectorConfiguration(config);
				}
			}
		}
	}

	private void updateXSLTFile(Document doc, ConnectorConfiguration config) throws Exception {
		if (config instanceof GenericConnectorConfiguration) {
			GenericConnectorConfiguration genericConfig = (GenericConnectorConfiguration) config;
			if (genericConfig.isXMLFileType() && doc.getConnectorConfigWrapper() != null) {
				uploadPreAndPostProcessXSLTFile(doc.getConnectorConfigWrapper(), genericConfig);
			}
		}
	}

	private void uploadPreAndPostProcessXSLTFile(ConnecterConfigurationWrapper configWrapper, GenericConnectorConfiguration config) throws Exception {

		if (configWrapper.isDisablePreXSLTFlag()) {
			config.deletePreXSLTFile();
		}
		if (configWrapper.getPreXSLTFile() != null && !configWrapper.getPreXSLTFile().isEmpty()) {
			config.updatePreProcessXSLTFile(configWrapper.getPreXSLTFile());
		}
		if (configWrapper.isDisablePostXSLTFlag()) {
			config.deletePostXSLTFile();
		}
		if (configWrapper.getPostXSLTFile() != null && !configWrapper.getPostXSLTFile().isEmpty()) {
			config.updatePostProcessXSLTFile(configWrapper.getPostXSLTFile());
		}
		config.clearFileLocation();
	}

	private void processConnectorChange(DocumentChannelEditWrapper docChanEditWrapper) throws Exception {
		long connectorId = docChanEditWrapper.getConnectorId();
		Document document = docChanEditWrapper.getDocument();
		ConnectorConfiguration connConfiguration = document.getConnectorConfiguration();
		// force hibernate to deproxy this object
		if (connConfiguration != null) {
			connConfiguration = HibernateDeproxyUtil.deproxy(connConfiguration, ConnectorConfiguration.class);
			long oldConnectorId = connConfiguration.getConnector().getId();
			if ( connectorId != oldConnectorId && connectorId > 0 ) {
				connConfiguration.delete();
				createConfiguration(docChanEditWrapper);
			} else {
				// update configuration
				connConfiguration.setFileName(docChanEditWrapper.getXmlFileName());
				connConfiguration.setFormatType(docChanEditWrapper.getFormatType());
				connConfiguration.setCustomerDriverInputFileName(docChanEditWrapper.getCustomerDriverInputFilename());
				connConfiguration.setPubFile(docChanEditWrapper.getPubFileName());
				connConfiguration.setWorkflowFile(docChanEditWrapper.getWorkflowFileName());
				connConfiguration.setAppliedDeVersion(docChanEditWrapper.getAppliedDeVersion());
				connConfiguration.setInputCharacterEncoding(docChanEditWrapper.getInputCharacterEncoding());
				connConfiguration.setOutputCharacterEncoding(docChanEditWrapper.getOutputCharacterEncoding());
				updateXSLTFile(document, connConfiguration);
			}
		} else {
			// create new configuration
			createConfiguration(docChanEditWrapper);
		}
	}
}