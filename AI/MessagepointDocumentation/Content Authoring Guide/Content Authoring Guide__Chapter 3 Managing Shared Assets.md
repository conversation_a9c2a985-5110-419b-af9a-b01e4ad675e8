




You are here: 

# Chapter 3: Managing Shared Assets

Messagepoint has centralized repositories of shared text and images. These allow you to create and store snippets of text or images across your messages and touchpoints.

Smart text is shared static or dynamic text content that can be used within messages across different touchpoints. They are accessed on the Smart Text page which acts as a centralized repository outside of the touchpoints.

Smart canvas allows you to create libraries of freeform canvases that you can enter directly into your messages. Smart canvas can only be used in freeform zones or sections.

Smart text and Smart canvas can be:

  * Static. It contains the same static content.

  * Dynamic. Its content varies depending on conditions - for example, location. A selector is used _for determining_ the content.

  * Global. It is available to all touchpoints. It has no touchpoint specific attributes:



  * It cannot have advanced features.

  * It cannot be dynamic.

  * It cannot have targeting.

  * It cannot include variables, smart canvas, text styles or paragraph styles in content.




You can:

  * Apply font and paragraph styles to content.

  * Apply targeting and timing.

  * Use smart text in standard text zones as well as multi-part text zones.

  * Use smart canvas in freeform, Messagepoint Composition, Messagepoint Composer, and Sefas zones.

  * Reference smart text in other smart text.




The Image Library is a centralized repository for images. It allows you to centrally store, reuse and manage images across messages and touchpoints. This significantly reduces the effort and error associated with managing the same images across multiple assets, thus ensuring consistency and compliance.

Images can be:

  * Static. They contain the same static content.

  * Dynamic. Their content varies depending on conditions - for example, location. A selector is used for determining the content.




Image files from the Image Library can be used in both graphic and multi-part message zones. Both of these are accessed on the Content menu.

To access the shared asset repositories:

  1. Open the Content menu.

  2. Click:

  3. Smart Text to access your smart text assets.

  4. Image Library to access the images stored in the image library.



﻿




You are here: 

## Using the shared asset pages

You create and manage your smart text on the Smart Text page and your shared images on the Image library page, which are found in the Content menu. The pages can be either touchpoint-centric or global. Touchpoint-centric means that the asset list is filtered by touchpoint. You can change the touchpoint by using the touchpoint selector. The Global View lists all the smart text.

The assets are listed in the appropriate shared asset list. The list is similar to the Message list. It tells you the name of the asset, its state, to whom it is assigned, status, type, and whether timing and targeting have been attached.

Note: The Image Library page does not have columns for timing and targeting as you cannot assign timing and targeting to images.

![Smart text list screenshot](../Resources/Images/Content Authoring Guide/Using the shared asset pages_577x404.png)

The Shared asset pages lists the appropriate shared asset. They can be filtered by touchpoint, assignment, and state.
﻿




You are here: 

### Switching between name or content view

You can view the asset list either by

  * Name view. This lists the asset by name and consists of several fields. 

  * Content view. This lists the asset, di _splaying their content. Information about asset - state, are accessed by hovering t_ he cursor over the appropriate icon. 




To switch between name or content views:

  * Click ![View toggle icon](../Resources/Images/Content Authoring Guide/Switching between name or content view_19x19.png)

  * Tip: When in content view, you can hover or double-click on embedded objects to see a pop-up for the embedded content. You can view or edit from there.



﻿




You are here: 

### Description of the shared asset list in the name view

The list consists of the following fields

Field |  Description  
---|---  
Name |  The name of the asset.  
State |  State of the asset: ![Working copy icon](../Resources/Images/Content Authoring Guide/Moving messages to a different zone_10x10.jpg) Working copy. ![Active icon](../Resources/Images/Content Authoring Guide/Description of the list in the name view_10x10.png) Active. ![Pending icon](../Resources/Images/Content Authoring Guide/Description of the list in the name view_1_1_19x19.jpg) Pending <approval/translation step name>. ![](../Resources/Images/Content Authoring Guide/Description of the list in the name view_1_1_10x10.png) Archived. Assets may be in more than one state and have more than one icon showing. For example, you may have a smart text asset in both active and working copy states. You can open up the object in either state by clicking the appropriate icon.  
Assigned |  The user to whom the asset is currently assigned. (Working copy or in approval workflow only)  
Type |  The type of asset: _-_ <Dash >. Indicates that the asst is _Static_. _D_. Indicates that the smart text is _Dynamic_.  
![Timing icon](../Resources/Images/Content Authoring Guide/Description of the list in the name view_1_1_19x19.jpg) |  ![Asset has timing icon](../Resources/Images/Content Authoring Guide/Description of the list in the name view_1.png). Indicates that the asset has timing attached.  
![Targeting icon](../Resources/Images/Content Authoring Guide/Description of the list in the name view_2_19x19.jpg) |  ![Asset has targeting icon](../Resources/Images/Content Authoring Guide/Description of the list in the name view_1.png). Indicates that the asset is targeted.  
  
The list can be filtered and sorted. You can also hide columns.

When you click the ![](../Resources/Images/Content Authoring Guide/The Exact Matches tile_10x10.png) in the name column, an embedded table opens. It consists of three fields:

Column |  Description  
---|---  
![Workflow status icon](../Resources/Images/Content Authoring Guide/Description of the list in the name view_3_29x19.jpg) |  Click to view the workflow status.  
Tags |  These are the tags that have been entered on the _Details_ page. For more information, see _[Using tags and the tag cloud](Using tags and the tag cloud.htm#_Ref86396737)_.  
Created |  The date that the asset was created.
﻿




You are here: 

### Description of the shared asset list in the content view

The asset list in the Content view consists of the following fields

Field |  Description  
---|---  
Content |  For smart text:

  * The name of the smart text in a small font above the content of the smart text.
  * The smart text status and type, touchpoint name and assigned to is found under the image.

For images:

  * The name of the image in a small font above the image.
  * The image status and type, touchpoint name and assigned to is found under the image.


﻿




You are here: 

### Viewing asset in a pop-up window

You can view the asset in a pop-up window directly from the asset list. This allows you to view the content of an asset without having to open it.

To view an asset in a pop-up window:

  * Hover the cursor over the name of the asset.




A pop-up window displaying the content opens.
﻿




You are here: 

### Managing assets with the toolbar

You can use either the toolbar buttons or the right-click menu to manage your assets. 

_Note_ : Only actions available to the asset will be enabled on the toolbar buttons. Only actions available to the asset will appear in the right-click menu.

The following commands are available using the toolbar buttons:

Button |  Action  
---|---  
Edit |  Edits the asset.  
Clone |  Clones the asset.  
Audit |  Creates an audit report. The Asset Audit Report provides detailed information on a specific asset. The report is broken into the following sections:

  * Report Request Summary.
  * Details.

For more information, see the Touchpoint administrator guide.  
More |  Create working copy. Creates a working copy from an active copy. Discard working copy. Deletes the working copy.  
Archive. Archives the asset.  
Reassign to user. Reassigns the asset to another user.  
Activate. Activates the asset. If you have an approval workflow, Activate is replaced by:

  * Release for Approval
  * Approve.
  * Reject.

For more information, see [Activating assets and variants .](file:///C:/Education Resources/Software Resources/23.1 Software Guides/Content Authoring Guide/Content Authoring Guide.docx#bookmark797)  
Associate Touchpoints. Allows you to associate the asset with other touchpoints, even if the asset is active.  
Where Used. Creates a report showing where the asset is being used.  
  
The following commands are available using the right-click menu:

Action |  Description  
---|---  
Create working copy |  If the asset is active, choose this option to edit the asset.  
Edit |  Edits the asset.  
Activate |  Activates the working copy asset. (This appears if there is no approval workflow for the touchpoint.)  
Release for approval |  This option replaces Activate if there is an approval workflow. Action will initiate the first step in the approval workflow.  
Approve |  Approves a step in the approval workflow. (Approve and override appears for workflow owners.) After final approval, asset is active (activated).  
Reject |  Rejects a step in the approval workflow. (Reject and override appears for workflow owners.)  
Abort |  Stops the approval workflow. Asset goes back to working copy state.  
Retry |  Re-submits the failed translations handled by a service.  
Discard Working Copy |  Deletes the working copy. If no active copy exists, the asset is deleted.  
Menu separator line  
Reassign |  Reassign the asset to another user.  
Clone |  Clones the asset.  
Create local |  Creates a local asset by cloning the active version.  
Move to local |  Changes the global asset to a local asset.  
Associate touchpoints |  Assigns the local asset to be available to the specified touchpoint(s).  
Archive |  Archive the active asset.  
Delete Archive |  Delete the archived asset. (Visible only in the archived assets view.)  
Where Used |  Provides report of where the asset is being used.  
Menu separator line  
Calculate hash |  Recalculate the value that is added to the asset to help identify it  
Add Task |  Add a task to the asset.
﻿




You are here: 

## Creating smart text

Smart text is created and managed in the Smart Text page. Although smart text is not created on the touchpoints page, you must associate the text with touchpoints to make it available for those touchpoints.

Note: If you are using metadata templates, you add and fill the template when creating the smart text,

Creating smart text consists of four steps:

  1. Add the metadata form. (optional)

  2. Complete the Details page.

  3. Add the content.

  4. Add the targeting. (Optional)



﻿




You are here: 

### Types of smart text

There are two types of smart text assets:

  * Static. It contains the same static content.

  * Dynamic. Its content varies depending on conditions - for example, location. A _selector_ is used for determining the content.




### Warnings when the smart text type changes

You can change the message type but there are implications if the dynamic has variants:

  * If the dynamic smart text variants ever had custom content, including archive and active versions, you will see a warning message like the one below. The versions affected will be mentioned.

![Warning screenshot](../Resources/Images/Content Authoring Guide/Warnings when the message type changes_572x111.png)



﻿




You are here: 

## Completing the Shared Asset Details page

The Details page contains the properties of the smart text. After completing the fields on the page and you click Continue, Messagepoint saves the page and opens the Content page.

To complete the Details page:

  1. Go to Content > Content > Smart Text.



  2. Click Add.




![smart text details](../Resources/Images/Content Authoring Guide/Completing the details page_shared_3_576x236.png)

  3. If you are using metadata, add the metadata form: (Optional)



  * Select the form from the Optional metadata drop-down list.

  * Click >Continue.



  1. Enter the Name of the content.




Note: Double spaces are not supported in asset names.

  5. Enter any Tags. (Optional)

  6. Set the Allow use of variables slide toggle to:



  * Enabled.

  * Disabled. If disabled, you cannot insert into the content any variable or other content with variables.



  * The hover tip says “Allowing variables to be used may restrict this content to specific touchpoints. Disallowing variables means this content can be used in all touchpoints.”



  1. To make the smart text dynamic, you must:



  * Select the Selector from the Varied By drop-down list.

  * Add variants and selection data.




Note: You cannot disable Allow use of variables and select a selector for dynamic variants at the same time.

For more information, see [Managing dynamic assets](Managing dynamic assets.htm#_Ref86403061).

  1. Enter a Description of the content. (Optional)

  2. From the Content drop-down list, select Text.

  3. Select the Usage by clicking the appropriate button:



  * Message. For use in Messagepoint text messages.

  * Connected. For use in Connected text messages. There are some features that can be applied only to Connected smart text. For more information, see the Connected administrator guide.

  * Unrestricted. For use in either Messagepoint or Connected text messages.



  1. Enable the following Components by clicking the appropriate button:



  * Tables.

  * Forms.

  * Barcodes.

  * Content menus.




Note: The “Content menus” component is optional for smart text usages of Message and Unrestricted. It is always enabled for Connected smart text, so the component does not appear when the usage is Connected. Once the smart text is saved, the component is locked.

Note: Smart text with these components enabled can only be used in messages found in zones that allow these components. For example, you cannot place smart text with tables in a zone that is not enabled for tables.

  1. Check Render as Tagged Text to restrict the addition of new tables, links, and alternate text.

This option only appears if there is a touchpoint with the Mixed DXF/Tagged Text Mode selected in the Channel Configuration.

Existing tables, links and alternate text may not appear correctly in the output.

  2. Complete the metadata fields if you have attached a metadata template.

  3. Select the touchpoint(s) for which you want the content to be accessible:



  * The touchpoint that you are on when you create the embedded content is automatically selected when you create the content.



  * To add one touchpoint at a time:

  * Drag the touchpoints from the Available Touchpoints field to the Selected Touchpoints field.

  * To add multiple touchpoints at once:

  * Click the appropriate check boxes in the Available Touchpoints field.

  * Drag the touchpoints to the Selected Touchpoints field.



  1. Set the Timing (Optional):



  * Click the Start Date field to enter the first date that the asset is available for use. (Optional)

  * Click the End Date field to enter the last date that the asset is available for use. (Optional)

  * Select the Repeat Each Year? check box to make the asset available on the same dates in following years. (Optional)




Note: Timing is based on the date that the job bundle is created and not the run date.

  1. Set the Delivery options:



  * If the targeting or timing of this asset does not apply for a recipient, indicate what happens to a message that contains the smart text. Select the appropriate action on the slide toggle:

  * Send the message anyway

  * Do not send the message.



  1. Enter any Comments. (Optional)

  2. Click:

  3. Continue to save and go to the Content page.

  4. Save to save and return to the Smart Text list.




_Note_ : If you click the _Save_ drop-down arrow, you can click:

  * _Save & View_. Smart Text is saved, and you are on the View smart text page.

  * _Save & Go to list_. Smart Text is saved, and you go to the Messages list.



﻿




You are here: 

### Completing the Details page for markup smart text

Markup smart text is designed to support markup authoring and the markup is passed to composition as is. As result all formatting tags must be added manually to the content. Markup smart text can be any type of smart text.

In a DXF-enabled touchpoint, markup smart text:

  * May be used in any Exstream zone content.

  * Only has the smart text name displayed in the message editor, the smart text content _is not_ rendered.

  * Content is directly passed through to the job bundle.




Note: Authored DXF content must explicitly define all styling and the snippet must be well formed. DXF smart text does not inherit styling from the message.

Note: DXF is not supported on a mainframe.

In a Quadient touchpoint, Markup smart text can only be used in markup zones. Quadient markup zones can only use Markup smart text, they cannot use Text type smart text.

Smart text content is displayed in a Quadient markup zone.

To create markup smart text, you must select Markup in the Content drop-down list; it cannot be text.

Note: If the smart text is migrated, it defaults to Text and has to be reset as Markup.

To create Markup smart text:

  1. Go to Content > Content > Smart Text.

  2. Click Add.

  3. If you are using metadata, add the metadata form: (Optional)



  * Select the form from the Optional metadata drop-down list.

  * Click _Continue_.



  1. Enter the Name of the content.

  2. Enter any Tags. (Optional)

  3. Set the _Allow use of variables_ slide toggle to:



  * _Enabled_.

  * _Disabled_. If disabled:

  * You cannot insert any variable or other content with the variables into the content.

  * This content object (smart text) is touchpoint independent, and it can be used in any touchpoint.

  * The hover tip says “Allowing variables to be used may restrict this content to specific touchpoints. Disallowing variables means this content can be used in all touchpoints.”



  1. To make the smart text dynamic, you must:



  * Select the _Selector_ from the _Varied By_ drop-down list.

  * Add variants and selection data.




Note: You cannot disable _Allow use of variables_ and select a selector for dynamic variants at the same time

  1. Enter a _Description_ of the content. (Optional)

  2. From the _Content_ drop-down list, select _Markup_ :

  3. Select the _Usage_ by clicking the appropriate button:



  * _Message_. For use in Messagepoint text messages.

  * _Connected_. For use in Connected text messages. There are some features that can be applied only to Connected smart text. For more information, see the Connected administrator guide.

  * _Unrestricted_. For use in either Messagepoint or Connected text messages.



  1. Enable the following Components by clicking the appropriate button:



  * Content menus.




Note: This component is optional for unrestricted smart text. It is always enabled for Connected smart text, and the slide toggle does not exist for Connected smart text.

  1. Complete the metadata fields if you have attached a metadata template.

  2. Select the touchpoint(s) for which you want the content to be accessible:



  * The touchpoint that you are on when you create the embedded content is automatically selected when you create the content.



  * To add one touchpoint at a time:

  * Drag the touchpoints from the _Available Touchpoints_ field to the _Selected Touchpoints_ field.

  * To add multiple touchpoints at once:

  * Click the appropriate check boxes in the _Available Touchpoints_ field.

  * Drag the touchpoints to the _Selected Touchpoints_ field.



  1. Set the _Timing_ (Optional):



  * Click the _Start Date_ field to enter the first date that the asset is available for use. (Optional)

  * Click the _End Date_ field to enter the last date that the asset is available for use. (Optional)

  * Select the _Repeat Each Year?_ check box to make the asset available on the same dates in following years. (Optional)




Note: Timing is based on the date that the job bundle is created and not the run date. If the smart text is not appearing in the output, check the date range.

  15. Set the Delivery options:




If the targeting or timing of this asset do not apply for a recipient, indicate what happens to a message that contains the smart text. Select the appropriate action on the slide toggle:

  * Send the message anyway.

  * Do not send the message.



  1. Enter any Comments.

  2. Click:



  * Continue to save and go to the Content page.

  * Saveto save and return to the Smart Text list.




_Note:_ If you click the _Save_ drop-down arrow, you can click:

  * _Save & View_. Smart Text is saved, and you are on the View smart text page.

  * _Save & Go to list_. Smart Text is saved, and you go to the Messages list.



  * Cancel & View to return to view mode when editing an existing asset.

  * Cancel & Go to List to return to the asset list without creating a new asset.



﻿




You are here: 

### Completing the Details page for compound smart text

Compound smart text is used to dynamically insert repeating data values in message content. Before you can create the compound smart text, you require:

  * Aggregated variable. The data type must be "string". The aggregation level must be higher than the data group types. 

  * Selector. The selector uses the aggregated variable. 

  * For more information, see the Touchpoint administrator guide.




To create compound smart text:

  1. Go to Content > Content > Smart Text.

  2. Click Add.

  3. If you are using metadata, add the metadata form: (Optional)



  * Select the form from the Optional metadata drop-down list.

  * Click Continue.



  1. Enter the Name of the content.

  2. Enter any Tags. (Optional)

  3. Set the Allow use of variables slide toggle to Enabled.



  * The hover tip says “Allowing variables to be used may restrict this content to specific touchpoints. Disallowing variables means this content can be used in all touchpoints.”



  1. Select the Selector containing the aggregated variable from the Varied By drop-down list.




Note: You must add the variants and their data to the smart text asset.

  1. Enter a Description of the content. (Optional)

  2. Select the content type from the Content drop-down list.



  1. If you have the _Connected_ module enabled, select the _Usage_ by clicking the appropriate button:



  * _Message_. For use in Messagepoint text messages.

  * _Connected_. For use in Connected text messages. There are some features that can be applied only to Connected smart text. For more information, see the _Connected administrator guide_.

  * _Unrestricted_. For use in either Messagepoint or Connected text messages.



  1. Enable the following _Components_ by clicking the appropriate button:



  * Tables.

  * Forms.

  * Barcodes.

  * Content menus.




Note: The “Content menus” component is optional for unrestricted smart text. It is always enabled for Connected smart text, and the slide toggle does not exist for Connected smart text.

Note: Smart text with these enabled can only be used in messages found in zones that allow these parameters. For example, you cannot place smart text with tables in a zone that is not enabled for tables.

  1. Complete the metadata fields if you have attached a metadata template.

  2. Select the touchpoint(s) for which you want the content to be accessible:



  * The touchpoint that you are on when you create the embedded content is automatically selected when you create the content.

  * To add one touchpoint at a time:

  * Drag the touchpoints from the _Available Touchpoints_ field to the _Selected Touchpoints_ field.

  * To add multiple touchpoints at once:

  * Click the individual touchpoints in the _Available Touchpoints_ field.

  * Drag the touchpoints to the _Selected Touchpoints_ field.

  * To remove a touchpoint that you added by accident, double-click it.



  1. Set the _Timing_ (Optional):



  * Click the _Start Date_ field to enter the first date that the asset is available for use. (Optional)

  * Click the _End Date_ field to enter the last date that the asset is available for use. (Optional)

  * Select the _Repeat Each Year?_ check box to make the asset available on the same dates in following years. (Optional)




Note: Timing is based on the date that the job bundle is created and not the run date.

  1. Set the _Delivery options_ :




If the targeting or timing of this asset do not apply for a recipient, indicate what happens to a message that contains the smart text. Select the appropriate action on the slide toggle:

  * Send the message anyway

  * Do not send the message.



  1. Enter any _Comments_.

  2. Click:



  * _Continue_ to save and go to the _Content_ page.

  * _Save_ to save and return to the _Smart Text_ list.




Note: If you click the _Save_ drop-down arrow, you can click:

  * _Save & View_. Smart Text is saved, and you are on the View smart text page.

  * _Save & Go to list_. Smart Text is saved, and you go to the Messages list.



﻿




You are here: 

## Adding the smart text content

Once you have created the smart text asset, you must add its content. Adding the content to a smart text asset follows the same procedures as adding content to a text message.

You can add variables, symbols, and other smart text assets to the content. You can also set styles and paragraph styles to the content if the styles have been set in the touchpoint.

Note: Text Content may not contain the following:

  * %% - double percentage signs.

  * | - pipe.




![adding smart text content](../Resources/Images/Content Authoring Guide/Adding the smart text content_2_576x156.png)

The smart text content page contains the text editor, along with some additional settings that are smart text specific. If you want to add targeting to the smart text, click Continue to save and open the Targeting page.
﻿




You are here: 

### Adding content

To add the content:

  1. In the editor window, enter your content by either:



  * Typing the text into the editor.

  * Pasting text from another source.



  2. Apply a Text style to text.

  3. Apply a Paragraph style to text.

  4. Insert a Smart text asset.




_Note:_ When referencing smart text in smart text, make sure that they are not referencing each other. (This occurs if you insert smart text A in smart text B and then insert smart text B in smart text A.) If this occurs, Messagepoint throws an error.

_Note:_ The nested smart text does not have to be associated with the touchpoint in order to be included in the job.

_Note:_ Smart text must be activated before it appears in the editor drop-down list.

  1. Insert a variable or compound variable.



  * Compound variables allow you to join two or more variables together in the smart text:

  * If only 1 variable: < variable1>

  * If only 2 variables: <variable1> and <variable2>

  * If 3 or more variables: <variable1>, <variable2>, ... , and <variableN>




_Note:_ Variables display only if they have been enabled for use.

  1. Insert a symbol.

  2. If you inserted a compound variable, select the Compound Variable Format from the drop- down list.

  3. If you want to enter the smart text as a complete paragraph, select Yes on the Insert as paragraph slide toggle.




If the smart text is flagged as Insert as Paragraph the summary panel on the Details view page indicates the Insert as paragraph status.

A validation is displayed if Insert as paragraph is set to No and a paragraph style has been applied to the first paragraph. Applies to both smart text and local smart text.

_Note:_ If the smart text is multi-paragraph, selecting _Yes_ on _Insert as paragraph_ causes an extra white space to be added.

  1. If you want to Trim empty paragraphs from the smart text, select the Trim option from _the drop-down_ list.

  2. To enable language-specific content:



  * Select the language from the Language drop-down list.

  * Deselect the Use same content as default language (default language) slider on the other language tabs.

_Note:_ This slider only appears if the touchpoint has more than one language enabled.

_Note:_ In alternate languages, “same as default” means that the output is getting the default language-locale content. Embedded objects (i.e. smart text) will be rendered in the default language-locale and not rendered in the alternate language.

  * Repeat steps 1 to 8 for each language.

  * If the Use same content as default language Idefault language) slider remains enabled and the subsequent language content is not added, customers who qualify for the message receive the default language message content.



  1. Click:



  * Continue to save and go to the Content page.

  * Save to save and return to the Smart Text list.




_Note:_ If you click the _Save_ drop-down arrow, you can click:

  * _Save & View_. Smart Text is saved, and you are on the View smart text page.

  * _Save & Go to list_. Smart Text is saved, and you go to the Smart Texts list.



  * Cancel & View to return to view mode when editing an existing asset.

  * Cancel & Go to List to return to the asset list without creating a new asset.



﻿




You are here: 

### Adding content for markup smart text

Markup smart text is configured to support markup authoring and the markup is passed to composition as is. Therefore, all formatting tags must be added manually to the content, for example <t> for text and <p> tags for paragraphs. Nested tagging is not supported. For more information on Quadient tags, see Variable Formatted Flows in the Quadient Inspire connector guide. Markup smart text can reference variables and other Markup smart text.

To enter a content for Markup smart text:

  1. In the editor window, enter your smart text by either:



  * Typing the text into the editor.

  * Pasting text from another source.




_Note:_ The content is passed directly to composition as is. Therefore, you need to add all formatting tags manually, for example _< t>_ for text and _< p>_ tags for paragraphs. For more information on Quadient tags, see the _Quadient Inspire connector guide_.

  2. Insert smart text.




_Note:_ Only active smart text is listed.

  1. Insert a variable or compound variable.




_Note:_ Variables display only if they have been enabled for use by your system administrator.

  1. Insert a symbol.

  2. To enable language-specific content:



  * Select the language from the Language drop-down list.

  * Deselect the Use same content as default language (default language) slider on the other language tabs. 




_Note:_ This slider only appears if the touchpoint has more than one language enabled.

_Note:_ In alternate languages, “same as default” means that the output is getting the default language-locale content. Embedded objects (i.e. smart text) will be rendered in the default language-locale and not rendered in the alternate language.

  * Repeat steps 1 to 8 for each language.

  * If the Use same content as default language (default language) slider remains enabled and the subsequent language content is not added, customers who qualify for the message receive the default language message content.



  1. Click:



  * Continue to save and go to the Content page.

  * Save to save and return to the Smart Text list.




_Note:_ If you click the _Save_ drop-down arrow, you can click:

  * _Save & View_. Smart Text is saved, and you are on the View smart text page.

  * _Save & Go to list_. Smart Text is saved, and you go to the Messages list.

  * Cancel & View to return to view mode when editing an existing asset.

  * Cancel & Go to List to return to the asset list without creating a new asset.



﻿




You are here: 

## Managing a content menu in smart text

You can create a drop-down list of inputs in your smart text by using the Content Menu. Your content authors can then select from a drop-down list when they enter the smart text into their content.

When you create your drop-down list, you can:

  * Allow users to enter:

  * A custom value. A selection where they can type in a value.

  * An empty value. Allows the user to have no value.

  * Use smart text / variables as values in the menu.




Note: You can only select in-line smart text. Paragraph styles and block content are excluded.

  * You can use targeted smart text as your values to provide you with a data driven list. For example, you can use an Empty value as your default and targeted smart text. If the smart text does not qualify, then the value is empty and it does not appear in the message.

  * Format the options.




![Content menu screenshot](../Resources/Images/Content Authoring Guide/Managing a content menu in smart text_480x326.jpg)

You create the content menu by adding the values to your drop-down list.
﻿




You are here: 

### Adding a content menu to smart text

To add a content menu in smart text:

  1. Place the cursor in the smart text where you want to place the content menu.

  2. Go to Insert > Content menu.

  3. To allow users to enter an Empty value, select Enabled on the slide toggle.

  4. To allow users to enter a Custom value, select Enabled on the slide toggle.

  5. Enter a description of the menu. (Optional)




The description creates a pop-up description that appears when you hover the cursor over the content menu.

  1. Add the content values:




Note: Content values cannot be multi-line. If you want to enter a multi-line value, create a smart text, and then enter the smart text as a value.

  * Click ![Add content value icon](../Resources/Images/Content Authoring Guide/Adding a content menu to smart text_19x19.png).

  * Enter the value by:

  * Typing in the value.

  * Entering a smart text.

  * Formatting the value. (Optional)

  * Click _Set Value_.

  * Repeat the above for each value.




Note: The first value entered is the default value.

  1. You can change the order of the list by using drag and drop for the values.




Note: If you re-order the list, the value at the top of the list is the default value.

  1. Click Apply.



﻿




You are here: 

### Editing the content menu

To edit a content menu.

  1. Double click the content menu.

  2. To allow users to enter an Empty value, select Enabled on the slide toggle.

  3. To allow users to enter a Custom value, select Enabled on the slide toggle.

  4. Enter a description of the menu. (Optional)

  5. You can:




Add a content value:

  * Click ![Add content value icon](../Resources/Images/Content Authoring Guide/Adding a content menu to smart text_19x19.png).

  * Enter the value by:

  * Typing in the value.

  * Entering a smart text.

  * Formatting the value. (Optional)

  * Click Set Value.




Delete a content value:

  * Click ![Delete content value icon](../Resources/Images/Content Authoring Guide/Editing the content menu_19x19.jpg) for the value you want to delete.




Edit a content Value:

  * Click ![Edit content value icon](../Resources/Images/Content Authoring Guide/Editing the content menu_1_34x22.jpg) for the value you want to edit.

  * Edit the value.

  * Click Set Value.




Re-order the values by using drag and drop.

Note: If you re-order the list, the value at the top of the list is the default value.

  6. Click Apply.



﻿




You are here: 

## Adding the smart canvas content

The smart canvas content is created by adding containers in which you place your text or tables. Images are added directly to the canvas. These images and containers can be placed anywhere on the canvas.

_Note_ : Before adding content to the smart canvas, you need to size the canvas. You cannot place smart canvas into a zone that is smaller than the canvas size.
﻿




You are here: 

### Adding text to the smart canvas

You add text by clicking anywhere on the field, creating a container, and adding text. When the container is created, the width is fixed, but the length expands to accommodate the text. Once you have added the container, you can resize and move the container around in the text editor.

_Note_ : If you leave a container empty, the container disappears as soon as it is no longer selected. This means that you cannot place empty containers to lay out the page.

When the container is selected, you can format text as you would in any text message. To add text:

  1. Click the field.

  2. Enter the text.



﻿




You are here: 

### Adding a table to the smart canvas

You add a table by creating a container and adding the table to it. Once you have added the table, you resize the container and table. You can move them around the canvas as any other container.

To create a table:

  1. Click the field.

  2. Click ![Create table dropdown menu](../Resources/Images/Content Authoring Guide/Adding a table_37x22.jpg).

  3. Select Insert table.

  4. Highlight the number of columns and rows you want for the table.

  5. Left-click the mouse.

  6. To resize the table, place the cursor on a resize square and drag to increase table size.



﻿




You are here: 

### Adding an image to the smart canvas

Images must be in one of the following formats:

  * PNG.

  * JPEG.

  * GIF.




Once you have inserted the image, you can resize and move the image around on the canvas.

To insert an image:

  1. Click ![Add image icon](../Resources/Images/Content Authoring Guide/Adding an image_28x22.jpg).

  2. Click Select image.

  3. Add the image.

  4. Click Apply.



﻿




You are here: 

## Targeting smart text and smart canvas

You can specify targeting for smart text or smart canvas. For a full description of using targeting, see the Touchpoint administrator guide.

_Note_ : Targeting cannot be added to a global smart text.
﻿




You are here: 

### Using AND or OR rules for combining target groups in smart text and smart canvas

You can apply more than one target group. Groups can be combined by using either AND or OR rules:

  * AND rules use multiple conditions to indicate targeting. Only customers _meeting all the requirements_ of the AND rule conditions are targeted. AND rules can be used to include or exclude customers.

  * OR rules let you target one group or another. For example, to target customers who are either “French Speaking Canadians” OR customers who are “Spanish Speaking Americans”, select both groups and use the OR rule. OR rules _can be_ used to include or exclude customers.




Note: If a recipient is found in both included and excluded groups, they do not receive the content. In other words, Exclude overrides Include.
﻿




You are here: 

### Adding targeting to a smart canvas

To add the targeting:

  1. In the View window, click Edit.

  2. Click Targeting.

  3. Drag and drop the appropriate group(s) under Include.




_Note:_ If you select a target group that contains:

  * _User specified parameters_. You must enter values for those parameters.

  * _User specified rule based on a date argument_. You must enter the indicator, logic, and values for the date argument.



  4. If there is more than one target group under Include, from the slide toggle, select:



  * At least one \- to create an OR statement.

  * All \- to create an AND statement.



  5. Drag and drop the appropriate group(s) under Exclude. 




_Note:_ If you select a target group that contains:

  * _User specified parameters._ You must enter values for those parameters.

  * _User specified rule based on a date argument._ You must enter the indicator, logic, and values for the date argument.



  6. If there is more than one target group under Exclude, from the slide toggle, select:



  * At least one \- to create an OR statement.

  * All \- to create an AND statement.



  1. Click Save



  * You can also cancel from the Targeting page.



﻿




You are here: 

### Removing a target group

You can remove any or all attached target groups. To remove an assigned target group:

  1. Go to the Targeting tab.

  2. Place your cursor on the target group that you want to remove.

  3. Click ![Remove target group icon](../Resources/Images/Content Authoring Guide/Removing a target group_19x19.jpg) in the upper right hand corner of the target group.

  4. Click Save.



﻿




You are here: 

## Using compound smart text

Compound smart text is used to dynamically insert repeating data values in message content. You insert compound smart text into your message just like any other smart text. After you enter the compound smart text into the message, you format it as you would format a variable.

You can enter smart text or variables into compound smart text.

Note: If you enter an aggregated variable into compound smart text, Messagepoint outputs every repeating record whenever that variant is hit.

Note: The repeating delimiter in local smart text does not work if the content is "Insert as paragraph" or has a paragraph style which implies "Insert as paragraph".

You can format the list either as a delimited lists or each list item to be a separate list item, each in their own paragraph. Since Messagepoint does not natively support lists coupled with compound smart text, you cannot number or bullet the list from Messagepoint.

You must add content to the default smart text:

  * Smart text has no default content, not even an empty paragraph; it is just blank.

  * If you want the default smart text content to be blank, edit the default content in the source code editor and add _< p>&nbsp;&nbsp;</p>_. When you use the delimiter attributes, the empty paragraph is not trimmed. If you use the "starts new paragraph" attribute, the blank paragraph is retained. This forces a blank line into the content.




To use compound smart text:

  1. Click the insertion point in the content.

  2. Go to Content > Smart Text.

  3. Select the Smart Text from the drop-down list.

  4. Double click the smart text in the editor.

  5. Click the _Compound values_ check box in the pop-up box.

  6. Click the Insert as paragraphs check box if you want to display each value as a separate paragraph.

  7. Enter the _Delimiter_.



  * The delimiter can be any character. You are limited to two characters for the delimiter. We recommend one character and one space.

  * _Delimiter_ and _List end delimiter_ are used only if the Insert as paragraphs option is not selected.



  1. Select the _List end delimiter_ from the drop-down list.




The _List end delimiter_ goes before that last item in the variable list.

  1. Select the content parameters from the drop-down list:



  * _All matches_. Returns all values that match.

  * _No duplicates_. Returns all values that match but removes any duplicates from the list.

  * _First non-empty match_. Returns the first match that contains content.

  * _Not empty or duplicate_. Returns all matches unless empty or a duplicate of a previous match.



  1. Click _OK_.



﻿




You are here: 

## Uploading images

You can upload images into the Image Library in two ways - individually or in bulk:

  * When you upload images individually, you add the tags and comments, make _the image_ static or dynamic, and associate the image with touchpoints.

  * When you upload images in bulk, you need to go and set the details for each image after they have been uploaded. You can, however, add the same tag(s) to all images during a bulk upload.

  * For bulk uploads, images are automatically associated with the touchpoint shown in the touchpoint selector.



﻿




You are here: 

### File formats to upload

You can upload files in one of the following formats:

  * For use in print touchpoints:

  * JPG.

  * TIFF.

  * PDF.

Note for Messagepoint Composer/Sefas: When uploading PDFs, please check that the PDF structure is correct: 

• PDFs must have all used fonts embedded.

• PDFs must have all fillable forms content removed.

  * GIF.

  * RTF.

  * PNG.

  * PSEG.

  * IMG.

  * EPS.

Note: RTF, IMG and EPS images are not displayed in Messagepoint.

  * DLF.

  * DXF.

Note: You must have the OpenText Exstream DXF connector to use the DXF and DLF formats.

Note: DXF is not supported on a mainframe.

  * DOCX.

Note: Since DOCX files are uploaded in a Graphic-DOCX zone they are uploaded as other image files used in graphic zones. For more information, see the Touchpoint administrator guide.

  * For use in email and web touchpoints:

  * JPG.

  * GIF.



﻿




You are here: 

## Uploading images individually

You can upload images individually into the Image Library.

Note: If you are using metadata templates, you add and complete the template when uploading an individual image.
﻿




You are here: 

### Types of Images

Images can be one of the two following types:

  * Static. Images contains the same static content.

  * Dynamic. Image content varies depending on conditions - for example, location.




### Warnings when the image type changes

You can change the image type but there are implications if the dynamic image has variants:

  * If the dynamic image variants ever had custom content, including archive and active versions, you will see a warning message like the one below. The versions affected will be mentioned.

![Warning screenshot](../Resources/Images/Content Authoring Guide/Warnings when the message type changes_572x111.png)



﻿




You are here: 

### Completing the Image Details page

The image detail page allows you select the image type and the configuration of the image as well as associate the image with touchpoint(s).

To upload an image:

  1. Go to Content > Content > Image Library.

  2. Click Add.




![completing image details](../Resources/Images/Content Authoring Guide/Completing the details page_images_3_576x212.png)

  3. If you are using metadata, add the metadata form: (Optional)



  * Select the form from the Optional metadata drop-down list.

  * Click Continue.



  1. Enter the Name of the content.




Note: Double spaces are not supported in asset names.

  5. Enter any Tags. (Optional)




For more information, see [Using tags and the tag cloud](Using tags and the tag cloud.htm#_Ref86401944).

  1. Set the Allow use of variables slide toggle to:



  * Enabled. The image is static but can be modified to be able to have variants based on _the message’s selector_ or by the touchpoint’s variants.

  * Disabled. If disabled, you cannot insert into the content any variable or other content _with the variables._

  * The hover tip says “Allowing variables to be used may restrict this content to specific touchpoints. Disallowing variables means this content can be used in all touchpoints.”



  1. To make the image dynamic, you must:



  * Select the Selector from the Varied By drop-down list.

  * Add variants and selection data.




For more information, see [Managing dynamic assets](Managing dynamic assets.htm#_Ref86403061).

  1. Enter a Description of the content. (Optional)

  2. Select the Usage by clicking the appropriate button:



  * Message. For use in Messagepoint text messages.

  * Connected. For use in Connected text messages. There are some features that can _be applied_ only to Connected smart text. For more information, see the Connected administrator guide.

  * Unrestricted. For use in either Messagepoint or Connected text messages.



  1. Complete the metadata fields if you have attached a metadata template.

  2. Select the touchpoint(s) for which you want the content to be accessible:



  * The touchpoint that you are on when you create the embedded content is automatically selected when you create the content.

  * To add one touchpoint at a time:

  * Drag the touchpoints from the Available Touchpoints field to the Selected Touchpoints field.

  * To add multiple touchpoints at once:

  * Click the appropriate check boxes in the Available Touchpoints field.

  * Drag the touchpoints to the Selected Touchpoints field.



  1. Add any Comments. (Optional)

  2. To enable language-specific content:



  * Select the language from the Language drop-down list.

  * Deselect the Use same content as default language (default language) slider on the other language tabs.




Note: This slider only appears if the touchpoint has more than one language enabled.

Note: In alternate languages, “same as default” means that the output is getting the default language-locale content. Embedded objects (i.e. smart text) will be rendered in the default language-locale and not rendered in the alternate language.

  * Repeat steps 1 to 8 for each language.



  * If the Use same content as default language (default language) slider remains enabled and the subsequent language content is not added. Customers who qualify for the message received the default language message content.



  1. Click:



  * Continue to save and go to the Content page.

  * Save to save and return to the Image Library.




Note: If you click the Save drop-down arrow, you can click:

  * Save & View. Image is saved, and you are on the View image page.

  * Save & Go to list. Image is saved and you go to the Image Library.



﻿




You are here: 

### Uploading the image

Once you have completed the Details page, you can upload the image. To upload the image:

  1. Click New asset.

  2. Navigate to the desired image file.

  3. Click Open.

  4. Add an Applied Image name. (Optional)




Note: The Applied Image name must have an extension that is the same as the original image.

  1. Add an Image Link. (Optional)




Note: This appears for digital touchpoints only. It is the URL that the user will be sent to when clicking on the Image.

  1. Enter the descriptive text in the Alt Text field. (Optional)



  * The text can include Smart Text or variables.

  * DXF alternate text for images is only supported for JPG files.



  1. Add an External Link. (Optional)




Note: This appears for digital touchpoints and digital channels of omnichannel touchpoints only. It is the URL of an image on an external server. Messagepoint will not render the image inside the email, rather it will leave it as a link. This is to save on the size of the email and allow users to use their corporate CDNs (content delivery networks) instead of having to reload assets in Messagepoint.

Best practice: Use the external link option for email images.

  1. Add an External Path. (Optional)




_Note:_ this option is for Messagepoint Composer/Sefas and Quadient Inspire only. It is the location for the composition engine to pull the images at run time.

_Note_ : when using external path, an image of the correct image type must be loaded into the image library so that the image library object can be used in a message in a zone of the same image type.

  1. Click:



  * _Continue_ to save and go to the _Targeting_ page.

  * _Save_ to save the image.




Note: If you click the Save drop-down arrow, you can click:

  * _Save & View_. Image is saved, and you are on the _View image_ page.

  * _Save & Go to list_. Image is saved and you go to the _Image Library_ list.



﻿




You are here: 

## Uploading images in bulk

You can also upload images in bulk using the bulk uploader. The images are uploaded as static images. After they are uploaded, you can make them into dynamic images if you wish. When you carry out bulk upload, the images are assigned to one touchpoint or to all touchpoints. If you want to assign then to more than one touchpoint, you must re-assign them to other touchpoints. For more information, see [[Associating shared assets with multiple touchpoints](Associating shared assets with multiple.htm#_Ref86403722)](Associating shared assets with multiple.htm).

Note: If you are uploading images in bulk, you cannot assign metadata templates to the images during the upload process.
﻿




You are here: 

### Uploading images with bulk upload

If you have a large number of images, you can upload the images using bulk upload.

![Image library upload window](../Resources/Images/Content Authoring Guide/Uploading images in bulk_1_480x230.jpg)

The bulk image upload allows you to import large numbers of images at once.

To upload images in bulk:

  1. On the Image Library page, select the appropriate touchpoint from the Touchpoint Selector.

  2. Click Bulk Upload.

  3. Click Add Files.

  4. Navigate to the folder containing the image files.

  5. Select the multiple files:



  * Ctrl + click the individual image files. 




or

  * Select an image in the list.

  * Shift + click  another image file in the list.




The two selected objects plus all image files between them are selected.

Note: To delete an image from the list, click Cancel for the image.

  1. Click Open.

  2. Add the same tag(s) to all of the files by entering the tag in the Tags field. (Optional)




For more information, see [Using tags and the tag cloud](Using tags and the tag cloud.htm#_Ref86403876).

  1. On the Variable content enabled slide toggle, select:



  * No to assign the images to all touchpoints.

  * Yes to assign the images to the current touchpoint.



  1. Rename any file names by editing the name in the field. (Optional)




Note: Image names can only contain the following characters: Alphanumeric, Underscore, Dash, Apostrophe, and Space.

  * When renaming your image files, do not use spaces in the file name. Sometimes when you are downloading an image from Messagepoint, the file name is shortened at the first space.



  1. Upload the files:

  2. Individually: Click Start for the individual file.

  3. All at once: Click Start Upload.




Once you have uploaded the images into the Image Library, you can edit their details.
﻿




You are here: 

## Managing the SFTP image upload

You can also use SFTP to upload your images into the Image Library. This allows you to use SFTP to transfer images from your CSM to Messagepoint. In order to use SFTP, it must first be configured in the instance’s System Settings. For more information, see the System administrator guide.

Before using the upload, you must place the image files in the SFTP Repository file or one of its sub folders.

Note: Image names can only contain the following characters: Alphanumeric, Underscore, Dash, Apostrophe, and Space.
﻿




You are here: 

### Uploading images using SFTP

When uploading images using SFTP, you can only select and upload only images that have not been previously uploaded. If you want to upload a new version of an existing file, you use the sync command. 

To upload the images using SFTP:

  1. Click SFTP Upload.




Note: If the SFTP Repository Settings have not been set up in System Settings, the SFTP Upload button does not appear.

  2. If you are using a Repository sub folder, select the Folder from the drop-down list.

  3. Add any appropriate tags.

  4. Select the images by:



  * Selecting the appropriate images.

  * Clicking SELECT ALL.




Note: If you wish to deselect all the selections and start again, click DESELECT ALL.

  1. Click Upload.



﻿




You are here: 

### Syncing the SFTP folder

If you have already uploaded images from an SFTP folder or sub folder, you can use the Sync folder command. This command uploads only images that have been added to the folder or that have been modified. When you sync an image, the new version retains the state of the existing image. For example, if the image is Active when synced, the new version of image is also active.

To sync the SFTP folder:

  1. Click SFTP Upload.

  2. If you are using a Repository sub folder, select the Folder from the drop-down list.

  3. Click Sync folder.



﻿




You are here: 

## Editing shared assets

You can edit a shared asset if:

  * You have the permissions to do so, and the asset is assigned to you.

  * Someone else is not currently editing that asset.

  * It is in the working copy state.



  * You can associate active assets to touchpoints without creating a working copy. For more information, see [Associating shared assets with multiple touchpoints](Associating shared assets with multiple.htm#_Ref86404421).




When you edit a shared asset, you can update the asset's detail page, content, and if smart text, timing and targeting. If you want to edit an active or approved asset, you must first make a working copy of the asset. The original asset stays in production until the edited asset is activated or approved.

  * If you have the power edit permission, you can fast edit smart text, local smart text, and placeholders in a message even if the message has been assigned to another user. For more information, see [Click-through view and edit of smart text and local smart text](Click through view and edit of smart.htm#_Ref86404495).



﻿




You are here: 

### Editing smart text

To edit a smart text:

  1. Select the smart text.

  2. Click Edit.

  3. Update the smart text as required.

  4. Click Save.



﻿




You are here: 

### Editing images

If you have uploaded images in bulk, you should edit their Details page before they are activated.

To edit an image:

  1. Select the image.

  2. Click Edit

  3. If you want to update the image content:



  * Click New asset.

  * Navigate to folder containing image.

  * Select image.

  * Click Open.

  * Enter Applied image name. (Optional)

  * Repeat for each language.

  * Scroll down to edit the image link or alternate text: (Optional)

  * Click Image Link.

  * Edit the link.

  * Click Alt Text.

  * Edit the text.



  1. If you want to edit the Details page, click Previous.

  2. Make your edits.

  3. Click Save.



﻿




You are here: 

## Adding alternate text to images

Alternate text provides accessibility for the visually impaired who are using a machine to read PDF documents. The alt text is read out to describe the image.

The alternate text feature can be used with Messagepoint Composition, Messagepoint Composer, and DXF touchpoints and zones.

Note: Alternate text is not supported for images inserted into tables or freeform.

![adding alternate text](../Resources/Images/Content Authoring Guide/Adding alternate text to images_2_576x224.png)

You enter the alternate text in the Alt Text field. This field is found below the image.

To add alternate text:

  1. Select the image.

  2. Click Edit.

  3. Click Content.

  4. Scroll down the content box until you can see the Alt Text field.

  5. Enter the descriptive text in the field.



  * The text can include Smart Text or variables.



  1. Click Save.



﻿




You are here: 

## Adding image links for digital touchpoints

If the touchpoint is a digital touchpoint,- you can add image links to the image. When the user clicks the image, they are taken to the designated web page. You can add links to images that are either in the Image Library or are uploaded directly into the message:

  * If the image is part of the Image Library, you add the link on the Edit Image page. _When the_ image is added to the message, the link is automatically added.

  * If the image has been uploaded directly into the message, you add the link in the Add / Edit Message page.




Note: If the image is a dynamic image, you must enter a link for each variant of the image. If the image is not inherited, the link is not inherited.
﻿




You are here: 

### Adding a link to an image in the Image Library

If the image is part of the Image Library, you add the link in the Edit Image page. To add a link to an image in the Image Library:

  1. Select the image.

  2. Click Edit.

  3. Click Content.

  4. Scroll down the content box until you can see the image link field.

  5. Enter the link in the field.



  * The link can include Smart Text or a variable.



  6. Click Save.



﻿




You are here: 

### Uploading an image and adding the link

If the image is uploaded directly into a message, you add the link in the Edit Message page. To add a link to an uploaded image:

  1. Import the image into the message:



  * Select Uploaded on the slide toggle.

  * Click New Asset.

  * Navigate to the desired image file.

  * Click Open.

  * Add an Applied Image name. (Optional)




Note: The Applied Image name must have an extension that is the same as the original image.

  2. Scroll down the content box until you can see the image link field.

  3. Enter the link in the field.



  * The link can include Smart Text or a variable.



  4. Click Save.



﻿




You are here: 

### Adding or editing a link to a previously uploaded image

To add or edit a link to a previously uploaded image:

  1. Select the image or message.

  2. Click Edit.

  3. Click the Content tab.

  4. Scroll down the content box until you can see the image link field.

  5. Enter / edit the link in the field.

  6. Click Save.



﻿




You are here: 

## Associating shared assets with multiple touchpoints

You can assign smart text or images to touchpoints, independent of the state of the asset. This allows you to associate active shared assets with other touchpoints directly. You do not have to create a working copy of an active asset and then have to approve the re-association of the object for each touchpoint.

![Set touchpoints screen](../Resources/Images/Content Authoring Guide/Associating shared assets with multiple_2_480x273.png)

You associate shared assets with touchpoints by dragging the touchpoints from the available touchpoints list to the selected touchpoints list.

When you associate shared assets to other touchpoints, the association is for all current versions of the asset, both active and working copy. It also does not remove existing associations.

Note: If you are associating smart text that contains variables to touchpoints using different data models, you must ensure that the variables have been mapped from one data model to the other. For more information, see the Touchpoint administrator guide.

To associate smart text with multiple touchpoints:

  1. On the shared asset page, select the asset.

  2. Go to More > Associate Touchpoints.

  3. Under Available Touchpoints, click the touchpoint(s) that you want to associate with _the asset._

  4. Drag and drop the touchpoint(s) to the Selected Touchpoints field.

  5. Click Save.



﻿




You are here: 

## Finding where shared assets are being used

You can create a report showing where a specific shared asset is being used. This is useful if you are trying to archive a referenced asset.

To find where a smart text asset is being used:

  1. On the shared asset page, select the asset.

  2. Go to More > Where Used.

  3. Click Continue.




The Where used Report opens in a new window.
﻿




You are here: 

## Using images in email

By default, images are blocked by the email clients for many of your recipients. So you want to keep your design simple and ensure that no important content is being suppressed by image blocking.

Keep the following in mind when adding your email images:

  * Avoid spacer images. Most email clients replace images with an empty placeholder in the same dimensions, others strip the image altogether. Since image blocking is on by default in most email clients, this can lead to broken formats. Use fixed cell widths to keep your formatting in place with or without images.

  * Always include the dimensions of your image in the code. If you do not set the dimensions, some email clients substitute their own sizes when images are blocked and break your layout.

  * Ensure that any images are correctly sized before adding them to your email. Some email clients rely on the true dimensions of your image. For example, if the image size in the email is 201 x 76 pixels, then resize the image file to that size as well.

  * Images should be kept to a maximum width of 600 pixels. This is important due to email client preview panes. As well, this size ensures that your design is readable even when scaled down for mobile email clients.

  * Use only PNG, GIF or JPG formats for images. Not all image formats are supported by various email clients. GIF and JPG formats are universally supported.

  * Provide fall back colors for background images. If you want to use a background image in your design, always provide a background color the email client can fall back on.

  * Best Practice: Use the external link option for email images.



