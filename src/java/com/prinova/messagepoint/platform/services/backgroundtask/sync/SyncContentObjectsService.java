package com.prinova.messagepoint.platform.services.backgroundtask.sync;

import com.google.common.collect.Multimap;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.admin.sync.SyncObjectFactory;
import com.prinova.messagepoint.model.admin.sync.SyncObjectTypeEnum;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.common.SyncObjectType;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.util.SyncTouchpointUtil;
import com.prinova.messagepoint.model.util.TaskUtil;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

public class SyncContentObjectsService extends AbstractSyncService {
    public static final String SERVICE_NAME = "sync.syncContentObjectsService";
    private static final Log log = LogUtil.getLog(SyncContentObjectsService.class);
    public static ThreadLocal<SyncRequest> syncRequestInThread = new ThreadLocal<>();
    private ThreadLocal<Map<Long, Long>> clonedOrSyncedModelMap = ThreadLocal.withInitial(()->new HashMap<>()); //. new HashMap<>(); // Cloned to Source model

    @Transactional(propagation = Propagation.REQUIRED, readOnly = false)
    public void execute(ServiceExecutionContext context) {
        try {
            log.info("SyncContentObjectsService starts");

            validate(context);
            if (hasValidationError(context)) {
                return;
            }

            SyncServiceRequest request = (SyncServiceRequest) context.getRequest();
            ServiceResponse response = context.getResponse();
            syncRequestInThread.set(request.getSyncRequest());
            syncContentObjects(request, response);

            checkZoneGlobalObjects(request);
            swapContentIfNotSwappedYet(request);
            swapComplexValueIfNotSwappedYet(request);
            updateSqlExpression(request);
            updateContentObjectsSyncHistory(request, SyncHistory.SYNC_TYPE_SYNCHRONIZING);
            log.info("SyncContentObjectsService ends");
        } catch (Exception e) {
            log.error(" unexpected exception when invoking SyncContentObjectsService execute method", e);
            this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
                    context.getLocale());
            throw new RuntimeException(e.getMessage());
        }
    }

    public void validate(ServiceExecutionContext context) {
        // TODO Auto-generated method stub

    }

    private void syncContentObjects(SyncServiceRequest serviceRequest, ServiceResponse response) {
        long result = 0L;

        SyncRequest syncRequest = syncRequestInThread.get();

        User requestor = syncRequest.getRequestor();
        boolean syncFromOther = !syncRequest.getSourceSchema().equalsIgnoreCase(syncRequest.getTargetSchema());
        String targetNodeGuid = CloneHelper.queryInSaveSession(() -> Node.getCurrentNode().getGuid());

        Document sourceDocument = syncRequest.getSourceDocument();
        Document targetDocument = syncRequest.getTargetDocument();

        Long sourceDocumentId = sourceDocument.getId();
        long targetDocumentId = targetDocument.getId();

        boolean logDifferences = syncRequest.getLogDifferences();
        boolean hideUntilNextChange = syncRequest.getHideUntilNextChange();
        boolean forceSync = syncRequest.getForceSync();
        boolean syncActiveOnly = syncRequest.isSyncActiveOnly();

        boolean activeObjectsSyncThroughWorkflow = CloneHelper.queryInSaveSession(()->targetDocument.isActiveObjectsSyncThroughWorkflow());

        Set<Long> languageIDsForSync = syncRequest.getLanguagesForSync();

        Map<Long, String> languagesForSync = languageIDsForSync == null ? null : languageIDsForSync
                .stream()
                .map(MessagepointLocale::findById)
                .collect(Collectors.toMap(MessagepointLocale::getId, MessagepointLocale::getCode));

        int syncObjectTypeId = serviceRequest.getSyncObjectTypeId();

        Multimap<Long, Map<Long, Long>> syncObjectMap = serviceRequest.getSyncObjectMap();
        List<Map<Long, Long>> contentObjectsStatusMapList = (List<Map<Long, Long>>) syncObjectMap.get((long) syncObjectTypeId);
        Set<Long> contentObjectIDsUpdated = new HashSet<>();
        Map<ContentObject, ContentObject> clonedContentObjectsMap = new HashMap<>();
        Map<Long, Long> newContentObjectIDsMap = new HashMap<>();

        for(Map<Long, Long> syncObjectStatusMap : contentObjectsStatusMapList) {
            for(Long objectIdx10 : syncObjectStatusMap.keySet()) {
                long objectId = objectIdx10 / 0x10;
                long objectStatus = syncObjectStatusMap.get(objectIdx10);
                boolean objectIsReferenced = (objectStatus & 0x100000) > 0;
                boolean objectIsFromOther = (objectStatus & 0x10000) > 0;
                objectStatus = objectStatus & 0xFFFF;
//                if (syncFromOther != objectIsFromOther)
//                    continue;

                if(objectIsFromOther && ! hideUntilNextChange) {
                    checkNeedExistPrerequisiteObjects(syncRequest, targetDocument, objectIdx10);
                }

                ContentObject referenceModel =  objectIsFromOther ?
                        ContentObject.findById(objectId) :
                        CloneHelper.queryInSaveSession(()->ContentObject.findById(objectId));

                String contentObjectName = referenceModel.getName();
                String contentObjectDna = referenceModel.getDna();

                boolean isDocumentContentObject = referenceModel.isMessage() || referenceModel.isLocalContentObject();

                ContentObject sourceModel = objectIsFromOther ?
                        referenceModel :
                        (isDocumentContentObject ?
                                ContentObject.findByDnaAndDocument(contentObjectDna, sourceDocument) :
                                ContentObject.findGlobalObjectByDna(contentObjectDna));

                if(sourceModel != null) {
                    SyncObjectTypeEnum syncObjectType = SyncObjectTypeEnum.MESSAGE;
                    if (sourceModel.isGlobalImage()) {
                        syncObjectType = SyncObjectTypeEnum.IMAGE_LIBRARY;
                    } else if (sourceModel.isGlobalSmartText()) {
                        syncObjectType = SyncObjectTypeEnum.SMART_TEXT;
                    } else if (sourceModel.isLocalImage()) {
                        syncObjectType = SyncObjectTypeEnum.LOCAL_IMAGE_LIBRARY;
                    } else if (sourceModel.isLocalSmartText()) {
                        syncObjectType = SyncObjectTypeEnum.LOCAL_SMART_TEXT;
                    } else if (sourceModel.isMessage()) {
                        syncObjectType = SyncObjectTypeEnum.MESSAGE;
                    }

                    if(SyncTouchpointUtil.hasFailedPrerequisiteObjects(syncRequest, objectIdx10, contentObjectName)) {
                        String syncNote = "Skip " + (objectIsReferenced ? "referenced" : "selected") + " contentObject source id = (" + sourceModel.getId() + "), name = \"" + contentObjectName + "\"";
                        log.info(syncNote);
                        SyncObjectFactory.addSkippedSyncObject(syncObjectType, syncRequest, sourceModel, null, objectIdx10);
                        continue;
                    }

                    try {
                        ContentObject targetModel = objectIsFromOther ?
                                CloneHelper.queryInSaveSession(() -> isDocumentContentObject ?
                                        ContentObject.findByDnaAndDocument(contentObjectDna, targetDocument) :
                                        ContentObject.findGlobalObjectByDna(contentObjectDna)) :
                                referenceModel;

                        if (!forceSync) {
                            if (targetModel != null) {
                                ContentObject targetModelTempFinal = targetModel;
                                if (CloneHelper.queryInSaveSession(() -> targetModelTempFinal.hasWorkingData()
                                        && targetModelTempFinal.getLockedForId() != 0
                                        && targetModelTempFinal.getLockedForId() != requestor.getId()
                                )) {
                                    SyncObjectFactory.addSkippedSyncObject(syncObjectType, syncRequest, sourceModel, targetModelTempFinal, true, objectIdx10);
                                    continue;
                                }
                            }
                        }

                        if (hideUntilNextChange && sourceModel != null /*&& targetModel != null*/) {
                            String hideNote = "Hide " + (objectIsReferenced ? "referenced" : "selected") + " contentObject source id = (" + sourceModel.getId() + "), name = \"" + contentObjectName + "\"";
                            log.info(hideNote);

                            boolean needFlush = false;
                            if(targetModel == null) {
                                ContentObject clonedTargetModel = CloneHelper.clone(sourceModel, o->o.clone(targetDocument, ContentObject.CLONE_CONTENT_NONE, languagesForSync, 0));
                                CloneHelper.execInSaveSession(()->{
                                    clonedTargetModel.save();
                                });
                                targetModel = clonedTargetModel;
                                swapContentIfNotSwappedYet(serviceRequest);
                                swapComplexValueIfNotSwappedYet(serviceRequest);
                                needFlush = true;
                            }
                            else if(targetModel.isRemoved()) {
                                ContentObject targetModelFinal = targetModel;
                                CloneHelper.execInSaveSession(()->{
                                    targetModelFinal.setRemoved(false);
                                    targetModelFinal.getContentObjectAssociations().clear();
                                    targetModelFinal.getContentObjectDataTypeMap().clear();
                                    targetModelFinal.save();
                                });
                            }

                            if(sourceModel.isGlobalContentObject()){
                                ContentObject targetModelFinal = targetModel;
                                if(CloneHelper.queryInSaveSession(()->!targetModelFinal.getVisibleDocuments().stream().anyMatch(d -> d.getId() == targetDocumentId))) {
                                    CloneHelper.execInSaveSession(() -> {
                                        targetModelFinal.getVisibleDocuments().add(targetDocument);
                                    });
                                    needFlush = true;
                                }
                            }

                            if(needFlush) {
                                doHibernateUtilFlush();
                                doHibernateUtilClear(null, null, 0);
                            }

                            clonedOrSyncedModelMap.get().put(targetModel.getId(), sourceModel.getId());
                            SyncObjectFactory.addCompletedSyncObject(syncObjectType, syncRequest, sourceModel, targetModel, true, objectIdx10);
                            result = result + 1;
                            continue;
                        }

                        String syncNote = "Sync " + (objectIsReferenced ? "referenced" : "selected") + " contentObject source id = (" + sourceModel.getId() + "), name = \"" + contentObjectName + "\"";
                        log.info(syncNote);

                        if (logDifferences) {
                            SyncTouchpointUtil.logSyncReason(log, sourceModel, targetModel);
                        }

                        Long sourceLockedFor = sourceModel.getLockedFor();
                        Long targetLockedFor = requestor.getId();

                        boolean isNewContentObject = false;
                        if (sourceModel == null || sourceModel.isRemoved()) {
                            if (targetModel != null) {
                                {
                                    ContentObject targetModelFinal = targetModel;
                                    targetModel = CloneHelper.queryInSaveSession(() -> {
                                        targetModelFinal.setRemoved(true);
                                        targetModelFinal.save();
                                        HibernateUtil.getManager().getSession().flush();
                                        HibernateUtil.getManager().getSession().clear();
                                        return ContentObject.findById(targetModelFinal.getId());
                                    });
                                }

                                if (sourceModel == null || !sourceModel.hasWorkingData()) {
                                    ContentObject targetModelFinal = targetModel;
                                    targetModel = CloneHelper.queryInSaveSession(() -> {
                                        if (targetModelFinal.hasWorkingData()) {
                                            targetModelFinal.discardWorkingData();
                                            HibernateUtil.getManager().getSession().flush();
                                            HibernateUtil.getManager().getSession().clear();
                                        }
                                        return ContentObject.findById(targetModelFinal.getId());
                                    });
                                }

                                if (sourceModel == null || !sourceModel.hasActiveData()) {
                                    ContentObject targetModelFinal = targetModel;
                                    targetModel = CloneHelper.queryInSaveSession(() -> {
                                        if (targetModelFinal.hasActiveData()) {
                                            targetModelFinal.archiveActiveData(false);
                                            HibernateUtil.getManager().getSession().flush();
                                            HibernateUtil.getManager().getSession().clear();
                                        }
                                        return ContentObject.findById(targetModelFinal.getId());
                                    });
                                }

                                if (sourceModel == null || !sourceModel.hasArchivedData()) {
                                    ContentObject targetModelFinal = targetModel;
                                    targetModel = CloneHelper.queryInSaveSession(() -> {
                                        if (targetModelFinal.hasArchivedData()) {
                                            targetModelFinal.deleteArchiveData();
                                            HibernateUtil.getManager().getSession().flush();
                                            HibernateUtil.getManager().getSession().clear();
                                        }
                                        return ContentObject.findById(targetModelFinal.getId());
                                    });
                                }
                            }
                        }
                        else {
                            boolean syncThroughWorkflow = activeObjectsSyncThroughWorkflow
                                && sourceModel.hasActiveData()
                                && CloneHelper.queryInSaveSession(()->targetDocument.getMessageWorkflow() != null);

                            if (syncThroughWorkflow) {
                                if(targetModel == null) {
                                    targetModel = CloneHelper.clone(sourceModel, o -> {
                                        ContentObject clonedContentObject = o.clone(targetDocument, ContentObject.CLONE_CONTENT_NONE, languagesForSync, 0);
                                        CloneHelper.execInSaveSession(() -> {
                                            clonedContentObject.createWorkingDataEmpty();
                                            if (sourceLockedFor != null) {
                                                clonedContentObject.setLockedFor(targetLockedFor);
                                            }
                                            clonedContentObject.save();
                                            AuditEventUtil.auditWorkflowChanged(requestor, clonedContentObject, AuditActionType.ID_CHANGE_SYNCED, null);
                                        });
                                        return clonedContentObject;
                                    });
                                }
                                else {
                                    ContentObject targetModelFinal = targetModel;
                                    targetModel = CloneHelper.queryInSaveSession(() -> {
                                        {
                                            ContentObject model = ContentObject.findById(targetModelFinal.getId());
                                            if (model.hasWorkingData()) {
                                                model.discardWorkingData();
                                                HibernateUtil.getManager().getSession().flush();
                                                HibernateUtil.getManager().getSession().clear();
                                            }
                                        }
                                        {
                                            ContentObject model = ContentObject.findById(targetModelFinal.getId());
                                            if (model.hasActiveData()) {
                                                model.createWorkingDataFromActive();
                                            } else {
                                                model.createWorkingDataEmpty();
                                            }
                                            HibernateUtil.getManager().getSession().flush();
                                            HibernateUtil.getManager().getSession().clear();
                                        }
                                        {
                                            ContentObject model = ContentObject.findById(targetModelFinal.getId());
                                            if (sourceLockedFor != null) {
                                                model.setLockedFor(targetLockedFor);
                                            }
                                        }
                                        HibernateUtil.getManager().getSession().flush();
                                        HibernateUtil.getManager().getSession().clear();
                                        return ContentObject.findById(targetModelFinal.getId());
                                    });
                                }

                                doHibernateUtilFlush();

                                SyncTouchpointUtil.syncContentObjectAttributes(sourceDocument, targetDocument, sourceModel, targetModel, requestor);
                                SyncTouchpointUtil.mapDynamicTreeNodes(sourceDocument, targetDocument, sourceModel, targetModel, ContentObject.DATA_TYPE_ACTIVE, ContentObject.DATA_TYPE_WORKING);
                                Map<Long, String> dynamicVariantPgtnNoLongerUsed = new HashMap<>();
                                SyncTouchpointUtil.syncContentObjectDataType(sourceDocument, targetDocument, sourceModel, targetModel, ContentObject.DATA_TYPE_ACTIVE, ContentObject.DATA_TYPE_WORKING, languagesForSync, dynamicVariantPgtnNoLongerUsed);
                                SyncTouchpointUtil.cleanUnusedDynamicVariantPgtn(targetModel, dynamicVariantPgtnNoLongerUsed);

                                doHibernateUtilFlush();

                                SyncTouchpointUtil.releaseForApproval(targetModel, requestor, "Sync");
                            }
                            else if (targetModel == null) {
                                int dataType = syncActiveOnly ? ContentObject.DATA_TYPE_ACTIVE_AND_ARCHIVED : ContentObject.DATA_TYPE_ALL;
                                targetModel = CloneHelper.clone(sourceModel, o -> {
                                    ContentObject clonedContentObject = o.clone(targetDocument, ContentObject.CLONE_CONTENT_ONLY_EXISTING_COA, languagesForSync, dataType);
                                    if (sourceLockedFor != null) {
                                        clonedContentObject.setLockedFor(targetLockedFor);
                                    }
                                    CloneHelper.execInSaveSession(() -> {
                                        clonedContentObject.save();
                                        AuditEventUtil.auditWorkflowChanged(requestor, clonedContentObject, AuditActionType.ID_CHANGE_SYNCED, null);
                                    });
                                    return clonedContentObject;
                                });
                                isNewContentObject = true;
                                if(targetModel.hasWorkingData()){
                                    ContentObject targetModelFinal = targetModel;
                                    CloneHelper.execInSaveSession(()->{
                                        TaskUtil.createTaskForWorkingCopy(targetModelFinal, requestor);
                                    });
                                }
                            }
                            else {
                                if (hideUntilNextChange && targetModel != null && sourceModel != null) {
                                    clonedOrSyncedModelMap.get().put(targetModel.getId(), sourceModel.getId());
                                    continue;
                                }

                                long targetModelId = targetModel.getId();

                                sourceModel = ContentObject.findById(sourceModel.getId());

                                targetModel = CloneHelper.queryInSaveSession(() -> ContentObject.findById(targetModelId));

                                {
                                    if (CloneHelper.queryInSaveSession(() -> ContentObject.findById(targetModelId).isRemoved() || ContentObject.findById(targetModelId).getContentObjectDataTypeMap().isEmpty())) {
                                        CloneHelper.execInSaveSession(() -> {
                                            AuditEventUtil.auditWorkflowChanged(requestor, ContentObject.findById(targetModelId), AuditActionType.ID_CHANGE_SYNCED, null);
                                        });
                                    }
                                    else {
                                        AuditEventUtil.auditContentObjectDetailsChangedForSync(requestor, sourceModel, targetModel);
                                    }
                                }

                                boolean sourceHasArchived = sourceModel.hasArchivedData();
                                boolean sourceHasActive = sourceModel.hasActiveData();
                                boolean sourceHasWorking = sourceModel.hasWorkingData();

                                Long saveTargetModelId;

                                {
                                    saveTargetModelId = CloneHelper.queryInSaveSession(() -> {
                                        ContentObject model = ContentObject.findById(targetModelId);
                                        ContentObject clonedModel = null;
                                        if(sourceHasActive && sourceHasWorking && model.hasWorkingData()) {
                                            clonedModel = model.clone(targetDocument, ContentObject.CLONE_CONTENT_RESOLVE_COW_COA, null, ContentObject.DATA_TYPE_WORKING);
                                            clonedModel.save();
                                            HibernateUtil.getManager().getSession().flush();
                                            HibernateUtil.getManager().getSession().clear();
                                            clonedModel = ContentObject.findById(clonedModel.getId());
                                        }
                                        return clonedModel == null ? null : clonedModel.getId();
                                    });

                                    targetModel = CloneHelper.queryInSaveSession(() -> ContentObject.findById(targetModelId));
                                }

//                                SyncTouchpointUtil.syncContentObjectVersionStatus(sourceDocument, targetDocument, sourceModel, targetModel, requestor);
                                {
                                    SyncTouchpointUtil.syncContentObjectAttributes(sourceDocument, targetDocument, sourceModel, targetModel, requestor);

                                    targetModel = CloneHelper.queryInSaveSession(() -> {
                                        HibernateUtil.getManager().getSession().flush();
                                        HibernateUtil.getManager().getSession().clear();
                                        return ContentObject.findById(targetModelId);
                                    });
                                }

                                if(sourceHasActive) {
                                    targetModel = CloneHelper.queryInSaveSession(() -> {
                                        {
                                            ContentObject model = ContentObject.findById(targetModelId);
                                            if (model.hasWorkingData()) {
                                                model.discardWorkingData();
                                                HibernateUtil.getManager().getSession().flush();
                                                HibernateUtil.getManager().getSession().clear();
                                            }
                                        }

                                        {
                                            ContentObject model = ContentObject.findById(targetModelId);
                                            if (model.hasActiveData()) {
                                                model.createWorkingDataFromActive();
                                                HibernateUtil.getManager().getSession().flush();
                                                HibernateUtil.getManager().getSession().clear();
                                            } else {
                                                model.createWorkingDataEmpty();
                                                HibernateUtil.getManager().getSession().flush();
                                                HibernateUtil.getManager().getSession().clear();
                                            }
                                        }

                                        {
                                            ContentObject model = ContentObject.findById(targetModelId);
                                            if (sourceLockedFor != null) {
                                                model.setLockedFor(targetLockedFor);
                                                HibernateUtil.getManager().getSession().flush();
                                            }
                                        }
                                        return ContentObject.findById(targetModelId);
                                    });

                                    SyncTouchpointUtil.mapDynamicTreeNodes(sourceDocument, targetDocument, sourceModel, targetModel, ContentObject.DATA_TYPE_ACTIVE, ContentObject.DATA_TYPE_WORKING);
                                    Map<Long, String> dynamicVariantPgtnNoLongerUsed = new HashMap<>();
                                    SyncTouchpointUtil.syncContentObjectDataType(sourceDocument, targetDocument, sourceModel, targetModel, ContentObject.DATA_TYPE_ACTIVE, ContentObject.DATA_TYPE_WORKING, languagesForSync, dynamicVariantPgtnNoLongerUsed);
                                    SyncTouchpointUtil.cleanUnusedDynamicVariantPgtn(targetModel, dynamicVariantPgtnNoLongerUsed);

                                    targetModel = CloneHelper.queryInSaveSession(() -> {
                                        HibernateUtil.getManager().getSession().flush();
                                        HibernateUtil.getManager().getSession().clear();
                                        return ContentObject.findById(targetModelId);
                                    });

                                    targetModel = CloneHelper.queryInSaveSession(() -> {
                                        ContentObject model = ContentObject.findById(targetModelId);
                                        model.activateWorkingData();
                                        HibernateUtil.getManager().getSession().flush();
                                        HibernateUtil.getManager().getSession().clear();
                                        TaskUtil.updateStatusForApprovables(List.of(ContentObject.findById(targetModelId)), -1, requestor);
                                        HibernateUtil.getManager().getSession().flush();
                                        HibernateUtil.getManager().getSession().clear();
                                        return ContentObject.findById(targetModelId);
                                    });
                                }

                                if (sourceHasWorking) {
                                    if (saveTargetModelId != null) {
                                        targetModel = CloneHelper.queryInSaveSession(() -> {
                                            {
                                                ContentObject model = ContentObject.findById(targetModelId);

                                                if (model.hasWorkingData()) {
                                                    model.discardWorkingData();
                                                    HibernateUtil.getManager().getSession().flush();
                                                    HibernateUtil.getManager().getSession().clear();
                                                }
                                            }

                                            {
                                                ContentObject model = ContentObject.findById(targetModelId);
                                                ContentObject savedModel = ContentObject.findById(saveTargetModelId);
                                                model.createWorkingDataFrom(savedModel.getContentObjectData(ContentObject.DATA_TYPE_WORKING));
                                                if (sourceLockedFor != null) {
                                                    model.setLockedFor(targetLockedFor);
                                                }
                                                HibernateUtil.getManager().getSession().flush();
                                                HibernateUtil.getManager().getSession().clear();
                                            }

                                            return ContentObject.findById(targetModelId);
                                        });
                                    }
                                    else {
                                        targetModel = CloneHelper.queryInSaveSession(()->{
                                            ContentObject model = ContentObject.findById(targetModelId);
                                            if (!model.hasWorkingData()) {
                                                if (model.hasActiveData()) {
                                                    model.createWorkingDataFromActive();
                                                } else if (model.hasArchivedData()) {
                                                    model.createWorkingDataFromArchive();
                                                } else {
                                                    model.createWorkingDataEmpty();
                                                }
                                                if (sourceLockedFor != null) {
                                                    model.setLockedFor(targetLockedFor);
                                                }
                                                HibernateUtil.getManager().getSession().flush();
                                                HibernateUtil.getManager().getSession().clear();
                                            }

                                            return ContentObject.findById(targetModelId);
                                        });
                                    }

                                    {
                                        if (CloneHelper.queryInSaveSession(() -> ContentObject.findById(targetModelId).hasWorkingData())) {
                                            SyncTouchpointUtil.mapDynamicTreeNodes(sourceDocument, targetDocument, sourceModel, targetModel, ContentObject.DATA_TYPE_WORKING, ContentObject.DATA_TYPE_WORKING);

                                            targetModel = CloneHelper.queryInSaveSession(() -> {
                                                return ContentObject.findById(targetModelId);
                                            });
                                        }
                                    }

                                    if (!syncActiveOnly) {
                                        Map<Long, String> dynamicVariantPgtnNoLongerUsed = new HashMap<>();
                                        SyncTouchpointUtil.syncContentObjectDataType(sourceDocument, targetDocument, sourceModel, targetModel, ContentObject.DATA_TYPE_WORKING, ContentObject.DATA_TYPE_WORKING, languagesForSync, dynamicVariantPgtnNoLongerUsed);
                                        SyncTouchpointUtil.cleanUnusedDynamicVariantPgtn(targetModel, dynamicVariantPgtnNoLongerUsed);
                                        targetModel = CloneHelper.queryInSaveSession(() -> {
                                            HibernateUtil.getManager().getSession().flush();
                                            HibernateUtil.getManager().getSession().clear();
                                            return ContentObject.findById(targetModelId);
                                        });
                                    }
                                }

                                if(sourceHasArchived && (!sourceHasActive) && (!sourceHasWorking)) {
                                    targetModel = CloneHelper.queryInSaveSession(() -> {
                                        {
                                            ContentObject model = ContentObject.findById(targetModelId);

                                            if (!model.hasArchivedData()) {
                                                if (!model.hasActiveData()) {
                                                    if (model.hasWorkingData()) {
                                                        model.activateWorkingData();
                                                        HibernateUtil.getManager().getSession().flush();
                                                        HibernateUtil.getManager().getSession().clear();
                                                    }
                                                }

                                                model = ContentObject.findById(targetModelId);
                                                if (model.hasActiveData()) {
                                                    model.archiveActiveData(false);
                                                    HibernateUtil.getManager().getSession().flush();
                                                    HibernateUtil.getManager().getSession().clear();
                                                }
                                            }
                                        }

                                        {
                                            ContentObject model = ContentObject.findById(targetModelId);
                                            if (model.hasWorkingData()) {
                                                model.discardWorkingData();
                                                HibernateUtil.getManager().getSession().flush();
                                                HibernateUtil.getManager().getSession().clear();
                                            }
                                        }

                                        {
                                            ContentObject model = ContentObject.findById(targetModelId);
                                            if (model.hasActiveData()) {
                                                model.archiveActiveData(false);
                                                HibernateUtil.getManager().getSession().flush();
                                                HibernateUtil.getManager().getSession().clear();
                                            }
                                        }

                                        return ContentObject.findById(targetModelId);
                                    });
                                }
                                else if(!sourceHasArchived) {
                                    targetModel = CloneHelper.queryInSaveSession(() -> {
                                        ContentObject model = ContentObject.findById(targetModelId);

                                        if(model.hasArchivedData()) {
                                            model.deleteArchiveData();

                                            HibernateUtil.getManager().getSession().flush();
                                            HibernateUtil.getManager().getSession().clear();
                                        }

                                        return ContentObject.findById(targetModelId);
                                    });
                                }

                                {
                                    CloneHelper.execInSaveSession(() -> {
                                        ContentObject model = ContentObject.findById(targetModelId);

                                        if (model.hasWorkingData()) {
                                            TaskUtil.createTaskForWorkingCopy(model, requestor);
                                        }
                                    });
                                }

                                if(saveTargetModelId != null) {
                                    CloneHelper.execInSaveSession(() -> {
                                        ContentObject model = ContentObject.findById(saveTargetModelId);
                                        model.deleteAllData();
                                        model.delete();
                                        HibernateUtil.getManager().getSession().flush();
                                    });
                                }
                            }
                        }

                        if (sourceModel != null && targetModel != null) {
                            ContentObject targetModelFinal = targetModel;
                            CloneHelper.execInSaveSession(()->{
                                targetModelFinal.save();
                            });
                            clonedOrSyncedModelMap.get().put(targetModel.getId(), sourceModel.getId());
                        }

                        if (syncObjectTypeId == SyncObjectType.ID_CONTENT_LIBRARY || syncObjectTypeId == SyncObjectType.ID_SMART_TEXT) {
                            associateDocuments(targetModel, new ArrayList<>(), targetDocument);
                        }

                        doHibernateUtilFlush();
                        doHibernateUtilClear(null, null, 0);



                        swapContentIfNotSwappedYet(serviceRequest);
                        swapComplexValueIfNotSwappedYet(serviceRequest);

                        doHibernateUtilFlush();
                        doHibernateUtilClear(null, null, 0);

                        long targetModelId = targetModel.getId();
                        targetModel = CloneHelper.queryInSaveSession(()->{
                            ContentObject contentObject = ContentObject.findById(targetModelId);
                            if(contentObject != null) {
                                SyncTouchpointUtil.updateContentObjectHash(contentObject.getId(), true);
                            }
                            HibernateUtil.getManager().getSession().flush();
                            HibernateUtil.getManager().getSession().clear();
                            return ContentObject.findById(targetModelId);
                        });

                        targetModel = CloneHelper.queryInSaveSession(() -> {
                            ContentObject contentObject = ContentObject.findById(targetModelId);
                            List<ParameterGroupTreeNode> pgtns = new ArrayList<>();
                            if (contentObject.isDynamicVariantEnabled(ContentObject.DATA_TYPE_WORKING)) {
                                pgtns.addAll(ParameterGroupTreeNode.findAllNodesForDynamicAsset(contentObject, ContentObject.DATA_TYPE_WORKING));
                            }
                            if (contentObject.isDynamicVariantEnabled(ContentObject.DATA_TYPE_ACTIVE)) {
                                pgtns.addAll(ParameterGroupTreeNode.findAllNodesForDynamicAsset(contentObject, ContentObject.DATA_TYPE_ACTIVE));
                            }
                            if (contentObject.isDynamicVariantEnabled(ContentObject.DATA_TYPE_ARCHIVED)) {
                                pgtns.addAll(ParameterGroupTreeNode.findAllNodesForDynamicAsset(contentObject, ContentObject.DATA_TYPE_ARCHIVED));
                            }
                            SyncTouchpointUtil.setupDynamicPgtnOriginObjects(pgtns);
                            HibernateUtil.getManager().getSession().flush();
                            HibernateUtil.getManager().getSession().clear();
                            return ContentObject.findById(targetModelId);
                        });

                        SyncObjectFactory.addCompletedSyncObject(syncObjectType, syncRequest, sourceModel, targetModel, objectIdx10);

                        if(isNewContentObject && sourceModel.getZone() != null) {
                            newContentObjectIDsMap.put(sourceModel.getId(), targetModel.getId());
                        }

                        result = result + 1;
                    } catch (Exception ex) {
                        SyncTouchpointUtil.rollbackAndContinue();
                        log.error(" unexpected exception when invoking SyncContentObjectsService syncContentObjects method", ex);
                        String objectName = sourceModel == null ? " UNKNOWN " : sourceModel.getName();
                        log.error("Failed to synchronize content object (id=" + objectId + ") \"" + objectName + "\" error " + ex.getMessage());
                        SyncObjectFactory.addErrorSyncObject(syncObjectType, syncRequest, sourceModel, null, ex.getMessage(), objectIdx10);
                    } finally {
                        SyncTouchpointUtil.commitAndContinue();
                        doHibernateUtilClear(Collections.singletonList(sourceDocument), Collections.singletonList(targetDocument), 0);
                    }

                    response.setResultValueBean(result);
                }

                try {
                    doHibernateUtilFlush();
                } catch (Exception e) {
                    throw e;
                }
            }
        }

        try {
            if(! newContentObjectIDsMap.isEmpty()) {
//                  tsdna+zonedna
                Map<String, Map<String, ContentObjectZonePriority>> sourceVariantZoneContentObjectPriorities = new HashMap<>();
                Map<String, Map<String, ContentObjectZonePriority>> targetVariantZoneContentObjectPriorities = new HashMap<>();
                TouchpointSelection masterVariant = sourceDocument.getMasterTouchpointSelection();
                getVariantZoneContentObjectPriorityMap(newContentObjectIDsMap.keySet(), sourceVariantZoneContentObjectPriorities, sourceDocument);
                CloneHelper.execInSaveSession(()->
                    getVariantZoneContentObjectPriorityMap(newContentObjectIDsMap.values(), targetVariantZoneContentObjectPriorities, targetDocument)
                );

                for(String tsZoneDna : sourceVariantZoneContentObjectPriorities.keySet()) {
                    if(targetVariantZoneContentObjectPriorities.containsKey(tsZoneDna)) {
                        Map<String, ContentObjectZonePriority> sourceContentObjectPriorityMap = sourceVariantZoneContentObjectPriorities.get(tsZoneDna);
                        Map<String, ContentObjectZonePriority> targetContentObjectPriorityMap = targetVariantZoneContentObjectPriorities.get(tsZoneDna);
                        if(sourceContentObjectPriorityMap.size() == targetContentObjectPriorityMap.size()) {
                            if(sourceContentObjectPriorityMap.keySet().containsAll(targetContentObjectPriorityMap.keySet())) {
                                List<String> contentObjectDataIds = new ArrayList<>(sourceContentObjectPriorityMap.keySet());

                                Collections.sort(contentObjectDataIds, (s1, s2)->{
                                    ContentObjectZonePriority o1 = sourceContentObjectPriorityMap.get(s1);
                                    ContentObjectZonePriority o2 = sourceContentObjectPriorityMap.get(s2);
                                    return o1.getContentObjectPriority().compareTo(o2.getContentObjectPriority());
                                });

                                CloneHelper.execInSaveSession(()-> {
                                    LinkedList<Long> targetPriorityNumbers = new LinkedList(
                                        targetContentObjectPriorityMap.values()
                                            .stream()
                                            .map(ContentObjectZonePriority::getContentObjectPriority)
                                            .sorted()
                                            .collect(Collectors.toList())
                                    );

                                    for (String contentObjectDataId : contentObjectDataIds) {
                                        ContentObjectZonePriority contentObjectZonePriority = targetContentObjectPriorityMap.get(contentObjectDataId);
                                        Long contentObjectPriority = targetPriorityNumbers.removeFirst();
                                        contentObjectZonePriority.setContentObjectPriority(contentObjectPriority);
                                        contentObjectZonePriority.save();
                                    }
                                });
                            }
                        }
                    }
                }
            }
        } catch (Exception ex) {
            SyncTouchpointUtil.rollbackAndContinue();
        } finally {
            SyncTouchpointUtil.commitAndContinue();
        }

//        doHibernateUtilFlush();
//        doHibernateUtilClear(Arrays.asList(sourceDocument), Arrays.asList(targetDocument));
    }

    private static final String masterVariantDna = "masterVariantDna";

    private void getVariantZoneContentObjectPriorityMap(Collection<Long> contentObjectIDs, Map<String, Map<String, ContentObjectZonePriority>> variantZoneContentObjectPriorities, Document document) {
        TouchpointSelection masterVariant = document.getMasterTouchpointSelection();

        for(Long contentObjectID : contentObjectIDs) {
            ContentObject contentObject = ContentObject.findById(contentObjectID);
            List<ContentObjectZonePriority> contentObjectZonePriorityList = ContentObjectZonePriority.findByContentObject(contentObject);
            for(ContentObjectZonePriority contentObjectZonePriority : contentObjectZonePriorityList) {
                TouchpointSelection touchpointSelection = contentObjectZonePriority.getTouchpointSelection();
                String touchpointSelectionDna = touchpointSelection == null ? masterVariantDna : touchpointSelection.getDna();
                Zone zone = contentObjectZonePriority.getZone();
                int dataType = contentObjectZonePriority.getDataType();
                String tsZoneDna = touchpointSelectionDna + " - " + zone.getDna();
                Map<String, ContentObjectZonePriority> contentObjectPriorityMap = variantZoneContentObjectPriorities.get(tsZoneDna);
                if(contentObjectPriorityMap == null) {
                    contentObjectPriorityMap = new HashMap<>();
                    variantZoneContentObjectPriorities.put(tsZoneDna, contentObjectPriorityMap);
                }
                String codDna = contentObject.getDna() + " - " + dataType;
                contentObjectPriorityMap.put(codDna, contentObjectZonePriority);
            }
        }
    }


    private void checkNeedExistPrerequisiteObjects(SyncRequest syncRequest, Document targetDocument, Long objectIdx10) {
        Map<Long, List<Long>> needExistMap = syncRequest.getNeedExistMap();
        Map<Long, Long> objectTypeMap = syncRequest.getObjectTypeMap();
        Map<Long, Long> objectStatusMap = syncRequest.getObjectStatusMap();

        if(needExistMap != null) {
            List<Long> needExistList = needExistMap.get(objectIdx10);
            if(needExistList != null) {
                for(Long needExistObjectIdx10 : needExistList) {
                    Long needExistObjectType = objectTypeMap.get(needExistObjectIdx10);
                    Long needExistObjectStatus = objectStatusMap.get(needExistObjectIdx10);
                    long needExistobjectId = needExistObjectIdx10 / 0x10;

                    boolean needExistObjectIsReferenced = (needExistObjectStatus & 0x100000) > 0;
                    boolean needExistObjectIsFromOther = (needExistObjectStatus & 0x10000) > 0;
                    needExistObjectStatus = needExistObjectStatus & 0xFFFF;

                    if(needExistObjectIsFromOther) {
                        switch (needExistObjectType.intValue()) {
                            case SyncObjectType.ID_SMART_TEXT:
                            case SyncObjectType.ID_CONTENT_LIBRARY:
                            case SyncObjectType.ID_LOCAL_SMART_TEXT:
                            case SyncObjectType.ID_LOCAL_IMAGE:
                                ContentObject sourceNeedExistContentObject = ContentObject.findById(needExistobjectId);
                                if(sourceNeedExistContentObject != null) {
                                    String dna = sourceNeedExistContentObject.getDna();
                                    boolean isGlobalContentObject = sourceNeedExistContentObject.isGlobalContentObject();

                                    ContentObject targetNeedExistContentObject = CloneHelper.queryInSaveSession(() ->
                                        isGlobalContentObject ?
                                            ContentObject.findGlobalObjectByDna(dna) :
                                            ContentObject.findByDnaAndDocument(dna, targetDocument)
                                        );

                                    if(targetNeedExistContentObject == null) {
                                        ContentObject clonedContentObject = CloneHelper.clone(sourceNeedExistContentObject, o -> {
                                            ContentObject clone = o.clone(targetDocument, ContentObject.CLONE_CONTENT_NONE, null, ContentObject.DATA_TYPE_ALL);
                                            return clone;
                                        });

                                        if (isGlobalContentObject) {
                                            CloneHelper.execInSaveSession(() -> {
                                                clonedContentObject.getDocuments().add(targetDocument);
                                            });
                                        }

                                        CloneHelper.execInSaveSession(() -> {
                                            clonedContentObject.save();
                                        });

                                        Map<Long, SyncState> syncStateMap = syncRequest.getSyncStateMap();
                                        if (syncStateMap == null) {
                                            syncStateMap = new HashMap<>();
                                            syncRequest.setSyncStateMap(syncStateMap);
                                        }
                                        syncStateMap.put(needExistObjectIdx10, SyncState.CREATED);
                                    }
                                }
                                break;
                        }
                    }
                }
            }
        }
    }

    private void associateDocuments(ContentObject targetContentObject, List<Long> documentIDs, Document targetDocument) {
        CloneHelper.execInSaveSession(()->{
            Set<Long> targetContentObjectDocumentIDs = targetContentObject.getDocuments().stream().map(d->d.getId()).collect(Collectors.toSet());
            if(documentIDs != null) {
                for (Long documentId : documentIDs) {
                    if (!targetContentObjectDocumentIDs.contains(documentId)) {
                        Document document = Document.findById(documentId);
                        if (document != null) {
                            targetContentObject.getVisibleDocuments().add(document);
                        }
                        targetContentObjectDocumentIDs.add(documentId);
                    }
                }
            }

            if(targetDocument != null) {
                if (!targetContentObjectDocumentIDs.contains(targetDocument.getId())) {
                    Document document = Document.findById(targetDocument.getId());
                    if (document != null) {
                        targetContentObject.getVisibleDocuments().add(document);
                    }
                    targetContentObjectDocumentIDs.add(targetDocument.getId());
                }
            }

            Document.findAllRemovedOrTrash().forEach(
                deletedDocument -> targetContentObject.getVisibleDocuments().remove(deletedDocument)
            );

            targetContentObject.save();
        });
    }

    private void checkZoneGlobalObjects(SyncServiceRequest serviceRequest) {
        Document sourceDocument = serviceRequest.getSyncRequest().getSourceDocument();
        Document targetDocument = serviceRequest.getSyncRequest().getTargetDocument();
        User requestor = serviceRequest.getSyncRequest().getRequestor();

        Set<Zone> targetZones = CloneHelper.queryInSaveSession(()->targetDocument.getZones());
        for(Zone targetZone : targetZones) {
            String zoneDna = CloneHelper.queryInSaveSession(()->targetZone.getDna());
            Zone sourceZone = Zone.findByDnaAndDocument(zoneDna, sourceDocument);
            if(sourceZone != null) {
                {
                    ContentObject sourceDefaultCommunicationTemplateSmartText = sourceZone.getDefaultCommunicationTemplateSmartText();
                    if(sourceDefaultCommunicationTemplateSmartText != null) {
                        String smartTextDna = sourceDefaultCommunicationTemplateSmartText.getDna();
                        ContentObject targetDefaultCommunicationTemplateSmartText = CloneHelper.queryInSaveSession(() -> ContentObject.findGlobalObjectByDna(smartTextDna));
                        CloneHelper.execInSaveSession(() -> targetZone.setDefaultCommunicationTemplateSmartText(targetDefaultCommunicationTemplateSmartText));
                    }
                }

                {
                    Set<ContentObject> sourceEmbeddedContentAssets = sourceZone.getSmartTextAssets();
                    for (ContentObject sourceModel : sourceEmbeddedContentAssets) {
                        String smartTextDna = sourceModel.getDna();
                        ContentObject targetModel = CloneHelper.queryInSaveSession(() -> ContentObject.findGlobalObjectByDna(smartTextDna));
                        if(targetModel != null) {
                            CloneHelper.execInSaveSession(() -> {
                                if(! targetZone.getSmartTextAssets().stream().anyMatch(co->co.getId() == targetModel.getId())) {
                                    targetZone.getSmartTextAssets().add(targetModel);
                                }
                            });
                        }
                    }
                }

                {
                    ContentObject sourceDefaultCommunicationTemplateImage = sourceZone.getDefaultCommunicationTemplateImage();
                    if(sourceDefaultCommunicationTemplateImage != null) {
                        String imageDna = sourceDefaultCommunicationTemplateImage.getDna();
                        ContentObject targetDefaultCommunicationTemplateImage = CloneHelper.queryInSaveSession(()->ContentObject.findGlobalObjectByDna(imageDna));
                        CloneHelper.execInSaveSession(()->targetZone.setDefaultCommunicationTemplateImage(targetDefaultCommunicationTemplateImage));
                    }
                }

                {
                    Set<ContentObject> sourceImageAssets = sourceZone.getImageAssets();
                    for(ContentObject sourceModel : sourceImageAssets) {
                        String imageDna = sourceModel.getDna();
                        ContentObject targetModel = CloneHelper.queryInSaveSession(()->ContentObject.findGlobalObjectByDna(imageDna));
                        if(targetModel != null) {
                            CloneHelper.execInSaveSession(() -> {
                                if(! targetZone.getImageAssets().stream().anyMatch(co->co.getId() == targetModel.getId())) {
                                    targetZone.getImageAssets().add(targetModel);
                                }
                            });
                        }
                    }
                }
            }
        }

        doHibernateUtilFlush();
    }

    private void swapContentIfNotSwappedYet(SyncServiceRequest serviceRequest) {
        Document sourceDocument = serviceRequest.getSyncRequest().getSourceDocument();
        Document targetDocument = serviceRequest.getSyncRequest().getTargetDocument();
//        User requestor = serviceRequest.getSyncRequest().getRequestor();

        Map<Long, Long> objectIDsMap = CloneHelper.getClonedIDsMap();

        CloneHelper.execInSaveSession(()->{
            Set<Long> contentIDs = CloneHelper.getContentIDsForSwap();
            int total = contentIDs.size();
            if(total == 0) return;
            int count = 0;
            for(Long contentId : contentIDs) {
                Content content = Content.findById(contentId);
                if(content != null) {
                    content.swapCrossSchemaObjectsRef(objectIDsMap);
                    content.setNeedRecalculateHash(false);
                    content.setResolveRelations(true);
                    content.save();
                }
/*
                count ++;
                int progress = startPercentage + (count * totalPercentage) / total;
                if(progress != statusPollingBackgroundTask.getProgressInPercentInThread()) {
                    statusPollingBackgroundTask.setProgressInPercentInThread(progress);
                    statusPollingBackgroundTask.save();
                    doHibernateUtilFlush();
                    doHibernateUtilClear(null, null, 0);
                }
 */
            }
        });

        doHibernateUtilFlush();
        doHibernateUtilClear(Collections.singletonList(sourceDocument), Collections.singletonList(targetDocument));
    }

    private void swapComplexValueIfNotSwappedYet(SyncServiceRequest serviceRequest) {
        Document sourceDocument = serviceRequest.getSyncRequest().getSourceDocument();
        Document targetDocument = serviceRequest.getSyncRequest().getTargetDocument();
//        User requestor = serviceRequest.getSyncRequest().getRequestor();

        Map<Long, Long> objectIDsMap = CloneHelper.getClonedIDsMap();

        CloneHelper.execInSaveSession(()->{
            Set<Long> complexValueIDs = CloneHelper.getComplexValueIDsForSwap();
            int total = complexValueIDs.size();
            if(total > 0) {
                int count = 0;
                for(Long complexValueId : complexValueIDs) {
                    ComplexValue complexValue = ComplexValue.findById(complexValueId);
                    if(complexValue != null) {
                        complexValue.swapCrossSchemaObjectsRef(objectIDsMap);
                        complexValue.save();
                    }
                    count ++;
/*
                    int progress = startPercentage + (count * totalPercentage) / total;
                    if(progress != statusPollingBackgroundTask.getProgressInPercentInThread()) {
                        statusPollingBackgroundTask.setProgressInPercentInThread(progress);
                        statusPollingBackgroundTask.save();
                        doHibernateUtilFlush();
                    }
 */
                }
            }
        });

        doHibernateUtilFlush();
        doHibernateUtilClear(Collections.singletonList(sourceDocument), Collections.singletonList(targetDocument));
    }

    private void updateSqlExpression(SyncServiceRequest serviceRequest) {
        Document sourceDocument = serviceRequest.getSyncRequest().getSourceDocument();
        Document targetDocument = serviceRequest.getSyncRequest().getTargetDocument();
        CloneHelper.execInSaveSession(()->{
            List<DataElementVariable> allDocVisibleDevs = DataElementVariable.findAllForDocument(targetDocument);
            for(DataElementVariable dev : allDocVisibleDevs) {
                if(dev.isExpressionVariable()) {
                    ComplexValue cv = dev.getExpression();
                    if(cv != null) {
                        String sqlExpression = DataElementVariable.calculateSQLExpression(dev);
                        dev.setSqlExpression(sqlExpression);
                        dev.save();
                    }
                }
            }
        });

        doHibernateUtilFlush();
        doHibernateUtilClear(Collections.singletonList(sourceDocument), Collections.singletonList(targetDocument));
    }


    private void updateContentObjectsSyncHistory(SyncServiceRequest serviceRequest, Integer syncOperationType) {
        Document sourceDocument = serviceRequest.getSyncRequest().getSourceDocument();
        Document targetDocument = serviceRequest.getSyncRequest().getTargetDocument();
        User requestor = serviceRequest.getSyncRequest().getRequestor();

        String sourceNodeGuid = Node.getCurrentNode().getGuid();
        String targetNodeGuid = CloneHelper.queryInSaveSession(() -> Node.getCurrentNode().getGuid());
        String targetSchemaName = CloneHelper.queryInSaveSession(() -> Node.getCurrentNode().getSchemaName());

        boolean syncActiveOnly = serviceRequest.getSyncRequest().isSyncActiveOnly();
        boolean hideUntilNextChange = serviceRequest.getSyncRequest().getHideUntilNextChange();

        Set<Long> languagesForSync = serviceRequest.getSyncRequest().getLanguagesForSync();

        for(Map.Entry<Long, Long> entry :  clonedOrSyncedModelMap.get().entrySet()) {
            Long targetModelId = entry.getKey();
            Long sourceModelId = entry.getValue();
            Map<Long, Long> modelIdMap = new HashMap<>();
            modelIdMap.put(targetModelId, sourceModelId);

            SyncTouchpointUtil.updateContentObjectSyncHistory(syncOperationType,
                    modelIdMap,
                    sourceDocument.getId(), targetDocument.getId(),
                    requestor.getId(),
                    sourceNodeGuid, targetNodeGuid,
                    targetSchemaName,
                    syncActiveOnly,
                    hideUntilNextChange, languagesForSync);

            doHibernateUtilFlush();
            doHibernateUtilClear(null, null, 0);
        }

        doHibernateUtilClear(Collections.singletonList(sourceDocument), Collections.singletonList(targetDocument));
    }

    private void doHibernateUtilFlush() {
        long startTime = System.currentTimeMillis();
        if(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
            HibernateUtil.getManager().getSession().flush();
        } else {
            CloneHelper.getCloneSession().flush();
        }
        log.info("doHibernateUtilFlush; TIME TAKEN:<" + (System.currentTimeMillis() - startTime) + ">");
    }

    private int clearSessionThreshold = 200000; // from experience

    public void setClearSessionThreshold(int clearSessionThreshold) {
        this.clearSessionThreshold = clearSessionThreshold;
    }

    private void doHibernateUtilClear(Collection<IdentifiableMessagePointModel> sourceObjects, Collection<IdentifiableMessagePointModel> targetObjects) {
        doHibernateUtilClear(sourceObjects, targetObjects, clearSessionThreshold);
    }

    private void doHibernateUtilClear(Collection<IdentifiableMessagePointModel> sourceObjects, Collection<IdentifiableMessagePointModel> targetObjects, int threshold) {
        if(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
            int entityCount = HibernateUtil.getManager().getSession().getStatistics().getEntityCount();
            if(entityCount > threshold) {
                HibernateUtil.getManager().getSession().clear();
                if (sourceObjects != null) {
                    for(IdentifiableMessagePointModel sourceObj : sourceObjects) {
                        HibernateUtil.getManager().getSession().refresh(sourceObj);
                    }
                }
                if (targetObjects != null) {
                    for(IdentifiableMessagePointModel targetObj : targetObjects) {
                        HibernateUtil.getManager().getSession().refresh(targetObj);
                    }
                }
            }
        } else {
            int entityCount = CloneHelper.getCloneSession().getStatistics().getEntityCount();
            if(entityCount > threshold) {
                CloneHelper.getCloneSession().clear();
                if (targetObjects != null) {
                    CloneHelper.execInSaveSession(() -> {
                        for(IdentifiableMessagePointModel targetObj : targetObjects) {
                            HibernateUtil.getManager().getSession().refresh(targetObj);
                        }
                    });
                }
            }
        }
    }

}
