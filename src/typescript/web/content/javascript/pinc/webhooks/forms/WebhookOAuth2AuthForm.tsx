
interface WebhookOAuth2AuthFormProps {
    oauth2Auth: OAuth2Auth;
    setOAuth2Auth: (value: OAuth2Auth) => void;
}

const WebhookOAuth2AuthForm = (props: WebhookOAuth2AuthFormProps) => {

    const { oauth2Auth, setOAuth2Auth } = props;

    return (
        <>
            {/* Client ID */}
            <TextInput
                label={client_messages.pinc.client_id}
                value={oauth2Auth.clientId}
                onChange={value => setOAuth2Auth({ ...oauth2Auth, clientId: value })}
            />

            {/* Client Secret */}
            <TextInput
                label={client_messages.pinc.client_secret}
                value={oauth2Auth.clientSecret !== undefined ? oauth2Auth.clientSecret : ''}
                onChange={value => setOAuth2Auth({ ...oauth2Auth, clientSecret: value })}
                masked={true}
            />

            {/* Token URL */}
            <TextInput
                label={client_messages.pinc.token_url}
                value={oauth2Auth.tokenUri}
                onChange={value => setOAuth2Auth({ ...oauth2Auth, tokenUri: value })}
            />

            {/* Scope */}
            <TextInput
                label={client_messages.pinc.scope}
                value={oauth2Auth.scope}
                onChange={value => setOAuth2Auth({ ...oauth2Auth, scope: value })}
            />
        </>
    );
}