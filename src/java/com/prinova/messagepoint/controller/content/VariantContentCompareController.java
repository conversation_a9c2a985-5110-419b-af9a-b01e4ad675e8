package com.prinova.messagepoint.controller.content;

import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.common.SyncObjectType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.jstree.TouchpointSelectionWrapper;
import org.json.JSONArray;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

public class VariantContentCompareController extends MessagepointController {
	public static final String PARM_ACTION 					= "action";
	public static final String PARAM_MODEL_ID 				= "modelId";
	public static final String PARAM_COMPARE_FROM_ID		= "variantId";
	public static final String PARAM_COMPARE_TO_ID			= "compareToId";
    public static final String PARAM_COMPARE_DATA_TYPE		= "compareDataType";
	public static final String PARAM_LOCALE_ID 				= "localeId";
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		long modelId = ServletRequestUtils.getLongParameter(request, PARAM_MODEL_ID, -1L);
		long selectedVariantId = ServletRequestUtils.getLongParameter(request, PARAM_COMPARE_FROM_ID, -1L);
		long localeId	= ServletRequestUtils.getLongParameter(request, PARAM_LOCALE_ID, -1L);
		int dateType = ServletRequestUtils.getIntParameter(request, PARAM_LOCALE_ID, -1);
		ParameterGroupTreeNode selectedPGTN = null;
		if(selectedVariantId > 0){
			selectedPGTN = ParameterGroupTreeNode.findById(selectedVariantId);
		}else{
			selectedVariantId = ParameterGroupTreeNode.MASTER_VARIANCE_ID;
		}

		SyncObjectType objectType = null;
		boolean isDynamicAsset = false;
		boolean isStructuredAsset = false;
		Long channelId = null;
		JSONArray textStyleData = null, paragraphStyleData = null, listStyleData = null;
		String defaultViewCSSFilePath = null, textCSSFilename = null, paragraphCSSFilename = null, listCSSFilename = null;
		ContentObject contentObject = ContentObject.findById(modelId);
		Document document = contentObject.getFirstDocumentDelivery();

		if (contentObject.isMessage())
		{
			objectType = new SyncObjectType(SyncObjectType.ID_MESSAGE);
			channelId = contentObject.getZone().getChannel();
		}
		else if (contentObject.isLocalSmartText())
		{
			objectType = new SyncObjectType(SyncObjectType.ID_LOCAL_SMART_TEXT);
			channelId = contentObject.getDocument().getConnectorConfiguration().getChannel().getId();
		}
		else if (contentObject.isLocalImage())
		{
			objectType = new SyncObjectType(SyncObjectType.ID_LOCAL_IMAGE);
		}
		else if (contentObject.isGlobalSmartText())
		{
			objectType = new SyncObjectType(SyncObjectType.ID_SMART_TEXT);
			channelId = contentObject.getFirstDocumentDelivery().getConnectorConfiguration().getChannel().getId();
		}
		else if (contentObject.isGlobalImage())
		{
			objectType = new SyncObjectType(SyncObjectType.ID_CONTENT_LIBRARY);
		}

		if(contentObject.getStyles() != null && !contentObject.getStyles().isEmpty()){
			textStyleData = contentObject.getTextStyleData();
		}
		if(contentObject.getParagraphStyles() != null && !contentObject.getParagraphStyles().isEmpty()){
			paragraphStyleData = contentObject.getParagraphStyleData();
		}
		if(contentObject.getListStyles() != null && !contentObject.getListStyles().isEmpty()){
			listStyleData = contentObject.getListStyleData();
		}
		defaultViewCSSFilePath = contentObject.getDefaultViewCSSFilePath();
		textCSSFilename = contentObject.getCSSFilename();
		paragraphCSSFilename = contentObject.getParagraphCSSFilename();
		listCSSFilename = contentObject.getListCSSFilename();

		if(contentObject.isStructuredContentEnabled()){
			TouchpointSelectionWrapper tpSelectionWrapper = new TouchpointSelectionWrapper(document.getMasterTouchpointSelection(), contentObject.getOwningTouchpointSelection(), contentObject.getId(), contentObject.isFocusOnActiveData());
			referenceData.put("tpSelectionWrapper", tpSelectionWrapper);

			TouchpointSelection touchpointSelection = contentObject.getOwningTouchpointSelection() != null ? contentObject.getOwningTouchpointSelection() : document.getMasterTouchpointSelection();
			referenceData.put("variant", touchpointSelection);

			selectedVariantId = TouchpointSelection.findByPgTreeNodeId(selectedPGTN.getId()).getId();
		}

		if(contentObject.isMultipartType()){
			List<ZonePart> zoneParts = new ArrayList<>();
			Set<String> zonePartDnaProcessed = new HashSet<>();
			for(ZonePart zonePart : contentObject.getZone().getParts()) {
				zoneParts.add(zonePart);
				zonePartDnaProcessed.add(zonePart.getDna());
			}

			Collections.sort(zoneParts, new ZonePartComparator());
			referenceData.put("zoneParts", zoneParts);
		}

		referenceData.put("model", contentObject);
		isDynamicAsset = contentObject.isDynamicVariantEnabled(dateType);
		isStructuredAsset = contentObject.isStructuredContentEnabled();

		referenceData.put("objectType", objectType);
		referenceData.put("channelId", channelId);
		referenceData.put("textStyleData", textStyleData);
		referenceData.put("paragraphStyleData", paragraphStyleData);
		referenceData.put("listStyleData", listStyleData);
		referenceData.put("defaultViewCSSFilePath", defaultViewCSSFilePath);
		referenceData.put("textCSSFilename", textCSSFilename);
		referenceData.put("paragraphCSSFilename", paragraphCSSFilename);
		referenceData.put("listCSSFilename", listCSSFilename);
		referenceData.put("selectedVariantId", selectedVariantId);
		referenceData.put("isDynamicAsset", isDynamicAsset);
		referenceData.put("isStructuredAsset", isStructuredAsset);
		
		referenceData.put("locales", contentObject.getContentObjectLanguagesAsLocales());
		
		if(localeId > 0){
			Document doc = UserUtil.getCurrentTouchpointContext();
			if(doc != null){
				MessagepointLocale langLocale = MessagepointLocale.findById(localeId);
				referenceData.put("langLocale", langLocale);
			}
		}
		
		return referenceData;
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		return new VariantContentCompareWrapper();
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		
	}
	
	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command,
			BindException errors) throws Exception {
		return null;
	}
}
