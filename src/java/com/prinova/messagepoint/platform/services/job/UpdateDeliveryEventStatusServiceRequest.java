package com.prinova.messagepoint.platform.services.job;

import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class UpdateDeliveryEventStatusServiceRequest implements ServiceRequest {

	private static final long serialVersionUID = 5356223676332906466L;
	private long deliveryEventId; 
	private long newStatusId;
	private User requestor;
	public long getDeliveryEventId() {
		return deliveryEventId;
	}
	public void setDeliveryEventId(long deliveryEventId) {
		this.deliveryEventId = deliveryEventId;
	}
	public long getNewStatusId() {
		return newStatusId;
	}
	public void setNewStatusId(long newStatusId) {
		this.newStatusId = newStatusId;
	}
	public User getRequestor() {
		return requestor;
	}
	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}
}
