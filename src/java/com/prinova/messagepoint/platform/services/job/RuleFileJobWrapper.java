package com.prinova.messagepoint.platform.services.job;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.ZipOutputStream;

import com.prinova.messagepoint.util.filewriterimpl.BinaryFileWriter;

// Takes files given in conditions, and defines the unique name that the file
// will have.  Also able to write the file to the job zip file.
public class RuleFileJobWrapper 
{
	private Map<String, String> paths = new HashMap<>();
	private long nextId = 1;

	public String add( String path )
	{
		if ( paths.containsKey(path) )
			return paths.get(path);
		
		String zipFilename = "rules/R" + nextId++ + ".txt";
		paths.put(path, zipFilename);

		return zipFilename;
	}

	public void writeToZip(ZipOutputStream fileOut) throws Exception
	{
		for( String path : paths.keySet() )
		{
			File f = new File(path);
			String filename = paths.get(path);

			BinaryFileWriter imageWriter = new BinaryFileWriter(f);
			imageWriter.writeWithName(filename, fileOut);

			fileOut.flush();
		}
	}
}
