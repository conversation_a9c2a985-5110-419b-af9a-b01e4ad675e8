package com.prinova.messagepoint.model.message.content;

import com.prinova.messagepoint.controller.content.ContentVO;
import com.prinova.messagepoint.controller.tpadmin.TouchpointContentSelectionViewController;
import com.prinova.messagepoint.controller.tpadmin.TouchpointSelectionsListVO;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.ContentType;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroupInstanceCollection;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.common.TouchpointSelectionListFilterType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.tag.view.tree.TreeConstants;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.UserUtil;

import java.text.MessageFormat;
import java.util.*;

/**
 * 
 * Prinova Inc. 1998-2011
 * 678 Belmont Ave West, Unit 201, KW, ON N2M 1N6
 * All rights reserved.
 * 
 * SelectableContentUtil
 *
 * @contentRefactor
 * 
 * @since 3.5
 * <AUTHOR> Team
 */
public class SelectableContentUtil {

	public static final String GRAPHIC_IMAGE_NAME 			= "imageName";
	public static final String GRAPHIC_UPLOAD_DATE 			= "uploadDate";
	public static final String GRAPHIC_APPLIED_IMAGE_NAME 	= "appliedImageName";
	public static final String GRAPHIC_IMAGE_LINK 			= "imageLink";
	public static final String GRAPHIC_IMAGE_ALT_TEXT		= "imageAltText";
	public static final String GRAPHIC_IMAGE_EXT_LINK		= "imageExtLink";
	public static final String GRAPHIC_IMAGE_EXT_PATH		= "imageExtPath";
	
	// TOUCHPOINT SELECTION TREE
	public static String getTouchpointSelectionTreeAsHTML(long selectedId, Document document, boolean isStatusViewActive, User requestor) {
		StringBuffer treeHTML = new StringBuffer();
		treeHTML.append("");
		TouchpointSelection masterSelection = document.getMasterTouchpointSelection();
		long contentObjectId;
		TouchpointContentObjectContentSelection cSel = TouchpointContentObjectContentSelection.findById(selectedId);
		contentObjectId = cSel.getContentObject().getId();

		TouchpointSelectionsListVO masterSelectionVO = new TouchpointSelectionsListVO(masterSelection, 0, TouchpointSelectionListFilterType.ID_ALL, requestor, -1, null, null);
		getTouchpointSelectionHTMLchildNodes(treeHTML, masterSelectionVO, requestor, selectedId, contentObjectId, isStatusViewActive);

		return treeHTML.toString();
	}
	
	public static String getTouchpointSelectionAnsestorPath(long contentSelectionId, Document document, boolean isStatusViewActive){	
		long contentObjectId;
		ParameterGroupTreeNode node = null;
		TouchpointContentObjectContentSelection cSel = TouchpointContentObjectContentSelection.findById(contentSelectionId);
		ContentObjectAssociation ca = ContentObjectAssociation.findById(contentSelectionId);
		node = ca.getPGTreeNode();

		TouchpointSelection tpSelection = TouchpointSelection.findByPGTN(node.getId());
		List<TouchpointSelection> ansestors = tpSelection.getAncesters();
		StringBuilder parentsPath = new StringBuilder();
		for(TouchpointSelection parent : ansestors){
			long childContentSelectionId = -1L;
			ContentObjectAssociation mca = ContentObjectAssociation.findByContentObjectAndParameters(cSel.getContentObject(), isStatusViewActive ? ContentObject.DATA_TYPE_ACTIVE : ContentObject.DATA_TYPE_WORKING, parent.getParameterGroupTreeNode(), null, null);
			if (mca!=null) childContentSelectionId = mca.getId();
			if ( childContentSelectionId != -1L)
				parentsPath.append(childContentSelectionId).append(" ");
		}
		return parentsPath.toString().trim();
	}
	
	public static StringBuffer getTouchpointSelectionHTMLChildNodes(long contentSelectionId, long parentSelectedId, Document document, boolean isStatusViewActive, User requestor) {		
		StringBuffer treeHTML = new StringBuffer();
		treeHTML.append("");
		
		long contentObjectId;
		TouchpointContentObjectContentSelection cSel = TouchpointContentObjectContentSelection.findById(contentSelectionId);
		ContentObject contentObject = cSel.getContentObject();

		List<TouchpointSelection> childrenTpSelections = new ArrayList<>();
		if(parentSelectedId==0){ // Get the master selection node
			TouchpointSelection masterSelection;
			if (contentObject != null && contentObject.isVariantType())
				masterSelection = contentObject.getOwningTouchpointSelection();
			else
				masterSelection = document.getMasterTouchpointSelection();
			childrenTpSelections.add(masterSelection);
		}else{
			ContentObjectAssociation ca = HibernateUtil.getManager().getObject(ContentObjectAssociation.class, parentSelectedId);
			ParameterGroupTreeNode node = ca.getPGTreeNode();
			TouchpointSelection tpSelection = TouchpointSelection.findByPGTN(node.getId());
			childrenTpSelections.addAll(tpSelection.getChildrenOrderByName());
		}
		if(!childrenTpSelections.isEmpty()){

			for(TouchpointSelection childSelection : childrenTpSelections){
				
				boolean nodeIsVisible		= childSelection.isFullyVisible() || childSelection.isVisible(requestor);
				
				if (!nodeIsVisible && !childSelection.hasVisibleChildrenForUser(requestor))
					continue;
				
				boolean noProduction = true;
				long childContentSelectionId = -1L;
				ContentObjectAssociation mca = ContentObjectAssociation.findByContentObjectAndParameters(cSel.getContentObject(), isStatusViewActive ? ContentObject.DATA_TYPE_ACTIVE : ContentObject.DATA_TYPE_WORKING, childSelection.getParameterGroupTreeNode(), null, null);
				if (mca!=null) childContentSelectionId = mca.getId();

				boolean nodeDisabled 		= ( isStatusViewActive && (noProduction || (document.isEnabledForVariantWorkflow() && !childSelection.getHasActiveCopy() && !childSelection.isMaster())) ) || ( isStatusViewActive && childSelection.isMaster() && !contentObject.hasActiveData() ) || !nodeIsVisible;
				boolean nodeHasChildren 	= touchpointSelectionHasChild(childSelection.getParameterGroupTreeNode(), isStatusViewActive);
				String hrefPath 			= nodeDisabled ? "#" : "touchpoint_content_selection_view.form?contentSelectionId=" + childContentSelectionId + "&touchpointSelectionId=" + childSelection.getId() + "&statusViewId=" + (isStatusViewActive ? TouchpointContentSelectionViewController.VIEW_ACTIVE : TouchpointContentSelectionViewController.VIEW_WORKING_COPY);
				String nodeName 			= childSelection.getName();
				String nodeSelectStatus 	= (contentSelectionId == childContentSelectionId) ? "selectedTreeNode" : "";
				String nodeEnabledStatus 	= nodeDisabled ? "disabledTreeNode" : "";
				
				// Node HTML
				treeHTML.append("<li id=\"").append(childContentSelectionId).append("\" class=\"").append(nodeHasChildren ? "jstree-open" : "jstree-leaf").append("\"> \n");
				
				treeHTML.append( MessageFormat.format(TreeConstants.HTML_NODE_LINK_W_PARAM, new Object[] { hrefPath, nodeName,
						 nodeName, "", nodeSelectStatus + " " + nodeEnabledStatus }) );		
				if(nodeHasChildren){
					treeHTML.append("<ul>\n");
					treeHTML.append("<li class='dummy' style='display:none;'/>");
					treeHTML.append("</ul>\n");
				}
				treeHTML.append("</li>\n");
			}
		}

		return treeHTML;
	}
	
	public static boolean touchpointSelectionHasChild(ParameterGroupTreeNode node, boolean isStatusViewActive){
		TouchpointSelection tpSelection = TouchpointSelection.findByPGTN(node.getId());
		List<TouchpointSelection> childrenTpSelections = tpSelection.getChildrenOrderByName();
		User requestor = UserUtil.getPrincipalUser();
		// Filter the children by the selection's visibility
		for ( TouchpointSelection childrenTpSelection : childrenTpSelections )
			if ( !childrenTpSelection.isDeleted() && ( childrenTpSelection.isFullyVisible() || childrenTpSelection.isVisible(requestor) ) )
				return true;
		return false;
	}
	
	// getSelectableContentPopupHTMLchildNodes: Recursive function of getSelectableContentPopupNodesAsHTML
	public static StringBuffer getTouchpointSelectionHTMLchildNodes(StringBuffer treeHTML, TouchpointSelectionsListVO selectionVO, User requestor, long selectedId, long contentObjectId, boolean isStatusViewActive) {
		if (selectionVO == null)
			return treeHTML;

		TouchpointSelection tpSelection = selectionVO.getTouchpointSelection();
		if (tpSelection.isDeleted()) 
			return treeHTML;
		
		boolean nodeIsVisible		= tpSelection.isFullyVisible() || tpSelection.isVisible(requestor);
		if (!nodeIsVisible && !tpSelection.hasVisibleChildrenForUser(requestor))
			return treeHTML;

		boolean noProduction = true;
		long contentSelectionId = -1L;
		ContentObjectAssociation mca = ContentObjectAssociation.findByContentObjectAndParameters(ContentObject.findById(contentObjectId), isStatusViewActive ? ContentObject.DATA_TYPE_ACTIVE : ContentObject.DATA_TYPE_WORKING, tpSelection.getParameterGroupTreeNode(), null, null);
		if (mca!=null) contentSelectionId = mca.getId();

		// Child Nodes HTML
		List<TouchpointSelection> childrenTpSelections = tpSelection.getChildrenOrderByName();
		int childrenTreeNodeLevel = selectionVO.getTreeNodeLevel() + 1;
		List<TouchpointSelection> visibleChildrenTpSelections = new ArrayList<>();
		// Visibility check at the top level Touchpoint selection if required.
		for (TouchpointSelection currentSelection: childrenTpSelections)
			if ( !currentSelection.isDeleted() && currentSelection.isVisible(requestor))
				visibleChildrenTpSelections.add(currentSelection);
		

		boolean nodeDisabled 		= ( isStatusViewActive && (noProduction || (tpSelection.getDocument().isEnabledForVariantWorkflow() && !tpSelection.getHasActiveCopy())) ) || !nodeIsVisible;
		boolean nodeHasChildren 	= !visibleChildrenTpSelections.isEmpty();
		String hrefPath 			= nodeDisabled 						 ? "#" : "touchpoint_content_selection_view.form?contentSelectionId=" + contentSelectionId + "&touchpointSelectionId=" + tpSelection.getId() + "&statusViewId=" + (isStatusViewActive ? TouchpointContentSelectionViewController.VIEW_ACTIVE : TouchpointContentSelectionViewController.VIEW_WORKING_COPY);
		String nodeName 			= tpSelection.getName();
		String nodeSelectStatus 	= (contentSelectionId == selectedId) ? "selectedTreeNode" : "";
		String nodeEnabledStatus 	= nodeDisabled 						 ? "disabledTreeNode" : "";
		
		// Node HTML
		treeHTML.append("<li id=\"").append(contentSelectionId).append("\" class=\"").append(nodeHasChildren ? "jstree-open" : "jstree-leaf").append("\"> \n");
		
		treeHTML.append( MessageFormat.format(TreeConstants.HTML_NODE_LINK_W_PARAM, new Object[] { hrefPath, nodeName,
				 nodeName, "", nodeSelectStatus + " " + nodeEnabledStatus }) );
		
		if (nodeHasChildren) {
				treeHTML.append("<ul>\n");
				for (TouchpointSelection childTpSelection : visibleChildrenTpSelections) {
					TouchpointSelectionsListVO childTpSelectionVO = new TouchpointSelectionsListVO(childTpSelection,
																			childrenTreeNodeLevel,
																			TouchpointSelectionListFilterType.ID_ALL,
																			requestor,
																			-1, null, null);
					getTouchpointSelectionHTMLchildNodes(treeHTML, childTpSelectionVO, requestor, selectedId, contentObjectId, isStatusViewActive);
				}
				treeHTML.append("</ul>\n");
		}

		treeHTML.append("</li>\n"); 

		return treeHTML;
	}

	/*
	 * Return the content tree node that contain the parameter value, -9 (master) if not found
	 */
	public static long searchByParameterValue(ContentObject contentObject, String[] seachValues, long userId, long statusViewId) {

		long resultTreeNodeid = ParameterGroupTreeNode.MASTER_VARIANCE_ID;

		if (contentObject != null && seachValues != null) {
			List<ParameterGroupTreeNode> treeNodeList = ContentObjectAssociation.findParameterGroupTreeNodesByObject(contentObject, contentObject.getFocusOnDataType());
			List<ParameterGroupInstanceCollection> collectionList = new ArrayList<>();
			// prepare the inputs ParameterGroupInstanceCollection.search()
			for (ParameterGroupTreeNode treeNode : treeNodeList) {
				if (treeNode != null && treeNode.getParameterGroupInstanceCollection() != null)
					collectionList.add(treeNode.getParameterGroupInstanceCollection());
			}

			ParameterGroupInstanceCollection matchedCollection = ParameterGroupInstanceCollection.search(contentObject, new ArrayList<>(Arrays.asList(seachValues)));
			if (matchedCollection != null) {
				// return overridesContent
				ParameterGroupTreeNode matchedTreeNode = findTreeNodeByColletion(treeNodeList, matchedCollection.getId());
				if (matchedTreeNode != null) {
					resultTreeNodeid = matchedTreeNode.getId();
				}
			}
		}

		return resultTreeNodeid;
	}

	public static long searchByParameterValueForFormattingSelections(Document document, String[] seachValues, long userId, long statusViewId) {

		long resultTreeNodeid = ParameterGroupTreeNode.MASTER_VARIANCE_ID;

		if (document != null && seachValues != null) {
			List<ParameterGroupTreeNode> treeNodeList = FormattingSelection.findParameterGroupTreeNodes(document);

			ParameterGroupInstanceCollection matchedCollection = ParameterGroupInstanceCollection.searchForFormattingSelection(document, new ArrayList<>(Arrays.asList(seachValues)));
			if (matchedCollection != null) {
				// return overridesContent
				ParameterGroupTreeNode matchedTreeNode = findTreeNodeByColletion(treeNodeList, matchedCollection.getId());
				if (matchedTreeNode != null) {
					resultTreeNodeid = matchedTreeNode.getId();
				}
			}
		}

		return resultTreeNodeid;
	}

	private static ParameterGroupTreeNode findTreeNodeByColletion(List<ParameterGroupTreeNode> treeNodeList, long collectionId) {
		ParameterGroupTreeNode retTreeNode = null;
		if (treeNodeList != null && collectionId > 0) {
			for (ParameterGroupTreeNode treeNode : treeNodeList) {
				if (treeNode != null && treeNode.getParameterGroupInstanceCollection() != null)
					if (treeNode.getParameterGroupInstanceCollection().getId() == collectionId) {
						retTreeNode = treeNode;
						break;
					}
			}

		}
		return retTreeNode;
	}

	/*
	 * Map is by lang code.
	 */

	public static Map<Long, String> getTPContentSelectionContentMap(long touchpointSelectionId, long contentObjectId, int dataType) {
		TouchpointSelection tpSelection = TouchpointSelection.findById(touchpointSelectionId);
		ContentObject contentObject = ContentObject.findById(contentObjectId);
		Map<Long, String> contentMap = new HashMap<>();
		List<MessagepointLocale> languages = tpSelection.getDocument().getTouchpointLanguagesAsLocales();
		for (MessagepointLocale locale : languages) {
			contentMap.put(locale.getId(), "");
		}
		if (!contentObject.isMultipartType()) {
			Map<Long, ContentVO> contents = ContentObjectAssociation.getMasterOrTPVariantContents(contentObject, dataType, -1, true, tpSelection.getParameterGroupTreeNode());
			for (MessagepointLocale locale : languages) {
				if (contentObject.getContentType().getId() == ContentType.TEXT ||
						contentObject.getContentType().getId() == ContentType.VIDEO) {
					contentMap.put(locale.getId(), contents.get(locale.getId()).getContent());
				} else {
					contentMap.put(locale.getId(), contents.get(locale.getId()).getImageLocation());
				}
			}
		}
		return contentMap;
	}

	public static Map<Long, String> getTPContentSelectionGraphicDataMap(long touchpointSelectionId, long contentObjectId, String dataType) {
		TouchpointSelection tpSelection = TouchpointSelection.findById(touchpointSelectionId);
		ContentObject contentObject = ContentObject.findById(contentObjectId);
		Map<Long, String> dataMap = new HashMap<>();
		List<MessagepointLocale> locales = tpSelection.getDocument().getTouchpointLanguagesAsLocales();
		for (MessagepointLocale locale : locales) {
			dataMap.put(locale.getId(), "");
		}
		if (!contentObject.isMultipartType()) {
			Map<Long, ContentVO> contents = ContentObjectAssociation.getMasterOrTPVariantContents(contentObject, contentObject.getFocusOnDataType(), -1, true, tpSelection.getParameterGroupTreeNode());
			for (MessagepointLocale locale : locales) {
				if (contentObject.getContentType().getId() == ContentType.GRAPHIC) {
					if ( GRAPHIC_IMAGE_NAME.equalsIgnoreCase(dataType) )
						dataMap.put( locale.getId(), contents.get(locale.getId()).getImageName() );
					else if ( GRAPHIC_UPLOAD_DATE.equalsIgnoreCase(dataType) )
						dataMap.put( locale.getId(), contents.get(locale.getId()).getImageUploadedDate() != null ?
														DateUtil.formatDateTime(contents.get(locale.getId()).getImageUploadedDate()) :
														"" );
					else if ( GRAPHIC_APPLIED_IMAGE_NAME.equalsIgnoreCase(dataType) )
						dataMap.put( locale.getId(), contents.get(locale.getId()).getAppliedImageFilename() );
					else if ( GRAPHIC_IMAGE_LINK.equalsIgnoreCase(dataType) )
						dataMap.put( locale.getId(), contents.get(locale.getId()).getImageLink() != null ?  contents.get(locale.getId()).getImageLinkforView() : "");
					else if ( GRAPHIC_IMAGE_ALT_TEXT.equalsIgnoreCase(dataType) )
						dataMap.put( locale.getId(), contents.get(locale.getId()).getImageAltText() != null ?  contents.get(locale.getId()).getImageAltTextforView() : "");
					else if ( GRAPHIC_IMAGE_EXT_LINK.equalsIgnoreCase(dataType) )
						dataMap.put( locale.getId(), contents.get(locale.getId()).getImageExtLink() != null ?  contents.get(locale.getId()).getImageExtLinkforView() : "");
					else if ( GRAPHIC_IMAGE_EXT_PATH.equalsIgnoreCase(dataType) )
						dataMap.put( locale.getId(), contents.get(locale.getId()).getImageExtPath() != null ?  contents.get(locale.getId()).getImageExtPathforView() : "");
				}
			}
		}
		return dataMap;
	}

	/*
	 * Map is by zoneparid, lang to content.
	 */
	public static Map<Long, Map<Long, String>> getTPContentSelectionMPContentMap(long touchpointSelectionId, long contentObjectId) {
		TouchpointSelection tpSelection = TouchpointSelection.findById(touchpointSelectionId);
		ContentObject contentObject = ContentObject.findById(contentObjectId);
		Map<Long, Map<Long, String>> zonepartContentMap = new HashMap<>();
		List<MessagepointLocale> languages = tpSelection.getDocument().getTouchpointLanguagesAsLocales();
			Zone zone = contentObject.getZone();
			for (ZonePart zp : zone.getParts()) {
				Map<Long, String> contentMap = new HashMap<>();
				Map<Long, ContentVO> contents = ContentObjectAssociation.getMasterOrTPVariantContents(contentObject, contentObject.getFocusOnDataType(), zp.getId(), true, tpSelection.getParameterGroupTreeNode());
				for (MessagepointLocale locale : languages) {
					if (contentObject.isMultipartType()) {
						if (zp.getContentType().getId() == ContentType.TEXT) {
							contentMap.put(locale.getId(), contents.get(locale.getId()).getContent());
						} else {
							contentMap.put(locale.getId(), contents.get(locale.getId()).getImageLocation());
						}
					}
				}
				zonepartContentMap.put(Long.valueOf(zp.getId()), contentMap);
			}
		return zonepartContentMap;
	}
	
	public static Map<Long, Map<Long, String>> getTPContentSelectionMPGraphicDataMap(long touchpointSelectionId, long contentObjectId, String dataType) {

		TouchpointSelection tpSelection = TouchpointSelection.findById(touchpointSelectionId);
		ContentObject contentObject = ContentObject.findById(contentObjectId);
		Map<Long, Map<Long, String>> zonepartDataMap = new HashMap<>();
		List<MessagepointLocale> languages = tpSelection.getDocument().getTouchpointLanguagesAsLocales();
		Zone zone = contentObject.getZone();
		for (ZonePart zp : zone.getParts()) {
			Map<Long, String> dataMap = new HashMap<>();
			Map<Long, ContentVO> contents = ContentObjectAssociation.getMasterOrTPVariantContents(contentObject, contentObject.getFocusOnDataType(), zp.getId(), true, tpSelection.getParameterGroupTreeNode());
			for (MessagepointLocale locale : languages) {
				if (contentObject.isMultipartType()) {
					if (zp.getContentType().getId() == ContentType.GRAPHIC) {
						if ( GRAPHIC_IMAGE_NAME.equalsIgnoreCase(dataType) )
							dataMap.put( locale.getId(), contents.get(locale.getId()).getImageName() );
						else if ( GRAPHIC_UPLOAD_DATE.equalsIgnoreCase(dataType) )
							dataMap.put( locale.getId(), contents.get(locale.getId()).getImageUploadedDate() != null ?
															DateUtil.formatDateTime(contents.get(locale.getId()).getImageUploadedDate()) :
															"" );
						else if ( GRAPHIC_APPLIED_IMAGE_NAME.equalsIgnoreCase(dataType) )
							dataMap.put( locale.getId(), contents.get(locale.getId()).getAppliedImageFilename() );
						else if ( GRAPHIC_IMAGE_LINK.equalsIgnoreCase(dataType) )
							dataMap.put( locale.getId(), contents.get(locale.getId()).getImageLink() != null ?  contents.get(locale.getId()).getImageLinkforView() : "");
						else if ( GRAPHIC_IMAGE_ALT_TEXT.equalsIgnoreCase(dataType) )
							dataMap.put( locale.getId(), contents.get(locale.getId()).getImageAltText() != null ?  contents.get(locale.getId()).getImageAltTextforView() : "");
						else if ( GRAPHIC_IMAGE_EXT_LINK.equalsIgnoreCase(dataType) )
							dataMap.put( locale.getId(), contents.get(locale.getId()).getImageExtLink() != null ?  contents.get(locale.getId()).getImageExtLinkforView() : "");
						else if ( GRAPHIC_IMAGE_EXT_PATH.equalsIgnoreCase(dataType) )
							dataMap.put( locale.getId(), contents.get(locale.getId()).getImageExtPath() != null ?  contents.get(locale.getId()).getImageExtPathforView() : "");
					}
				}
			}
			zonepartDataMap.put(zp.getId(), dataMap);
		}
		return zonepartDataMap;
	}

}
