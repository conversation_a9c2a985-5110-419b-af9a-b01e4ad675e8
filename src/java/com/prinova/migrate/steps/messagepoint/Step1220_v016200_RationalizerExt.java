package com.prinova.migrate.steps.messagepoint;

import java.sql.Connection;

import com.prinova.migrate.MigrationStep;
import com.prinova.migrate.models.ColumnDefinition;

public class Step1220_v016200_RationalizerExt extends MigrationStep {
	
	@Override
	public void migrate(Connection conn) throws Exception {
		
		// Rationalizer: Auto generate metadata item on import
		if ( !columnExists( conn, "metadata_form_item_definition", "auto_generate_on_import" ) ) {
			addColumn(conn, "metadata_form_item_definition", ColumnDefinition.createBoolean("auto_generate_on_import", true));
			executeUpdate(conn, "UPDATE metadata_form_item_definition SET auto_generate_on_import = 0");
			alterColumn(conn, "metadata_form_item_definition", ColumnDefinition.createBoolean("auto_generate_on_import", false));
		}
		
		// Rationalizer Query: Trim white-space
		if ( !columnExists( conn, "rationalizer_query", "trim_whitespace" ) ) {
			addColumn(conn, "rationalizer_query", ColumnDefinition.createBoolean("trim_whitespace", true));
			executeUpdate(conn, "UPDATE rationalizer_query SET trim_whitespace = 0");
			alterColumn(conn, "rationalizer_query", ColumnDefinition.createBoolean("trim_whitespace", false));
		}
		
		// Metadata: Extend length of metadata item value
		if (!isColumnNullable(conn, "metadata_form_item_value_set", "string_value"))
			alterColumn(conn, "metadata_form_item_value_set", ColumnDefinition.createString("string_value", 4000, true));
		else
			executeUpdate(conn, "alter table metadata_form_item_value_set modify (string_value varchar2(4000 char))");
	}
}