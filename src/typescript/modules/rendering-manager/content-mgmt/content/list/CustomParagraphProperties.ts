import {RightComponentIF} from "../../../containers/right/RightComponentIF";
import {RightComponentType} from "../../../containers/right/RightComponentType";
import {MessageTranslations} from "../../../util/MessageTranslations";
import {ConnectedSidebar} from "../../../containers/connected/ConnectedSidebar";

export class CustomParagraphProperties implements RightComponentIF {

    private url: string = null;

    constructor(private ed: any) {
    }

    initEventListeners() {


    }

    public setUrl(url: string): void {
        this.url = url;
    }

    private closeCustomParagraphPropertiesHandler(event) {

        this.ed.settings.customparagraph.ele = null;
        this.ed.settings.customparagraph.selected_ele_lock = false;

        if (this.ed.prinovaPlugins.tagpanel != undefined) {
            this.ed.execCommand("mceRestoreTagPanel", false);
        }

        event.stopPropagation();
        ConnectedSidebar.getInstance().closeSidebar();
    }

    public addEventHandlersForView() {

        let customParagraphPropertiesCloseAction = $('#custom-paragraph-properties .mce-close');
        if (customParagraphPropertiesCloseAction.length > 0) {
            customParagraphPropertiesCloseAction.off("click");
            customParagraphPropertiesCloseAction.on("click", this.closeCustomParagraphPropertiesHandler.bind(this));
        }

    }

    private rerenderComponent() {
        $('#custom-list-properties').replaceWith(this.getContentForView());
        this.addEventHandlersForView();

        this.initEventListeners();
    }

    getType(): string {
        return RightComponentType.CUSTOM_PARAGRAPH_PROP;
    }

    getContentForView(): string {
        return "<div class='left-container-sticky'><div id='custom-paragraph-properties' class='mce-container mce-panel mce-custompanel mce-menu' role='application' style='position: relative; border: 0px; box-shadow: none; max-height: 100%; width: 365px; padding: 0; margin: -1.5rem; margin-top: 0; overflow: hidden;'>" +
            "<div class='mce-container-body mce-stack-layout' role='menu'>" +
            "<iframe name='customParagraphStylesPanelPropertiesIframe' src='" + this.url + "' tabindex='-1' style='width: 365px; height: 100vh; border: none;'></iframe>" +
            "</div>" +
            "</div></div>";
    }

    private getCloseType() {
        return MessageTranslations.getInstance().getTranslationForKey("client_messages.text.close");
    }
}
