# API Keys Configuration Template
# Copy this to AI/Tools/local/config/api_keys.yaml and fill in your actual keys
# This template file is safe to commit - it contains no actual keys

# OpenAI API for semantic search embeddings
openai:
  api_key: "${OPENAI_API_KEY}"  # Set environment variable or replace with actual key

# Perplexity API for research collection
perplexity:
  api_key: "${PERPLEXITY_API_KEY}"  # Set environment variable or replace with actual key

# Google Gemini API for content assessment and qualification
google:
  api_key: "${GOOGLE_API_KEY}"  # Set environment variable or replace with actual key

# Replicate API for SDXL image generation
replicate:
  api_key: "${REPLICATE_API_KEY}"  # Set environment variable or replace with actual key

# Placeholder for other AI providers (if needed)
# anthropic:
#   api_key: "${ANTHROPIC_API_KEY}"

# Instructions for team setup:
# 1. Copy this file to AI/Tools/local/config/api_keys.yaml
# 2. Replace ${VARIABLE_NAME} with your actual API keys
# 3. Or set environment variables and the system will use those
# 4. The local config directory is gitignored for security
