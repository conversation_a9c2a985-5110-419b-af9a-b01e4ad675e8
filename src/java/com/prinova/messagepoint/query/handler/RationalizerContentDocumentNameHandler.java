package com.prinova.messagepoint.query.handler;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import com.prinova.messagepoint.query.DefaultPage;
import com.prinova.messagepoint.query.IPage;

public class RationalizerContentDocumentNameHandler implements PostQueryHandler {

	private boolean isOrderAsc;
	
	public boolean isOrderAsc() {
		return isOrderAsc;
	}

	public void setOrderAsc(boolean isOrderAsc) {
		this.isOrderAsc = isOrderAsc;
	}
	
	@SuppressWarnings("unchecked")
	public IPage handle(IPage page) {
		List<RationalizerDocumentContent> items = (List<RationalizerDocumentContent>)page.getList();
		
		Collections.sort(items, new Comparator<>() {
            public int compare(RationalizerDocumentContent o1, RationalizerDocumentContent o2) {
                String name1 = o1.getRationalizerDocument().getName();
                String name2 = o2.getRationalizerDocument().getName();
                if (isOrderAsc()) {
                    if (name1.equals(name2)) {
                        return o1.getOrder().compareTo(o2.getOrder());
                    } else {
                        return name1.compareToIgnoreCase(name2);
                    }
                } else {
                    if (name1.equals(name2)) {
                        return o2.getOrder().compareTo(o1.getOrder());
                    } else {
                        return name2.compareToIgnoreCase(name1);
                    }
                }
            }
        });
		
        ((DefaultPage)page).setList(items);
		return page;
	}
}
