<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="com.prinova.messagepoint.model.insert.InsertScheduleBinAssignment" table="insert_schedule_bin_assignment" >
        <id name="id" column="id">
         	<generator class="native"/>
        </id>

		<property 		name="guid" 				column="guid"				length="255" 	not-null="true" 	unique="true" />                                  
		<property 		name="binNo" 				column="bin_no" 			not-null="true" />
  		<property 		name="priority" 			column="priority" />  
		<property 		name="available" 			column="available" 			not-null="true" />

		<property 		name="startDate" 			column="start_date" />
		<property 		name="endDate" 				column="end_date" />

		<many-to-one 	name="insertSchedule" 		column="insert_schedule_id" class="com.prinova.messagepoint.model.insert.InsertSchedule" not-found="ignore" />
		<many-to-one 	name="insert" 				column="insert_id" 			class="com.prinova.messagepoint.model.insert.Insert" not-found="ignore" />

   	</class>
</hibernate-mapping>