package com.prinova.messagepoint.controller.insert;

import java.io.Serializable;

public class InsertContentEditWrapper implements Serializable {

	private static final long serialVersionUID = 6261353127911714027L;

	private long id;
	private String name;
	private String status;
	private String frontNewImageLocation = "";
	private String frontOriginalImageLocation = "";
	private String backNewImageLocation = "";
	private String backOriginalImageLocation = "";
	
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getFrontNewImageLocation() {
		return frontNewImageLocation;
	}
	public void setFrontNewImageLocation(String frontNewImageLocation) {
		this.frontNewImageLocation = frontNewImageLocation;
	}
	public String getFrontOriginalImageLocation() {
		return frontOriginalImageLocation;
	}
	public void setFrontOriginalImageLocation(String frontOriginalImageLocation) {
		this.frontOriginalImageLocation = frontOriginalImageLocation;
	}
	public String getBackNewImageLocation() {
		return backNewImageLocation;
	}
	public void setBackNewImageLocation(String backNewImageLocation) {
		this.backNewImageLocation = backNewImageLocation;
	}
	public String getBackOriginalImageLocation() {
		return backOriginalImageLocation;
	}
	public void setBackOriginalImageLocation(String backOriginalImageLocation) {
		this.backOriginalImageLocation = backOriginalImageLocation;
	}
}