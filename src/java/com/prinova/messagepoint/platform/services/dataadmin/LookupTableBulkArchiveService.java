package com.prinova.messagepoint.platform.services.dataadmin;

import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.version.ModelVersionMapping;
import com.prinova.messagepoint.model.version.VersionActivityReason;
import com.prinova.messagepoint.model.version.VersionStatus;
import com.prinova.messagepoint.model.version.VersionedPrivateModelImpl;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

public class LookupTableBulkArchiveService extends AbstractService {
	public static final String SERVICE_NAME = "dataadmin.LookupTableBulkArchiveService";
	private static final Log log = LogUtil.getLog(LookupTableBulkArchiveService.class);
	
	@Transactional(propagation = Propagation.SUPPORTS)	
	public void execute(ServiceExecutionContext context) {
		LookupTableBulkInstanceActionServiceRequest request = (LookupTableBulkInstanceActionServiceRequest) context.getRequest();
		List<LookupTableInstance> instances = request.getLookupTableInstances();
		if (instances == null || instances.isEmpty()) {
			return;
		}
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			User requestor = UserUtil.getPrincipalUser();
			String userNote = request.getUserNote();
			for (LookupTableInstance instance : instances) {
				VersionedPrivateModelImpl model = instance.getModel();
				if (model != null) {
					ModelVersionMapping mapping = model.getLatestProduction().getVersionInfo();
					if (model.isCheckedout()) {
						model.setStatus(VersionStatus.findById(VersionStatus.MODEL_NEW));
					} else {
						model.setStatus(VersionStatus.findById(VersionStatus.MODEL_ARCHIVE));
					}
					HibernateUtil.getManager().getSession().save(model);

					mapping.setEffectEndDate(new Date(System.currentTimeMillis()));
					mapping.setLatestArchived(true);
					mapping.setExpiryReason(VersionActivityReason.findById(VersionActivityReason.EXPIRY_REASON_ARCHIVED));
					mapping.setExpiryUserNote(userNote);
					HibernateUtil.getManager().getSession().save(mapping);
				}
			}

		} catch (Exception e) {
			log.error(" unexpected exception when invoking LookupTableBulkArchiveService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION", ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}

	}

	public void validate(ServiceExecutionContext context) {
		/**
		 * Use the ArchiveWIPService to validate each check out request
		 */
		LookupTableBulkInstanceActionServiceRequest request = (LookupTableBulkInstanceActionServiceRequest) context.getRequest();
		List<LookupTableInstance> instances = request.getLookupTableInstances();
		if (instances == null || instances.isEmpty()) {
			return;
		}
	}

	public static ServiceExecutionContext createContext(List<LookupTableInstance> instances, User requestor, String userNote) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		LookupTableBulkInstanceActionServiceRequest request = new LookupTableBulkInstanceActionServiceRequest();
		context.setRequest(request);
		request.setLookupTableInstances(instances);
		request.setRequestor(requestor);
		request.setUserNote(userNote);

		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}

}