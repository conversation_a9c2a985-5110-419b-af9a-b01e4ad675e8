package com.prinova.messagepoint.platform.tpimport;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.xml.parsers.DocumentBuilder;

import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.platform.services.imports.*;
import com.prinova.messagepoint.util.*;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.w3c.dom.Element;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.content.SelectableValidationUtil;
import com.prinova.messagepoint.dialogue.ApplicationReportParser;
import com.prinova.messagepoint.dialogue.GMCAppReportParser;
import com.prinova.messagepoint.dialogue.GeneralAppReportParser;
import com.prinova.messagepoint.email.EmailTemplateUtils;
import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.admin.ApplicationReportContainer;
import com.prinova.messagepoint.model.admin.ConnectorConfiguration;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.dialogue.DialogueConfiguration;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.task.TPImportTask;
import com.prinova.messagepoint.model.util.MessagepointObjectImportReport;
import com.prinova.messagepoint.platform.services.ImportTaskScheduler;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.imports.document.AlternateLayoutImportHandler;
import com.prinova.messagepoint.platform.services.imports.document.ChannelLayoutImportHandler;
import com.prinova.messagepoint.platform.services.imports.document.ContentImportHandler;
import com.prinova.messagepoint.platform.services.imports.document.ExportImportHandler;
import com.prinova.messagepoint.platform.services.imports.document.ImportBulkUploadHandler;
import com.prinova.messagepoint.platform.services.imports.document.ImportMessageExportHandler;
import com.prinova.messagepoint.platform.services.imports.document.TemplateModifierImportHandler;
import com.prinova.messagepoint.platform.services.imports.document.TouchpointImportHandler;
import com.prinova.messagepoint.platform.services.imports.document.TouchpointLayoutImportHandler;
import com.prinova.messagepoint.platform.services.imports.document.ZoneImportHandler;
import com.prinova.messagepoint.platform.services.tpadmin.UpdateApplicationService;
import com.prinova.messagepoint.platform.services.workflow.WorkflowReleaseForApprovalService;
import com.ximpleware.AutoPilot;
import com.ximpleware.VTDGen;
import com.ximpleware.VTDNav;

public class TouchpointImport implements ImportAction{
	
	public static final int XML_DEFINITION_TYPE_NOT_VALID				= -1;
	public static final int XML_DEFINITION_TYPE_EXPORT_CONTAINER_ONLY	= 0;
	public static final int XML_DEFINITION_TYPE_EXPRT_OLD_TP			= 1;
	public static final int XML_DEFINITION_TYPE_EXPORT_TP				= 2;
	public static final int XML_DEFINITION_TYPE_BULK_UPLOAD				= 3;

	public static final int XML_DEFINITION_TYPE_EXPORT_FUTURE_TP		= 9;
	
	public static final int XML_FORMAT_TYPE_NOT_VALID					= -1;
	public static final int XML_FORMAT_TYPE_EXPORT_TP					= 0;
	public static final int XML_FORMAT_TYPE_OLD_VERSION					= 1;
	public static final int XML_FORMAT_TYPE_NEW_VERSION					= 2;
	public static final int XML_FORMAT_TYPE_BULK_DYNAMIC_IMAGE_VARIANTS	= 3;
	public static final int XML_FORMAT_TYPE_BULK_DYNAMIC_ST_VARIANTS	= 4;
	public static final int XML_FORMAT_TYPE_BULK_TP_VARIANTS			= 5;
	public static final int XML_FORMAT_TYPE_BULK_DYNAMIC_MESSAGE		= 6;
	public static final int XML_FORMAT_TYPE_BULK_TP_MESSAGES			= 8;
	public static final int XML_FORMAT_TYPE_GMC_REPORT					= 9;
	public static final int XML_FORMAT_TYPE_BULK_VARIANT_METADATA		= 10;
	public static final int XML_FORMAT_TYPE_BULK_USERS					= 11;
	public static final int XML_FORMAT_TYPE_BULK_USERS_UPDATE			= 12;
	
	
	private static final Log log = LogUtil.getLog(TouchpointImport.class);
	
	private TPImportTask tpImportTask = null;
	private int checkIsInternalXmlFormat = XML_FORMAT_TYPE_NOT_VALID;
	ApplicationReportContainer applicationReportContainer = null;
	
	public ApplicationReportContainer getApplicationReportContainer() {
		return applicationReportContainer;
	}

	public void setApplicationReportContainer(ApplicationReportContainer applicationReportContainer) {
		this.applicationReportContainer = applicationReportContainer;
	}

	public int getCheckIsInternalXmlFormat() {
		return checkIsInternalXmlFormat;
	}

	public void setCheckIsInternalXmlFormat(int checkIsInternalXmlFormat) {
		this.checkIsInternalXmlFormat = checkIsInternalXmlFormat;
	}

	public TPImportTask getTpImportTask() {
		return tpImportTask;
	}

	@Override
	public ImportTaskBase getImportTask() {
		return getTpImportTask();
	}

	@Override
	public int getImportTaskType() {
		return ImportTaskBase.IMPORT_TYPE_TOUCHPOINT_XML;
	}

	public void setTpImportTask(TPImportTask tpImportTask) {
		this.tpImportTask = tpImportTask;
	}

    public boolean validateImport(File importFile, String origFileName, int expectedType, long expectedAppId, BindException errors) throws Exception {
	    return validateImport(importFile, origFileName, expectedType, expectedAppId, null, errors);
    }

	public boolean validateImport(File importFile, String origFileName, int expectedType, long expectedAppId, Document documentToUpdate, BindException errors) throws Exception {
		String[] importFileName = {origFileName};
		File finalImportFile = importFile;
		checkIsInternalXmlFormat = TouchpointImport.getInternalXmlFormatType( importFile);
		if (checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_DYNAMIC_IMAGE_VARIANTS 
			|| checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_DYNAMIC_ST_VARIANTS
			|| checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_DYNAMIC_MESSAGE
			|| checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_TP_VARIANTS
			|| checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_TP_MESSAGES
			|| checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_VARIANT_METADATA
			|| checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_USERS
			|| checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_USERS_UPDATE) {	
			String bulkTmpFilename		= "bulk_temp_" + UserUtil.getPrincipalUserId() + "_" + DateUtil.timeStamp() + "_" + RandomGUID.getGUID() + ".xml";
			String bulkTmpFilerootPath 	= ApplicationUtil.getProperty( SystemPropertyKeys.Folder.KEY_MessagepointObjectExportDir );
			File temp = new File(bulkTmpFilerootPath + bulkTmpFilename);
			if(checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_USERS || checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_USERS_UPDATE){
				cleanup_user_xml(importFile, temp, errors);
			}else{
				cleanup_xml(importFile, temp, errors);
			}
			if (errors.hasErrors()) {
				log.error("TouchpointImport file is not valid for " + importFile.getPath() + ": invalid XML format, temp file=" + (temp != null ? temp.getPath() : "null") + ", checkIsInternalXmlFormat=" + checkIsInternalXmlFormat);
				log.error("Error message: " + errors.getMessage());
			}
			else {
				FileUtils.deleteQuietly(importFile);
			}
			finalImportFile = temp;
//			importFile = finalImportfile;
		}

		if ( expectedType != XML_FORMAT_TYPE_NOT_VALID && checkIsInternalXmlFormat != expectedType ) {
			// Don't delete the file if validation failed
			// FileUtils.deleteQuietly(finalImportFile);
			log.error("TouchpointImport file is not valid for " + origFileName + ": expected XML internal format " + expectedType + ", temp file=" + (finalImportFile != null ? finalImportFile.getPath() : "null") + ", checkIsInternalXmlFormat=" + checkIsInternalXmlFormat);
			errors.reject("error.message.expected.object.not.matching", importFileName, "");
			return false;
		} else if ( checkIsInternalXmlFormat == XML_FORMAT_TYPE_NEW_VERSION ) {
			// Container import file
			applicationReportContainer = new GeneralAppReportParser().parseContainerFile( finalImportFile );
		} else if ( checkIsInternalXmlFormat == XML_FORMAT_TYPE_OLD_VERSION) {
			// Application Report file from HP
			applicationReportContainer = new ApplicationReportParser().parseAppReport( finalImportFile );
		} else if ( checkIsInternalXmlFormat == XML_FORMAT_TYPE_GMC_REPORT) {
			// Application Report file from GMC
			applicationReportContainer = new GMCAppReportParser().parseContainerFile( finalImportFile );
		} else {
			int checkTPXmlFormatResult = checkTPXmlFormat(finalImportFile); 
			if ( checkTPXmlFormatResult < 2) {
				// if it's not correct XML import file
				if ( origFileName.equalsIgnoreCase("") )
					errors.reject("error.import.file.is.empty", importFileName, "");
				else if ( checkTPXmlFormatResult == XML_DEFINITION_TYPE_EXPORT_CONTAINER_ONLY )
					errors.reject("error.import.file.is.a.container", importFileName, "");
				else if (checkTPXmlFormatResult == XML_DEFINITION_TYPE_EXPRT_OLD_TP )
					errors.reject("error.import.file.is.an.old.version", importFileName, "");
				else{
					log.error("TouchpointImport file is not valid for " + origFileName + ": invalid XML format, temp file=" + (finalImportFile != null ? finalImportFile.getPath() : "null") + ", checkIsInternalXmlFormat=" + checkIsInternalXmlFormat + ", checkTPXmlFormatResult="+ checkTPXmlFormatResult);
					errors.reject("error.import.file.is.not.valid", importFileName, "");
				}

			    return false;
			}
			else if (checkTPXmlFormatResult == XML_DEFINITION_TYPE_EXPORT_FUTURE_TP)
			{
				errors.reject("error.import.file.is.a.new.version", importFileName, "");
				return false;
			}
	
			tpImportTask = new TouchpointParser().parseImportFile(finalImportFile);
			if (tpImportTask != null) {
				tpImportTask.setImportContainer(false);
				setImportTaskImportFile(tpImportTask, finalImportFile, origFileName);
				if(documentToUpdate != null) {
				    String documentDna = documentToUpdate.getDna();
				    String tpDna = tpImportTask.getTpDna();
				    if(documentDna == null || documentDna.isEmpty() || tpDna == null || tpDna.isEmpty() || !documentDna.equalsIgnoreCase(tpDna)) {
                        errors.reject("error.import.file.touchpoint.different.dna", importFileName, "");
                        return false;
                    }
				    tpImportTask.setDocToUpdateId(documentToUpdate.getId());
                }
				if(checkIsInternalXmlFormat ==  XML_FORMAT_TYPE_BULK_DYNAMIC_IMAGE_VARIANTS){
					tpImportTask.setBulkImportType(TPImportTask.BULK_IMPORT_TYPE_DYNAMIC_IMAGE);
					long appId = getTargetModelId(finalImportFile, importFileName, errors);
					if(errors.hasErrors())
						return false;
					if(expectedAppId != -1 && appId != expectedAppId) {
						errors.reject("error.message.invalid.guid");					 
					}
					if (errors.hasErrors())
						return false;
					else
						tpImportTask.setAppId(appId);
			    	return true;
				} else if(checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_DYNAMIC_ST_VARIANTS){
					tpImportTask.setBulkImportType(TPImportTask.BULK_IMPORT_TYPE_DYNAMIC_TEXT);
					long appId = getTargetModelId(finalImportFile, importFileName, errors);
					if(errors.hasErrors())
						return false;
					if(expectedAppId != -1 && appId != expectedAppId) {
						errors.reject("error.message.invalid.guid");					 
					}
					if (errors.hasErrors())
						return false;
					else
						tpImportTask.setAppId(appId);
					return true;
				} else if(checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_DYNAMIC_MESSAGE ||
						checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_TP_VARIANTS ||
						checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_TP_MESSAGES ||
						checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_VARIANT_METADATA ||
						checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_USERS ||
						checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_USERS_UPDATE){
					tpImportTask.setBulkImportType(checkIsInternalXmlFormat);
					long appId = getTargetModelId(finalImportFile, importFileName, errors);
					if (errors.hasErrors())
						return false;
					else
						tpImportTask.setAppId(appId);
		    		return true;
				}
				return true;
			}
			log.error("TouchpointImport file is not valid for " + origFileName + ": unknown XML internal format and tpImportTask is null, temp file=" + (finalImportFile != null ? finalImportFile.getPath() : "null") + ", checkIsInternalXmlFormat=" + checkIsInternalXmlFormat);
			errors.reject("error.import.file.is.not.valid", importFileName, "");
		    return false;
		}

		tpImportTask = new TPImportTask();
		tpImportTask.setImportContainer(true);
		tpImportTask.setImportFile(finalImportFile);
		tpImportTask.setOriginalFilename(origFileName);
		
		if (applicationReportContainer != null) {
			for (com.prinova.messagepoint.model.Document doc : applicationReportContainer.getDocuments()) {
				ConnectorConfiguration config = doc.getConnectorConfiguration();
				if ( config instanceof DialogueConfiguration ) {
					DialogueConfiguration dialogueConfiguration = (DialogueConfiguration) config;
					dialogueConfiguration.setReport( importFile.getName() );
				}
			}
		}		
   		return true;
	}
	
	 public static  boolean bulkUpload(User user, TPImportTask tpImportTask, BindException errors, StatusPollingBackgroundTask statusPollingBackgroundTask) {
		if (tpImportTask != null && !tpImportTask.isImportInProcess()) {
			tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_PROCESSING);
			
			try (AutoCloseable autoUnlock = ImportTaskScheduler.lockImportingProcess("bulkUpload")) {
				MessagepointObjectImportReport reportLog = new MessagepointObjectImportReport(user, tpImportTask.getAppId());
				reportLog.appendReportLog("Import file name: " +  tpImportTask.getOriginalFilename());
				tpImportTask.setImportReport(reportLog);

				ServiceExecutionContext context = ImportBulkUploadService.createContext(-1, tpImportTask.getImportFile(), user.getId(), tpImportTask);
				Service importBulkUploadService = MessagepointServiceFactory.getInstance().lookupService(ImportBulkUploadService.SERVICE_NAME, ImportBulkUploadService.class);
				importBulkUploadService.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
		
				if (!serviceResponse.isSuccessful())  {
					tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_ERROR);
				    ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
				    return false;
				}
				
				ImportBulkUploadHandler handler = (ImportBulkUploadHandler) serviceResponse.getResultValueBean();
				switch(tpImportTask.getBulkImportType()){
					case TPImportTask.BULK_IMPORT_TYPE_USERS_UPDATE:
					{
						ServiceExecutionContext usersImportContext = BulkUploadUsersUpdateService.createContext(handler, user.getLocalDcsUser().getId(), tpImportTask, com.prinova.messagepoint.model.Node.getCurrentNode(), statusPollingBackgroundTask);
						Service service = MessagepointServiceFactory.getInstance().lookupService(BulkUploadUsersUpdateService.SERVICE_NAME, BulkUploadUsersUpdateService.class);
						service.execute(usersImportContext);
						BulkUploadUsersServiceResponse bulkuploadUsersServiceResponse = (BulkUploadUsersServiceResponse) usersImportContext.getResponse();
						if ( !bulkuploadUsersServiceResponse.isSuccessful() ) {
							tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_ERROR);
				            ServiceResponseConverter.convertToSpringErrors(bulkuploadUsersServiceResponse, errors);
				            return false;
						}
						break;
					}
					
					default:{
						ServiceExecutionContext contextImportContentLibrary = BulkUploadVariantsService.createContext(handler, user.getId(), tpImportTask, statusPollingBackgroundTask);
						Service bulkUploadVariantsService = MessagepointServiceFactory.getInstance().lookupService(BulkUploadVariantsService.SERVICE_NAME, BulkUploadVariantsService.class);
						bulkUploadVariantsService.execute(contextImportContentLibrary);
						BulkUploadVariantsServiceResponse bulkuploadVariantsServiceResponse = (BulkUploadVariantsServiceResponse) contextImportContentLibrary.getResponse();
						if ( !bulkuploadVariantsServiceResponse.isSuccessful() ) {
							tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_ERROR);
							ServiceResponseConverter.convertToSpringErrors(bulkuploadVariantsServiceResponse, errors);
							return false;
						}
						break;
					}
				}
				
//				FileUtil.deleteAllByRegex("temp_" + user.getId() + "_*", new File(tmpFilerootPath));
//				FileUtil.deleteAllByRegex("bulk_temp_" + user.getId() + "_*", new File(tmpFilerootPath));
				
				tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_DONE);
				reportLog.finishReport();
						
				if(!reportLog.isSuccessfulImport()){
					//display log button
					tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_ERROR);
					tpImportTask.setImportReportFilePath(reportLog.getReportFileName());
					errors.addError(new ObjectError("Import is successful", "Import is done with some errors. Check report log for details."));
					log.error("Import is done with some errors. Check report log for details:" + reportLog.getReportFileName());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return false;
				}
				tpImportTask.setImportReportFilePath(reportLog.getReportFileName());
				return true;
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
				tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_ERROR);
				return false;
			}
			
		}	
		return false;
	}
	
	public static boolean touchpointUpload(User user, TPImportTask tpImportTask, BindException errors, boolean importOnlyDataCollection, boolean updateDataSourceAndVariables, boolean renameTPIfDuplicated, boolean importActiveAsWorking, boolean createNewCopiesOfEmbeddedContent, boolean createNewCopiesOfContentLibrary, long dataSourceAssociationId, StatusPollingBackgroundTask statusPollingBackgroundTask) {
		if (tpImportTask != null && !tpImportTask.isImportInProcess()) {
			tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_PROCESSING);
			
			try (AutoCloseable autoUnlock = ImportTaskScheduler.lockImportingProcess("touchpointUpload")) {
				tpImportTask.setRenameTPIfDuplicated(renameTPIfDuplicated);
				tpImportTask.setEmbeddedContentWithNoGUIDs(createNewCopiesOfEmbeddedContent);
				tpImportTask.setImportActiveCopiesInExportAsWorkingCopy(importActiveAsWorking);
				tpImportTask.setContentLibraryWithNoGUIDs(createNewCopiesOfContentLibrary);
				
				long importDataCollectionId = dataSourceAssociationId;
				if (importOnlyDataCollection)
					importDataCollectionId = -2L;
				
				ServiceExecutionContext context = ImportDocumentService.createContext(importDataCollectionId, tpImportTask.getImportFile(), user.getId(), updateDataSourceAndVariables, tpImportTask, statusPollingBackgroundTask);
				Service importDocumentService = MessagepointServiceFactory.getInstance().lookupService(ImportDocumentService.SERVICE_NAME, ImportDocumentService.class);
				
				importDocumentService.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				statusPollingBackgroundTask.setProgressInPercentInThread(20);

				if (!serviceResponse.isSuccessful()) 
				{
					tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_ERROR);
				    ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
				    return false;
				}
				
				Document doc = Document.findById(tpImportTask.getDocId());

				if (importOnlyDataCollection || tpImportTask.isImportOnlySharedObjects()) {
					if(doc != null && importOnlyDataCollection) {
						doc.setEnabled(false);
						doc.delete();
					}
					tpImportTask.setDocId(0);
					tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_DONE);
					return true;
				}
				
				if (doc != null) {
					String tpName = doc.getName();
					if(! tpName.equals(tpImportTask.getTpName())) {
						tpImportTask.setTpName(tpName);
						tpImportTask.setTpNameChanged(true);
					}
					statusPollingBackgroundTask.setDescription(tpName);
				}
				
				if (doc != null) {
					doc.setEnabled(false);
					doc.save();
				}
				
				statusPollingBackgroundTask.setProgressInPercentInThread(40);

                ImportMessageExportHandler handler = (ImportMessageExportHandler) serviceResponse.getResultValueBean();

                {
                    // We will create empty image library items here first in case any of them are referenced
                    // We don't create content yet.

                    ServiceExecutionContext contextImportContentLibrary = ImportContentLibraryService.createDummyObjectContext(handler, user.getId(), tpImportTask);
                    Service importContentLibraryService = MessagepointServiceFactory.getInstance().lookupService(ImportContentLibraryService.SERVICE_NAME, ImportContentLibraryService.class);

                    importContentLibraryService.execute(contextImportContentLibrary);
                    ImportMessagesServiceResponse serviceImportContentLibraryResponse = (ImportMessagesServiceResponse) contextImportContentLibrary.getResponse();
                    if ( !serviceImportContentLibraryResponse.isSuccessful() ) {
                        tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_ERROR);
                        ServiceResponseConverter.convertToSpringErrors(serviceImportContentLibraryResponse, errors);
                        enableDoc(doc);
                        return false;
                    }


                    // We will create empty embedded contents here first in case any of them are referenced in image library
                    // We don't create content yet so as to avoid any references to image library.
                    ServiceExecutionContext contextImportEmbeddedContents = ImportEmbeddedContentsService.createDummyObjectContext(handler, user.getId(), tpImportTask);
                    Service importEmbeddedContentsService = MessagepointServiceFactory.getInstance().lookupService(ImportEmbeddedContentsService.SERVICE_NAME, ImportEmbeddedContentsService.class);

                    importEmbeddedContentsService.execute(contextImportEmbeddedContents);
                    ImportMessagesServiceResponse serviceImportEmbeddedContentsResponse = (ImportMessagesServiceResponse) contextImportEmbeddedContents.getResponse();

                    if ( !serviceImportEmbeddedContentsResponse.isSuccessful() )
                    {
                        tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_ERROR);
                        ServiceResponseConverter.convertToSpringErrors(serviceImportEmbeddedContentsResponse, errors);
                        enableDoc(doc);
                        return false;
                    }
                }

				ServiceExecutionContext contextImportContentLibrary = ImportContentLibraryService.createContext(handler, user.getId(), tpImportTask);
				Service importContentLibraryService = MessagepointServiceFactory.getInstance().lookupService(ImportContentLibraryService.SERVICE_NAME, ImportContentLibraryService.class);
				
				importContentLibraryService.execute(contextImportContentLibrary);
				ImportMessagesServiceResponse serviceImportContentLibraryResponse = (ImportMessagesServiceResponse) contextImportContentLibrary.getResponse();
				if ( !serviceImportContentLibraryResponse.isSuccessful() ) {
					tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_ERROR);
		            ServiceResponseConverter.convertToSpringErrors(serviceImportContentLibraryResponse, errors);
					enableDoc(doc);
		            return false;
				}

				// Activate new imported content library
				List<ContentObject> contentLibraryInstanceList = serviceImportContentLibraryResponse.getContentLibraryInstanceList();
	            if (!contentLibraryInstanceList.isEmpty()) {
	            	String userNote = "Import Activation";

					ServiceExecutionContext contextActivation = WorkflowReleaseForApprovalService.createContextWithAction(user, userNote, true, contentLibraryInstanceList.toArray(new ContentObject[]{}));
					Service service = MessagepointServiceFactory.getInstance().lookupService(WorkflowReleaseForApprovalService.SERVICE_NAME, WorkflowReleaseForApprovalService.class);
					service.execute(contextActivation);
					statusPollingBackgroundTask.setProgressInPercentInThread(50);
					ServiceResponse serviceActivationResponse = contextActivation.getResponse();

					if (!serviceActivationResponse.isSuccessful()) {
						tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_ERROR);
			            ServiceResponseConverter.convertToSpringErrors(serviceActivationResponse, errors);
						enableDoc(doc);
			            return false;
					}
	            }        	

	            ServiceExecutionContext contextImportEmbeddedContents = ImportEmbeddedContentsService.createContext(handler, user.getId(), tpImportTask);
				Service importEmbeddedContentsService = MessagepointServiceFactory.getInstance().lookupService(ImportEmbeddedContentsService.SERVICE_NAME, ImportEmbeddedContentsService.class);
		
				importEmbeddedContentsService.execute(contextImportEmbeddedContents);
				ImportMessagesServiceResponse serviceImportEmbeddedContentsResponse = (ImportMessagesServiceResponse) contextImportEmbeddedContents.getResponse();

				if ( !serviceImportEmbeddedContentsResponse.isSuccessful() )
				{
					tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_ERROR);
		            ServiceResponseConverter.convertToSpringErrors(serviceImportEmbeddedContentsResponse, errors);
					enableDoc(doc);
		            return false;
				}

				// Activate new imported embedded content
				List<ContentObject> embeddedContentList = serviceImportEmbeddedContentsResponse.getEmbeddedContentInstanceList();
	            if (!embeddedContentList.isEmpty()) {
	            	String userNote = "Import Activation";

					ServiceExecutionContext contextActivation = WorkflowReleaseForApprovalService.createContextWithAction(user, userNote, true, embeddedContentList.toArray(new ContentObject[]{}));
					Service service = MessagepointServiceFactory.getInstance().lookupService(WorkflowReleaseForApprovalService.SERVICE_NAME, WorkflowReleaseForApprovalService.class);
					service.execute(contextActivation);
					statusPollingBackgroundTask.setProgressInPercentInThread(60);
					ServiceResponse serviceActivationResponse = contextActivation.getResponse();

                    if (!serviceActivationResponse.isSuccessful()) {
                        tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_ERROR);
                        ServiceResponseConverter.convertToSpringErrors(serviceActivationResponse, errors);
                        enableDoc(doc);
                        return false;
                    }
	            }

                HibernateUtil.getManager().getSession().flush();

	            ExportImportHandler root = (ExportImportHandler)handler.getRoot();
                ZoneImportHandler.processZoneCommunicationTemplates(root);
                
	            statusPollingBackgroundTask.setProgressInPercentInThread(80);
				ServiceExecutionContext contextImportMessages = ImportMessagesService.createContext(handler, user.getId(), tpImportTask);
				Service importMsgsService = MessagepointServiceFactory.getInstance().lookupService(ImportMessagesService.SERVICE_NAME, ImportMessagesService.class);
		
				importMsgsService.execute(contextImportMessages);
				ImportMessagesServiceResponse serviceImportMessagesResponse = (ImportMessagesServiceResponse) contextImportMessages.getResponse();

//				FileUtil.deleteAllByRegex("temp_" + user.getId() + "_*", new File(tmpFilerootPath));
				
				if ( !serviceImportMessagesResponse.isSuccessful() ) {
					tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_ERROR);
		            ServiceResponseConverter.convertToSpringErrors(serviceImportMessagesResponse, errors);
					enableDoc(doc);
		            return false;
				}

				// finish importing template modifiers
				if (doc != null) {
/*					
			   		List<TemplateModifier> modifiers = TemplateModifier.findAllActiveByTouchpointOrderById(doc);
			        if (modifiers != null && modifiers.size() > 0) {
			    		for (TemplateModifier modifier : modifiers) {
		    				ComplexValue complexValue = modifier.getComplexValue();
			    			if( complexValue != null && complexValue.getEncodedValue() != null) {
			    	    		Content contentValue = new Content();
			    	    		complexValue.setEncodedValue(ContentImportHandler.resolveAndCorrectContent(complexValue.getEncodedValue(), (ExportImportHandler)handler.getRoot(), contentValue));
			    	    		complexValue.setVariables(contentValue.getVariables());
			    	    		complexValue.setConstants(contentValue.getConstants());
			    	    		complexValue.setGlobalConstants(contentValue.getGlobalConstants());
			    	    		complexValue.setEmbeddedContents(contentValue.getEmbeddedContent());
			    	    		complexValue.save();
			    			}
			    		}
			        }
*/
		            TouchpointImportHandler tpih = root.getTouchpoint();
		            if (tpih != null) {
		            	createTemplateModifiers(doc, handler, tpih, user);
		            }
				}
				
	            processEmailTemplates(doc);
	            updateTouchpointVariantsHashes(doc);

				tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_DONE);
				
				enableDoc(doc);
				
			    return true;
			}
            catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
				tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_ERROR);
				return false;
			}
			finally {
                statusPollingBackgroundTask.setTargetObjectId(null);
            }
		}
		return true;
	}
	
	private static void createTemplateModifierWithResolveAndCorrectContent(ImportMessageExportHandler xmlDocHandler, TemplateModifierImportHandler tmih, TouchpointImportHandler tpHandler, TouchpointLayoutImportHandler ltHandler, Document doc, User user) {
		String value = tmih.getValue();
		Content contentValue = new Content();
		String resolvedValue = ContentImportHandler.resolveAndCorrectContent(value, (ExportImportHandler) xmlDocHandler.getRoot(), contentValue);
		tmih.setResolvedValue(resolvedValue);
    	tmih.createTemplateModifier(tpHandler, ltHandler, doc, user);
	}
	
    private static void createTemplateModifiers(Document doc, ImportMessageExportHandler xmlDocHandler, TouchpointImportHandler tpHandler, User user)
    {
        for (TemplateModifierImportHandler tmih : tpHandler.getTemplateModifiers())
        {
        	createTemplateModifierWithResolveAndCorrectContent(xmlDocHandler, tmih, tpHandler, null, doc, user);
        }
        
    	for(AlternateLayoutImportHandler alih : tpHandler.getAlternateLayouts()) {
    		Document alternateLayout = alih.getDocument();
            for (TemplateModifierImportHandler tmih : alih.getTemplateModifiers())
            {
            	createTemplateModifierWithResolveAndCorrectContent(xmlDocHandler, tmih, tpHandler, alih, alternateLayout, user);
            }
    	}
    	
    	for(ChannelLayoutImportHandler clih : tpHandler.getChannelLayouts()) {
    		Document channelLayout = clih.getDocument();
            for (TemplateModifierImportHandler tmih : clih.getTemplateModifiers())
            {
            	createTemplateModifierWithResolveAndCorrectContent(xmlDocHandler, tmih, tpHandler, clih, channelLayout, user);
            }
    	}
    	
    	for(ChannelLayoutImportHandler rootclih : tpHandler.getChannelLayouts()) {
    		long rootrefid = rootclih.getId();
        	for(AlternateLayoutImportHandler alih : tpHandler.getAlternateLayouts()) {
        		long alternateLayoutRefId = alih.getId();
        		for(ChannelLayoutImportHandler clih : alih.getChannelLayouts()) {
        			long parentRefId = clih.getParentRefId();
        			long channelParentRefId = clih.getChannelParentRefId();
        			
        			if(parentRefId == rootrefid && channelParentRefId == alternateLayoutRefId) {
        				Document channelLayout = clih.getDocument();
        	            for (TemplateModifierImportHandler tmih : clih.getTemplateModifiers())
        	            {
        	            	createTemplateModifierWithResolveAndCorrectContent(xmlDocHandler, tmih, tpHandler, clih, channelLayout, user);
        	            }
        			}
        		}
        	}
    	}
    }

    private static void processEmailTemplates(Document doc) {
    	if(doc.isEmailTouchpoint() || doc.isWebTouchpoint()) {
            EmailTemplateUtils.getParsedEmailTemplatesForDocument(doc.getId(), false);
    	}
	    List<Document> rootChannels = doc.getChannelAlternateDocuments();
        for(Document channelDoc : rootChannels) {
            EmailTemplateUtils.getParsedEmailTemplatesForDocument(channelDoc.getId(), false);
        }
        List<Document> alternateLayouts = doc.getAlternateLayouts();
        for(Document alternateLayout : alternateLayouts) {
        	if(alternateLayout.isEmailTouchpoint() || alternateLayout.isWebTouchpoint()) {
                EmailTemplateUtils.getParsedEmailTemplatesForDocument(alternateLayout.getId(), false);
        	}
            List<Document> alternateChannels = alternateLayout.getChannelAlternateDocuments();
            for(Document channelDoc : alternateChannels) {
                EmailTemplateUtils.getParsedEmailTemplatesForDocument(channelDoc.getId(), false);
            }
        }
	}

    private static void updateTouchpointVariantsHashes(Document doc) {
        if(doc.isEnabledForVariation()) {
            doc.getTouchpointSelections().forEach(ts -> {
                ts.makeHash(false);
            });
        }
    }
	
	private static void enableDoc(Document doc) {
		if (doc != null) {
 			doc.setEnabled(true);
			doc.save();
		}
	}
	
	public static int getInternalXmlFormatType(File applicationReport ) {
		if( applicationReport == null)
			return XML_FORMAT_TYPE_NOT_VALID;

		if(JSONUtils.isJsonFile(applicationReport) || ZipUtil.isZipFile(applicationReport))
			return ContentJsonImportTask.BULK_IMPORT_TYPE_JSON_CONTENT;

    	try
    	{
    		VTDGen vg = new VTDGen();
    		if (!vg.parseFile(applicationReport.getCanonicalPath(), false))
    			return XML_FORMAT_TYPE_NOT_VALID;
    		
    		VTDNav vn = vg.getNav();
    		
    		if (vn.matchElement("MpAppDefinition"))
    			return XML_FORMAT_TYPE_NEW_VERSION;
    		
    		if (vn.matchElement("MpTouchpointDefinition"))
    			return XML_FORMAT_TYPE_EXPORT_TP;
    		
    		if (vn.matchElement("MpBulkUploadDefinition"))
    		{
    			// Bulk Upload ContentLibrary
        		if (vn.toElement(VTDNav.FIRST_CHILD, "DynamicImageVariants"))
        			return XML_FORMAT_TYPE_BULK_DYNAMIC_IMAGE_VARIANTS;
    			
    			// Bulk Upload Smart Text
        		if (vn.toElement(VTDNav.FIRST_CHILD, "DynamicSmartTextVariants"))
        			return XML_FORMAT_TYPE_BULK_DYNAMIC_ST_VARIANTS;
    			
    			// Bulk Upload Dynamic Message
        		if (vn.toElement(VTDNav.FIRST_CHILD, "DynamicMessageVariants"))
        			return XML_FORMAT_TYPE_BULK_DYNAMIC_MESSAGE;

				// Bulk Upload Touchpoint Variants
        		if (vn.toElement(VTDNav.FIRST_CHILD, "TouchpointVariants"))
        			return XML_FORMAT_TYPE_BULK_TP_VARIANTS;
    			
    			// Bulk Upload Touchpoint Messages
        		if (vn.toElement(VTDNav.FIRST_CHILD, "TouchpointMessages"))
        			return XML_FORMAT_TYPE_BULK_TP_MESSAGES;
        		
        		// Bulk Upload Variant Metadata
        		if (vn.toElement(VTDNav.FIRST_CHILD, "TPVariantMetadataFormItems"))
        			return XML_FORMAT_TYPE_BULK_VARIANT_METADATA;
        		
        		// Bulk Upload Users
        		if (vn.toElement(VTDNav.FIRST_CHILD, "Users"))
        			if (vn.toElement(VTDNav.FIRST_CHILD, "User"))
        				if (vn.toElement(VTDNav.FIRST_CHILD, "GUID"))
        					return XML_FORMAT_TYPE_BULK_USERS_UPDATE;
        				else
        					return XML_FORMAT_TYPE_BULK_USERS;
    		}

    		if (vn.matchElement("Application"))
    			return XML_FORMAT_TYPE_OLD_VERSION;
    		
    		if (vn.matchElement("WorkFlow"))
    			return XML_FORMAT_TYPE_GMC_REPORT;
    	}
    	catch( Throwable t)
    	{
    		return XML_FORMAT_TYPE_NOT_VALID;
    	}
    	
    	return XML_FORMAT_TYPE_NOT_VALID;
    }
	    
    private int checkTPXmlFormat( File importFile ) {
		if( importFile == null )
			return XML_DEFINITION_TYPE_NOT_VALID;

		// create a DOM from the XML, and see if the document's top level tag has at least
		// <MpTouchpointDefinition>
    	try
    	{
			InputStream inputStream = new FileInputStream( importFile );
    		DocumentBuilder builder = XmlParserConfigUtil.getDocumentBuilderFactory().newDocumentBuilder();
    		org.w3c.dom.Document document = builder.parse( inputStream );

    		NodeList nodeList = document.getElementsByTagName("MpTouchpointDefinition");
    		
    		if ( nodeList.getLength() > 0 )
    		{
        		nodeList = document.getElementsByTagName("Metadata");
        		
        		if ( nodeList.getLength() > 0 )
        		{
            		nodeList = document.getElementsByTagName("Version");
            		
            		if ( nodeList.getLength() > 0 )
            		{
            			Node node = nodeList.item(0);
            			
            			if (node != null)
            			{
            				String[] strVersion = getAttribute(node, "version").split("\\.");
							String build = getAttribute(node,"build");
        					int intVersion = 0;
        					if ( strVersion.length == 2 )
        					{
        						intVersion = Integer.parseInt(strVersion[0]) * 100000 + Integer.parseInt(strVersion[1]) * 10000;
        					}
        					else if ( strVersion.length == 3 )
        					{
        						intVersion = Integer.parseInt(strVersion[0]) * 100000 + Integer.parseInt(strVersion[1]) * 10000 + Integer.parseInt(strVersion[2]);
        					}
        					else if ( strVersion.length == 4 )
        					{
        						intVersion = Integer.parseInt(strVersion[0]) * 100000 + Integer.parseInt(strVersion[1]) * 10000 + Integer.parseInt(strVersion[2]) * 1000 + Integer.parseInt(strVersion[3]);            						
        					}
        					if (intVersion >= 360006)
        					{
								String buildVersion = MessagepointLicenceManager.getInstance().getBuildRevision();
								float versionNumber = Float.parseFloat(buildVersion.substring(0, 4));
								float versionNumberOfXMLFile;
								int index = build.indexOf(46);
								if (index > 0)
									versionNumberOfXMLFile = Float.parseFloat(build.substring(0, index + 2));
								else
									return XML_DEFINITION_TYPE_NOT_VALID;

								if (versionNumber < versionNumberOfXMLFile)
									return  XML_DEFINITION_TYPE_EXPORT_FUTURE_TP;
								else
									return  XML_DEFINITION_TYPE_EXPORT_TP;
        					}
            			}
            			// Old version of TP export file
            			return XML_DEFINITION_TYPE_EXPRT_OLD_TP;
            		}
        			// Not valid file
        			return XML_DEFINITION_TYPE_NOT_VALID;
        		}
        		// Old version of TP export file
    			return XML_DEFINITION_TYPE_EXPRT_OLD_TP;
    		}
    		
    		nodeList = document.getElementsByTagName("MpBulkUploadDefinition");
    		
    		if ( nodeList.getLength() > 0 )
    		{
    			// Bulk Upload import
    			return XML_DEFINITION_TYPE_BULK_UPLOAD;
    		}

    		nodeList = document.getElementsByTagName("MpAppDefinition");
    		
    		if ( nodeList.getLength() > 0 )
    		{
    			// Export file of a container
    			return XML_DEFINITION_TYPE_EXPORT_CONTAINER_ONLY;
    		}
    	}
    	catch( Throwable t)
    	{
    		return XML_DEFINITION_TYPE_NOT_VALID;
    	}
    	
    	return XML_DEFINITION_TYPE_NOT_VALID;
    }
	    
    private String getAttribute(Node node, String attributeName) {
    	if (node.getNodeType()==Node.ELEMENT_NODE) {
	    	NamedNodeMap map = ((Element)node).getAttributes();
	    	if( map != null ) {
	    		Node attributeNode = (Node)map.getNamedItem(attributeName);
	    		if( attributeNode != null ) {
	    			return attributeNode.getNodeValue();
	    		}
	    	}
    	}
    	return "";
    }
    
    private void cleanup_xml(File importFile, File temp, BindException errors) throws IOException {
		BufferedReader r = new BufferedReader(new InputStreamReader(new FileInputStream(importFile), "UTF8"));
	    BufferedWriter out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(temp), "UTF-8"));
	    
	    String line;
	    String output = "";
	    while ((line = r.readLine()) != null) {
	    	line = cleanupContentTag(line, "Content");
	    	output = output.concat(line);
	    }
	    r.close();
	    
	    Pattern definitiontPattern = Pattern.compile("<MpBulkUploadDefinition.*?>", Pattern.DOTALL);
	    Matcher definitionMatcher = definitiontPattern.matcher( output );
	    long masterVarinatId = 0;
	    com.prinova.messagepoint.model.Document document = null;
	    // get DocumentGUID if TPvariant upload, get master variant id for validation for selectorData
	    if(checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_TP_VARIANTS || checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_VARIANT_METADATA){
	    	while (definitionMatcher.find()) {
		    	Pattern guidPattern = Pattern.compile("guid=\"[^\"]*\"");
	            Matcher guidMatcher = guidPattern.matcher(definitionMatcher.group());
	            while (guidMatcher.find()) {
	            	String guid = guidMatcher.group().replace("guid=", "").replace("\"", "");
	            	if(!guid.isEmpty()){
	            		masterVarinatId = getMasterVariantId(guid);
	            		document = com.prinova.messagepoint.model.Document.findByGuid(guid);
	            	}
	            }
		    }
	    }
	    
	    // Validate Variant name and data
        Pattern variantPattern = Pattern.compile(".*?<Variant.*?>(.*?)</Variant>.*?[\\n\\r\\s]*(</.*?Variants>)?", Pattern.DOTALL);
        Matcher variantMatcher = variantPattern.matcher( output );
        Map<String, Long> variantNames = new HashMap<>();
        String variantGuid = "";
        Map<String, Long> usedSelectDataMap = new HashMap<>();
        Long variantIndex = 0L;

        Pattern refPattern = Pattern.compile("refid=\"[^\"]*\"");
        Pattern idPattern = Pattern.compile(" id=\"[^\"]*\"");
        Pattern guidPattern = Pattern.compile("guid=\"[^\"]*\"");
        Pattern deletePattern = Pattern.compile("delete=\"[^\"]*\"");
        Pattern namePattern = Pattern.compile("<Name[^>]*>.*?</Name>");

        while (variantMatcher.find()) {
			String stringVariantMatcher = variantMatcher.group();
        	// Performance fix. Check if closing tag on Variants has been reached - group 2 will contain </xxxVariants> XML tag - exit loop
			if(StringUtils.isNotBlank(variantMatcher.group(2))) break;

			Pattern attrPattern = Pattern.compile("<Variant.*?>");
            Matcher attrMatcher = attrPattern.matcher(stringVariantMatcher);
            long refId = 0L;

            boolean beingDeleted = false;

            while (attrMatcher.find()) {
                Matcher refMatcher = refPattern.matcher(attrMatcher.group());
                while (refMatcher.find() && checkIsInternalXmlFormat != XML_FORMAT_TYPE_BULK_VARIANT_METADATA) {
                	String id = refMatcher.group().replace("refid=", "").replace("\"", "");
                	if(!id.isEmpty())
            			refId = Long.valueOf(id);
                }
                if(checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_TP_VARIANTS){
                	if ( document != null && document.getSelectionParameterGroup().getParameterGroupItems().size() == 1
                			&& (refId != 0 && refId != masterVarinatId)){
                		errors.reject("page.text.single.selector.touchpoint.return.to.master", new String[]{}, "");
            			out.close();
            			return;
                	}
                }

                Matcher idMatcher = idPattern.matcher(attrMatcher.group());
                long id = 0L;
                if(!idMatcher.find()){
                	errors.reject("error.id.mandatory.and.has.to.be.greater.than.0", new String[]{}, "");
        			out.close();
        			return;
                }
                while (idMatcher.find()) {
                	String idString = idMatcher.group().replace("id=", "").replace("\"", "");
                	if(!idString.isEmpty()){
            			id = Long.valueOf(idString);
            			if(id < 1){
        					errors.reject("error.id.mandatory.and.has.to.be.greater.than.0", new String[]{}, "");
                			out.close();
                			return;
        				}
                	}else{
                		errors.reject("error.id.mandatory.and.has.to.be.greater.than.0", new String[]{}, "");
            			out.close();
            			return;
                	}
                }

	            Matcher guidMatcher = guidPattern.matcher(attrMatcher.group());
	            while (guidMatcher.find()) {
	            	variantGuid = guidMatcher.group().replace("guid=", "").replace("\"", "");
	            }

                Matcher deleteMatcher = deletePattern.matcher(attrMatcher.group());
                while (deleteMatcher.find()) {
                    String deleteString = deleteMatcher.group().replace("delete=", "").replace("\"", "");
                    beingDeleted = deleteString != null &&
                        (
                            deleteString.equalsIgnoreCase("true")
                            || deleteString.equalsIgnoreCase("t")
                            || deleteString.equalsIgnoreCase("yes")
                            || deleteString.equalsIgnoreCase("y")
                            || deleteString.equalsIgnoreCase("1")
                        );
                }
            }

            if(beingDeleted) {
                continue;
            }

            Matcher nameMatcher = namePattern.matcher(stringVariantMatcher);
            String nameMatch = "";
            while (nameMatcher.find() && checkIsInternalXmlFormat != XML_FORMAT_TYPE_BULK_VARIANT_METADATA) {
            	nameMatch = nameMatcher.group();
            	nameMatch = nameMatch.replace("<Name>", "");
            	nameMatch = nameMatch.replace("</Name>", "");
            	if(this.checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_TP_VARIANTS){
            		SelectableValidationUtil.checkNodeNameValue(nameMatch, 2, 80, errors);
            	}else{
            		SelectableValidationUtil.checkNodeNameValue(nameMatch, errors);
            	}

            	if (errors.hasErrors()) {
            		out.close();
            		return;
            	}
            	if(!variantNames.containsKey(nameMatch)){
        			variantNames.put(nameMatch, refId);
        		}else{
        			long parentId = variantNames.get(nameMatch);
    				if(refId == parentId && checkIsInternalXmlFormat != XML_FORMAT_TYPE_BULK_TP_MESSAGES){
    					errors.reject("error.touchpointselection.selection.name.already.exists.name", new String[]{nameMatch,}, "");
            			out.close();
            			return;
    				}
        		}
            }

            Pattern emptyVariantPattern = Pattern.compile("<Name/>");
            Matcher emptyVariantMatcher = emptyVariantPattern.matcher(stringVariantMatcher);
            while (emptyVariantMatcher.find()) {
            	errors.reject("error.variant.name.can.not.be.empty", new String[]{importFile.getName(),}, "");
            	out.close();
            	return;
            }

            Pattern selectionPattern = Pattern.compile("<SelectionData[^>]*>.*?</SelectionData>");
            Matcher selectionMatcher = selectionPattern.matcher(stringVariantMatcher);
            Map<Long, String> parentSelectionValues = new HashMap<>();
            while (selectionMatcher.find() && checkIsInternalXmlFormat != XML_FORMAT_TYPE_BULK_VARIANT_METADATA) {
            	String selectionMatch = selectionMatcher.group();
            	String selectionValue = "";
            	selectionMatch = selectionMatch.replace("<SelectionData>", "");
            	selectionMatch = selectionMatch.replace("</SelectionData>", "");
            	if(selectionMatch.isEmpty()){
            		if((checkIsInternalXmlFormat != XML_FORMAT_TYPE_BULK_TP_VARIANTS
    						&& (checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_TP_VARIANTS && refId != masterVarinatId && refId != 0))
    						|| checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_DYNAMIC_MESSAGE
							|| checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_DYNAMIC_ST_VARIANTS
    						|| checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_DYNAMIC_IMAGE_VARIANTS){
                		errors.reject("error.input.mandatory", new String[] { "Selection Data" }, "Selection Data is mandatory");
                		out.close();
                		return;
                	}
            	}
            	selectionValue = selectionMatch;
            	selectionMatch = selectionMatch.toLowerCase();
            	SelectableValidationUtil.checkDataValueImport(nameMatch,"Variant selection data", selectionMatch, variantIndex, usedSelectDataMap, errors);
            	//SelectableValidationUtil.checkDataValue("Variant selection data", selectionMatch, errors);
            	if (errors.hasErrors()) {
            		out.close();
            		return;
            	}
            	if ( checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_TP_VARIANTS
            			&& document != null && document.getSelectionParameterGroup().getParameterGroupItems().size() == 1
            			&& (selectionMatch.contains(";"))){
            		errors.reject("page.text.single.selector.touchpoint.value.invalid", new String[]{}, "");
        			out.close();
        			return;
            	}

            	if(!parentSelectionValues.containsKey(refId)){
            		parentSelectionValues.put(refId, selectionMatch);
        		}else{
        			String firstSelectData = parentSelectionValues.get(refId);
    				if(firstSelectData.equals(selectionMatch)){
    					errors.reject("error.message.combition.is.duplicated.in.the.entered.value", new String[]{selectionMatch,}, "");
            			out.close();
            			return;
    				}
        		}
            	if ( checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_TP_VARIANTS && variantGuid.isEmpty()){
            		// validate name and selectionData with existing selectors
            		List<Long> pgTreeNodeIds = document.getTouchpointSelectionTreeNodeIds();
            		List<String> value = new ArrayList<>(Arrays.asList(selectionMatch.split(";")));
            		ParameterGroupTreeNode pgTreeNode = ParameterGroupTreeNode.searchForExactMatch(pgTreeNodeIds, value);
            		if (pgTreeNode != null) {
            			errors.reject("error.touchpointselection.selectors.add.selection.criteria.conflicts.with.an.existing.selection", new String[] { selectionValue, nameMatch }, "");
            			out.close();
            			return;
            		}
            	}
            }

        	Pattern emptySelectionDataPattern = Pattern.compile("<SelectionData/>");
            Matcher emptySelectionDataMatcher = emptySelectionDataPattern.matcher(stringVariantMatcher);
            while (emptySelectionDataMatcher.find()) {
            	if((checkIsInternalXmlFormat != XML_FORMAT_TYPE_BULK_TP_VARIANTS
						&& (checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_TP_VARIANTS && refId != masterVarinatId && refId != 0))
						|| checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_DYNAMIC_MESSAGE
						|| checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_DYNAMIC_ST_VARIANTS
						|| checkIsInternalXmlFormat == XML_FORMAT_TYPE_BULK_DYNAMIC_IMAGE_VARIANTS){
            		errors.reject("error.input.mandatory", new String[] { "Selection Data" }, "Selection Data is mandatory");
            		out.close();
            		return;
            	}
    		}

            ++ variantIndex;
        }
        
	    Pattern listTagPattern = Pattern.compile("<Content[^>]*>.*?</Content>");
        Matcher listTagMatcher = listTagPattern.matcher( output );
        while (listTagMatcher.find()) {
        	String tagMatcher = listTagMatcher.group();
        	if(!tagMatcher.contains("locale=") && checkIsInternalXmlFormat != XML_FORMAT_TYPE_BULK_VARIANT_METADATA){
        		errors.reject("error.input.mandatory", new String[] { "Content locale attribute" }, "Content locale attribute is mandatory");
            	out.close();
    			return;
        	}

			if (!tagMatcher.contains("![CDATA[")) {
				String listTagMatch = tagMatcher;
				Pattern styleAttrPattern = Pattern.compile("<Content[^>]*>");
				Matcher styleAttrMatcher = styleAttrPattern.matcher(listTagMatch);

				while (styleAttrMatcher.find()) {
					String styleMatch = styleAttrMatcher.group();
					listTagMatch = listTagMatch.replace(styleMatch, styleMatch.concat("<![CDATA["));
				}
				Pattern closingPattern = Pattern.compile("</Content>");
				Matcher closingMatcher = closingPattern.matcher(listTagMatch);

				while (closingMatcher.find()) {
					String styleMatch = closingMatcher.group();
					listTagMatch = listTagMatch.replace(styleMatch, "]]>".concat(styleMatch));
				}

				output = output.replace(tagMatcher, listTagMatch);
			}
        }
        out.write(output);
	    out.close();
	}
	
    
    private void cleanup_user_xml(File importFile, File temp, BindException errors) throws IOException {
		BufferedReader r = new BufferedReader(new InputStreamReader(new FileInputStream(importFile), "UTF8"));
	    BufferedWriter out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(temp), "UTF-8"));
	    
	    String line;
	    String output = "";
	    while ((line = r.readLine()) != null) {
	    	line = cleanupContentTag(line, "Instance");
	    	line = cleanupContentTag(line, "Domain");
	    	output = output.concat(line);
	    }
	    r.close();
	    
	    Pattern definitiontPattern = Pattern.compile("<MpBulkUploadDefinition.*?>", Pattern.DOTALL);
	    Matcher definitionMatcher = definitiontPattern.matcher( output );
	    // Get Domain from xml
	    Branch branch = null;
	    while (definitionMatcher.find()) {
	    	Pattern guidPattern = Pattern.compile("guid=\"[^\"]*\"");
            Matcher guidMatcher = guidPattern.matcher(definitionMatcher.group());
            while (guidMatcher.find()) {
            	String guid = guidMatcher.group().replace("guid=", "").replace("\"", "");
            	if(!guid.isEmpty()){
            		branch = Branch.findByGuid(guid);
            	}
            }
	    }
	    if(branch != null){
	    	
	    }
	    // User
	    Pattern userPattern = Pattern.compile(".*?<User.*?>(.*?)</User>.*?", Pattern.DOTALL);
        Matcher userMatcher = userPattern.matcher( output );
        while (userMatcher.find()) {
        	String stringUserPattern = userMatcher.group();
        	
        	// can't be empty tag names
        	String[] notEmptyTagNames = {"FirstName", "LastName", "UserName", "Email", "IDProvider"};
        	List<String> notEmptyTags = Arrays.asList(notEmptyTagNames);
        	for(String tagName: notEmptyTags){
        		Pattern selectionPattern = Pattern.compile("<"+ tagName+"[^>]*>.*?</" + tagName +">");
                Matcher selectionMatcher = selectionPattern.matcher(stringUserPattern);
                
                while (selectionMatcher.find()) {
                	String selectionMatch = selectionMatcher.group();
                	selectionMatch = selectionMatch.replace("<" + tagName + ">", "");
                	selectionMatch = selectionMatch.replace("</" + tagName + ">", "");
                	if(selectionMatch.isEmpty() || selectionMatch.trim().isEmpty()){
                		errors.reject("error.input.mandatory", new String[] { tagName }, tagName + " is mandatory");
                    		out.close();
                    		return;
                    }
                }
                // Empty tag
                Pattern emptySelectionDataPattern = Pattern.compile("<" + tagName + "/>");
                Matcher emptySelectionDataMatcher = emptySelectionDataPattern.matcher(stringUserPattern);
                while (emptySelectionDataMatcher.find()) {
                	errors.reject("error.input.mandatory", new String[] { tagName }, tagName + " is mandatory");
                		out.close();
                		return;
                }
        	}
        }
        Pattern listTagPattern = Pattern.compile("<Instance[^>]*>.*?</Instance>");
        Matcher listTagMatcher = listTagPattern.matcher( output );
        while (listTagMatcher.find()) {
        	String tagMatcher = listTagMatcher.group();
        	if(!tagMatcher.contains("roleid=")){
        		errors.reject("error.input.mandatory", new String[] { "roleid" }, "roleid attribute is mandatory");
            	out.close();
    			return;
        	}
        	if(!tagMatcher.contains("role=")){
        		errors.reject("error.input.mandatory", new String[] { "role" }, "role attribute is mandatory");
            	out.close();
    			return;
        	}
        	String listTagMatch = tagMatcher;
        	Pattern styleAttrPattern = Pattern.compile("<Instance[^>]*>");
            Matcher styleAttrMatcher = styleAttrPattern.matcher(listTagMatch);
            
            while (styleAttrMatcher.find()) {
            	String styleMatch = styleAttrMatcher.group();
            	listTagMatch = listTagMatch.replace(styleMatch, styleMatch.concat("<![CDATA["));
            }
            Pattern closingPattern = Pattern.compile("</Instance>");
            Matcher closingMatcher = closingPattern.matcher(listTagMatch);
            
            while (closingMatcher.find()) {
            	String styleMatch = closingMatcher.group();
            	listTagMatch = listTagMatch.replace(styleMatch, "]]>".concat(styleMatch));
            }
            
            output = output.replace(tagMatcher, listTagMatch);
        }
        out.write(output);
	    out.close();
	}
    
    private long getMasterVariantId(String xmlGuid) {
		com.prinova.messagepoint.model.Document document = com.prinova.messagepoint.model.Document.findByGuid(xmlGuid);
		if(document == null)
			return 0;
		return document.getMasterTouchpointSelection().getParameterGroupTreeNode().getId();
	}

	public String cleanupContentTag(String line, String keyWord){

		Pattern patt_start = Pattern.compile("<" + keyWord + "_[0-9]*", Pattern.CASE_INSENSITIVE);
		Pattern patt_end = Pattern.compile("</" + keyWord + "_[0-9]*>", Pattern.CASE_INSENSITIVE);

		Matcher m = patt_start.matcher(line);
		line = m.replaceAll("<" + keyWord);

		Matcher endm = patt_end.matcher(line);
		line = endm.replaceAll("</" + keyWord + ">");

	    return line.concat("\r");
	}

	private String getTargetInstanceGuid(File importFile, BindException errors) {
		String instanceGuid = null;
		VTDGen vg = null;
		VTDNav vn = null;
		vg = new VTDGen();
		try {   	    	
			if (vg.parseFile(importFile.getCanonicalPath(), false) && ((vn = vg.getNav()) != null)) {
		    	AutoPilot ap = new AutoPilot();
		    	ap.bind(vn);
	        	int i; 	
	        	ap.selectXPath("/MpBulkUploadDefinition/@instanceGuid");
	        	if ((i = ap.evalXPath()) != -1) {
	                instanceGuid = vn.toString(i + 1);
	            }
	            ap.resetXPath();
			}
		} catch(Throwable e) {
	    	;
	    }
		return instanceGuid;
	}
	
	private String getTargetModelGuid(File importFile, BindException errors) {
		String modelGuid = null;
		VTDGen vg = null;
		VTDNav vn = null;
		vg = new VTDGen();
		try {   	    	
			if (vg.parseFile(importFile.getCanonicalPath(), false) && ((vn = vg.getNav()) != null)) {
		    	AutoPilot ap = new AutoPilot();
		    	ap.bind(vn);
	        	int i; 	
	        	ap.selectXPath("/MpBulkUploadDefinition/@guid");
	        	if ((i = ap.evalXPath()) != -1) {
		            modelGuid = vn.toString(i + 1);
	            }
	            ap.resetXPath();
			}
		} catch(Throwable e) {
			;
	    }
		return modelGuid;
	}
	
	private long getTargetModelId(File importFile, String[] importFileName, BindException errors) {
		long id = -1;
		if (importFile == null || !importFile.exists()) {
			log.error("TouchpointImport file is not valid for " + Arrays.toString(importFileName) + ": failed in getTargetModelId because importFile is " + (importFile == null ? "null" : "not null") + ", importFile " + (importFile.exists() ? "exists" : "does not exist"));
			errors.reject("error.import.file.is.not.valid", importFileName, "");
			return id;
		}
		String guid = getTargetModelGuid(importFile, errors);
		if (guid == null || guid.isEmpty()) {
			log.error("TouchpointImport file is not valid for " + Arrays.toString(importFileName) + ": failed in getTargetModelId because guid is empty");
			errors.reject("error.import.file.is.not.valid", importFileName, "");
			return id;
		}
		switch (checkIsInternalXmlFormat) {
		   case XML_FORMAT_TYPE_BULK_DYNAMIC_MESSAGE:
		   	case XML_FORMAT_TYPE_BULK_DYNAMIC_IMAGE_VARIANTS:
			case XML_FORMAT_TYPE_BULK_DYNAMIC_ST_VARIANTS:
		   {
			   	ContentObject model = ContentObject.findByGuid(guid);
				if (model == null){
					errors.reject("error.message.model.not.exsiting");
				} else if (!model.getGuid().equals(tpImportTask.getTpOrientation())){
					// different model
					errors.reject("error.message.invalid.guid");					 
				} else {
					id = model.getId();
				}
		   }
		   break;	
		   case XML_FORMAT_TYPE_BULK_TP_VARIANTS:
		   case XML_FORMAT_TYPE_BULK_TP_MESSAGES:
		   case XML_FORMAT_TYPE_BULK_VARIANT_METADATA:
		   {
				com.prinova.messagepoint.model.Document model = com.prinova.messagepoint.model.Document.findByGuid(guid);
				if (model == null){
					errors.reject("error.message.model.not.exsiting");
				} else if (!model.getGuid().equals(tpImportTask.getTpOrientation())) {
	    			errors.reject("error.message.touchpoint.not.matching");
	    		} else {
	    			id = model.getId();
	    		}
		   }
		   break;
		   case XML_FORMAT_TYPE_BULK_USERS:
		   {
			   Branch branch = Branch.findByGuid(guid);
			   if(branch == null){
				   errors.reject("error.message.domain.not.matching.or.doesnot.existing");
				} else if (!branch.getGuid().equals(tpImportTask.getTpOrientation())) {
	    			errors.reject("error.message.domain.not.matching.or.doesnot.existing");
	    		} else {
	    			id = branch.getId();
	    		}
		   }
		   break;
			   
		   default:
			   ;
		}
		return id;
	}
	
	public static boolean touchpointContainerUpdate(BindException errors, ApplicationReportContainer application, StatusPollingBackgroundTask statusPollingBackgroundTask) {					
		try (AutoCloseable autoUnlock = ImportTaskScheduler.lockImportingProcess("touchpointUpload")) { // Reuse the touchpointUpload type because applicationreport upload would create new touchpoint
	    	if(application.getOtherCustomerDataElement1() == null)
	    		application.setOtherCustomerDataElementName1("");
	    	if(application.getOtherCustomerDataElement2() == null)
	    		application.setOtherCustomerDataElementName2("");
	    	
			ServiceExecutionContext context = UpdateApplicationService.createContext(application, null, null);

			Service updateApplicationService = MessagepointServiceFactory.getInstance().lookupService(UpdateApplicationService.SERVICE_NAME, UpdateApplicationService.class);
			updateApplicationService.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			
			if (!serviceResponse.isSuccessful()) {
				ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
				return false;
			} else {
				return true;
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			return false;
		}
		
	}

}
