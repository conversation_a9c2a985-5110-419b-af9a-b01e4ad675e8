{"_class": "MSArchiveHeader", "version": 103, "compatibilityVersion": 99, "metadata": {"commit": "4e7e2f5d7940a711b59f89190b5b7e3029f050f5", "appVersion": "50.2", "build": 55047, "app": "com.bohemiancoding.sketch3", "compatibilityVersion": 99, "version": 103, "variant": "NONAPPSTORE"}, "root": {"_class": "sharedStyle", "name": "Body2/Left/Regular/Success", "value": {"_class": "style", "endDecorationType": 0, "miterLimit": 10, "sharedObjectID": "4035B6EC-4E58-42C6-A99C-1FB9496C44AB", "startDecorationType": 0, "textStyle": {"_class": "textStyle", "encodedAttributes": {"paragraphStyle": {"_class": "paragraphStyle", "alignment": 0, "maximumLineHeight": 25.20000076293945, "minimumLineHeight": 25.20000076293945, "allowsDefaultTighteningForTruncation": 0}, "MSAttributedStringFontAttribute": {"_class": "fontDescriptor", "attributes": {"name": "MuseoSans-500", "size": 14}}, "MSAttributedStringColorAttribute": {"_class": "color", "alpha": 1, "blue": 0.1490196078431373, "green": 0.5176470588235295, "red": 0.2313725490196079}}, "verticalAlignment": 0}}}}