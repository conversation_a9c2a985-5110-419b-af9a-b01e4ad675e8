package com.prinova.messagepoint.platform.services.admin;

import com.prinova.messagepoint.model.tenant.Tenant;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class UpdateTenantServiceRequest implements ServiceRequest{

	private static final long serialVersionUID = -4499410312261603625L;

	private boolean inEditMode;
	private long id;
	private String name;
	private String code;
	private Tenant parent;
	private String themeName;
	private boolean enabled;
	private String contactName;
	private String contactTitle;
	private String contactEmail;
	private String contactPhone;
	private String contactMobile;
	private String contactFax;
	private String contactStreetAddress;
	private String contactSuitUnit;
	private String contactCity;
	private String contactCountry;
	private String contactProvinceState;
	private String contactPostalZipCode;
	private String contactWebSite;

	
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public boolean isInEditMode() {
		return inEditMode;
	}
	public void setInEditMode(boolean inEditMode) {
		this.inEditMode = inEditMode;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public Tenant getParent() {
		return parent;
	}
	public void setParent(Tenant parent) {
		this.parent = parent;
	}
	public String getThemeName() {
		return themeName;
	}
	public void setThemeName(String themeName) {
		this.themeName = themeName;
	}
	public boolean isEnabled() {
		return enabled;
	}
	public void setEnabled(boolean enabled) {
		this.enabled = enabled;
	}
	public String getContactName() {
		return contactName;
	}
	public void setContactName(String contactName) {
		this.contactName = contactName;
	}
	public String getContactTitle() {
		return contactTitle;
	}
	public void setContactTitle(String contactTitle) {
		this.contactTitle = contactTitle;
	}
	public String getContactEmail() {
		return contactEmail;
	}
	public void setContactEmail(String contactEmail) {
		this.contactEmail = contactEmail;
	}
	public String getContactPhone() {
		return contactPhone;
	}
	public void setContactPhone(String contactPhone) {
		this.contactPhone = contactPhone;
	}
	public String getContactMobile() {
		return contactMobile;
	}
	public void setContactMobile(String contactMobile) {
		this.contactMobile = contactMobile;
	}
	public String getContactFax() {
		return contactFax;
	}
	public void setContactFax(String contactFax) {
		this.contactFax = contactFax;
	}
	public String getContactStreetAddress() {
		return contactStreetAddress;
	}
	public void setContactStreetAddress(String contactStreetAddress) {
		this.contactStreetAddress = contactStreetAddress;
	}
	public String getContactSuitUnit() {
		return contactSuitUnit;
	}
	public void setContactSuitUnit(String contactSuitUnit) {
		this.contactSuitUnit = contactSuitUnit;
	}
	public String getContactCity() {
		return contactCity;
	}
	public void setContactCity(String contactCity) {
		this.contactCity = contactCity;
	}
	public String getContactCountry() {
		return contactCountry;
	}
	public void setContactCountry(String contactCountry) {
		this.contactCountry = contactCountry;
	}
	public String getContactProvinceState() {
		return contactProvinceState;
	}
	public void setContactProvinceState(String contactProvinceState) {
		this.contactProvinceState = contactProvinceState;
	}
	public String getContactPostalZipCode() {
		return contactPostalZipCode;
	}
	public void setContactPostalZipCode(String contactPostalZipCode) {
		this.contactPostalZipCode = contactPostalZipCode;
	}
	public String getContactWebSite() {
		return contactWebSite;
	}
	public void setContactWebSite(String contactWebSite) {
		this.contactWebSite = contactWebSite;
	}
}
