package com.prinova.messagepoint.controller.tpadmin;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.TouchpointAdminEvents;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.testing.TestingUtils;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditMetadataBuilder;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.email.TemplateModifier;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.tpadmin.UpdateTemplateModifiersService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.NestedPageUtils;

public class TouchpointTemplateModifierController extends MessagepointController {
//	private String templateModifierEditRedirect;
	
	public static final String REQ_PARAM_DOCUMENTID 	= "documentId";
	
	public static final int ACTION_EDIT 				= 1;
	
	@Override
	protected Map<String,Object> referenceData(HttpServletRequest request, Object commandObj, Errors errors) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		// Retrieve the available document sections
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1L);
		Document document = Document.findById(documentId);
		referenceData.put("document", document);
		Command command = (Command) commandObj;
		if (document != null) {
			
			referenceData.put("templateModifiers", command.getModifiers());
		}
		
		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor( TemplateModifier.class, new IdCustomEditor<>(TemplateModifier.class) );
		binder.registerCustomEditor( String.class, new StringXSSEditor() );
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1L);
		Document document = Document.findById(documentId);
		Command command = new Command();
		command.setDocument(document);
        List<TemplateModifier> modifiers = new ArrayList<>(TemplateModifier.findAllMasterActiveModifiersByTouchpoint(document));
		command.setModifiers(modifiers);
		Map<String, String> modifiersMap = new HashMap<>();
		for(TemplateModifier modifier:modifiers){
			modifiersMap.put(modifier.getConnectorName(), modifier.getName());
		}
		command.setModifiersMap(modifiersMap);
		return command;
	}
	
	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors)
			throws Exception {

		AnalyticsEvent<TouchpointAdminEvents> analyticsEvent = AnalyticsUtil.requestFor(TouchpointAdminEvents.TouchpointTemplateModifiers)
				.setAction(Actions.Update);

		try {
			long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1L);
			Document document = Document.findById(documentId);
			Command command = (Command) commandObj;

			ServiceExecutionContext modifierContext = UpdateTemplateModifiersService.createContext(document, command.getModifiers(), null, null);
			Service updateTemplateModifiersService = MessagepointServiceFactory.getInstance().lookupService(UpdateTemplateModifiersService.SERVICE_NAME, UpdateTemplateModifiersService.class);
			updateTemplateModifiersService.execute(modifierContext);
			ServiceResponse modifierServiceResponse = modifierContext.getResponse();

			if(!modifierServiceResponse.isSuccessful()){
				ServiceResponseConverter.convertToSpringErrors(modifierServiceResponse, errors);
				return super.showForm(request, response, errors);
			}
			// Audit (TP template modifiers Edit)
			AuditEventUtil.push(AuditEventType.ID_TOUCHPOINT_SETTING, AuditObjectType.ID_TOUCHPOINT, document.getName(), document.getId(), AuditActionType.ID_CHANGES,
					AuditMetadataBuilder.forTPTemplateModifiersChanges(command.getModifiersMap(), command.getModifiers()));
			Map<String, Object> parms = TestingUtils.getContextMapParms(request);
			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
			return new ModelAndView(new RedirectView("../frameClose.jsp"), parms);
		} finally {
			analyticsEvent.send();
		}

	}
	
	public static class Command {
		private Document document;
		private List<TemplateModifier> modifiers = new ArrayList<>();
		private Map<String, String>modifiersMap = new HashMap<>();
				
		public Document getDocument() {
			return document;
		}
		public void setDocument(Document document) {
			this.document = document;
		}
		
		public List<TemplateModifier> getModifiers() {
			return modifiers;
		}
		public void setModifiers(List<TemplateModifier> modifiers) {
			this.modifiers = modifiers;
		}
		public Map<String, String> getModifiersMap() {
			return modifiersMap;
		}
		public void setModifiersMap(Map<String, String> modifiersMap) {
			this.modifiersMap = modifiersMap;
		}
	}
}
