package com.prinova.messagepoint.platform.ws;

import org.apache.commons.logging.Log;
import org.jdom2.Element;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ws.server.endpoint.annotation.Endpoint;
import org.springframework.ws.server.endpoint.annotation.PayloadRoot;
import org.springframework.ws.server.endpoint.annotation.RequestPayload;
import org.springframework.ws.server.endpoint.annotation.ResponsePayload;

import com.prinova.messagepoint.initialization.CopySchema;
import com.prinova.messagepoint.integrator.MessagepointMultiTenantConnectionProvider;
import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;

@Endpoint
public class NodeToNodeCopyEndpoint extends MessagepointEndpoint 
{
	private static final Log log = LogUtil.getLog(NodeToNodeCopyEndpoint.class);

	public NodeToNodeCopyEndpoint()
	{
		super("NodeToNodeCopyResponse", "NodeToNodeCopyRequest");
	}

	@PayloadRoot(namespace = NAMESPACE_URI, localPart = "NodeToNodeCopyRequest")
	@ResponsePayload
	public Element handleRequest(@RequestPayload Element request) throws Exception
	{
		try
		{
			return handleRequestImpl(request);
		}
		catch( Exception ex )
		{
			log.error("Web service exception:", ex);
			throw ex;
		}
	}

	@Transactional(propagation = Propagation.REQUIRED, readOnly = false)
	public Element handleRequestImpl(Element request) throws Exception
	{
		String branchName = Node.getCurrentBranchName();
		Branch branch = Node.getCurrentBranch();

		Element fromElem = getChild(request, "FromNode", false);
		Element toElem = getChild(request, "ToNode", true );

		String fromName = fromElem != null ? fromElem.getText() : null;
		String toName = toElem.getText();

		if (fromName != null && fromName.equals("?"))
			fromName = null;

		String fromSchemaName = com.prinova.messagepoint.model.Node.getSchema(fromName, branchName);
		Node fromNode = null;
		if ( (fromSchemaName == null || fromSchemaName.isEmpty() ) && branch.isMaster() )
		{
			Node dcsNode = branch.getDcsNode();
			if ( dcsNode.getName().equals(fromName) )
			{
				fromSchemaName = MessagepointMultiTenantConnectionProvider.getPodMasterSchemaName();
				fromNode = dcsNode;
			}
		}
		String toSchemaName = com.prinova.messagepoint.model.Node.getSchema(toName, branchName);

		if ( fromSchemaName == null || fromSchemaName.isEmpty() )
			return makeFailureResponse("Invalid 'From' point requested");
		if ( toSchemaName == null || toSchemaName.isEmpty() )
			return makeFailureResponse("Invalid 'To' point requested");

		if ( fromNode == null )
			fromNode = Node.findBySchema(fromSchemaName);
		Node toNode = Node.findBySchema(toSchemaName);

		// We can copy from any type of a node
		//
		//  if ( fromNode.getNodeType() != Node.NODE_TYPE_PRODUCTION )
		// 	return makeFailureResponse("Must copy from a production instance");
		
		// We cannot copy into default node (production)
		//
		if ( toNode.isDcsNode() )
			return makeFailureResponse("You cannot copy into domain default instance (production).");

		if ( toNode.getStatus() == Node.NODE_STATUS_IN_INITIALIZING_PROCESS ||
			 toNode.getStatus() == Node.NODE_STATUS_IN_MIGRATING_PROCESS )
		{
			return makeFailureResponse("The target instance is currently busy doing other processing.");
		}
		
		if ( toNode.getStatus() != Node.NODE_STATUS_OFFLINE && toNode.getStatus() != Node.NODE_STATUS_ONLINE )
		{
			return makeFailureResponse("The target instance status has to be off-line or on-line. Please validate the instance.");
		}

		if ( fromNode.getStatus() != Node.NODE_STATUS_OFFLINE && fromNode.getStatus() != Node.NODE_STATUS_ONLINE )
		{
			return makeFailureResponse("The source instance status has to be off-line or on-line. Please validate the instance.");
		}

		if ( toNode.getId() == fromNode.getId() )
			return makeFailureResponse("The source and target instance is the same. You cannot copy to the same instance.");

		// take toNode to initializing process and disable it (deactivate) so no other processes will access it 
		Node.changeStatus(toNode.getId(), Node.NODE_STATUS_IN_INITIALIZING_PROCESS);
		Node.activateNode(toNode.getId(), false);
		
		CopySchema task = new CopySchema(fromNode, fromSchemaName, toNode, false);
		MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);

		return makeSuccessResponse("Starting to copy instance.");
	}
	
    public String getDisplayName() {
    	return ApplicationUtil.getMessage("page.label.web.service.node.to.node.copy");
    }

}
