package com.prinova.messagepoint.platform.services.tpadmin;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.workgroup.Workgroup;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;

public class BulkUpdateZoneVisibilityService extends AbstractService {
	public static final String SERVICE_NAME = "tpadmin.BulkUpdateZoneVisibilityService";
	
	private static final Log log = LogUtil.getLog(BulkUpdateZoneVisibilityService.class);
	
	public void execute(ServiceExecutionContext context) {
		BulkUpdateZoneVisibilityServiceRequest request = (BulkUpdateZoneVisibilityServiceRequest) context.getRequest();
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			
			List<Workgroup> selectedWorkgroups = new ArrayList<Workgroup>();
			if ( request.getWorkgroupIds() != null ) {
				for ( Long workgroupId : request.getWorkgroupIds() ) {
					selectedWorkgroups.add(Workgroup.findById(workgroupId));
				}
			}

			List<Workgroup> availableWorkgroups = Workgroup.findOrderByName();
			boolean singleSelected = request.getZoneIds().size() == 1;
			boolean overridden = request.isOverridden();
			
			for ( Workgroup workgroup : availableWorkgroups ) {
				if ( singleSelected ) {	// Single select, allow editing
					Zone zone = Zone.findById(request.getZoneIds().iterator().next());
					if ( zone.getWorkgroups().contains(workgroup) && !selectedWorkgroups.contains(workgroup) ) {	// Remove zone from workgroup
						workgroup.getZones().remove(zone);
					} else if ( !zone.getWorkgroups().contains(workgroup) && selectedWorkgroups.contains(workgroup) ) {	// Add zone to workgroup
						workgroup.getZones().add(zone);
					}

					// OMNI CHANNEL: Specialty zones are hidden; visibility control is set from alternates
					if ( (zone.isEmailSubjectLine() || zone.isWebTitleLine()) && zone.getParent() != null && zone.getParent().getDocument().isPrintTouchpoint() ) {
						Zone hiddenParent = zone.getParent();
						if ( hiddenParent.getWorkgroups().contains(workgroup) && !selectedWorkgroups.contains(workgroup) ) {	// Remove zone from workgroup
							workgroup.getZones().remove(hiddenParent);
						} else if ( !hiddenParent.getWorkgroups().contains(workgroup) && selectedWorkgroups.contains(workgroup) ) {	// Add zone to workgroup
							workgroup.getZones().add(hiddenParent);
						}
					}
					
					if ( zone.isAlternate() || zone.getDocument().isChannelAlternate() ) {
						zone.setOverrideWorkgroups(true);
						zone.save();
					}
					
				} else {	// Bulk select, only add new workgroups
					
					for ( Long zoneId : request.getZoneIds() ) {
						
						Zone zone = Zone.findById(zoneId);
						
						if ( overridden ) {
							if ( zone.getWorkgroups().contains(workgroup) && !selectedWorkgroups.contains(workgroup) ) {	// Remove zone from workgroup
								workgroup.getZones().remove(zone);
							} else if ( !zone.getWorkgroups().contains(workgroup) && selectedWorkgroups.contains(workgroup) ) {	// Add zone to workgroup
								workgroup.getZones().add(zone);
							}
						} else {
							if( !zone.getWorkgroups().contains(workgroup) && selectedWorkgroups.contains(workgroup) ) {	// Add zone to workgroup
									workgroup.getZones().add(zone);
							}
						}

						// OMNI CHANNEL: Specialty zones are hidden; visibility control is set from alternates
						if ( (zone.isEmailSubjectLine() || zone.isWebTitleLine()) && zone.getParent() != null && zone.getParent().getDocument().isPrintTouchpoint() ) {
							Zone hiddenParent = zone.getParent();
							if ( overridden ) {
								if ( hiddenParent.getWorkgroups().contains(workgroup) && !selectedWorkgroups.contains(workgroup) ) {	// Remove zone from workgroup
									workgroup.getZones().remove(hiddenParent);
								} else if ( !hiddenParent.getWorkgroups().contains(workgroup) && selectedWorkgroups.contains(workgroup) ) {	// Add zone to workgroup
									workgroup.getZones().add(hiddenParent);
								}
							} else {
								if( !hiddenParent.getWorkgroups().contains(workgroup) && selectedWorkgroups.contains(workgroup) ) {	// Add zone to workgroup
										workgroup.getZones().add(hiddenParent);
								}
							}
						}
						
						if ( zone.isAlternate() || zone.getDocument().isChannelAlternate() ) {
							zone.setOverrideWorkgroups(true);
							zone.save();
						}
						
					}
				}
				workgroup.save();
			}
		} catch (Exception e) {
			log.error(" unexpected exception when invoking BulkUpdateZoneVisibilityService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e.toString(),
					context.getLocale());
			throw new RuntimeException(e);
		}		
	}

	public void validate(ServiceExecutionContext context) {
		
	}
	
	public static ServiceExecutionContext createContext(Set<Long> zoneIds, List<Long> workgroupIds, boolean overridden){
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		BulkUpdateZoneVisibilityServiceRequest request = new BulkUpdateZoneVisibilityServiceRequest();
		context.setRequest(request);
		
		request.setZoneIds(zoneIds);
		request.setWorkgroupIds(workgroupIds);
		request.setOverridden(overridden);
		
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);
		
		return context;		
	}
}
