# API Generation Process - Systematic Approach

> **Process**: Complete REST API Generation Process
> **Status**: ✅ Steps 1-2 Validated and Documented
> **Last Updated**: 2025-01-18

## 🎯 PROCESS OVERVIEW

This document outlines the systematic process for creating comprehensive DTOs for REST API generation, validated through the ContentObject implementation.

## 📋 STEP-BY-STEP DTO CREATION PROCESS

### **Phase 1: Analysis and Planning**

#### **1.1 Domain Model Analysis**
- ✅ **Examine entity model** (e.g., ContentObject.java)
- ✅ **Review Hibernate mapping** (e.g., ContentObject.hbm.xml)
- ✅ **Identify simple fields** (exclude complex associations)
- ✅ **Trace enumeration values** from constants classes
- ✅ **Document field groups** by business function

#### **1.2 Related Entity Analysis**
- ✅ **Identify associated entities** (e.g., ContentObjectData, ContentObjectAssociation)
- ✅ **Determine relationship patterns** (one-to-many, many-to-one, etc.)
- ✅ **Plan DTO structure** (separate DTOs vs embedded fields)
- ✅ **Check for existing DTOs** to reuse (e.g., TouchpointDto)

#### **1.3 Reference Pattern Analysis**
- ✅ **Study existing DTO patterns** (e.g., VariableDto.java)
- ✅ **Follow package structure** (`com.prinova.messagepoint.platform.mprest.dto`)
- ✅ **Apply consistent annotations** (@Schema, @JsonPropertyOrder, @JsonInclude)
- ✅ **Use correct file locations** (`src/java/...` not `frontend/src/java/...`)

### **Phase 2: Core DTO Implementation**

#### **2.1 Main Entity DTO Creation**
```java
// Template structure for main entity DTO
@Schema(description = "Entity description for REST API operations")
@JsonPropertyOrder({"field1", "field2", "field3", ...})
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EntityDto extends RestResourceDtoBase {
    
    // Core Identity Fields
    @Schema(description = "Field description", example = "example_value")
    private String field1;
    
    // ... other simple fields ...
    
    // Association Fields (populated separately)
    @Schema(description = "Associated data", accessMode = Schema.AccessMode.READ_ONLY)
    private Map<Integer, AssociatedDataDto> associatedData;
    
    // Constructors, getters, setters
}
```

#### **2.2 Field Selection Criteria**
- ✅ **Include simple fields only** (String, Integer, Boolean, Date, etc.)
- ✅ **Exclude complex associations** (handled by separate DTOs)
- ✅ **Exclude sensitive fields** (lockedFor, lockedForName, sha256Hash per user request)
- ✅ **Include computed fields** for API usability (isLocked, typeName, etc.)
- ✅ **Include audit fields** (created, updated, createdBy, updatedBy)

#### **2.3 Enhanced Documentation**
- ✅ **Trace enumeration values** from constants classes
- ✅ **Add allowableValues** to @Schema annotations
- ✅ **Provide business context** in descriptions
- ✅ **Include example values** for better API documentation

### **Phase 3: Associated Entity DTOs**

#### **3.1 Related Entity DTO Creation**
- ✅ **Create separate DTOs** for each related entity
- ✅ **Follow same patterns** as main entity DTO
- ✅ **Minimize field count** for efficiency (e.g., ContentObjectDataDto: 4 essential fields)
- ✅ **Include traced enumeration values** for all relevant fields

#### **3.2 Association Integration**
- ✅ **Add association fields** to main DTO
- ✅ **Use appropriate collection types** (Map for keyed data, List for collections)
- ✅ **Implement auto-population** in constructor
- ✅ **Handle null collections** gracefully

### **Phase 4: Auto-Population Implementation**

#### **4.1 Constructor Enhancement**
```java
public EntityDto(Entity entity) {
    if (entity != null) {
        // Core field population
        this.field1 = entity.getField1();
        
        // Association population
        this.associatedData = populateAssociatedData(entity);
        this.associations = populateAssociations(entity);
        
        // Existing DTO integration
        this.relatedEntity = entity.getRelatedEntity() != null ? 
            new RelatedEntityDto(entity.getRelatedEntity()) : null;
    }
}
```

#### **4.2 Helper Methods**
```java
private Map<Integer, AssociatedDataDto> populateAssociatedData(Entity entity) {
    Map<Integer, AssociatedDataDto> dataMap = new HashMap<>();
    
    if (entity.getAssociatedDataMap() != null) {
        entity.getAssociatedDataMap().forEach((key, data) -> {
            if (data != null) {
                dataMap.put(key, new AssociatedDataDto(data));
            }
        });
    }
    
    return dataMap.isEmpty() ? null : dataMap;
}
```

### **Phase 5: Quality Validation**

#### **5.1 Compilation Verification**
```bash
./gradlew compileJava --continue
# Must return: BUILD SUCCESSFUL
```

#### **5.2 Pattern Compliance Check**
- ✅ **RestResourceDtoBase inheritance**
- ✅ **@JsonPropertyOrder** with all field names
- ✅ **@JsonInclude(JsonInclude.Include.NON_NULL)**
- ✅ **No redundant @JsonProperty** annotations
- ✅ **Complete getter/setter methods**

#### **5.3 Documentation Quality**
- ✅ **Complete @Schema descriptions**
- ✅ **Traced enumeration values** with allowableValues
- ✅ **Business context** in field descriptions
- ✅ **Access mode annotations** (READ_ONLY for computed fields)

## 🎯 VALIDATED OUTCOMES

### **ContentObject Implementation Results**:
- ✅ **ContentObjectDto**: 29 fields (26 core + 3 associations)
- ✅ **ContentObjectDataDto**: 4 essential fields with traced values
- ✅ **ContentObjectAssociationDto**: 16 fields with computed helpers
- ✅ **TouchpointDto integration**: Existing DTO reused
- ✅ **Auto-population**: Complete domain model conversion
- ✅ **Compilation success**: All DTOs compile without errors

### **API Response Quality**:
- ✅ **Single API call** provides complete entity context
- ✅ **Rich metadata** with traced enumeration values
- ✅ **Structured associations** (Map for keyed data, List for collections)
- ✅ **Null safety** throughout the implementation
- ✅ **Type safety** with proper generic handling

## 📋 REUSABLE CHECKLIST

### **For Each New Entity DTO Creation**:

#### **Analysis Phase**:
- [ ] Examine entity model and Hibernate mapping
- [ ] Identify simple fields and exclude complex associations
- [ ] Trace enumeration values from constants classes
- [ ] Check for existing related DTOs to reuse
- [ ] Study reference patterns (VariableDto, etc.)

#### **Implementation Phase**:
- [ ] Create main entity DTO with proper annotations
- [ ] Create related entity DTOs (minimized field sets)
- [ ] Implement auto-population in constructors
- [ ] Add helper methods for association conversion
- [ ] Integrate existing DTOs where applicable

#### **Validation Phase**:
- [ ] Verify compilation success
- [ ] Check pattern compliance (inheritance, annotations)
- [ ] Validate documentation quality
- [ ] Test null safety and type safety
- [ ] Update field counts and summaries

#### **Documentation Phase**:
- [ ] Create implementation summary
- [ ] Document field groups and business context
- [ ] Record traced enumeration values
- [ ] Update API generation guidelines

---

## 📋 STEP 2: REST CONTROLLER CREATION PROCESS

### **Phase 1: Reference Pattern Analysis**

#### **2.1 Controller Pattern Study**
- ✅ **Examine existing controller** (e.g., Variables.java)
- ✅ **Identify endpoint patterns** (filter, list, get single)
- ✅ **Study authorization patterns** (scopes and roles)
- ✅ **Review parameter handling** (PaginationParams, CursorParams, etc.)
- ✅ **Understand response patterns** (sendSuccessResponse, error handling)

#### **2.2 Component Pattern Study**
- ✅ **Examine existing component** (e.g., DataVariablesComponent.java)
- ✅ **Study business logic patterns** (filtering, querying, single retrieval)
- ✅ **Review Hibernate usage** (criteria queries, findByGuid patterns)
- ✅ **Understand service integration** (FilterComponent usage)

### **Phase 2: Controller Implementation**

#### **2.1 REST Controller Creation**
```java
// Template structure for REST controller
@Tag(name = "Entity Name", description = "Entity Management API")
@RestController
@RequestMapping("/entityname")
public class EntityController extends RestResourceBase {

    private final EntityComponent entityComponent;

    // Constructor injection
    public EntityController(EntityComponent entityComponent) {
        this.entityComponent = entityComponent;
    }

    // Three core endpoints: filter, list, get single
}
```

#### **2.2 Endpoint Implementation Patterns**
- ✅ **Filter endpoint**: POST with FilterRequest body
- ✅ **List endpoint**: GET with DTO query parameters
- ✅ **Get single endpoint**: GET with GUID path parameter
- ✅ **Consistent authorization**: scope and role-based
- ✅ **Standard parameters**: pagination, cursor, partial response, include

#### **2.3 Component Implementation**
```java
// Template structure for component
@Component
public class EntityComponent {

    // Filter method using FilterComponent
    public FilterResult<Entity> filterEntities(FilterRequest filterRequest,
                                              PaginationParams paginationParams,
                                              CursorParams cursorParams) {
        FilterComponent<Entity, EntityDto> filterComponent = new FilterComponent<>(Entity.class, EntityDto.class);
        return filterComponent.filter(filterRequest, paginationParams, cursorParams);
    }

    // Query method using Hibernate criteria
    public List<Entity> findEntities(EntityDto entityParams) {
        // Build MessagepointCriterion list
        // Use MessagepointRestrictions for conditions
        // Apply MessagepointOrder for sorting
        // Execute with HibernateUtil.getManager().getObjectsAdvanced()
    }

    // Single retrieval method
    public Entity findEntityByGuid(String guid) {
        return Entity.findByGuid(guid);
    }
}
```

### **Phase 3: Infrastructure Updates**

#### **3.1 RestResourceType Enhancement**
- ✅ **Add new resource type** to RestResourceType enum
- ✅ **Assign unique ID** and label key
- ✅ **Follow existing naming patterns**

#### **3.2 Authorization Configuration**
- ✅ **Define appropriate scopes** (entity:read, entity:admin)
- ✅ **Define appropriate roles** (ROLE_ENTITY_LIST, ROLE_ENTITY_EDIT)
- ✅ **Apply consistently** across all endpoints

### **Phase 4: Quality Validation**

#### **4.1 Compilation Verification**
```bash
./gradlew compileJava --continue
# Must return: BUILD SUCCESSFUL
```

#### **4.2 Pattern Compliance Check**
- ✅ **Controller extends RestResourceBase**
- ✅ **Component uses @Component annotation**
- ✅ **Proper import statements** (correct package paths)
- ✅ **Consistent endpoint patterns** with reference controller
- ✅ **Complete OpenAPI documentation**

#### **4.3 Authorization Validation**
- ✅ **@PreAuthorize annotations** on all endpoints
- ✅ **Scope and role combinations** properly configured
- ✅ **Consistent authorization** across similar operations

## 🎯 VALIDATED OUTCOMES

### **ContentObject Implementation Results**:
- ✅ **ContentObjects controller**: 3 endpoints (filter, list, get single)
- ✅ **ContentObjectsComponent**: 3 business methods with Hibernate integration
- ✅ **RestResourceType.CONTENT_OBJECT**: Infrastructure integration
- ✅ **Authorization**: content:read, content:admin, ROLE_CONTENT_LIST, ROLE_CONTENT_EDIT
- ✅ **Compilation success**: All components compile without errors

### **API Endpoint Quality**:
- ✅ **Complete OpenAPI documentation** with proper annotations
- ✅ **Consistent parameter handling** (pagination, cursor, partial response)
- ✅ **Proper error handling** (404 for not found, validation errors)
- ✅ **Type safety** with DTO parameter validation
- ✅ **Performance optimization** with Hibernate criteria queries

## 📋 REUSABLE CHECKLIST

### **For Each New Entity Controller Creation**:

#### **Analysis Phase**:
- [ ] Study Variables controller pattern and endpoint structure
- [ ] Study DataVariablesComponent pattern and business logic
- [ ] Identify appropriate authorization scopes and roles
- [ ] Plan endpoint operations (filter, list, get, create, update, delete)

#### **Implementation Phase**:
- [ ] Create REST controller with proper annotations and inheritance
- [ ] Create component with business logic methods
- [ ] Add RestResourceType enum entry
- [ ] Implement proper authorization with @PreAuthorize
- [ ] Add complete OpenAPI documentation

#### **Validation Phase**:
- [ ] Verify compilation success
- [ ] Check pattern compliance with reference implementations
- [ ] Validate authorization configuration
- [ ] Test endpoint parameter handling
- [ ] Verify error handling and response patterns

#### **Documentation Phase**:
- [ ] Create implementation summary
- [ ] Document endpoint patterns and business logic
- [ ] Record authorization configuration
- [ ] Update API generation guidelines

---

## 🔧 CRITICAL FEEDBACK CORRECTIONS

### **Authorization and Security**

#### **1. Use Existing Roles from applicationContext-security.xml**
- ❌ **WRONG**: `ROLE_CONTENT_LIST`, `ROLE_CONTENT_EDIT` (non-existent roles)
- ✅ **CORRECT**: Use existing roles from security configuration:
  - `ROLE_MESSAGE_VIEW_ALL` - View all messages/content
  - `ROLE_MESSAGE_VIEW_MY` - View own messages/content
  - `ROLE_EMBEDDED_CONTENT_VIEW` - View embedded content
  - `ROLE_EMBEDDED_CONTENT_ADMIN` - Admin embedded content

**Process**: Always check `/content/..` URL patterns in `securityMetadataSource` property of applicationContext-security.xml

#### **2. Add New Scopes to RestApiScopes Enum**
- ❌ **WRONG**: Using scopes in `hasAnyAuthority()` without adding to enum
- ✅ **CORRECT**: Add scopes to `RestApiScopes` enum first:
```java
CONTENT_READ(12, "content:read", "page.label.services.scopes.content.read"),
CONTENT_ADMIN(14, "content:admin", "page.label.services.scopes.content.admin");
```

**Process**: Always add new scopes to RestApiScopes enum before using in @PreAuthorize

### **URL and Endpoint Patterns**

#### **3. Use Kebab-Case for Request Mappings**
- ❌ **WRONG**: `@RequestMapping("/contentobjects")`
- ✅ **CORRECT**: `@RequestMapping("/content-objects")`

**Process**: Always use kebab-case for multi-word endpoint names

### **Include Parameters and Reference Handling**

#### **4. Remove includeParams for Single Get Operations**
- ❌ **WRONG**: Include `IncludeParams` parameter in single get methods
- ✅ **CORRECT**: Remove `IncludeParams` and hardcode `isReferenced=true`

**Implementation Pattern**:
```java
// Add isReferenced field to DTO
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Indicates whether the entity is referenced by other content")
private Boolean isReferenced;

// Add constructor with IncludeParams
public EntityDto(@Nullable Entity entity, IncludeParams includeExpensiveParams) {
    this(entity);
    if(includeExpensiveParams != null && entity != null){
        if(includeExpensiveParams.hasInclude("isReferenced")){
            this.setReferenced(entity.isReferenced());
        }
    }
}

// Use in controller
EntityDto dto = new EntityDto(entity, new IncludeParams().include("isReferenced"));
```

## 📋 UPDATED REUSABLE CHECKLIST

### **For Each New Entity Controller Creation**:

#### **Authorization Phase**:
- [ ] Check applicationContext-security.xml for existing roles under `/entity/..` patterns
- [ ] Add new scopes to RestApiScopes enum if needed
- [ ] Use existing roles in @PreAuthorize annotations
- [ ] Apply scope and role combinations consistently

#### **Endpoint Configuration Phase**:
- [ ] Use kebab-case for multi-word request mappings
- [ ] Remove includeParams from single get operations
- [ ] Add isReferenced field and constructor to DTO
- [ ] Hardcode isReferenced=true for single get operations

#### **Validation Phase**:
- [ ] Verify authorization uses existing roles
- [ ] Verify scopes exist in RestApiScopes enum
- [ ] Verify kebab-case URL patterns
- [ ] Verify isReferenced implementation follows Variables pattern

---

**This systematic API generation process ensures consistent, high-quality REST APIs that follow established patterns and provide comprehensive functionality with proper security and documentation.** 🎯
