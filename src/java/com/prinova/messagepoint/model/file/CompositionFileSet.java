package com.prinova.messagepoint.model.file;

import java.io.*;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.ConnectorConfiguration;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.*;
import com.prinova.messagepoint.wtu.ReferencableObject;
import org.apache.commons.lang.StringUtils;
import org.hibernate.query.NativeQuery;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.springframework.web.multipart.MultipartFile;

import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.dialogue.DialogueConfiguration;
import com.prinova.messagepoint.model.gmc.GMCConfiguration;
import com.prinova.messagepoint.model.testing.TestScenario;

public class CompositionFileSet extends IdentifiableMessagePointModel {

    private static final long serialVersionUID = 9182690567945695085L;

    private String					templateFileName;
    private String 					compositionConfigurationFileName;
    private Document 				document;
    private TouchpointCollection	touchpointCollection;
    private String					fileUploadSyncKey;

    private String                  sha256Hash;
    private boolean                 needRecalculateHash = true;

    private Set<DatabaseFile> additionalFiles = new HashSet<>();
    // Public default constructor
    //
    public CompositionFileSet()
    {
    }

    // Copy constructor (not public) for cloning
    //
    protected CompositionFileSet(CompositionFileSet cloneFrom, Document clonedDocument, TouchpointCollection clonedTouchpointCollection)
    {
        super(cloneFrom);
        this.document = clonedDocument;
        this.touchpointCollection = clonedTouchpointCollection;
        this.save();
        copyDataFrom(cloneFrom);
    }

    @Override
    public Object clone()
    {
        return clone(this.getDocument(), this.getTouchpointCollection());
    }

    public CompositionFileSet clone(Document document)
    {
        return clone(document, null);
    }

    public CompositionFileSet clone(Document document, TouchpointCollection touchpointCollection)
    {
        return new CompositionFileSet(this, document, touchpointCollection);
    }

    public void copyDataFrom(CompositionFileSet cloneFrom) {
        CloneHelper.execInSaveSession(()->{
            if(! StringUtil.isEmptyOrNull(templateFileName)) {
                try {
                    deleteFile(templateFileName);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            if(! StringUtil.isEmptyOrNull(compositionConfigurationFileName)) {
                try {
                    deleteFile(compositionConfigurationFileName);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            if(additionalFiles != null && ! additionalFiles.isEmpty()) {
                additionalFiles.clear();
            }
        });


//        this.touchpointCollection = cloneFrom.touchpointCollection;

        this.setTemplateFileName                    ( cloneFrom.getTemplateFileName() );
        this.setCompositionConfigurationFileName    ( cloneFrom.getCompositionConfigurationFileName() );
        this.setFileUploadSyncKey                   ( cloneFrom.getFileUploadSyncKey() );

        if (cloneFrom.additionalFiles != null) {
            for (DatabaseFile file : cloneFrom.additionalFiles) {
                DatabaseFile clone = CloneHelper.clone(file);
                CloneHelper.execInSaveSession(()->{
                    this.additionalFiles.add(clone);
                });
            }
        }

        CloneHelper.execInSaveSession(()->{
            String dir = getCompositionFilePath(true);
            new File(dir).mkdirs();
        });

        {
            File sourceTemplateFile = cloneFrom.getTemplateFile();
            if (sourceTemplateFile.exists()) {
                File targetTemplateFile = CloneHelper.queryInSaveSession(() -> this.getTemplateFile());
                FileUtil.copy(sourceTemplateFile, targetTemplateFile);
            }
        }

        {
            File sourceCompositionConfigurationFile = cloneFrom.getCompositionConfigurationFile();
            if (sourceCompositionConfigurationFile.exists()) {
                File targetCompositionConfigurationFile = CloneHelper.queryInSaveSession(() -> this.getCompositionConfigurationFile());
                FileUtil.copy(sourceCompositionConfigurationFile, targetCompositionConfigurationFile);
            }
        }
    }
    public static CompositionFileSet findById(long id){
        return HibernateUtil.getManager().getObject(CompositionFileSet.class, id);
    }

    public static List<CompositionFileSet> findAll() {
        return HibernateUtil.getManager().getObjects(CompositionFileSet.class);
    }

    public static CompositionFileSet findBySyncKey(String key){
        return HibernateUtil.getManager().getObjectUnique(CompositionFileSet.class, MessagepointRestrictions.eq("fileUploadSyncKey", key).ignoreCase());
    }
    public static List<CompositionFileSet> findByDocumentId(Long documentId){
        return HibernateUtil.getManager().getObjectsAdvanced(CompositionFileSet.class, MessagepointRestrictions.eq("document.id", documentId), MessagepointOrder.asc("name"));
    }
    public static List<CompositionFileSet> findByTpCollectionId(Long tpCollectionId){
        return HibernateUtil.getManager().getObjectsAdvanced(CompositionFileSet.class, MessagepointRestrictions.eq("touchpointCollection.id", tpCollectionId), MessagepointOrder.asc("name"));
    }
    public static CompositionFileSet findByDocumentAndDna(Long documentId, String dna){
        return HibernateUtil.getManager().getObjectsAdvanced(CompositionFileSet.class, MessagepointRestrictions.and(
            MessagepointRestrictions.eq("document.id", documentId),
            MessagepointRestrictions.eq("dna", dna)
        ), MessagepointOrder.asc("name"))
            .stream().findFirst().orElse(null);
    }

    public static CompositionFileSet findByNameAndDocumentId(Long documentId, String compositionFileSetName){
        return HibernateUtil.getManager().getObjectsAdvanced(CompositionFileSet.class, MessagepointRestrictions.and(
                        MessagepointRestrictions.eq("document.id", documentId),
                        MessagepointRestrictions.eq("name", compositionFileSetName)
                ), MessagepointOrder.asc("name"))
                .stream().findFirst().orElse(null);
    }

    public static CompositionFileSet findByNameAndTpCollection(Long tpCollectionId, String compositionFileSetName){
        return HibernateUtil.getManager().getObjectsAdvanced(CompositionFileSet.class, MessagepointRestrictions.and(
                        MessagepointRestrictions.eq("touchpointCollection.id", tpCollectionId),
                        MessagepointRestrictions.eq("name", compositionFileSetName)
                ), MessagepointOrder.asc("name"))
                .stream().findFirst().orElse(null);
    }

    public boolean isReferenced() {
        boolean diaConfigsRef = HibernateUtil.getManager().exists(DialogueConfiguration.class, MessagepointRestrictions.eq("compositionFileSet", this));
        if(diaConfigsRef){
            return true;
        }
        boolean gmcConfigsRef = HibernateUtil.getManager().exists(GMCConfiguration.class, MessagepointRestrictions.eq("compositionFileSet", this));
        if(gmcConfigsRef){
            return true;
        }
        boolean testScenariosRef = HibernateUtil.getManager().exists(TestScenario.class, MessagepointRestrictions.eq("compositionFileSet", this));
        if(testScenariosRef){
            return true;
        }
        boolean tpCollectionRef = HibernateUtil.getManager().exists(TouchpointCollection.class, MessagepointRestrictions.eq("defaultCompositionFileSet", this));
        if(tpCollectionRef){
        		return true;
        }
        return false;
    }

    public String getCompositionFilePath(boolean saveRequest) {
        if( getId() == 0 ) throw new RuntimeException("Must save CompositionFileSet to database before attempting to save file");
        
        String path = "";
        if (saveRequest && MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() != null) 
        {
        	path = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.Uploaded.KEY_CompositionFileSetDir, MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier()) + "/" + getId() + "/";
        }
        else
        {
        	path = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.Uploaded.KEY_CompositionFileSetDir) + "/" + getId() + "/";
        }
        
        return path;
    }

    public void saveFile( MultipartFile file ) throws IOException {
        String dir = getCompositionFilePath(true);
        new File(dir).mkdirs();
        file.transferTo( new File(dir +  file.getOriginalFilename()));
    }

    public void saveFile( File file ) {
        String dir = getCompositionFilePath(true);
        new File(dir).mkdirs();
        FileUtil.copy(file, new File(dir +  file.getName()));
    }

    public void deleteFile ( String filename ) {
        File file = new File ( getCompositionFilePath(false) + filename );
        file.delete();
    }

    public File getTemplateFile() {
        String filepath = getCompositionFilePath(false) + getTemplateFileName();
        File file = new File( filepath );
        return file;
    }
    public File getCompositionConfigurationFile() {
        String filepath = getCompositionFilePath(false) + getCompositionConfigurationFileName();
        File file = new File( filepath );
        return file;
    }

    @Override
    public String getSha256Hash() {
        return sha256Hash;
    }

    @Override
    public void setSha256Hash(String sha256Hash) {
        this.sha256Hash = sha256Hash;
    }

    public boolean isNeedRecalculateHash() {
        return needRecalculateHash;
    }

    public void setNeedRecalculateHash(boolean needRecalculateHash) {
        this.needRecalculateHash = needRecalculateHash;
    }

    @Override
    public int hashCode() {
        final int PRIME = 31;
        int result = 1;
        result = PRIME * result + ((created == null) ? 0 : created.hashCode());
        result = PRIME * result + ((templateFileName == null) ? 0 : templateFileName.hashCode());
        result = PRIME * result + ((compositionConfigurationFileName == null) ? 0 : compositionConfigurationFileName.hashCode());
        result = PRIME * result + ((name == null) ? 0 : name.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (!(obj instanceof CompositionFileSet))
            return false;
        final CompositionFileSet other = (CompositionFileSet) obj;
        if(!this.getObjectSchemaName().equals(other.getObjectSchemaName()))
            return false;
        if (name == null) {
            if (other.getName() != null)
                return false;
        } else if (!name.equals(other.getName()))
            return false;
        if (created == null) {
            if (other.getCreated() != null)
                return false;
        } else if (created.equals(other.getCreated()))
            return false;
        if (templateFileName == null) {
            if (other.getTemplateFileName() != null)
                return false;
        } else if (!templateFileName.equals(other.getTemplateFileName()))
            return false;
        if (compositionConfigurationFileName == null) {
            if (other.getCompositionConfigurationFileName() != null)
                return false;
        } else if (!compositionConfigurationFileName.equals(other.getCompositionConfigurationFileName()))
            return false;
        return true;
    }

    public boolean isValid(){
        return StringUtils.isNotBlank(getName());
    }
    public String getTemplateFileName() {
        return templateFileName;
    }
    public void setTemplateFileName(String templateFileName) {
        this.templateFileName = templateFileName;
    }

    public String getCompositionConfigurationFileName() {
        return compositionConfigurationFileName;
    }
    public void setCompositionConfigurationFileName(
            String compositionConfigurationFileName) {
        this.compositionConfigurationFileName = compositionConfigurationFileName;
    }

    public Document getDocument() {
        return document;
    }
    public void setDocument(Document document) {
        this.document = document;
    }

    public TouchpointCollection getTouchpointCollection() {
        return touchpointCollection;
    }
    public void setTouchpointCollection(TouchpointCollection touchpointCollection) {
        this.touchpointCollection = touchpointCollection;
    }

    public String getFileUploadSyncKey() {
        return fileUploadSyncKey;
    }
    public void setFileUploadSyncKey(String fileUploadSyncKey) {
        this.fileUploadSyncKey = fileUploadSyncKey;
    }

    public Set<DatabaseFile> getAdditionalFiles() {
        return additionalFiles;
    }

    public void setAdditionalFiles(Set<DatabaseFile> additionalFiles) {
        this.additionalFiles = additionalFiles;
    }

    @Override
    public void preSave(Boolean isNew) {
        super.preSave(isNew);
        if(needRecalculateHash) {
            makeHash(false);
        }
    }

    @Override
    public void makeHash(boolean isAlgorithmChanged) {
        additionalFiles.forEach(df->df.makeHash(isAlgorithmChanged));

        StringBuilder hashDataStringBuilder = new StringBuilder();

        hashDataStringBuilder.append("compositionPackage");

        if(name != null) {
            hashDataStringBuilder.append(" name: " + name);
        }

        if(dna != null) {
            hashDataStringBuilder.append(" dna: " + dna);
        }

        if(fileUploadSyncKey != null) {
            hashDataStringBuilder.append(" fileUploadSyncKey: " + fileUploadSyncKey);
        }

        if(templateFileName != null) {
            hashDataStringBuilder.append(" templateFileName: " + templateFileName);
            if(getId() > 0) {
                File templateFile = getTemplateFile();
                if (templateFile.exists()) {
                    String templateFileHash = calculateFileHash(null, false, null, templateFile);
                    hashDataStringBuilder.append(" templateFileHash: " + templateFileHash);
                }
            }
        }

        if(compositionConfigurationFileName != null) {
            hashDataStringBuilder.append(" compositionConfigurationFileName: " + compositionConfigurationFileName);
            if(getId() > 0) {
                File compositionConfigurationFile = getCompositionConfigurationFile();
                if (compositionConfigurationFile.exists()) {
                    String compositionConfigurationHash = calculateFileHash(null, false, null, compositionConfigurationFile);
                    hashDataStringBuilder.append(" compositionConfigurationHash: " + compositionConfigurationHash);
                }
            }
        }

        if(additionalFiles != null && ! additionalFiles.isEmpty()) {
            List<DatabaseFile> additionalFileList = new ArrayList<>(additionalFiles);

            Collections.sort(additionalFileList, this::compareDatabaseFile);

            hashDataStringBuilder.append(" additionalFiles: " + additionalFileList
                .stream()
                .sequential()
                .map(df->"[" + df.getFileName() + "]=" + df.getHashSafe())
                .collect(Collectors.joining(",")));
        }

        String objectHashKey = getObjectHashKey();
        sha256Hash = calculateSha256Hash(objectHashKey, isAlgorithmChanged, sha256Hash, hashDataStringBuilder.toString());
    }

    @Override
    public Map<String, Object> getAttributesMap() {
        Map<String, Object> attributesMap = super.getAttributesMap();
        attributesMap.put("page.label.attribute.name", name);

        if(fileUploadSyncKey != null) {
            attributesMap.put("page.label.attribute.fileUploadSyncKey", fileUploadSyncKey);
        }

        if(templateFileName != null) {
            attributesMap.put("page.label.attribute.composition.template.file.name", templateFileName);
            File templateFile = getTemplateFile();
            if(templateFile.exists()) {
                String templateFileHash = calculateFileHash(null, false, null, templateFile);
                attributesMap.put("page.label.attribute.composition.template.file.content.hash", templateFileHash);
            }
        }

        if(compositionConfigurationFileName != null) {
            attributesMap.put("page.label.attribute.composition.configuration.file.name", compositionConfigurationFileName);
            File compositionConfigurationFile = getCompositionConfigurationFile();
            if(compositionConfigurationFile.exists()) {
                String compositionConfigurationHash = calculateFileHash(null, false, null, compositionConfigurationFile);
                attributesMap.put("page.label.attribute.composition.configuration.file.content.hash", compositionConfigurationHash);
            }
        }

        List<DatabaseFile> additionalFileList = new ArrayList<>();
        if(additionalFiles != null && !additionalFiles.isEmpty()) {
            additionalFileList.addAll(additionalFiles);
        }

        Collections.sort(additionalFileList, this::compareDatabaseFile);

        Map<String, Object> additionalFilesMap = new LinkedHashMap<>();
        additionalFileList.forEach(df->{
            Map<String, Object> dfMap = df.getAttributesMap();
            String dfKey = "page.label.attribute.file.name|page.label.file|" + df.getFileName();
            additionalFilesMap.put(dfKey, dfMap);
        });

        attributesMap.put("page.label.attribute.composition.addition.files", additionalFilesMap);

        return attributesMap;
    }

    public static Map<Long, List<ReferencableObject>> getAllReferencesMap() {
        // returns the objects referencing this CompositionFileSet.

        Map<Long, List<ReferencableObject>> allReferencesMap = new HashMap<>();
        Map<Long, Set<Long>> alreadyMappedObjects = new HashMap<>();

        {
            String query =
                    "select c.id as config_id, cfs.id as file_set_id from dialogue_configuration c\n" +
                            "    inner join composition_file_set cfs on c.composition_file_set_id = cfs.id\n" +
                            "union\n" +
                            "select c.id as config_id, cfs.id as file_set_id from gmc_configuration c\n" +
                            "    inner join composition_file_set cfs on c.composition_file_set_id = cfs.id\n" +
                            "union\n" +
                            "select c.id as config_id, cfs.id as file_set_id from sefas_configuration c\n" +
                            "    inner join composition_file_set cfs on c.composition_file_set_id = cfs.id\n" +
                            "union\n" +
                            "select c.id as config_id, cfs.id as file_set_id from mphcs_configuration c\n" +
                            "    inner join composition_file_set cfs on c.composition_file_set_id = cfs.id";
            getAllReferencesMapByQuery(query, ConnectorConfiguration.class, allReferencesMap, alreadyMappedObjects);
        }

        return allReferencesMap;
    }

    private static void getAllReferencesMapByQuery(String query, Class<? extends IdentifiableMessagePointModel> referencesClass, Map<Long, List<ReferencableObject>> allReferencesMap, Map<Long, Set<Long>> alreadyMappedObjects) {
        NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
        for(Object obj : sqlQuery.list()){
            Object[] fields = (Object[])obj;
            BigInteger configId = (BigInteger)fields[0];
            BigInteger compositionFileSetId = (BigInteger)fields[1];

            CompositionFileSet compositionFileSet = CompositionFileSet.findById(compositionFileSetId.longValue());
            IdentifiableMessagePointModel referencingModelObject = HibernateUtil.getManager().getObject(referencesClass, configId.longValue());

            if(compositionFileSet != null && referencingModelObject != null) {
                Set<Long> mappedObjectsForThisTargetGroup = alreadyMappedObjects.get(compositionFileSet.getId());
                if(mappedObjectsForThisTargetGroup == null) {
                    mappedObjectsForThisTargetGroup = new HashSet<>();
                    alreadyMappedObjects.put(compositionFileSet.getId(), mappedObjectsForThisTargetGroup);
                }
                if(! mappedObjectsForThisTargetGroup.contains(referencingModelObject.getId())) {
                    ReferencableObject referencableObject = new ReferencableObject(referencingModelObject);
                    List<ReferencableObject> list = allReferencesMap.get(compositionFileSet.getId());
                    if(list == null) {
                        list = new ArrayList<>();
                        allReferencesMap.put(compositionFileSet.getId(), list);
                    }
                    list.add(referencableObject);
                    mappedObjectsForThisTargetGroup.add(referencingModelObject.getId());
                }
            }
        }
    }

    private int compareDatabaseFile(DatabaseFile df1, DatabaseFile df2) {
        int comp = df1.getType() - df2.getType();
        if(comp != 0) return comp;
        comp = df1.getContentType().compareTo(df2.getContentType());
        if(comp != 0) return comp;
        comp = df1.getFileName().compareTo(df2.getFileName());
        if(comp != 0) return comp;
        return Long.valueOf(df1.getId()).compareTo(df2.getId());
    }

}

