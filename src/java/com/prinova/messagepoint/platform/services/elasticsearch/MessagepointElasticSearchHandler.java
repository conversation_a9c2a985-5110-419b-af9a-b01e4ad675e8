package com.prinova.messagepoint.platform.services.elasticsearch;

import ai.mpr.marcie.content.rationalizer.SearchContext;
import ai.mpr.marcie.reindex.common.stats.model.ApplicationStatsItemAggregator;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.prinova.messagepoint.controller.dashboards.GlobalDashboardContentDto;
import com.prinova.messagepoint.controller.dashboards.TotalCountAndGlobalDashboardContentDtosSlice;
import com.prinova.messagepoint.controller.dashboards.TotalCountAndTranslateDashboardContentDtosSlice;
import com.prinova.messagepoint.model.brand.BrandProfileEnum;
import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.platform.services.elasticsearch.dto.ElasticSearchApplicationMetadata;
import com.prinova.messagepoint.platform.services.elasticsearch.dto.ElasticsearchContentDto;
import com.prinova.messagepoint.platform.services.elasticsearch.search.query.RangeOperation;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

public interface MessagepointElasticSearchHandler {

    String getElasticAppId();

    void addOptionalPropertiesToContentJson(ElasticsearchContentDto elasticsearchContentDto, JsonObject contentJson);

    Set<String> getSupportedOptionalPropertyNames();

    boolean supportsOptionalProperty(String propertyXPath);

    Map<String, ElasticsearchAppOptionalProperty> readOptionalProperties();
    
    boolean recreateIndexForReIndexing(ElasticSearchApplicationMetadata elasticSearchApplicationMetadata);
    
    boolean createIndex(ElasticSearchApplicationMetadata elasticSearchApplicationMetadata);

    void deleteIndex();

    boolean indexExists();

    void sendBulkData(JsonArray contentJsonArray);

    void deleteContent(String contentGuid);

    String getContentIdByContentGuid(String contentGuid);

    Map<GlobalDashboardContentDto, Integer> searchDuplicatesWithFilterMap(Map<String, String> filterMap, List<DashboardFilter> dashboardFilterList);

    List<GlobalDashboardContentDto> searchDuplicatesByHashWithFilterMap(String hash, Map<String, String> filterMap, List<DashboardFilter> dashboardFilterList);

    Map<GlobalDashboardContentDto, Integer> searchSimilaritiesWithFilterMap(Map<String, String> filterMap, List<DashboardFilter> dashboardFilterList);

    Map<GlobalDashboardContentDto, Float> getFromMatchesSimilarityFieldByContentId(String contentId, float minSimilarity, float maxSimilarity);

    Float computeReadabilityFleschReadingAverageWithFilterMap(Map<String, String> filterMap, List<DashboardFilter> dashboardFilterList);

    Map<String, Integer> computeSentimentWithFilterMap(Map<String, String> filterMap, List<DashboardFilter> dashboardFilterList);

    Map<String, Integer> computeTranslateAccuracyCount(Map<String, String> filterMap,  List<String> selectedLanguageCode, String selectedStatus, String contentTypeFilterValue);

    JsonArray getContentsForGlobalSearch(String sSearch, List<String> tpIdsList,  List<String> targetItemTypesList, List<String> statusTargetList, boolean isExactMatch);

    TotalCountAndGlobalDashboardContentDtosSlice getContentIdsBySentimentWithFilterMap(
            String sentimentValue,
            Map<String, String> filterMap,
            List<DashboardFilter> dashboardFilterList,
            int startIndexInclusive,
            int endIndexExclusive
    );
    TotalCountAndTranslateDashboardContentDtosSlice getContentsByTranslationAccuracy(
            String translateAccuracyValue,
            Map<String, String> filterMap,
            int startIndexInclusive,
            int endIndexExclusive,
            List<String> selectedLanguages, String selectedContentStatus, String selectedContentTypeFilterValue
    );
    Integer countContentWithReadabilityComparedToTarget(RangeOperation operation, Double readabilityTargetValue, Map<String, String> filterMap, List<DashboardFilter> dashboardFilterList);

    TotalCountAndGlobalDashboardContentDtosSlice getContentIdsWithReadabilityComparedToTarget(
            RangeOperation operation,
            Double readabilityTargetValue,
            Map<String, String> filterMap,
            List<DashboardFilter> dashboardFilterList,
            int startIndexInclusive,
            int endIndexExclusive
    );

    TotalCountAndGlobalDashboardContentDtosSlice getContentIdsByBrandProfileEnumWithMetadataFilter(
            BrandProfileEnum brandProfileEnum,
            Map<String, String> filterMap,
            String termFilter,
            int startIndexInclusive,
            int endIndexExclusive
    );

    Map<String, Integer> computeBrandViolationsCountsWithMetadataFilter(List<String> enabledBrandProfileItems, Map<String, String> filterMap);

    JsonArray searchDuplicatesByHash(String hash);

    JsonArray searchSimilaritiesByText(String text, SearchContext<JsonObject> searchContext);

    void contentGetAllForQuery(JsonObject query, Function<JsonElement, Void> mappingFunction, Map<String, String> addToParams);

    boolean deleteBrandForAllContents();

    JsonObject loadMarcieStats();

    ApplicationStatsItemAggregator loadMarcieStatsItemAggregator();

    String loadMarcieStatsException(String statsId);

    void normalizeMarcieStatsOnMessagepointStartUp();
}
