package com.prinova.messagepoint.model.contentintelligence;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.util.HibernateUtil;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class ContentAssistant extends IdentifiableMessagePointModel {

    private String query;
    private boolean enabled = true;
    private String triggerCriteria = "1,3";
    private boolean systemControlled = false;
    private String contentSelector = null;
    private Integer batchThrottle = -1;

    private boolean targetAllTouchpoints = true;
    private Set<Document> targetTouchpoints;
    private boolean targetAllObjects = true;
    private String targetObjects = "0,1,4";
    private String targetObjectsStatus = "3";
    private boolean targetAllLocales = true;
    private Set<MessagepointLocale> targetLocales;

    public static ContentAssistant findById(long id) {
        return HibernateUtil.getManager().getObject(ContentAssistant.class, id);
    }

    public static List<ContentAssistant> findAll() {
        return HibernateUtil.getManager().getObjects(ContentAssistant.class);
    }

    public static List<ContentAssistant> findValidatorsForContentObject(ContentObject contentObject) {

        Set<Document> documents = contentObject.getDocuments();
        int objectType = contentObject.getObjectType();

        String query =
                "SELECT 		ca " +
                "FROM 			ContentAssistant AS ca " +
                "WHERE 			ca.enabled = true " +
                "AND 			(ca.targetAllObjects = true OR ca.targetObjects LIKE :objectType) " +
                "AND 			(ca.targetAllTouchpoints = true OR EXISTS (SELECT 1 FROM ca.targetTouchpoints tp WHERE tp IN (:documents))) ";

        Map<String, Object> params = new HashMap<>();

        params.put("objectType", "%" + objectType + "%");

        if ( documents != null && !documents.isEmpty() )
            params.put("documents", documents);
        else {
            Document doc = new Document();
            doc.setId(-1L);
            Set<Document> docs = new HashSet<>();
            docs.add(doc);
            params.put("documents", docs);
        }

        return (List<ContentAssistant>) HibernateUtil.getManager().getObjectsAdvanced(query, params);

    }


    // getters and setters

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getTriggerCriteria() {
        return triggerCriteria;
    }

    public void setTriggerCriteria(String triggerCriteria) {
        this.triggerCriteria = triggerCriteria;
    }

    public boolean isSystemControlled() {
        return systemControlled;
    }

    public void setSystemControlled(boolean systemControlled) {
        this.systemControlled = systemControlled;
    }

    public String getContentSelector() { return contentSelector; }

    public void setContentSelector(String contentSelector) { this.contentSelector = contentSelector; }

    public Integer getBatchThrottle() { return batchThrottle; }

    public void setBatchThrottle(Integer batchThrottle) { this.batchThrottle = batchThrottle; }

    public boolean isTargetAllTouchpoints() {
        return targetAllTouchpoints;
    }

    public void setTargetAllTouchpoints(boolean targetAllTouchpoints) {
        this.targetAllTouchpoints = targetAllTouchpoints;
    }

    public Set<Document> getTargetTouchpoints() {
        return targetTouchpoints;
    }

    public void setTargetTouchpoints(Set<Document> targetTouchpoints) {
        this.targetTouchpoints = targetTouchpoints;
    }

    public boolean isTargetAllObjects() {
        return targetAllObjects;
    }

    public void setTargetAllObjects(boolean targetAllObjects) {
        this.targetAllObjects = targetAllObjects;
    }

    public String getTargetObjects() {
        return targetObjects;
    }

    public void setTargetObjects(String targetObjects) {
        this.targetObjects = targetObjects;
    }

    public String getTargetObjectsStatus() { return targetObjectsStatus;    }

    public void setTargetObjectsStatus(String targetObjectsStatus) { this.targetObjectsStatus = targetObjectsStatus;    }

    public boolean isTargetAllLocales() {
        return targetAllLocales;
    }

    public void setTargetAllLocales(boolean targetAllLocales) {
        this.targetAllLocales = targetAllLocales;
    }

    public Set<MessagepointLocale> getTargetLocales() {
        return targetLocales;
    }

    public void setTargetLocales(Set<MessagepointLocale> targetLocales) {
        this.targetLocales = targetLocales;
    }
}
