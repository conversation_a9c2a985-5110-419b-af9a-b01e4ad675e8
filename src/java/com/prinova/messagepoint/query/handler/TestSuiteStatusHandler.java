package com.prinova.messagepoint.query.handler;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import com.prinova.messagepoint.model.testing.TestSuite;
import com.prinova.messagepoint.query.DefaultPage;
import com.prinova.messagepoint.query.IPage;

public class TestSuiteStatusHandler implements PostQueryHandler {

	private boolean isOrderAsc;
	
	public boolean isOrderAsc() {
		return isOrderAsc;
	}

	public void setOrderAsc(boolean isOrderAsc) {
		this.isOrderAsc = isOrderAsc;
	}
	
	@SuppressWarnings("unchecked")
	public IPage handle(IPage page) {
		List<TestSuite> suites = (List<TestSuite>)page.getList();
		
		Collections.sort(suites, new Comparator<>() {
            public int compare(TestSuite o1, TestSuite o2) {
                String status1 = getSuiteStatus(o1);
                String status2 = getSuiteStatus(o2);
                if (isOrderAsc()) {
                    return status1.compareTo(status2);
                } else {
                    return status2.compareTo(status1);
                }
            }
        });
		
        ((DefaultPage)page).setList(suites);
		return page;
	}

	private String getSuiteStatus(TestSuite suite){
		if(suite.isError() || ( suite.getDeliveryEvent() != null && suite.getDeliveryEvent().isError()) ){
			return "Error";
		}

		if(suite.isComplete()){
			return "Complete";
		}else{
			return "In process";
		}
	}
}
