<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="com.prinova.messagepoint.model.communication.ExternalProofValidation" table="external_proof_validation"  >
		<cache usage="read-write"/>
		<id name="id" column="id">
			<generator class="native" />
		</id>

		<many-to-one 	name="communication" 		column="communication_id" 		class="com.prinova.messagepoint.model.communication.Communication" 		not-found="ignore" />
		<many-to-one 	name="proof" 				column="communication_proof_id" class="com.prinova.messagepoint.model.communication.CommunicationProof" not-found="ignore" />

		<property name="statusId" 					column="status_id" />
		<property name="externalValidationEmail" 	column="external_validation_email" />
		<property name="dateResponseReceived" 		column="response_date" type="timestamp" />
        <property name="validationFeedback">
       		<column name="validation_feedback" sql-type="BYTEA"/>
		</property>

		<property name="created" type="timestamp"/>
		<property name="updated" type="timestamp"/>
		<property name="createdBy" 					column="created_by_id" />
		<property name="updatedBy" 					column="updated_by_id" />
   	</class>
</hibernate-mapping>
