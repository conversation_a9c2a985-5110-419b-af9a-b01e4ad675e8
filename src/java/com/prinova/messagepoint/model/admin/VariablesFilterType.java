package com.prinova.messagepoint.model.admin;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.common.StaticType;
import com.prinova.messagepoint.util.ApplicationUtil;

public class VariablesFilterType extends StaticType {
	private static final long serialVersionUID = 1151669253968555828L;
	
	public static final int ID_ALL = 1;
	public static final int ID_REFERENCED_ONLY = 2;
	public static final int ID_UNREFERENCED_ONLY = 3;
	
	public static final String MESSAGE_CODE_ALL = "page.label.list.filter.type.all";
	public static final String MESSAGE_CODE_REFERENCED_ONLY = "page.label.list.filter.type.referenced";
	public static final String MESSAGE_CODE_UNREFERENCED_ONLY = "page.label.list.filter.type.unreferenced";
	
	public VariablesFilterType(Integer id){
		super();
		switch(id){
		case ID_ALL:
			this.setId(ID_ALL);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ALL));
			this.setDisplayMessageCode(MESSAGE_CODE_ALL);
			break;
		case ID_REFERENCED_ONLY:
			this.setId(ID_REFERENCED_ONLY);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_REFERENCED_ONLY));
			this.setDisplayMessageCode(MESSAGE_CODE_REFERENCED_ONLY);
			break;
		case ID_UNREFERENCED_ONLY:
			this.setId(ID_UNREFERENCED_ONLY);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_UNREFERENCED_ONLY));
			this.setDisplayMessageCode(MESSAGE_CODE_UNREFERENCED_ONLY);
			break;
		}
	
	}	
	
	public VariablesFilterType(String name) {
		super();
		if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_ALL))) { 
			this.setId(ID_ALL);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ALL));
			this.setDisplayMessageCode(MESSAGE_CODE_ALL);
		}
		if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_REFERENCED_ONLY))) { 
			this.setId(ID_REFERENCED_ONLY);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_REFERENCED_ONLY));
			this.setDisplayMessageCode(MESSAGE_CODE_REFERENCED_ONLY);
		}
		if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_UNREFERENCED_ONLY))) { 
			this.setId(ID_UNREFERENCED_ONLY);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_UNREFERENCED_ONLY));
			this.setDisplayMessageCode(MESSAGE_CODE_UNREFERENCED_ONLY);
		}		
	}	
	
	public static List<VariablesFilterType> listAll() {
		List<VariablesFilterType> allListFilterTypes = new ArrayList<>();

		VariablesFilterType listFilterType = null;
		
		listFilterType = new VariablesFilterType(ID_ALL);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new VariablesFilterType(ID_REFERENCED_ONLY);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new VariablesFilterType(ID_UNREFERENCED_ONLY);
		allListFilterTypes.add(listFilterType);		
		
		return allListFilterTypes;
	}		
}
