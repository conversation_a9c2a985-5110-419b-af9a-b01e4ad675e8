package com.prinova.messagepoint.query.service;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.query.criterion.MessagepointOrder;

import com.prinova.messagepoint.platform.services.ServiceRequest;

public abstract class AbstractPaginationServiceRequest implements ServiceRequest {

	private static final long serialVersionUID = -1720355292501054110L;
	
	private Class<?> modelClass;
	private int pageSize;
	private int pageIndex;
	private List<MessagepointOrder> orderList = new ArrayList<>();
	protected int queryType;
	
	public List<MessagepointOrder> getOrderList() {
		return orderList;
	}

	public void setOrderList(List<MessagepointOrder> orderList) {
		this.orderList = orderList;
	}

	public Class<?> getModelClass() {
		return modelClass;
	}

	public void setModelClass(Class<?> modelClass) {
		this.modelClass = modelClass;
	}

	public int getPageIndex() {
		return pageIndex;
	}

	public void setPageIndex(int pageIndex) {
		this.pageIndex = pageIndex;
	}
	
	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}
	
	public int getQueryType() {
		return queryType;
	}
}
