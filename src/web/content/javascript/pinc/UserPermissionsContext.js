var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var _this = this;
// probably PincRole enum is unnecessary, just for reference
var PincRole;
(function (PincRole) {
    PincRole["PINC_COMPANY_READ"] = "pinc-company-read";
    PincRole["PINC_COMPANY_TEST"] = "pinc-company-test";
    PincRole["PINC_COMPANY_PRODUCTION"] = "pinc-company-production";
    PincRole["PINC_COMPANY_AUTHOR"] = "pinc-company-author";
    PincRole["PINC_COMPANY_ADMIN"] = "pinc-company-admin";
    PincRole["PINC_ADMIN_OPERATOR"] = "pinc-admin-operator";
})(PincRole || (PincRole = {}));
var UserPermissionsContext = React.createContext(null);
var UserPermissionsState = function (props) {
    var _a = React.useContext(PincApiContext), pincConfig = _a.pincConfig, apiGet = _a.apiGet, setErrors = _a.setErrors;
    var _b = React.useState([]), userPermissions = _b[0], setUserPermissions = _b[1];
    React.useEffect(function () {
        var getPermissionsFromRoles = function () { return __awaiter(_this, void 0, void 0, function () {
            var userRoles, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        userRoles = Array.isArray(PincConfiguration.pincUserRoles()) ? PincConfiguration.pincUserRoles() : [];
                        result = [];
                        return [4 /*yield*/, apiGet(pincConfig.apiServer + getApiEndpoint(ApiEndpoint.ROLES), function (roles) {
                                if (Array.isArray(roles)) {
                                    roles.forEach(function (role) {
                                        if (userRoles.indexOf(role.name) > -1) {
                                            role.permissions.forEach(function (value) { return addPermission(value, result); });
                                        }
                                    });
                                }
                            }, function (error) { return setErrors([error]); })];
                    case 1:
                        _a.sent();
                        return [2 /*return*/, result];
                }
            });
        }); };
        var addPermission = function (value, list) {
            var permission = value.substring(0, value.indexOf('('));
            if (list.indexOf(permission) === -1) {
                list.push(permission);
            }
        };
        getPermissionsFromRoles().then(function (permissions) {
            var userPermissions = Array.isArray(PincConfiguration.pincUserPermissions()) ? PincConfiguration.pincUserPermissions() : [];
            userPermissions.forEach(function (value) { return addPermission(value, permissions); });
            setUserPermissions(permissions);
        });
    }, []);
    var isPermitted = function (permission) { return userPermissions.indexOf(permission) > -1; };
    return (React.createElement(UserPermissionsContext.Provider, { value: { isPermitted: isPermitted }, children: props.children }));
};
