package com.prinova.messagepoint.platform.services.elasticsearch.action.sentiment;

import com.google.common.collect.ImmutableMap;
import com.google.gson.JsonObject;
import org.junit.Test;

import java.util.Collections;

import static ai.mpr.marcie.content.rationalizer.misc.ApplicationUtils.readJsonObject;
import static com.prinova.messagepoint.platform.services.elasticsearch.search.query.RationalizerMetadataFilterUtil.prefixDocumentMetadataFiltersForRationalizer;
import static org.junit.Assert.assertEquals;

public class ComputeSentimentTest {

    @Test
    public void testBuildQuery() {
        ComputeSentiment systemUnderTest = new ComputeSentiment();
        ComputeSentiment.Request req = ComputeSentiment.Request.builder()
                .appId("12345")
                .documentMetadataFilters(Collections.singletonList(prefixDocumentMetadataFiltersForRationalizer(ImmutableMap.<String, String>builder()
                        .put("1_Filename", "Adjustment to Superior Court of California")
                        .put("24_Excel_Department", "Public Health Services Department")
                        .build()))
                )
                .build();
        JsonObject query = systemUnderTest.buildQuery(req);
        assertEquals(query, readJsonObject("{\n" +
                "   \"size\":0,\n" +
                "   \"query\":{\n" +
                "      \"bool\":{\n" +
                "         \"should\":[\n" +
                "            {\n" +
                "               \"bool\":{\n" +
                "                  \"must\":[\n" +
                "                     {\n" +
                "                        \"term\":{\n" +
                "                           \"document.metadata.1_Filename.keyword\":\"Adjustment to Superior Court of California\"\n" +
                "                        }\n" +
                "                     },\n" +
                "                     {\n" +
                "                        \"term\":{\n" +
                "                           \"document.metadata.24_Excel_Department.keyword\":\"Public Health Services Department\"\n" +
                "                        }\n" +
                "                     }\n" +
                "                  ]\n" +
                "               }\n" +
                "            }\n" +
                "         ]\n" +
                "      }\n" +
                "   },\n" +
                "   \"aggs\":{\n" +
                "      \"sentiment_buckets\":{\n" +
                "         \"composite\":{\n" +
                "            \"sources\":[\n" +
                "               {\n" +
                "                  \"marcie_sentiment\":{\n" +
                "                     \"terms\":{\n" +
                "                        \"field\":\"marcie.sentiment.label\"\n" +
                "                     }\n" +
                "                  }\n" +
                "               }\n" +
                "            ],\n" +
                "            \"size\":10000\n" +
                "         }\n" +
                "      }\n" +
                "   }\n" +
                "}"));
    }
}
