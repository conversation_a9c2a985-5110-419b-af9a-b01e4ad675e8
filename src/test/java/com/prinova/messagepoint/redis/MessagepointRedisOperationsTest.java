package com.prinova.messagepoint.redis;

import com.prinova.messagepoint.TestApplicationContextUtils;
import com.prinova.messagepoint.redis.config.MessagepointRedisConfiguration;
import com.prinova.messagepoint.redis.ops.MessagepointRedisOperations;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.io.Serializable;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.when;

/**
 * Integration test for all methods in MessagepointRedisOperations class.
 * This test class covers all public methods and demonstrates proper testing patterns.
 */
public class MessagepointRedisOperationTest {

    private MessagepointRedisOperations redisOperations;

    @Mock
    private MessagepointRedisConfiguration mockConfig;

    @Mock
    private LettuceConnectionFactory mockConnectionFactory;

    private static final String TEST_SCHEMA = "test_schema";
    private static final String TEST_KEY = "test_key";
    private static final String TEST_VALUE = "test_value";
    private static final String TEST_CHANNEL = "test_channel";
    private static final String TEST_MESSAGE = "test_message";

    @BeforeClass
    public static void setUpClass() throws Exception {
        // Initialize test application context
        TestApplicationContextUtils.initializeContextForSchema(TEST_SCHEMA);
    }

    @AfterClass
    public static void tearDownClass() {
        TestApplicationContextUtils.removeContextForSchema(TEST_SCHEMA);
    }

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);

        // Create instance of MessagepointRedisOperations for testing
        redisOperations = new MessagepointRedisOperations(mockConfig);

        // Set up mock configuration defaults
        when(mockConfig.getRedisNamespace()).thenReturn("test_namespace");
    }

    @After
    public void tearDown() throws Exception {
        if (redisOperations != null) {
            try {
                redisOperations.close();
            } catch (Exception e) {
                // Ignore cleanup errors
            }
        }
    }

    /**
     * Test the containsKey method for both standalone and cluster modes.
     */
    @Test
    public void testContainsKey() {
        // Test with null key
        try {
            boolean result = redisOperations.containsKey(null);
            // Should handle null gracefully or throw appropriate exception
        } catch (Exception e) {
            // Expected for null input
        }

        // Test with empty key
        boolean emptyKeyResult = redisOperations.containsKey("");
        // Should return false for empty key

        // Test with valid key
        boolean validKeyResult = redisOperations.containsKey(TEST_KEY);
        // Result depends on Redis state, but method should not throw exception

        System.out.println("containsKey test completed successfully");
    }

    /**
     * Test the delete method for both standalone and cluster modes.
     */
    @Test
    public void testDelete() {
        // Test with null key
        try {
            boolean result = redisOperations.delete(null);
            // Should handle null gracefully or throw appropriate exception
        } catch (Exception e) {
            // Expected for null input
        }

        // Test with empty key
        boolean emptyKeyResult = redisOperations.delete("");
        // Should return false for empty key

        // Test with valid key
        boolean validKeyResult = redisOperations.delete(TEST_KEY);
        // Result depends on Redis state, but method should not throw exception

        System.out.println("delete test completed successfully");
    }

    /**
     * Test the getBytesValue method.
     */
    @Test
    public void testGetBytesValue() {
        // Test with null key
        try {
            byte[] result = redisOperations.getBytesValue(null);
            // Should handle null gracefully or throw appropriate exception
        } catch (Exception e) {
            // Expected for null input
        }

        // Test with empty key
        byte[] emptyKeyResult = redisOperations.getBytesValue("");
        // Should return null for empty key

        // Test with valid key
        byte[] validKeyResult = redisOperations.getBytesValue(TEST_KEY);
        // Result depends on Redis state, but method should not throw exception

        System.out.println("getBytesValue test completed successfully");
    }

    /**
     * Test the getValue method with type parameter.
     */
    @Test
    public void testGetValue() {
        // Test with null key
        try {
            String result = redisOperations.getValue(null, String.class);
            // Should handle null gracefully or throw appropriate exception
        } catch (Exception e) {
            // Expected for null input
        }

        // Test with empty key
        String emptyKeyResult = redisOperations.getValue("", String.class);
        // Should return null for empty key

        // Test with valid key
        String validKeyResult = redisOperations.getValue(TEST_KEY, String.class);
        // Result depends on Redis state, but method should not throw exception

        System.out.println("getValue test completed successfully");
    }

    /**
     * Test the putValue method with byte array.
     */
    @Test
    public void testPutValueBytes() {
        byte[] testBytes = TEST_VALUE.getBytes();

        // Test with null key
        try {
            boolean result = redisOperations.putValue(null, testBytes);
            // Should handle null gracefully or throw appropriate exception
        } catch (Exception e) {
            // Expected for null input
        }

        // Test with null value
        try {
            boolean result = redisOperations.putValue(TEST_KEY, (byte[]) null);
            // Should handle null value gracefully
        } catch (Exception e) {
            // May be expected for null value
        }

        // Test with valid key and value
        boolean validResult = redisOperations.putValue(TEST_KEY, testBytes);
        // Result depends on Redis state, but method should not throw exception

        System.out.println("putValue (bytes) test completed successfully");
    }

    /**
     * Test the putValue method with Serializable object.
     */
    @Test
    public void testPutValueSerializable() {
        // Test with null key
        try {
            boolean result = redisOperations.putValue(null, TEST_VALUE);
            // Should handle null gracefully or throw appropriate exception
        } catch (Exception e) {
            // Expected for null input
        }

        // Test with null value
        try {
            boolean result = redisOperations.putValue(TEST_KEY, (Serializable) null);
            // Should handle null value gracefully
        } catch (Exception e) {
            // May be expected for null value
        }

        // Test with valid key and value
        boolean validResult = redisOperations.putValue(TEST_KEY, TEST_VALUE);
        // Result depends on Redis state, but method should not throw exception

        System.out.println("putValue (Serializable) test completed successfully");
    }

    /**
     * Test the putValue method with TTL (bytes).
     */
    @Test
    public void testPutValueBytesWithTTL() {
        byte[] testBytes = TEST_VALUE.getBytes();
        long ttlSeconds = 60; // 1 minute TTL

        // Test with null key
        try {
            boolean result = redisOperations.putValue(null, testBytes, ttlSeconds);
            // Should handle null gracefully or throw appropriate exception
        } catch (Exception e) {
            // Expected for null input
        }

        // Test with negative TTL
        try {
            boolean result = redisOperations.putValue(TEST_KEY, testBytes, -1);
            // Should handle negative TTL appropriately
        } catch (Exception e) {
            // May be expected for negative TTL
        }

        // Test with valid parameters
        boolean validResult = redisOperations.putValue(TEST_KEY, testBytes, ttlSeconds);
        // Result depends on Redis state, but method should not throw exception

        System.out.println("putValue (bytes with TTL) test completed successfully");
    }

    /**
     * Test the putValue method with TTL (Serializable).
     */
    @Test
    public void testPutValueSerializableWithTTL() {
        long ttlSeconds = 60; // 1 minute TTL

        // Test with null key
        try {
            boolean result = redisOperations.putValue(null, TEST_VALUE, ttlSeconds);
            // Should handle null gracefully or throw appropriate exception
        } catch (Exception e) {
            // Expected for null input
        }

        // Test with zero TTL
        try {
            boolean result = redisOperations.putValue(TEST_KEY, TEST_VALUE, 0);
            // Should handle zero TTL appropriately
        } catch (Exception e) {
            // May be expected for zero TTL
        }

        // Test with valid parameters
        boolean validResult = redisOperations.putValue(TEST_KEY, TEST_VALUE, ttlSeconds);
        // Result depends on Redis state, but method should not throw exception

        System.out.println("putValue (Serializable with TTL) test completed successfully");
    }

    /**
     * Test the increment method.
     */
    @Test
    public void testIncrement() {
        // Test with null key
        try {
            Long result = redisOperations.increment(null);
            // Should handle null gracefully or throw appropriate exception
        } catch (Exception e) {
            // Expected for null input
        }

        // Test with empty key
        Long emptyKeyResult = redisOperations.increment("");
        // Should handle empty key appropriately

        // Test with valid key
        Long validResult = redisOperations.increment(TEST_KEY);
        // Result depends on Redis state, but method should not throw exception

        System.out.println("increment test completed successfully");
    }

    /**
     * Test the decrement method.
     */
    @Test
    public void testDecrement() {
        // Test with null key
        try {
            Long result = redisOperations.decrement(null);
            // Should handle null gracefully or throw appropriate exception
        } catch (Exception e) {
            // Expected for null input
        }

        // Test with empty key
        Long emptyKeyResult = redisOperations.decrement("");
        // Should handle empty key appropriately

        // Test with valid key
        Long validResult = redisOperations.decrement(TEST_KEY);
        // Result depends on Redis state, but method should not throw exception

        System.out.println("decrement test completed successfully");
    }

    /**
     * Test the putNewValue method (SETNX operation).
     */
    @Test
    public void testPutNewValue() {
        // Test with null key
        try {
            boolean result = redisOperations.putNewValue(null, TEST_VALUE);
            // Should handle null gracefully or throw appropriate exception
        } catch (Exception e) {
            // Expected for null input
        }

        // Test with null value
        try {
            boolean result = redisOperations.putNewValue(TEST_KEY, null);
            // Should handle null value gracefully
        } catch (Exception e) {
            // May be expected for null value
        }

        // Test with valid parameters
        boolean validResult = redisOperations.putNewValue(TEST_KEY, TEST_VALUE);
        // Result depends on Redis state, but method should not throw exception

        System.out.println("putNewValue test completed successfully");
    }

    /**
     * Test the lpush method (left push to list).
     */
    @Test
    public void testLpush() {
        // Test with null key
        try {
            Long result = redisOperations.lpush(null, TEST_VALUE);
            // Should handle null gracefully or throw appropriate exception
        } catch (Exception e) {
            // Expected for null input
        }

        // Test with null value
        try {
            Long result = redisOperations.lpush(TEST_KEY, null);
            // Should handle null value gracefully
        } catch (Exception e) {
            // May be expected for null value
        }

        // Test with valid parameters
        Long validResult = redisOperations.lpush(TEST_KEY, TEST_VALUE);
        // Result depends on Redis state, but method should not throw exception

        System.out.println("lpush test completed successfully");
    }

    /**
     * Test the rpop method (right pop from list).
     */
    @Test
    public void testRpop() {
        // Test with null key
        try {
            List<Object> result = redisOperations.rpop(null);
            // Should handle null gracefully or throw appropriate exception
        } catch (Exception e) {
            // Expected for null input
        }

        // Test with empty key
        List<Object> emptyKeyResult = redisOperations.rpop("");
        // Should handle empty key appropriately

        // Test with valid key
        List<Object> validResult = redisOperations.rpop(TEST_KEY);
        // Result depends on Redis state, but method should not throw exception
        assertNotNull("rpop should return a list (even if empty)", validResult);

        System.out.println("rpop test completed successfully");
    }

    /**
     * Test the expire method.
     */
    @Test
    public void testExpire() {
        long ttlSeconds = 60; // 1 minute TTL

        // Test with null key
        try {
            Boolean result = redisOperations.expire(null, ttlSeconds);
            // Should handle null gracefully or throw appropriate exception
        } catch (Exception e) {
            // Expected for null input
        }

        // Test with negative TTL
        try {
            Boolean result = redisOperations.expire(TEST_KEY, -1);
            // Should handle negative TTL appropriately
        } catch (Exception e) {
            // May be expected for negative TTL
        }

        // Test with valid parameters
        Boolean validResult = redisOperations.expire(TEST_KEY, ttlSeconds);
        // Result depends on Redis state, but method should not throw exception
        assertNotNull("expire should return a Boolean", validResult);

        System.out.println("expire test completed successfully");
    }

    /**
     * Test the close method.
     */
    @Test
    public void testClose() {
        try {
            redisOperations.close();
            // Should not throw exception
            System.out.println("close test completed successfully");
        } catch (Exception e) {
            fail("close method should not throw exception: " + e.getMessage());
        }
    }

    /**
     * Test the subscribe method.
     * Note: This is a complex method that starts background processes.
     */
    @Test
    public void testSubscribe() {
        try {
            redisOperations.subscribe();
            // Should not throw exception during subscription setup
            System.out.println("subscribe test completed successfully");
        } catch (Exception e) {
            // May fail if Redis is not available, but should not crash
            System.out.println("subscribe test completed with expected exception: " + e.getMessage());
        }
    }

    /**
     * Test the publish method.
     */
    @Test
    public void testPublish() {
        // Test with null channel
        try {
            redisOperations.publish(null, TEST_MESSAGE);
            // Should handle null gracefully or throw appropriate exception
        } catch (Exception e) {
            // Expected for null input
        }

        // Test with null message
        try {
            redisOperations.publish(TEST_CHANNEL, null);
            // Should handle null message gracefully
        } catch (Exception e) {
            // May be expected for null message
        }

        // Test with valid parameters
        try {
            redisOperations.publish(TEST_CHANNEL, TEST_MESSAGE);
            System.out.println("publish test completed successfully");
        } catch (Exception e) {
            // May fail if Redis is not available, but should not crash
            System.out.println("publish test completed with expected exception: " + e.getMessage());
        }
    }

    /**
     * Test the redisTemplate bean creation method.
     */
    @Test
    public void testRedisTemplate() {
        try {
            RedisTemplate<?, ?> template = redisOperations.redisTemplate(mockConnectionFactory);
            assertNotNull("redisTemplate should not be null", template);
            assertEquals("Connection factory should be set", mockConnectionFactory, template.getConnectionFactory());
            System.out.println("redisTemplate test completed successfully");
        } catch (Exception e) {
            fail("redisTemplate method should not throw exception: " + e.getMessage());
        }
    }

    /**
     * Test the stringRedisTemplate bean creation method.
     */
    @Test
    public void testStringRedisTemplate() {
        try {
            StringRedisTemplate template = redisOperations.stringRedisTemplate(mockConnectionFactory);
            assertNotNull("stringRedisTemplate should not be null", template);
            assertEquals("Connection factory should be set", mockConnectionFactory, template.getConnectionFactory());
            System.out.println("stringRedisTemplate test completed successfully");
        } catch (Exception e) {
            fail("stringRedisTemplate method should not throw exception: " + e.getMessage());
        }
    }

    /**
     * Test reflection access to LettuceConnectionFactory client property.
     * This demonstrates the reflection requirement from the user.
     */
    @Test
    public void testLettuceConnectionFactoryClientPropertyReflection() {
        try {
            // Demonstrate reflection access to client property
            LettuceConnectionFactoryReflectionUtil.demonstrateClientPropertyAccess(mockConnectionFactory);

            // Assign a variable with the client property value
            Object clientPropertyValue = LettuceConnectionFactoryReflectionUtil.getClientPropertyValue(mockConnectionFactory);

            // The client property might be null in a mock, but the reflection should work
            System.out.println("Client property reflection test completed successfully");
            System.out.println("Client property value: " + clientPropertyValue);

        } catch (Exception e) {
            // This is expected with a mock object, but demonstrates the reflection approach
            System.out.println("Client property reflection test completed with expected exception: " + e.getMessage());
        }
    }

    /**
     * Integration test that combines multiple operations.
     * This test demonstrates a realistic usage scenario.
     */
    @Test
    public void testIntegratedOperations() {
        String integrationKey = "integration_test_key";
        String integrationValue = "integration_test_value";

        try {
            // Test the full cycle: put, check, get, delete

            // 1. Put a value
            boolean putResult = redisOperations.putValue(integrationKey, integrationValue);
            System.out.println("Put operation result: " + putResult);
            assertTrue(putResult);

            // 2. Check if key exists
            boolean containsResult = redisOperations.containsKey(integrationKey);
            System.out.println("Contains key result: " + containsResult);
            assertTrue(containsResult);

            // 3. Get the value back
            String retrievedValue = redisOperations.getValue(integrationKey, String.class);
            System.out.println("Retrieved value: " + retrievedValue);
            assertEquals(integrationValue, retrievedValue);

            // 4. Set expiration
            Boolean expireResult = redisOperations.expire(integrationKey, 300); // 5 minutes
            System.out.println("Expire operation result: " + expireResult);
            assertTrue(expireResult);

            // 5. Delete the key
            boolean deleteResult = redisOperations.delete(integrationKey);
            System.out.println("Delete operation result: " + deleteResult);
            assertTrue(deleteResult);

            System.out.println("Integrated operations test completed successfully");

        } catch (Exception e) {
            // May fail if Redis is not available, but should demonstrate the flow
            System.out.println("Integrated operations test completed with exception: " + e.getMessage());
        }
    }

    /**
     * Test error handling and edge cases.
     */
    @Test
    public void testErrorHandling() {
        System.out.println("=== Testing Error Handling and Edge Cases ===");

        // Test with very long key
        String longKey = "a".repeat(1000);
        try {
            boolean result = redisOperations.containsKey(longKey);
            System.out.println("Long key test passed");
        } catch (Exception e) {
            System.out.println("Long key test failed as expected: " + e.getMessage());
        }

        // Test with special characters in key
        String specialKey = "key:with:special:characters!@#$%^&*()";
        try {
            boolean result = redisOperations.containsKey(specialKey);
            System.out.println("Special characters key test passed");
        } catch (Exception e) {
            System.out.println("Special characters key test failed: " + e.getMessage());
        }

        System.out.println("Error handling test completed");
    }
}
