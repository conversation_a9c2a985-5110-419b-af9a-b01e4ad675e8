<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
	<msgpt:HeaderNew extendedScripts="true">

		<msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css" />
		<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css"/>

		<msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js"/>
		<msgpt:Script src="_ux/js/mp.dragAndDropList.js"/>

		<style>
			.headerLabel {
				font-size: 12px;
				text-shadow: 0px 1px 0px #fff;  
				font-weight: bold;
				color: #333;
			}
		</style>

        <msgpt:Script>
            <script>
                function variableMappingResolveAction() {
                    var variableIdList= "";
                    var documentParm = "${ fn:length(command.touchpointAssignments) != 0 ? command.touchpointAssignments[0] : -1}";
                    var dsIds = "";
                    if ( $('#variableMapLinkBtnVariableIds').length != 0 ){
                        variableIdList = $('#variableMapLinkBtnVariableIds').val();
                        documentParm = $('#variableMapLinkBtnVariableIds').attr('documentid');
                        dsIds = $('#variableMapLinkBtnVariableIds').attr('dsids');
                    }
                    mappingPageTargetURL = context +
                        "/dataadmin/bridge_variables.form?tk=" + getParam('tk') +
                        "&variableIds=" + variableIdList +
                        "&documentId=" + documentParm +
                        "&dataSourceIds=" + dsIds +
                        "&returnPage=" + escape(context+"/content/content_object_touchpoint_assignment.form?selectedIds=" + getParam('selectedIds'));
                    window.location.href = mappingPageTargetURL;
                }

                $( function() {

                    if ( $('#variableMapLinkBtnContainer').length != 0 )
                        $('#variableMapLinkBtnContainer').append("<div style=\"padding-top: 8px;\"><input title=\"" + client_messages.button.resolve.variables + "\" type=\"button\" id=\"editVariableMappingBtn\" onclick=\"javascript:variableMappingResolveAction();\" style=\"display: none;\" /></div>");

                    $("input:button").styleActionElement();

					$('#touchpointAssociations').dragAndDropList({});

                    if ( getParam('nprSaveSuccess') == "true" )
                        getTopFrame().location.reload();

                });
            </script>
        </msgpt:Script>
	</msgpt:HeaderNew>

	<msgpt:BodyNew theme="minimal" type="iframe">
		<form:form method="post" modelAttribute="command">
		
			<div class="contentTableIframe backgroundTile_10p">
				<div class="contentPanel" style="padding: 10px 5px; min-height: 280px;">
		
					<form:errors path="*">
						<msgpt:Information errorMsgs="${messages}" type="error" />
					</form:errors>
					
					<c:if test="${empty param.nprSaveSuccess}">
							<msgpt:DataTable>
								<c:if test="${fn:length(availableTouchpoints) > 0}">
									<msgpt:TableItem>
										<div id="touchpointAssociations"
											 class="drag-and-drop row no-gutters align-items-stretch px-3 py-1">
											<div class="col-5">
												<h5><fmtSpring:message code="page.label.available.touchpoints"/></h5>
												<div class="droppable-section">
													<div class="form-control p-0 bg-lightest rounded-bottom-0 h-auto">
														<div class="position-relative z-index-1">
															<input class="form-control form-control-lg bg-lightest px-5 border-0 shadow-none"
																   type="text"
																   data-toggle="tagcloud" data-cloud-type="4"
																   aria-label="${msgpt:getMessage('page.label.search.for')}"
																   placeholder="${msgpt:getMessage('page.label.search')}"
																   disabled>
															<button class="btn btn-lg btn-icon-toggle bg-transparent shadow-none py-0 px-4 mr-2 position-absolute h-100 top-0 left-0"
																	type="button"
																	aria-label="${msgpt:getMessage('page.label.search')}"
																	tabindex="-1" disabled>
																<i class="far fa-search"
																   aria-hidden="true"></i>
																<i class="far fa-times text-primary"
																   aria-hidden="true"></i>
															</button>
														</div>
														<div class="pl-3 mb-1 text-muted">
															<div class="d-inline-block custom-control custom-checkbox w-auto no-text mr-3">
																<input type="checkbox" class="custom-control-input selectAll" id="selectAll" style="min-height: auto;">
																<label class="custom-control-label pl-2" for="selectAll">Select All</label>
															</div>
															<div class="d-inline-block custom-control custom-checkbox w-auto no-text mr-3">
																<input type="checkbox" class="custom-control-input viewSelected" id="viewSelected" style="min-height: auto;">
																<label class="custom-control-label pl-2" for="viewSelected">View Selected</label>
															</div>
														</div>
													</div>
													<div class="position-relative flex-grow-1 border border-top-0 border-light rounded-lg rounded-top-0 overflow-hidden">
														<div class="draggable-list-wrapper">
															<div class="draggable-list"></div>
															<div class="draggable-loading">
																<div class="progress-loader progress-loader-lg">
																	<i class="progress-loader-icon far fa-spinner-third"
																	   aria-hidden="true"></i>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
											<div class="col align-self-center">
												<div class="text-center text-muted">
													<div class="d-inline-block square-lg bg-secondary-lightest rounded-circle p-2 text-secondary fs-md">
														<i class="far fa-hand-paper drag-and-drop-indicator"
														   aria-hidden="true"></i>
													</div>
													<div class="mt-2 mb-n1 fs-xs">
														<fmtSpring:message
																code="client_messages.text.drag_and_drop"/>
													</div>
													<i class="fas fa-arrows-h" aria-hidden="true"></i>
												</div>
												<div class="drag-and-drop-datasource">
													<c:forEach var="touchpoint"
															   items="${availableTouchpoints}">
														<form:checkbox
																id="touchpointCheck${touchpoint.id}"
																path="touchpointAssignments"
																value="${touchpoint.id}"
																aria-label="${touchpoint.name}"
																data-filtervalue="${touchpoint.name}"
																data-filtermetatags="${touchpoint.metatags}"
																data-level="${touchpoint.projectDepth}"/>
													</c:forEach>
													<c:forEach var="collection"
															   items="${availableCollections}">
														<form:checkbox
																id="collectionCheck${collection.id}"
																path="selectedTouchpointCollectionIds"
																value="${collection.id}"
																aria-label="${collection.name}"
																data-filtervalue="${collection.name}"/>
													</c:forEach>
												</div>
											</div>
											<div class="col-5">
												<h5><fmtSpring:message code="page.label.selected.touchpoints"/></h5>
												<div class="droppable-section">
													<div class="form-control p-0 bg-lightest rounded-bottom-0 h-auto">
														<div class="position-relative z-index-1">
															<input class="form-control form-control-lg bg-lightest px-5 border-0 shadow-none"
																   type="text"
																   data-toggle="tagcloud" data-cloud-type="4"
																   aria-label="${msgpt:getMessage('page.label.search.for')}"
																   placeholder="${msgpt:getMessage('page.label.search')}"
																   disabled>
															<button class="btn btn-lg btn-icon-toggle bg-transparent shadow-none py-0 px-4 mr-2 position-absolute h-100 top-0 left-0"
																	type="button"
																	aria-label="${msgpt:getMessage('page.label.search')}"
																	tabindex="-1" disabled>
																<i class="far fa-search"
																   aria-hidden="true"></i>
																<i class="far fa-times text-primary"
																   aria-hidden="true"></i>
															</button>
														</div>
														<div class="pl-3 mb-1 text-muted">
															<div class="d-inline-block custom-control custom-checkbox w-auto no-text mr-3">
																<input type="checkbox" class="custom-control-input selectAll" id="selectAll2" style="min-height: auto;">
																<label class="custom-control-label pl-2" for="selectAll2">Select All</label>
															</div>
															<div class="d-inline-block custom-control custom-checkbox w-auto no-text mr-3">
																<input type="checkbox" class="custom-control-input viewSelected" id="viewSelected2" style="min-height: auto;">
																<label class="custom-control-label pl-2" for="viewSelected2">View Selected</label>
															</div>
														</div>
													</div>
													<div class="position-relative flex-grow-1 border border-top-0 border-light rounded-lg rounded-top-0 overflow-hidden">
														<div class="draggable-list-wrapper">
															<div class="draggable-list"></div>
															<div class="draggable-loading">
																<div class="progress-loader progress-loader-lg">
																	<i class="progress-loader-icon far fa-spinner-third"
																	   aria-hidden="true"></i>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</msgpt:TableItem>
								</c:if>
								<!-- Info: No Touchpoints -->
								<c:if test="${fn:length(availableTouchpoints) == 0}">
									<msgpt:TableItem>
										<div class="fullLineLabel" style="padding: 2px 8px;">
											<fmtSpring:message code="page.label.AVAILABLE.TOUCHPOINTS"/>
										</div>
										<div class="infoDiv_minimal">
											<fmtSpring:message code="page.label.no.available.touchpoints"/>
										</div>
									</msgpt:TableItem>		
								</c:if>
							</msgpt:DataTable>

					</c:if>
					
				</div>
			</div>
			
			<table width="100%" cellspacing="0" cellpadding="0" border="0" style="margin: 8px 0;"><tr>
				<td width="50%" align="right">
					<div class="mr-1"><input title="${msgpt:getMessage('page.label.CANCEL')}" type="button" id="cancelBtn" /></div>
				</td>
				<td width="50%" align="left">
					<div class="ml-1"><input title="${msgpt:getMessage('page.label.SAVE')}" type="button" id="saveBtn" onclick="javascript:doSubmit()" /></div>
				</td>
			</tr></table>

		</form:form>
	</msgpt:BodyNew>
</msgpt:Html5>