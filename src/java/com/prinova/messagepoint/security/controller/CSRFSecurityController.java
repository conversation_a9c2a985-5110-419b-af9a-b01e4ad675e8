package com.prinova.messagepoint.security.controller;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.util.Objects;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.SystemPropertyKeys.ApplicationServer;
import com.prinova.messagepoint.model.SystemPropertyKeys.WebAppSecurity;
import com.prinova.messagepoint.util.ApplicationUtil;

public class CSRFSecurityController implements Controller {

	public static final Pattern TOKEN_NAME_IDENTIFIER_PATTERN				= Pattern.compile("%TOKEN_NAME%");
	public static final Pattern TOKEN_VALUE_IDENTIFIER_PATTERN				= Pattern.compile("%TOKEN_VALUE%");
	public static final Pattern DOMAIN_ORIGIN_IDENTIFIER_PATTERN 			= Pattern.compile("%DOMAIN_ORIGIN%");
	public static final Pattern DOMAIN_STRICT_IDENTIFIER_PATTERN 			= Pattern.compile("%DOMAIN_STRICT%");
	public static final Pattern INJECT_INTO_XHR_IDENTIFIER_PATTERN 			= Pattern.compile("%INJECT_XHR%");
	public static final Pattern INJECT_INTO_FORMS_IDENTIFIER_PATTERN 		= Pattern.compile("%INJECT_FORMS%");
	public static final Pattern INJECT_INTO_ATTRIBUTES_IDENTIFIER_PATTERN 	= Pattern.compile("%INJECT_ATTRIBUTES%");
	public static final Pattern CONTEXT_PATH_IDENTIFIER_PATTERN				= Pattern.compile("%CONTEXT_PATH%");
	public static final Pattern SERVLET_PATH_IDENTIFIER_PATTERN				= Pattern.compile("%SERVLET_PATH%");
	public static final Pattern TOKENS_PER_PAGE_IDENTIFIER_PATTERN 			= Pattern.compile("%TOKENS_PER_PAGE%");
	public static final Pattern DOMAIN_STRICT_OVERRIDE_PATTERN 				= Pattern.compile("%DOMAIN_STRICT_OVERRIDE%");
	
	private Boolean injectIntoForms 		= false;
	private Boolean injectIntoAttributes 	= true;
	private Boolean domainStrict 			= false;
	private Pattern refererPattern 			= Pattern.compile(".*");
	private Boolean isAjaxEnabled 			= true;
	private Boolean isTokenPerPageEnabled 	= false;
	private Boolean isRotateEnabled 		= false;

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		response.setContentType("text/javascript");

        String ifNoneMatch = request.getHeader("If-None-Match");
        String token = request.getParameter(WebAppSecurity.CSRF_TOKEN_KEY);

        if (ifNoneMatch != null && token != null && Objects.equals(ifNoneMatch, token)) {
            response.setStatus(HttpServletResponse.SC_NOT_MODIFIED);
			response.setHeader("Cache-Control", "private, max-age=60");
			response.setHeader("ETag", token);
            return null;
        }
		
		String refererHeader = request.getHeader("referer");
		if(refererHeader == null || refererPattern.matcher(refererHeader).matches()) {
            response.setHeader("Cache-Control", "private, max-age=60");
			response.setHeader("ETag", token);
		    writeJavaScript(request, response);
		} else {
			response.sendError(404);
		}

		return null;
	}

	private void writeJavaScript(HttpServletRequest request, HttpServletResponse response) throws IOException {
		response.setContentType("text/javascript");

		// build dynamic javascript //
		String code = readFileContent(ApplicationUtil.getRootPath() + "/includes/javascript/security/Owasp.CsrfGuard.js");

		code = TOKEN_NAME_IDENTIFIER_PATTERN.matcher(code).replaceAll(WebAppSecurity.CSRF_TOKEN_KEY); // TO DO
		code = TOKEN_VALUE_IDENTIFIER_PATTERN.matcher(code).replaceAll(request.getParameter(WebAppSecurity.CSRF_TOKEN_KEY) == null? "" : request.getParameter(WebAppSecurity.CSRF_TOKEN_KEY)); // TO DO
		code = INJECT_INTO_FORMS_IDENTIFIER_PATTERN.matcher(code).replaceAll(injectIntoForms.toString());
		code = INJECT_INTO_ATTRIBUTES_IDENTIFIER_PATTERN.matcher(code).replaceAll(injectIntoAttributes.toString());
		code = INJECT_INTO_XHR_IDENTIFIER_PATTERN.matcher(code).replaceAll(isAjaxEnabled.toString());
		code = TOKENS_PER_PAGE_IDENTIFIER_PATTERN.matcher(code).replaceAll(isTokenPerPageEnabled.toString());
		code = DOMAIN_ORIGIN_IDENTIFIER_PATTERN.matcher(code).replaceAll(parseDomain(request.getRequestURL()));
		code = DOMAIN_STRICT_IDENTIFIER_PATTERN.matcher(code).replaceAll(domainStrict.toString());
		code = CONTEXT_PATH_IDENTIFIER_PATTERN.matcher(code).replaceAll(request.getContextPath());
		code = SERVLET_PATH_IDENTIFIER_PATTERN.matcher(code).replaceAll(request.getContextPath() + request.getServletPath());
		code = DOMAIN_STRICT_OVERRIDE_PATTERN.matcher(code).replaceAll(
				SystemPropertyManager.getInstance().getPodMasterBooleanSystemProperty(
						ApplicationServer.KEY_ApplicationServerCSRFDomainStrict, Boolean.TRUE) ? "true" : "false");

		// write dynamic javascript //
		OutputStream output = null;
		PrintWriter writer = null;

		try {
			output = response.getOutputStream();
			writer = new PrintWriter(output);

			writer.write(code);
			writer.flush();
		} finally {
			writer.close();
		}

	}

	public static String readFileContent(String fileName) {
		StringBuilder sb = new StringBuilder();
		InputStream is = null;

		try {
			is = new FileInputStream(fileName);
			int i = 0;

			while ((i = is.read()) > 0) {
				sb.append((char) i);
			}

			is.close();
		} catch (IOException ioe) {
			throw new RuntimeException(ioe);
		}

		return sb.toString();
	}

	private String parseDomain(StringBuffer url) {
		String token = "://";
		int index = url.indexOf(token);
		String part = url.substring(index + token.length());
		StringBuilder domain = new StringBuilder();

		for (int i = 0; i < part.length(); i++) {
			char character = part.charAt(i);

			if (character == '/' || character == ':') {
				break;
			}

			domain.append(character);
		}

		return domain.toString();
	}	
	
	

}