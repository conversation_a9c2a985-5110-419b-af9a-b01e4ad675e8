package com.prinova.messagepoint.controller.tpadmin;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.model.admin.Channel;
import com.prinova.messagepoint.model.admin.Connector;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class DocumentChannelEditValidator extends MessagepointInputValidator {

	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		DocumentChannelEditWrapper command = (DocumentChannelEditWrapper) commandObj;

		long connectorId = command.getConnectorId();
		if ( connectorId == 0 )
			errors.reject("error.document.connector.selection.required");

		// Validate the composition version input for composition engine
		if ( command.getChannelId() == Channel.CHANNEL_COMPOSITION && connectorId != Connector.NATIVE_COMPOSITION_FILE_ID ) {
			if ( command.getCompositionVersion() == null || command.getCompositionVersion().isEmpty()) {
				errors.reject("error.document.composition.version.required");
			} else {
				Matcher matcher = Pattern.compile("^\\w+(\\.\\w+)*$").matcher(command.getCompositionVersion());
				if ( !matcher.find() ) {
					errors.reject("error.document.composition.version.invalid");
				}
			}
		}
	}
}