package com.prinova.messagepoint.connected.rest;

import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.platform.auth.PINCOpenIdConnectUtil;
import org.apache.commons.lang.StringUtils;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

public class RestRequestContext extends HashMap<String, Object> {
    public static final String BASE_URL = "baseUrl";
    public static final String API_PATH = "apiPath";
    private static final String apiPathV1 = "/api/v1/";

    public static final String AUTH_METHOD = "AUTHORIZATION";
    public static final int AUTH_METHOD_BEARER = 1;
    public static final int AUTH_METHOD_PINC = 2;

    public static final String TOKEN = "token";
    public static final String COMPANY_ID  = "companyId";
    public static final String USER_ID = "userId";
    public static final  String MP_INSTANCE_ID  = "mpInstanceId";
    public static final  String BUNDLE_ID  = "bundleId";
    public static final  String ORDER_ID  = "orderId";
    public static final  String ORDER  = "order";

    public static final String UMH_POD_ID  = "podId";
    public static final String UMH_BRANCH_NAME  = "branchName";
    public static final String UMH_NODE_NAME  = "nodeName";

    public static final String COMMUNICATION_ID  = "commId";
    public static final String COMMUNICATION_GUID  = "commGUID";
    public static final String COMMUNICATION  = "communication";
    public static final String DETAILS  = "details";

    public static final String DEFAULT_TEST_ENVIRONMENT  = "defaultTestEnvironment";
    public static final String DEFAULT_PRODUCTION_ENVIRONMENT  = "defaultProductionEnvironment";

    // mpInstanceId = client.getMpInstanceId("tt242anx_iv89_p01", "master");

    public RestRequestContext(int authMethod, String token) {
        super();
        this.put(API_PATH, apiPathV1);
        this.put(AUTH_METHOD, authMethod);
        this.put(TOKEN, token);

        initDefaultContext();
    }

    public RestRequestContext(Map<String, Object> initialContext) {
        super(initialContext != null ? initialContext : new HashMap<>());
        initDefaultContext();
        if (initialContext != null)
            this.putAll(initialContext);
    }

    private void initDefaultContext(){
        if (!this.containsKey(API_PATH)) {
            this.put(API_PATH, apiPathV1);
        }
        if (!this.containsKey(AUTH_METHOD)) {
            this.put(AUTH_METHOD, AUTH_METHOD_PINC);
        }
        if (!this.containsKey(BASE_URL)) {
            this.put(BASE_URL, initBaseUrl());
        }
    }

    public void addParameter(String key, Object value) {
        this.put(key, value);
    }

    public Object getParameter(String key) {
        switch (key) {
            case COMPANY_ID:
                return getCompanyId();
            default:
                break;
        }
        return this.get(key);
    }

    public void removeParameter(String key) {
        this.remove(key);
    }


    private String initBaseUrl() {
        String pincServer = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.PINC.KEY_PINCServer);
        pincServer = !StringUtils.isEmpty(pincServer) && pincServer.startsWith("https://") ? pincServer : "";

        try {
            if (pincServer != null && !pincServer.isEmpty()) {
                URL pincServerUrl = new URL(pincServer);
                pincServer = pincServerUrl.getProtocol() + "://" + pincServerUrl.getHost();
            }
        } catch (MalformedURLException ex) {
            pincServer = "unknown";
        }

        return pincServer;
    }

    public String getCompanyId() {
        if (this.containsKey(COMPANY_ID) ) {
            return (String)this.get(COMPANY_ID);
        } else {
            try {
                    this.put(COMPANY_ID, getPINCCompanyId());
                    return (String)this.get(COMPANY_ID);
            } catch (Exception ex) {
                //TODO
            }
        }

        return null;
    }

    public String getBaseUrl() {
        return (String)this.getParameter(RestRequestContext.BASE_URL);
    }
    public String getApiPath() {
        return (String)this.getParameter(RestRequestContext.API_PATH);
    }
    private String formatUUID(String guid) {
        return guid.toLowerCase().replaceFirst("([a-f0-9]{8})([a-f0-9]{4})([a-f0-9]{4})([a-f0-9]{4})([a-f0-9]{12})", "$1-$2-$3-$4-$5");
    }
    private String getPINCCompanyId() {
        return formatUUID(this.getGuidSafely());
    }
    private String getGuidSafely() {
        try {
            return PINCOpenIdConnectUtil.getPincContextId();
        } catch (Exception e) {
            return PINCOpenIdConnectUtil.getPincCompanyId();
        }
    }
}
