package com.prinova.messagepoint.platform.services.imports;

import java.util.*;
import java.util.stream.Collectors;

import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.workgroup.Workgroup;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.platform.services.admin.ActivateDeactivateUserProxy;
import com.prinova.messagepoint.platform.services.admin.BulkUserCreateOrUpdateService;
import com.prinova.messagepoint.platform.services.admin.UserProxy;
import com.prinova.messagepoint.platform.services.imports.document.*;
import com.prinova.messagepoint.platform.services.utils.SessionHelper;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.logging.Log;
import org.springframework.orm.hibernate5.SessionHolder;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.prinova.messagepoint.MessagepointException;
import com.prinova.messagepoint.controller.admin.RoleUtil;
import com.prinova.messagepoint.model.security.Role;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.task.TPImportTask;
import com.prinova.messagepoint.platform.services.backgroundtask.ImportMessagepointObjectBackgroundTask;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;


// Service for importing a list of embedded contents given an exported XML file.
public class BulkUploadUsersUpdateService extends AbstractService
{
    public static String SERVICE_NAME = "imports.BulkUploadUsersUpdateService";
    public static final String ERROR_ERRORKEY_DELIMITER	= "||";
    public static final String ERROR_VALUE_DELIMITER	= "##";
    
	public static final int ACTION_NONE = 0;
	public static final int ACTION_UPDATE = 1;
	public static final int ACTION_ACTIVATE = 2;
	public static final int ACTION_DEACTIVATE = 3;
	public static final int ACTION_RESET_PASSWORD = 4;
	public static final int ACTION_SOFT_DEACTIVATE = 5;
	public static final int ACTION_CREATE = 6;
	public static final int ACTION_CREATE_ACTIVATE = 7;
	
	public static final String MESSAGE_CODE_ACTION_NONE = "page.label.none";
	public static final String MESSAGE_CODE_ACTION_UPDATE = "page.label.update";
	public static final String MESSAGE_CODE_ACTION_ACTIVATE = "page.label.activate";
	public static final String MESSAGE_CODE_ACTION_DEACTIVATE = "page.label.deactivate";
	public static final String MESSAGE_CODE_ACTION_RESET_PASSWORD = "page.label.reset.password";
	public static final String MESSAGE_CODE_ACTION_SOFT_DEACTIVATE = "page.label.soft.deactivate";
	public static final String MESSAGE_CODE_ACTION_RESEND_ACTIVATION = "page.label.resend.activation";
	public static final String MESSAGE_CODE_ACTION_CREATE = "page.label.create";
	public static final String MESSAGE_CODE_ACTION_CREATE_ACTIVATE = "page.label.create.activate";

    
	private ImportBulkUploadHandler handler;
	private ExportImportHandler root;
	
	private static final Log log = LogUtil.getLog(ImportMessagepointObjectBackgroundTask.class);

	// This service should not use a transaction, but any services under it will need one.
    @Transactional(propagation = Propagation.REQUIRED, readOnly = false)
    public void execute(ServiceExecutionContext context)
    {
		SessionHelper.clearSessions();

		BulkUploadUsersUpdateServiceRequest request = (BulkUploadUsersUpdateServiceRequest)(context.getRequest());
		TPImportTask tpImportTask = request.getTPImportTask();
		try
        {
			info("Starting import users processing");
			Branch branch = getBranch(request);
			User requester = getRequester(context, branch);
			List<BulkUploadUserUpdateImportHandler> usersToProcess = getUsersToProcess(request);
			info("Found " + usersToProcess.size() + " users to process");

			validate(branch, requester, usersToProcess);
    		if (hasValidationError(context)) {
    			info("Validation errors found, stop import process");
				return;
    		}

			if(tpImportTask.getBulkImportType() == TPImportTask.BULK_IMPORT_TYPE_USERS_UPDATE
				&& tpImportTask.getUsers() > 0){
				SessionHelper.poolSession(null);
				SessionHelper.poolSession(branch.getDcsSchemaName());

    			processUsersInBulk(context, branch, usersToProcess, requester);
    		}
		}
        catch( Exception ex )
        {
        	if (ex.getCause() != null)
            {
            	if (ex.getCause().getMessage() != null)
            	{
            		// message can be error key or regular message or system error
            		String errorKey = ex.getCause().getMessage();
        			String[] values = { "" };
        			String errorMsgWithPassedValue = "";
        			if(!ex.getLocalizedMessage().isEmpty()){
        				values = ex.getLocalizedMessage().split(ERROR_VALUE_DELIMITER);
        				try{
        		        	errorMsgWithPassedValue = ApplicationUtil.getMessage(errorKey, values);
        		        }catch( Exception e )
        		        {
        		        	errorMsgWithPassedValue = errorKey;
        		        }
        			}
        			this.getResponse(context).addErrorMessage(
            				"",
            				errorKey,
            				values,
                    		context.getServiceName(), context.getLocale());
                    this.getResponse(context).setReturnCode(SimpleServiceResponse.ERROR);
                    tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_ERROR);
                    info(errorMsgWithPassedValue.isEmpty() ?ApplicationUtil.getMessage(errorKey):errorMsgWithPassedValue);
                    
                    return;
            	}
            }
        	
            LogUtil.getLog(this.getClass()).error(" unexpected exception when invoking BulkUploadUsersUpdateService execute method", ex);
            this.getResponse(context).addErrorMessage(
                    "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
                    SERVICE_NAME + ex,
                    context.getLocale() );
            tpImportTask.setImportStatus(TPImportTask.IMPORT_STATUS_ERROR);
            error(" unexpected exception when invoking BulkUploadUsersUpdateService execute method:", ex);
            throw new RuntimeException(ex.getMessage());
        }
		finally {
			SessionHelper.clearSessions();
			if(tpImportTask.getImportReport() != null){
				tpImportTask.setImportReportFilePath(tpImportTask.getImportReport().getReportFileName());
			}
		}
    }

	@Override
	public void validate(ServiceExecutionContext context) {

	}

	private Branch getBranch(BulkUploadUsersUpdateServiceRequest request){
		TPImportTask tpImportTask = request.getTPImportTask();
		Branch branch = Branch.findByGuid(tpImportTask.getTpOrientation());
		if (branch == null) {
			throw new MessagepointException(tpImportTask.getTpOrientation(), new Throwable("#GUID#"));
		}

		return branch;
	}

	private List<BulkUploadUserUpdateImportHandler> getUsersToProcess(BulkUploadUsersUpdateServiceRequest request){
		ImportBulkUploadHandler handler = request.getHandler();
		root = (ExportImportHandler)handler.getRoot();
        List<BulkUploadUserUpdateImportHandler> users = new ArrayList<>(root.getBulkUploadUserUpdateListImportHandler().getFilteredUsers());
		Collections.sort(users, new BulkUploadUsersUpdateImportHandlerComparator());

		return users;
	}

	private User getRequester(ServiceExecutionContext context, Branch branch){
		User requester = null;
		SessionHolder sessionHolder = null;
		try{
			BulkUploadUsersUpdateServiceRequest request = (BulkUploadUsersUpdateServiceRequest)(context.getRequest());
			sessionHolder = HibernateUtil.getManager().openTemporarySession(branch.getDcsSchemaName());
			requester = User.findById(request.getUserId());
		}
		finally {
			HibernateUtil.getManager().restoreSession(sessionHolder);
		}

		return requester;
	}

	private void processUsersInBulk(ServiceExecutionContext context, Branch branch, List<BulkUploadUserUpdateImportHandler> userHandlers, User requester) {
		BulkUploadUsersUpdateServiceRequest request = (BulkUploadUsersUpdateServiceRequest)(context.getRequest());
		StatusPollingBackgroundTask statusPollingBackgroundTask = request.getStatusPollingBackgroundTask();
		Node requestedNode = request.getRequestedNode();

		Map<BulkUploadUserUpdateImportHandler, UserProxy> proxies = new HashMap<>();
		Map<UserProxy, User> updatedUsersProxyMap = null;
		Map<UserProxy, BulkUploadUserUpdateImportHandler> proxiesToHandlerMap = new HashMap<>();

		debug("Getting user proxies for further processing");
		SessionHolder sessionHolder = null;
		int totalProcessed = 0;
		Node dcsNode = branch.getDcsNode();

		try{
			sessionHolder = HibernateUtil.getManager().openTemporarySession(branch.getDcsSchemaName());

			userHandlers.forEach(uh -> {
				UserProxy proxy = uh.getUserProxy(branch, dcsNode, requester);
				if(proxy != null){
					proxies.put(uh, proxy);
				}
			});
		}
		finally {
			HibernateUtil.getManager().getSession().flush();
			HibernateUtil.getManager().restoreSession(sessionHolder);
		}

		int totalNumberUsers = userHandlers.size(),
			prev=0, interval = Math.round(totalNumberUsers/10);


		debug("Process each user proxy based on action");

		for(BulkUploadUserUpdateImportHandler uh: proxies.keySet()){
			if(uh.getActionInt() == ACTION_CREATE || uh.getActionInt() == ACTION_UPDATE || uh.getActionInt() == ACTION_CREATE_ACTIVATE){
				debug("Add for bulk processing user " + uh.getUserName() + ", action " + uh.getAction());
				proxiesToHandlerMap.put(proxies.get(uh), uh);
				continue;
			}

			totalProcessed++;
			if(totalProcessed-prev==interval){
				progressPercentage(10, statusPollingBackgroundTask);
				prev=totalProcessed;
			}

			UserProxy proxy = proxies.get(uh);
			boolean processed = uh.processUser(branch, dcsNode, proxy, requester);
			if(processed){
				debug("Successfully processed user " + uh.getUserName() + ", action " + uh.getAction());
			}
			else{
				info("Failed to process user " + uh.getUserName() + ", action " + uh.getAction());
			}
		}

		try{
			sessionHolder = HibernateUtil.getManager().openTemporarySession(branch.getDcsSchemaName());
			if(!proxiesToHandlerMap.isEmpty()){
				debug("Start bulk processing of " + proxiesToHandlerMap.size() + " users");
				ServiceExecutionContext ctx = BulkUserCreateOrUpdateService.createContext(proxiesToHandlerMap.keySet(), requester.getId(), true, false, dcsNode);
				Service updateService = MessagepointServiceFactory.getInstance().lookupService(BulkUserCreateOrUpdateService.SERVICE_NAME, BulkUserCreateOrUpdateService.class);
				updateService.execute(ctx);

				ServiceResponse serviceResponse1 = ctx.getResponse();
				if(!serviceResponse1.isSuccessful()){
					if(serviceResponse1.hasMessages()){
						getResponse(context).mergeResultMessages(serviceResponse1);
					}
					debug("Failed process user details in bulk");
				}
				else{
					totalProcessed += proxiesToHandlerMap.size();
					updatedUsersProxyMap = (Map<UserProxy, User>) serviceResponse1.getResultValueBean();
				}
			}
		}
		finally {
			HibernateUtil.getManager().getSession().flush();
			HibernateUtil.getManager().restoreSession(sessionHolder);
		}


		if(updatedUsersProxyMap != null){
			Map<BulkUploadUserUpdateImportHandler, User> updatedUserToHandlerMap = new HashMap<>();

			for(UserProxy proxy: proxiesToHandlerMap.keySet()){
				User user = updatedUsersProxyMap.get(proxy);
				BulkUploadUserUpdateImportHandler userHandler = proxiesToHandlerMap.get(proxy);
				updatedUserToHandlerMap.put(userHandler, user);

				if(user != null && userHandler != null){
					boolean processed = userHandler.processDomain(user, branch, userHandler.getDomain(branch.getName()), false, requester);
					if(processed){
						info("Domains processed successfully for " + userHandler.getUserName());
					}

					if(userHandler.getActionInt() == ACTION_CREATE_ACTIVATE){
						if(!user.isAccountActive()){
							String resultMsg = "";
							resultMsg = UserUtil.canActivateThisUser(user, branch, requestedNode, requester);
							if(!resultMsg.equals("true")){
								log.info("User:" + user.getUsername()+ " - " + resultMsg);
								continue;
							}
						}

						userHandler.setId(user.getId());
						userHandler.setAction(ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_ACTIVATE));
						UserProxy activateProxy = new ActivateDeactivateUserProxy(user, true, BulkUploadUserUpdateImportHandler.ACTIVATE_MODE);
						if(userHandler.processUser(branch, dcsNode, activateProxy, requester)){
							debug("Successfully activated after create user " + userHandler.getUserName() + " GUID=" + user.getGuid());
						}
						else{
							info("Failed to activate after create user " + userHandler.getUserName()+ " GUID=" + user.getGuid());
						}
					}
				}
			}
		}


		progressPercentage(100, statusPollingBackgroundTask);
		info("Total " + totalProcessed + " user(s) processed");
		BulkUploadUsersServiceResponse response = (BulkUploadUsersServiceResponse) context.getResponse();
		response.setModelId(branch.getId());
		response.setTotalProcessedCount(totalProcessed);
		if(updatedUsersProxyMap != null && !updatedUsersProxyMap.values().isEmpty()){
			response.setResultValueBean(updatedUsersProxyMap.values());
		}
	}

	private void progressPercentage(int percentage, StatusPollingBackgroundTask statusPollingBackgroundTask){
		if(statusPollingBackgroundTask != null){
			int finalPercentage = statusPollingBackgroundTask.getProgressInPercentInThread() + percentage;
			statusPollingBackgroundTask.setProgressInPercentInThread(Math.min(finalPercentage, 100));
			statusPollingBackgroundTask.save();
		}
	}

	public void validate(Branch branch, User requester, List<BulkUploadUserUpdateImportHandler> users)
	{
		info("Validating users attributes");
		validateUsers(users, branch);

		info("Validating users roles and workgroups");
        validateRolesAndWorkgroups(branch, users, requester);
	}

	private void validateUsers(List<BulkUploadUserUpdateImportHandler> users, Branch branch){
		SessionHolder sessionHolder = null;
		try{
			sessionHolder = HibernateUtil.getManager().openTemporarySession(branch.getDcsSchemaName());

			for ( BulkUploadUserUpdateImportHandler uh : users ) {
				if ((uh.getGuid() == null || uh.getGuid().isEmpty()) && uh.getActionInt() != ACTION_CREATE && uh.getActionInt() != ACTION_CREATE_ACTIVATE) {
					throw new MessagepointException("GUID", new Throwable("service.import.required.value.missing.with.missing.name"));
				}
				if (uh.getId() == 0) {
					throw new MessagepointException("ID", new Throwable("service.import.required.value.missing.with.missing.name"));
				}
				if (uh.getFirstName() == null || uh.getFirstName().isEmpty()) {
					throw new MessagepointException("FirstName", new Throwable("service.import.required.value.missing.with.missing.name"));
				}
				if (uh.getLastName() == null || uh.getLastName().isEmpty()) {
					throw new MessagepointException("LastName", new Throwable("service.import.required.value.missing.with.missing.name"));
				}
				if (uh.getEmail() == null || uh.getEmail().isEmpty()) {
					throw new MessagepointException("Email", new Throwable("service.import.required.value.missing.with.missing.name"));
				}
				if (uh.getUserName() == null || uh.getUserName().isEmpty()) {
					throw new MessagepointException("UserName", new Throwable("service.import.required.value.missing.with.missing.name"));
				}
				if (uh.getActionInt() != ACTION_CREATE && uh.getActionInt() != ACTION_CREATE_ACTIVATE) {
					User userDB = User.findById(uh.getId());
					User userByGUID = User.findByGuid(uh.getGuid());
					if (userDB == null || userByGUID == null) {
						throw new MessagepointException("ID", new Throwable("service.import.required.value.missing.with.missing.name"));
					}
				} else {
					// validate username, firstname, lastname
				}

				if (uh.getIDProvider() == User.IDP_TYPE_SSO_DCS_USER && (!branch.isPingSSOEnabled() || branch.getSsoIdPId().isEmpty() || branch.getSsoIdPId() == null))
					throw new MessagepointException(uh.getIDProvider() == User.IDP_TYPE_MP_DCS_USER ? ApplicationUtil.getMessage("page.label.local.domain") : ApplicationUtil.getMessage("page.label.local.sso"), new Throwable("error.idp.provider.type.is.not.valid"));
			}
		}
		finally {
			HibernateUtil.getManager().restoreSession(sessionHolder);
		}
	}

	private void validateRolesAndWorkgroups(Branch branch, List<BulkUploadUserUpdateImportHandler> users, User requester){
		Map<String, List<Long>> instanceRoleIDMap = new HashMap<>(); // instanceName, roleId
		Map<String, List<Long>> instanceWorkgroupIdMap = new HashMap<>(); // instanceName, workgroupId

		Map<Integer, Integer> requiredLicenseMap = new HashMap<>();
		requiredLicenseMap.put(1, 0);
		requiredLicenseMap.put(2, 0);
		requiredLicenseMap.put(3, 0);

		Node dcsNode = null;
		for(Node node : branch.getAllAccessibleNodes(true, requester.canAccessTestingInstances())){ // with DCS
			SessionHolder sessionHolder = null;
			try{
				sessionHolder = HibernateUtil.getManager().openTemporarySession(node.getSchemaName());

				List<Long> roleIdList = RoleUtil.findAll().stream().map(Role::getId).collect(Collectors.toList());
				List<Long> workgroupIdList = Workgroup.findAll().stream().map(Workgroup::getId).collect(Collectors.toList());
				roleIdList.add(0L);
				instanceRoleIDMap.put(node.getName(), roleIdList);
				instanceWorkgroupIdMap.put(node.getName(), workgroupIdList);
				if(node.isDcsNode()){
					dcsNode = node;
				}
			}
			finally {
				HibernateUtil.getManager().restoreSession(sessionHolder);
			}
		}

		for ( BulkUploadUserUpdateImportHandler uh : users ){
			int finalLicenseType = 0;
			// DCS instance must exist in the roles
			if(uh.getDcsRoles(branch, dcsNode) == null)
				throw new MessagepointException(branch.getName(), new Throwable("error.message.domain.controller.instance.role.missing"));

			for(BulkUploadUserUpdateInstancesImportHandler instanceHandler: uh.getDomainInstances(branch)){
				Set<BulkUploadUserUpdateInstanceRolesImportHandler> roleHandlers = instanceHandler.getRoleImportHandlers();

				if(roleHandlers == null || roleHandlers.isEmpty())
					continue;
				if(!instanceRoleIDMap.containsKey(instanceHandler.getInstance())){
					throw new MessagepointException(instanceHandler.getInstance(), new Throwable("error.message.given.instance.not.existing"));
				}

				if(!new HashSet<>(instanceRoleIDMap.get(instanceHandler.getInstance())).containsAll(roleHandlers.stream().map(BulkUploadUserUpdateInstanceRolesImportHandler::getId).collect(Collectors.toSet())))
					throw new MessagepointException(uh.getUserName(), new Throwable("error.message.role.is.not.valid.for.user"));

				Set<Role> roles = instanceHandler.getRoles(branch, instanceHandler.getInstance());
				int nodeLicenseType = Role.getLicenceTypeByRoles(roles);
				if( nodeLicenseType > finalLicenseType)
					finalLicenseType = nodeLicenseType;
			}
			if(finalLicenseType > 0){
				requiredLicenseMap.put(finalLicenseType, requiredLicenseMap.get(finalLicenseType) + 1);
			}

			// Validate workgroup id
			for(BulkUploadUserUpdateInstancesImportHandler instanceHandler: uh.getDomainInstances(branch)){
				if(instanceHandler.getWorkgroup() != null && instanceHandler.getWorkgroup().getId() > 0) {
					if (!instanceWorkgroupIdMap.containsKey(instanceHandler.getInstance())) {
						throw new MessagepointException(instanceHandler.getInstance(), new Throwable("error.message.given.instance.not.existing"));
					}
					if (!instanceWorkgroupIdMap.get(instanceHandler.getInstance()).contains(instanceHandler.getWorkgroup().getId())) {
						throw new MessagepointException(uh.getUserName(), new Throwable("error.message.workgroup.is.not.valid.on.the.instance"));
					}
				}
			}
		}

	}

	public static BulkUploadUserUpdateInstancesImportHandler getRole(String branchName, String instanceName, BulkUploadUserUpdateImportHandler uh) {
		BulkUploadUserUpdateDomainImportHandler domain = uh.getDomain(branchName);
		if(domain == null)
			return null;
		for(BulkUploadUserUpdateInstancesImportHandler role: domain.getInstances())
			if(role.getInstance().equals(instanceName))
				return role;
		return null;
	}
	
	public int getFinalUserTypeForDomain(BulkUploadUserUpdateImportHandler uh, Branch branch) {
		int finalLicense = 0;
		for(BulkUploadUserUpdateInstancesImportHandler role: uh.getDomainInstances(branch)){
			int nodeLicenseType = Role.getLicenceTypeByRoles(role.getRoles());
			if( nodeLicenseType > finalLicense)
				finalLicense = nodeLicenseType;
		}
		return finalLicense;
	}
	
    public static ServiceExecutionContext createContext(
    		ImportBulkUploadHandler handler,
            long importUserId,
            TPImportTask tpImportTask,
            StatusPollingBackgroundTask statusPollingBackgroundTask)
    {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        BulkUploadUsersUpdateServiceRequest request = new BulkUploadUsersUpdateServiceRequest(handler, importUserId, tpImportTask, statusPollingBackgroundTask);

        context.setRequest(request);

        BulkUploadUsersServiceResponse resp = new BulkUploadUsersServiceResponse();
		resp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(resp);

        return context;
    }

	public static ServiceExecutionContext createContext(
			ImportBulkUploadHandler handler,
			long importUserId,
			TPImportTask tpImportTask,
			Node requestedNode,
			StatusPollingBackgroundTask statusPollingBackgroundTask) {
		ServiceExecutionContext context = createContext(handler, importUserId, tpImportTask, statusPollingBackgroundTask);
		BulkUploadUsersUpdateServiceRequest request = (BulkUploadUsersUpdateServiceRequest) context.getRequest();

		if (requestedNode != null){
			request.setRequestedNode(requestedNode);
		}

		return context;

	}
    
    public static class BulkUploadUsersUpdateImportHandlerComparator implements Comparator<BulkUploadUserUpdateImportHandler> {

        public int compare(BulkUploadUserUpdateImportHandler arg0, BulkUploadUserUpdateImportHandler arg1) 
        {
            return arg0.compareUserNameTo(arg1);
        }
    }

	private static void info(String message){
		log.info(message);
	}

	private static void debug(String message){
		log.debug(message);
	}

	private static void error(String message, Exception ex){
		log.error(message, ex);
	}
}
