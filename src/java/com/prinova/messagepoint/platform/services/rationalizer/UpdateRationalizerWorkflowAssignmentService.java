package com.prinova.messagepoint.platform.services.rationalizer;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;

public class UpdateRationalizerWorkflowAssignmentService extends AbstractService {

	public static final String SERVICE_NAME = "rationalizer.UpdateRationalizerWorkflowAssignmentService";
	
	private static final Log log = LogUtil.getLog(UpdateRationalizerWorkflowAssignmentService.class);

	public void execute(ServiceExecutionContext context) {
		
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			UpdateRationalizerWorkflowAssignmentServiceRequest request = (UpdateRationalizerWorkflowAssignmentServiceRequest) context.getRequest();

			RationalizerApplication rationalizerApp = request.getRationalizerApplication();
			rationalizerApp.save();

		} catch (Exception e) {
			log.error(" unexpected exception when invoking UpdateRationalizerWorkflowAssignmentService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION", ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e, context.getLocale());
			throw new RuntimeException(e);
		}
	}

	// if master and if there are Empty content associations, reject
	public void validate(ServiceExecutionContext context) {

	}

	public static ServiceExecutionContext createContext(RationalizerApplication rationalizerApplication) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		UpdateRationalizerWorkflowAssignmentServiceRequest request = new UpdateRationalizerWorkflowAssignmentServiceRequest();
		context.setRequest(request);

		request.setRationalizerApplication(rationalizerApplication);

		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}
}