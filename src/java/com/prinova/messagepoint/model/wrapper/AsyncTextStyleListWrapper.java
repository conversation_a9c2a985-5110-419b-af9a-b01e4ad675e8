package com.prinova.messagepoint.model.wrapper;

import com.prinova.messagepoint.model.font.TextStyle;
import com.prinova.messagepoint.model.manager.HibernateObjectManager;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.query.handler.PostQueryHandler;
import com.prinova.messagepoint.query.service.HibernatePaginationService;
import com.prinova.messagepoint.query.service.PaginationServiceResponse;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import javax.persistence.criteria.JoinType;

import java.util.*;

public class AsyncTextStyleListWrapper extends AsyncAbstractListWrapper {

    private List<TextStyle> instanceList;

    public AsyncTextStyleListWrapper(String sSearch, Map<String, String> orderByMap, int pageSize, int pageIndex) {
        this.buildItemsList(sSearch, orderByMap, pageSize, pageIndex);
        this.instanceList = new ArrayList<>();
    }

    /**
     * Build the items list based on the input parameters
     **/
    private void buildItemsList(String sSearch, Map<String, String> orderByMap, int pageSize, int pageIndex) {
        // Alias init
        Map<String, String> firstLevelJoinAlias 		= new LinkedHashMap<>();
        Map<String, String> secondLevelJoinAlias 		= new LinkedHashMap<>();

        // MessagepointCriterion init
        List<MessagepointCriterion> firstLevelCriterionList 	= new ArrayList<>();
        List<MessagepointCriterion> secondLevelCriterionList 	= new ArrayList<>();

        // Join Type init
        Map<String, JoinType> secondLevelJoinType	= new HashMap<>();

        // MessagepointOrder list init
        List<MessagepointOrder> orderList 						= new ArrayList<>();

        // Sort
        this.addTableColumnsSort(orderByMap, orderList);

        PostQueryHandler postHandler = null;

        ServiceExecutionContext context = HibernatePaginationService.createContext(TextStyle.class, null, firstLevelCriterionList, secondLevelCriterionList, firstLevelJoinAlias, secondLevelJoinAlias, secondLevelJoinType, pageIndex, pageSize, orderList, null, postHandler);
        Service paginationService = MessagepointServiceFactory.getInstance().lookupService(HibernatePaginationService.SERVICE_NAME, HibernatePaginationService.class);
        paginationService.execute(context);
        PaginationServiceResponse serviceResponse = (PaginationServiceResponse) context.getResponse();
        List<?> list = serviceResponse.getPage().getList();

        for (Object o : list) {
            if(o instanceof TextStyle) {
                TextStyle instance = (TextStyle) o;
                this.instanceList.add(instance);
            }
        }

        super.setiTotalRecords(serviceResponse.getPage().getRowCount());
        super.setiTotalDisplayRecords(serviceResponse.getPage().getRowCount());
    }

    /**
     * Add the sort criterion from the list table plug-in to the order list
     **/
    private void addTableColumnsSort(Map<String, String> orderedByMap, List<MessagepointOrder> orderList){
        Set<String> keySet = orderedByMap.keySet();
        for(String key : keySet){
            String value = orderedByMap.get(key);
            String sortField = "";

            if(key.equals("name")){	// Sort by name
                sortField = HibernateObjectManager.MODEL_ALIAS_PREFIX + ".name";
            }

            if(!sortField.isEmpty()){
                // Add to order list
                if(value.equals("asc")){
                    orderList.add(MessagepointOrder.asc(sortField));
                }else{
                    orderList.add(MessagepointOrder.desc(sortField));
                }
            }
        }
    }

    @Override
    public void init() {
        List<AsyncDataFileListVO> aaData = new ArrayList<>();
        boolean dataUpdatePermission = UserUtil.isPermissionGranted(Permission.ID_ROLE_TOUCHPOINT_DATA_EDIT);

        for (TextStyle textStyle : this.instanceList) {
            AsyncDataFileListVO vo = new AsyncDataFileListVO();
            vo.setDisplayMode(getDisplayMode());

            // For drill-down
            vo.setDT_RowId(textStyle.getId());

            // Name
            vo.setName(textStyle.getName());

            aaData.add(vo);
        }
        super.setAaData(aaData);
    }

}
