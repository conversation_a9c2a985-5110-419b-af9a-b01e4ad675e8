package com.prinova.messagepoint.controller.admin;

import java.util.List;

import com.prinova.messagepoint.model.admin.ConditionOperator;
import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.targeting.ConditionItemValue;
import com.prinova.messagepoint.model.targeting.ConditionSubelement;
import com.prinova.messagepoint.model.targeting.FilterCondition;
import com.prinova.messagepoint.util.ConditionElementUtil;

public class ConditionElementSubElementVO {
	
    private String 				name;
	private DataElementVariable dataElementVariable;
	private long 				dataElementComparisonId 		= 0;
	private boolean 			parameterized;
	private ConditionOperator 	conditionOperator;
	private String 				dataElementValueString 			= "";
	private String				dataFilePath					= null;
	private Long				dataFileSandboxFileId;
	private long 				id;
	private FilterCondition 	filterCondition;
	private String				filterDataElementValueString 	= "";
	private Long				filterDataFileSandboxFileId;
	private boolean				toDelete						= false;
	
	public ConditionElementSubElementVO() {
		super();
	}

	public ConditionElementSubElementVO( ConditionSubelement subElement ) {
		this.id 						= subElement.getId();
		this.name 						= subElement.getName();
		this.dataElementVariable 		= subElement.getDataElementVariable();
		this.dataElementComparisonId 	= subElement.getDataElementComparisonId();
		this.parameterized 				= subElement.isParameterized();
		this.conditionOperator 			= subElement.getConditionOperator();
		this.dataElementValueString 	= ConditionElementUtil.getDataElementValueString( subElement.getDataElementComparisonId(), subElement.getDataElementVariable().getDataSubtypeId(), subElement.getConditionAttributes(), subElement.getDataFilePath());
		this.dataFilePath				= subElement.getDataFilePath();
		
		FilterCondition filterCondition 	= subElement.getFilterCondition() != null ? subElement.getFilterCondition() : new FilterCondition();
		this.filterCondition 				= filterCondition;
		this.filterDataElementValueString	= subElement.getFilterCondition() != null ? 
													ConditionElementUtil.getDataElementValueString( filterCondition.getDataElementComparisonId(), filterCondition.getDataElementVariable().getDataSubtypeId(), filterCondition.getConditionValueMap(), filterCondition.getDataFilePath()) :
													"";
		this.toDelete						= false;
	}

	public List<ConditionItemValue> getReferencingConditionItemValues() {
		List<ConditionItemValue> civList = ConditionItemValue.findByConditionSubelement(id);
		return civList;
	}
	public boolean isReferenced() {
		// The User cannot Delete this subelement if it is referenced by any of the conditionItemValue (hence used in target group).
		return (!getReferencingConditionItemValues().isEmpty());
	}

	public String getDataElementValueString() {
		return dataElementValueString;
	}
	public void setDataElementValueString(String dataElementValueString) {
		this.dataElementValueString = dataElementValueString;
	}

	public String getDataFilePath() {
		return dataFilePath;
	}
	public void setDataFilePath(String dataFilePath) {
		this.dataFilePath = dataFilePath;
	}

	public Long getDataFileSandboxFileId() {
		return dataFileSandboxFileId;
	}
	public void setDataFileSandboxFileId(Long dataFileSandboxFileId) {
		this.dataFileSandboxFileId = dataFileSandboxFileId;
	}

	public ConditionOperator getConditionOperator() {
		return conditionOperator;
	}
	public void setConditionOperator(ConditionOperator conditionOperator) {
		this.conditionOperator = conditionOperator;
	}

	public long getDataElementComparisonId() {
		return dataElementComparisonId;
	}
	public void setDataElementComparisonId(long dataElementComparisonId) {
		this.dataElementComparisonId = dataElementComparisonId;
	}

	public DataElementVariable getDataElementVariable() {
		return dataElementVariable;
	}
	public void setDataElementVariable(DataElementVariable dataElementVariable) {
		this.dataElementVariable = dataElementVariable;
	}

	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	public boolean isParameterized() {
		return parameterized;
	}
	public void setParameterized(boolean parameterized) {
		this.parameterized = parameterized;
	}

	public boolean isToDelete() {
		return toDelete;
	}

	public void setToDelete(boolean toDelete) {
		this.toDelete = toDelete;
	}

	@Override
	public int hashCode() {
		final int PRIME = 31;
		int result = 1;
		result = PRIME * result + ((conditionOperator == null) ? 0 : conditionOperator.hashCode());
		result = PRIME * result + (int) (dataElementComparisonId ^ (dataElementComparisonId >>> 32));
		result = PRIME * result + ((dataElementVariable == null) ? 0 : dataElementVariable.hashCode());
		result = PRIME * result + ((dataElementValueString == null) ? 0 : dataElementValueString.hashCode());
		result = PRIME * result + (int) (id ^ (id >>> 32));
		result = PRIME * result + ((name == null) ? 0 : name.hashCode());
		result = PRIME * result + (parameterized ? 1231 : 1237);
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (!(obj instanceof ConditionElementSubElementVO))
			return false;
		final ConditionElementSubElementVO other = (ConditionElementSubElementVO) obj;
		if (conditionOperator == null) {
			if (other.getConditionOperator() != null)
				return false;
		} else if (!conditionOperator.equals(other.getConditionOperator()))
			return false;
		if (dataElementComparisonId != other.getDataElementComparisonId())
			return false;
		if (dataElementVariable == null) {
			if (other.getDataElementVariable() != null)
				return false;
		} else if (!dataElementVariable.equals(other.getDataElementVariable()))
			return false;
		if (dataElementValueString == null) {
			if (other.getDataElementValueString() != null)
				return false;
		} else if (!dataElementValueString.equals(other.getDataElementValueString()))
			return false;
		if (id != other.getId())
			return false;
		if (name == null) {
			if (other.getName() != null)
				return false;
		} else if (!name.equals(other.getName()))
			return false;
		if (parameterized != other.isParameterized())
			return false;
		return true;
	}

	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}

	public FilterCondition getFilterCondition() {
		return filterCondition;
	}
	public void setFilterCondition(FilterCondition filterCondition) {
		this.filterCondition = filterCondition;
	}

	public String getFilterDataElementValueString() {
		return filterDataElementValueString;
	}
	public void setFilterDataElementValueString(String filterDataElementValueString) {
		this.filterDataElementValueString = filterDataElementValueString;
	}

	public Long getFilterDataFileSandboxFileId() {
		return filterDataFileSandboxFileId;
	}
	public void setFilterDataFileSandboxFileId(Long filterDataFileSandboxFileId) {
		this.filterDataFileSandboxFileId = filterDataFileSandboxFileId;
	}

}