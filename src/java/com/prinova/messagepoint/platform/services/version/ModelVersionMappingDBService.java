package com.prinova.messagepoint.platform.services.version;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import com.prinova.messagepoint.model.version.ModelVersionMapping;
import com.prinova.messagepoint.model.version.VersionInfo;
import com.prinova.messagepoint.model.version.VersionedInstance;
import com.prinova.messagepoint.model.version.VersionedModel;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;

/**
 * This service provide the basic database actions (insert, update, delete) of a MessageVersionMapping object. See
 * MessageVersionDBServiceRequest for interface requirement.
 * 
 * ReturnValueBean : the ModelVersionMapping
 * 
 * <AUTHOR>
 * 
 */
public class ModelVersionMappingDBService extends AbstractService {

	public static final String SERVICE_NAME = "version.ModelVersionMappingDBService";
	private static final Log log = LogUtil.getLog(ModelVersionMappingDBService.class);
	public static final String DB_ACTION_INSERT = "insert";
	public static final String DB_ACTION_UPDATE = "update";
	public static final String DB_ACTION_DELETE = "delete";

	public void execute(ServiceExecutionContext context) {
		String dbAction = null;
		ModelVersionMapping resultBean = null;
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			ModelVersionMappingDBServiceRequest request = (ModelVersionMappingDBServiceRequest) context.getRequest();
			dbAction = request.getDbAction();
			if (dbAction.equals(ModelVersionMappingDBService.DB_ACTION_INSERT)) {
				resultBean = insertMessageVersionMapping(request);
			}
			if (dbAction.equals(ModelVersionMappingDBService.DB_ACTION_UPDATE)) {
				ModelVersionMapping aVersionMapping = request.getMappingToAlter();
				aVersionMapping.update();
				resultBean = aVersionMapping;
			}
			if (dbAction.equals(ModelVersionMappingDBService.DB_ACTION_DELETE)) {
				ModelVersionMapping aVersionMapping = request.getMappingToAlter();
				aVersionMapping.delete();
			}
			context.getResponse().setResultValueBean(resultBean);

		} catch (Exception e) {
			log.error(" unexpected exception when invoking ModelVersionDBService execute method", e);
			log.error(" ModelVersionDBService Database Action is " + dbAction);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}

	}

	private ModelVersionMapping insertMessageVersionMapping(ModelVersionMappingDBServiceRequest request) {
		VersionInfo vInfo = request.getVersionInfo();
		VersionedModel model = request.getModel();
		VersionedInstance instance = request.getModelInstance();
		
		ModelVersionMapping vmap = model.newVersionMapping();
		vmap.setModel(model);
		vmap.setModelInstance(instance);
		vmap.setVersionInfo(vInfo);

		vmap.insert();
		return vmap;
	}

	public void validate(ServiceExecutionContext context) {

	}

	public static ServiceExecutionContext createContextForInsert(VersionedModel model, VersionedInstance instance,
			VersionInfo versionInfo) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		ModelVersionMappingDBServiceRequest request = new ModelVersionMappingDBServiceRequest();
		context.setRequest(request);

		request.setModel(model);
		request.setModelInstance(instance);
		request.setVersionInfo(versionInfo);
		request.setDbAction(DB_ACTION_INSERT);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

	public static ServiceExecutionContext createContextForUpdate(ModelVersionMapping mappingToAlter) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		ModelVersionMappingDBServiceRequest request = new ModelVersionMappingDBServiceRequest();
		context.setRequest(request);

		request.setMappingToAlter(mappingToAlter);
		request.setDbAction(DB_ACTION_UPDATE);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

	public static ServiceExecutionContext createContextForDelete(ModelVersionMapping mappingToAlter) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		ModelVersionMappingDBServiceRequest request = new ModelVersionMappingDBServiceRequest();
		context.setRequest(request);

		request.setMappingToAlter(mappingToAlter);
		request.setDbAction(DB_ACTION_DELETE);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}
	

}
