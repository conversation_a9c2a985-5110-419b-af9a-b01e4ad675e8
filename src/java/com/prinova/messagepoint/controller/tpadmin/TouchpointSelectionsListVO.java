package com.prinova.messagepoint.controller.tpadmin;

import java.io.Serializable;

import com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectListController;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowAction;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowInstance;
import com.prinova.messagepoint.util.UserUtil;

public class TouchpointSelectionsListVO implements Serializable {

	private static final long serialVersionUID = 234046640137003937L;
	
	private boolean selectedForAction;
	private TouchpointSelection touchpointSelection;
	private int treeNodeLevel;
	private boolean isActionable;
	private boolean canUpdate;
	private boolean canApprove;
	private boolean canReject;	
	private boolean canReleaseForApproval;
	private boolean canCreateWorkingCopy;	
	private boolean canDiscardWorkingCopy;	
	private boolean canRemove;
	private boolean canDelete;
	private boolean canReassign;
	
	private boolean hasNoStepForWorkflow;
	private boolean	isWorkflowOwner;

	public TouchpointSelectionsListVO() {
		super();
	}
	
	public TouchpointSelectionsListVO(	TouchpointSelection touchpointSelection, 
										int treeNodeLevel, int tpSelectionListFilterId, User requestor, 
										long searchType, String searchName, String[] searchValues) {
		super();
		
		boolean selectionReassignPerm	= UserUtil.isPermissionGranted(Permission.ROLE_TOUCHPOINT_SELECTION_REASSIGN);
				
		ConfigurableWorkflowAction wfAction = touchpointSelection.getWorkflowAction();
		ConfigurableWorkflowInstance wfInst = null;
		if(wfAction != null){	// Touchpoint selection participate in previous workflow before.
			// Need to check whether the touchpoint selection has been rejected, if yes, this message need to ready for involving in the new workflow instance.
			if(wfAction.isActionRejected()){
				wfInst = TouchpointSelection.findRootWorkflow(touchpointSelection).findActiveInstance();
			}else{
				if(wfAction.getConfigurableWorkflowStep() != null){ // By activate (Workflow does not have step)
					wfInst = wfAction.getConfigurableWorkflowStep().getConfigurableWorkflowInstance();
				}
			}					
		}else{
			ConfigurableWorkflow cfgWf = TouchpointSelection.findRootWorkflow(touchpointSelection);
			if(cfgWf != null){
				wfInst = cfgWf.findActiveInstance();
			}
			
		}
		this.isWorkflowOwner 		=	( wfInst != null && wfInst.getOwnerId() != null && wfInst.getOwnerId().longValue() == UserUtil.getPrincipalUserId());
		this.hasNoStepForWorkflow 	=	( wfInst == null || wfInst.getWorkflowSteps() == null || wfInst.getWorkflowSteps().isEmpty());
		boolean isUserInApprovalList	= wfAction != null && wfAction.getActionApprovers().stream().anyMatch(u->u.getId()==UserUtil.getPrincipalUserId());
		boolean isApprovedByUser		= wfAction != null && wfAction.isActionApprovedByUser(UserUtil.getPrincipalUser());
		
		this.touchpointSelection = touchpointSelection;
		this.treeNodeLevel = treeNodeLevel;
		if (touchpointSelection.canUpdate(requestor)) {
			this.canUpdate=true;
		}
		if (touchpointSelection.isAwaitingApproval()) {
			if (isWorkflowOwner || isUserInApprovalList) {
				
				this.canApprove=!isApprovedByUser;
				this.canReject=!isApprovedByUser;
			}	
		}
		if (touchpointSelection.isWIP()) {
			this.canReassign = selectionReassignPerm;
			if (touchpointSelection.getAssigneeId()==null) {
				this.canReleaseForApproval=true;
			} else if (touchpointSelection.getAssigneeId() == requestor.getId()) {
				this.canReleaseForApproval=true;
			}
		}
		this.canCreateWorkingCopy = touchpointSelection.canCreateWorkingCopy(requestor);
		this.canDiscardWorkingCopy = touchpointSelection.canDiscardWorkingCopy(requestor);
		this.canRemove = touchpointSelection.canRemove(requestor);
		this.canDelete = touchpointSelection.canDelete(requestor);
		
		boolean isMatchingListFilter = touchpointSelection.isMatchingFilter(tpSelectionListFilterId, requestor);
		boolean isMatchingSearchFilter = touchpointSelection.isMatchingSearchFilter(searchType, searchName, searchValues);
		
		this.isActionable = isMatchingListFilter && isMatchingSearchFilter;			
	}
	
	public String getPrimaryLinkParams() {
		if (this.touchpointSelection.getHasWorkingCopy())
			return "&"+TouchpointContentObjectListController.REQ_PARM_VIEWID+"="+TouchpointContentObjectListController.VIEW_WORKING_COPY;
		else
			return "&"+TouchpointContentObjectListController.REQ_PARM_VIEWID+"="+TouchpointContentObjectListController.VIEW_ACTIVE;
	}
	
	public boolean isSelectedForAction() {
		return selectedForAction;
	}
	public void setSelectedForAction(boolean selectedForAction) {
		this.selectedForAction = selectedForAction;
	}
	public TouchpointSelection getTouchpointSelection() {
		return touchpointSelection;
	}
	public void setTouchpointSelection(TouchpointSelection touchpointSelection) {
		this.touchpointSelection = touchpointSelection;
	}
	public boolean isCanUpdate() {
		return canUpdate;
	}
	public void setCanUpdate(boolean canUpdate) {
		this.canUpdate = canUpdate;
	}
	public boolean isCanApprove() {
		return canApprove;
	}
	public void setCanApprove(boolean canApprove) {
		this.canApprove = canApprove;
	}
	public int getTreeNodeLevel() {
		return treeNodeLevel;
	}
	public void setTreeNodeLevel(int treeNodeLevel) {
		this.treeNodeLevel = treeNodeLevel;
	}
	public boolean isActionable() {
		return isActionable;
	}
	public void setActionable(boolean isActionable) {
		this.isActionable = isActionable;
	}

	public boolean isCanReject() {
		return canReject;
	}

	public void setCanReject(boolean canReject) {
		this.canReject = canReject;
	}

	public boolean isCanReleaseForApproval() {
		return canReleaseForApproval;
	}

	public void setCanReleaseForApproval(boolean canReleaseForApproval) {
		this.canReleaseForApproval = canReleaseForApproval;
	}

	public boolean isCanCreateWorkingCopy() {
		return canCreateWorkingCopy;
	}

	public void setCanCreateWorkingCopy(boolean canCreateWorkingCopy) {
		this.canCreateWorkingCopy = canCreateWorkingCopy;
	}

	public boolean isCanDiscardWorkingCopy() {
		return canDiscardWorkingCopy;
	}

	public void setCanDiscardWorkingCopy(boolean canDiscardWorkingCopy) {
		this.canDiscardWorkingCopy = canDiscardWorkingCopy;
	}

	public boolean isCanRemove() {
		return canRemove;
	}

	public void setCanRemove(boolean canRemove) {
		this.canRemove = canRemove;
	}

	public boolean isCanDelete() {
		return canDelete;
	}

	public void setCanDelete(boolean canDelete) {
		this.canDelete = canDelete;
	}

	public boolean isHasNoStepForWorkflow() {
		return hasNoStepForWorkflow;
	}

	public void setHasNoStepForWorkflow(boolean hasNoStepForWorkflow) {
		this.hasNoStepForWorkflow = hasNoStepForWorkflow;
	}

	public boolean isWorkflowOwner() {
		return isWorkflowOwner;
	}

	public void setWorkflowOwner(boolean isWorkflowOwner) {
		this.isWorkflowOwner = isWorkflowOwner;
	}

	public boolean isCanReassign() {
		return canReassign;
	}

	public void setCanReassign(boolean canReassign) {
		this.canReassign = canReassign;
	}
}