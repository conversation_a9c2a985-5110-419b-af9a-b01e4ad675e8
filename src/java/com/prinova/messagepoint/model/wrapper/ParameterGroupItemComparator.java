package com.prinova.messagepoint.model.wrapper;

import java.util.Comparator;

import com.prinova.messagepoint.model.admin.ParameterGroupItem;


public class ParameterGroupItemComparator implements Comparator<ParameterGroupItem>{

	public int compare(ParameterGroupItem vo1, ParameterGroupItem vo2) {
		int result = Integer.valueOf(vo1.getItemOrder()).compareTo(Integer.valueOf(vo2.getItemOrder()));
			
		return result;
	}

}
