package com.prinova.messagepoint.platform.services.workflow;

import com.prinova.messagepoint.MessagepointDeferrable;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.audit.*;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectData;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.model.notification.*;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.model.task.TaskStatus;
import com.prinova.messagepoint.model.util.StateProviderStatusTriggeredEventHandler;
import com.prinova.messagepoint.model.util.TaskUtil;
import com.prinova.messagepoint.model.version.Approvable;
import com.prinova.messagepoint.model.version.ApprovableWithWfActions;
import com.prinova.messagepoint.model.workflow.*;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.platform.services.tasks.CreateOrUpdateTaskService;
import com.prinova.messagepoint.platform.services.tpselection.ApproveTouchpointVariantService;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.*;

public class WorkflowApproveService extends AbstractService {

	public static final String SERVICE_NAME = "workflow.WorkflowApproveService";

	private static final Log log = LogUtil.getLog(WorkflowApproveService.class);
	
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			
			WorkflowServiceRequest request = (WorkflowServiceRequest) context.getRequest();
			List<Approvable> models = request.getModels();
			User approvedBy = request.getApprover();
			ConfigurableWorkflow actionToWorkflow = request.getWorkflow();
			Date approvedDate = DateUtil.now();
			Map<Long, String> notes = request.getNotes();
			
			boolean isAutoApproved = request.isAutoApproved();
			boolean skipCurrentStep = request.isSkippedCurrentStep();
			boolean notifyUsers = request.isNotifyUsers();

			for (Approvable model : models) {
				String approvalNotes = "";
				if(model instanceof IdentifiableMessagePointModel) {
					IdentifiableMessagePointModel idMpModel = (IdentifiableMessagePointModel)model;
					approvalNotes = notes.get(idMpModel.getId());
				}
				
				//handle current step
				ConfigurableWorkflowAction currentAction = null;

				if(skipCurrentStep){
					currentAction = skipCurrentStep(model, actionToWorkflow, approvedDate);
				}else {
					currentAction = processCurrentApprovalStep(model, actionToWorkflow, approvedBy, approvedDate, approvalNotes, isAutoApproved);
				}

				boolean isOwnerAction = currentAction.getConfigurableWorkflowStep().getConfigurableWorkflowInstance().isOwnerAction(approvedBy);
				
				//check if go to next step
				boolean stepFinished = (isOwnerAction || isAutoApproved || skipCurrentStep || checkApprovalDetail(currentAction) );
				
				//it is the final approval
				if(stepFinished && WorkflowManager.getNextStep(currentAction) == null) {
					processFinalApproval(model, actionToWorkflow, approvedBy, approvedDate, approvalNotes, isAutoApproved, isOwnerAction);
				}
				
				//move to next step
				else if(stepFinished && WorkflowManager.getNextStep(currentAction) != null) {
					processMoveToNextStep(model, currentAction, approvedBy, approvalNotes, isAutoApproved, isOwnerAction, notifyUsers);
				}
				
				//stay on the current step
				else {
					//create the configurable workflow action
					int statePrior = ConfigurableWorkflowActionStateType.ID_INTERMEDIATE_APPROVAL;
					if(WorkflowManager.getPreviousStep(currentAction) == null)
						statePrior = ConfigurableWorkflowActionStateType.ID_RELEASE_FOR_APPROVAL;
					int approveType = ConfigurableWorkflowActionHistory.ACTION_REGULAR;
					if(isAutoApproved)
						approveType = ConfigurableWorkflowActionHistory.ACTION_AUTO_APPROVED;
					if(isOwnerAction)
						approveType = ConfigurableWorkflowActionHistory.ACTION_OVERRIDE;

					boolean isTranslationStep = currentAction.getConfigurableWorkflowStep().isTranslationStep();
					ConfigurableWorkflowActionHistory.generateAction(currentAction, model, (isTranslationStep?ConfigurableWorkflowActionType.ID_RELEASED_FROM_TRANS:ConfigurableWorkflowActionType.ID_APPROVED), statePrior, ConfigurableWorkflowActionStateType.ID_INTERMEDIATE_APPROVAL, currentAction.getConfigurableWorkflowStep().getState(), approvedBy, null, DateUtil.now(), approvalNotes, approveType);
				}
			}

			// Update models' tasks status
			TaskUtil.updateStatusForApprovables(models, -1, approvedBy);
		} catch (Exception e) {
			log.error(" unexpected exception when invoking WorkflowApproveService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
				context.getLocale());
			throw new RuntimeException(e.getMessage());
		}	
	}

	private void processFinalApproval(Approvable model, ConfigurableWorkflow actionToWorkflow, User approvedBy, Date approvedDate, String notes, boolean isAutoApproved, boolean isOwnerAction) throws Exception {
		//we don't clean up the action on an active object
		//we will replace it with a new created action when creating a working copy
		//what we need to do here is mark the action active
		boolean isSubworkflowApproval = false, allSubworkflowComplete = false;
		// Sub object workflow actions to remove
		List<ConfigurableWorkflowAction> subWfActionsToRemove = new ArrayList<>();
		// Retrieve the corresponding workflow action
		ConfigurableWorkflowAction action = null;
		if(model instanceof ApprovableWithWfActions model2){
			action = model2.getActionByWorkflow(actionToWorkflow);
		}else {
			action = model.getWorkflowAction();
		}

		action.setActive(true);
		action.save();
		if(model instanceof TouchpointSelection) {
			//use the existing service			
			List<TouchpointSelection> tpSelections = new ArrayList<>();
			TouchpointSelection ts = (TouchpointSelection) model;
			tpSelections.add(ts);
			ServiceExecutionContext context = ApproveTouchpointVariantService.createContext(tpSelections, approvedBy, notes, null, false);

			Service service = MessagepointServiceFactory.getInstance().lookupService(ApproveTouchpointVariantService.SERVICE_NAME, ApproveTouchpointVariantService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(ApproveTouchpointVariantService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				sb.append(" requestor = ").append(approvedBy.getUsername());
				sb.append(" approvals not applied. ");
				log.error(sb.toString());
				throw new RuntimeException(sb.toString());
			}
			// Set TPContentChanged to true
			if(!ts.getDocument().isTpContentChanged()){
				ts.getDocument().setTpContentChanged(true);
				ts.getDocument().save();
			}
		} else if(model instanceof ContentObject) {
			ContentObject contentObject = (ContentObject) model;
			// If this is a sub workflow, only update the content object is all the sub workflow are active
			ObjectWorkflowActionAssociation association = ObjectWorkflowActionAssociation.findByModelAndAction(model, action);

			if(association != null && association.getParentAction() != null){
				isSubworkflowApproval = true;
				ConfigurableWorkflowAction parentAction = association.getParentAction();
				List<ObjectWorkflowActionAssociation> allSubAssociations = ObjectWorkflowActionAssociation.findAllByParentAction(parentAction);
				allSubworkflowComplete = allSubAssociations.stream().allMatch(subAssociation -> subAssociation.getAction().isActive());
				if(allSubworkflowComplete) {
					for (ObjectWorkflowActionAssociation subAssociation : allSubAssociations) {
						subWfActionsToRemove.add(subAssociation.getAction());
					}
				}
			}

			if(!isSubworkflowApproval) {
				// Set TPContentChanged to true
				Date currentTime = new Date(System.currentTimeMillis());
				if (contentObject.getStartDate() == null || (contentObject.getStartDate() != null && contentObject.getStartDate().after(currentTime)))
					for (Document doc : contentObject.getDocuments()) {
						if (!doc.isTpContentChanged()) {
							doc.setTpContentChanged(true);
							doc.save();
						}
					}
				ContentObjectData contentObjectData = contentObject.getLatestContentObjectDataWorkingCentric();
				if (contentObjectData.isWorking()) {
					contentObject.setState(WorkflowState.findById(WorkflowState.STATE_PRODUCTION));
					contentObject.setReadyForApproval(false);
					contentObject.save();

					StateProviderStatusTriggeredEventHandler.performMainProcessing(contentObjectData, null, contentObject.getState());

					// add notification action
					int eventType = NotificationEventType.ID_MESSAGES, auditEventType = AuditEventType.ID_ASSET_UPDATE;
					int objectType = NotificationObjectType.ID_MESSAGE, auditObjectType = AuditObjectType.ID_MESSAGE;
					int actionType = NotificationActionType.ID_MESSAGE_ACTIVATED, auditActionType = AuditActionType.ID_MESSAGE_ACTIVATE;
					if (contentObject.isLocalSmartText()) {
						objectType = NotificationObjectType.ID_LOCAL_SMART_TEXT;
						actionType = NotificationActionType.ID_LOCAL_SMART_TEXT_ACTIVATED;
					} else if (contentObject.isLocalImage()) {
						objectType = NotificationObjectType.ID_LOCAL_IMAGE;
						actionType = NotificationActionType.ID_LOCAL_IMAGE_ACTIVATED;
					} else if (contentObject.isGlobalSmartText()) {
						eventType = NotificationEventType.ID_SMART_TEXT;
						objectType = NotificationObjectType.ID_SMART_TEXT;
						actionType = NotificationActionType.ID_SMART_TEXT_ACTIVATED;
					} else if (contentObject.isGlobalImage()) {
						eventType = NotificationEventType.ID_IMAGE_LIBRARY;
						objectType = NotificationObjectType.ID_IMAGE_LIBRARY;
						actionType = NotificationActionType.ID_IMAGE_ACTIVATED;
					}
					// Notification (Activation)
					long docId = contentObject.getIsTouchpointLocal() ? contentObject.getDocument().getId() : (contentObject.getDocuments().isEmpty() ? 0L : contentObject.getDocuments().iterator().next().getId());
					NotificationEventUtil.notificationEventHappen(
							action,
							model,
							null,
							eventType,
							objectType,
							contentObject.getName(),
							docId,
							contentObject.getId(),
							actionType,
							null);
					/** Audit (Activation)
					 AuditEventUtil.push(
					 auditEventType,
					 auditObjectType,
					 contentObject.getName(),
					 contentObject.getId(),
					 auditActionType, null);
					 */
				}
			}
		} else if(model instanceof Communication) {
			Communication c = (Communication) model;
			c.setState(WorkflowState.findById(WorkflowState.STATE_PRODUCTION));
			Document document = c.getDocument();
			if ( document.isCommunicationWebServiceProductionStatusEnabled() )
				c.setPollingProductionStatusResults(true);
			if ( document.getCommunicationProductionTypeId() == Document.COMMUNICATION_PRODUCTION_TYPE_ON_APPROVAL || document.getCommunicationProductionTypeId() == Document.COMMUNICATION_PRODUCTION_TYPE_ON_APPROVAL_MINI_BUNDLE ) {
				c.generateProductionProofPackage();
				c.setLastProductionDate(new Date());
			}
			c.save();
			StateProviderStatusTriggeredEventHandler.performMainProcessing(c, null, c.getState());
		} else if(model instanceof LookupTableInstance) {
			LookupTableInstance lti = (LookupTableInstance) model;
			lti.setState(WorkflowState.findById(WorkflowState.STATE_PRODUCTION));
			lti.save();
			StateProviderStatusTriggeredEventHandler.performMainProcessing(lti, null, lti.getState());
		}

		// Retrieve the corresponding workflow action
		ConfigurableWorkflowAction _action = null;
		if(model instanceof ApprovableWithWfActions model2){
			_action = model2.getActionByWorkflow(actionToWorkflow);
		}else {
			_action = model.getWorkflowAction();
		}
		//create the configurable workflow action
		int statePrior = ConfigurableWorkflowActionStateType.ID_INTERMEDIATE_APPROVAL;
		if(WorkflowManager.getPreviousStep(_action) == null)
			statePrior = ConfigurableWorkflowActionStateType.ID_RELEASE_FOR_APPROVAL;
		int approveType = ConfigurableWorkflowActionHistory.ACTION_REGULAR;
		if(isAutoApproved)
			approveType = ConfigurableWorkflowActionHistory.ACTION_AUTO_APPROVED;
		if(isOwnerAction)
			approveType = ConfigurableWorkflowActionHistory.ACTION_OVERRIDE;

		boolean isTranslationStep = action.getConfigurableWorkflowStep().isTranslationStep();
		ConfigurableWorkflowActionHistory.generateAction(_action, model, (isTranslationStep?ConfigurableWorkflowActionType.ID_RELEASED_FROM_TRANS:ConfigurableWorkflowActionType.ID_APPROVED), statePrior, ConfigurableWorkflowActionStateType.ID_FINAL_APPROVAL, action.getConfigurableWorkflowStep().getState(), approvedBy, null, DateUtil.now(), notes, approveType);

		if(model instanceof ApprovableWithWfActions model2){
			if(isSubworkflowApproval && allSubworkflowComplete) {
				// Remove the sub object workflow associations and auto approve the parent action
				for(ConfigurableWorkflowAction wfActionToRemove : subWfActionsToRemove){
					model2.replaceWorkflowAction(wfActionToRemove, null);
				}

				// Approve the parent action
				ServiceExecutionContext context = WorkflowApproveService.createContextForCompletingSubworkflowStep(null, model);
				Service service = MessagepointServiceFactory.getInstance().lookupService(WorkflowApproveService.SERVICE_NAME, WorkflowApproveService.class);
				service.execute(context);
			}
		}
	}
	
	private void processMoveToNextStep(Approvable model, ConfigurableWorkflowAction currentAction, User approvedBy, String notes, boolean isAutoApproved, boolean isOwnerAction, boolean notifyUsers) throws Exception{
		Map<ConfigurableWorkflowAction, Set<ConfigurableWorkflowAction>> subWfActionsmap = new HashMap<>();
		Date startDate = DateUtil.now();
		ConfigurableWorkflowAction nextAction = WorkflowManager.generateWFActionAndDetail(model, WorkflowManager.getNextStep(currentAction), startDate, startDate);
		ConfigurableWorkflowStep nextStep = nextAction.getConfigurableWorkflowStep();
		nextAction.setPreviousAction(currentAction);
		nextAction.setReleaseForApprovalDate(currentAction.getReleaseForApprovalDate());
		nextAction.save();

		currentAction.setNextAction(nextAction);
		currentAction.save();

		boolean notifyUsersRealTime = notifyUsers;
		boolean skipNextStep = false;
		if(model instanceof ContentObject){
			ContentObject contentObject = (ContentObject)model;
			skipNextStep = contentObject.skipNextWorkflowStep(nextStep);
		}
		
		Set<ConfigurableWorkflowApprovalDetail> details = nextAction.getApprovalDetails();
		// Get the start date updated, means now the approvals on this token has began
		for (ConfigurableWorkflowApprovalDetail detail : details) {
			detail.setStartedDate(startDate);
			detail.save();
		}

		// create the configurable workflow action
		int statePrior = ConfigurableWorkflowActionStateType.ID_INTERMEDIATE_APPROVAL;
		if (WorkflowManager.getPreviousStep(currentAction) == null)
			statePrior = ConfigurableWorkflowActionStateType.ID_RELEASE_FOR_APPROVAL;
		int approveType = ConfigurableWorkflowActionHistory.ACTION_REGULAR;
		if (isAutoApproved)
			approveType = ConfigurableWorkflowActionHistory.ACTION_AUTO_APPROVED;
		if (isOwnerAction)
			approveType = ConfigurableWorkflowActionHistory.ACTION_OVERRIDE;

		boolean isTranslationStep = currentAction.getConfigurableWorkflowStep().isTranslationStep();
		if(notifyUsersRealTime){
			ConfigurableWorkflowActionHistory.generateAction(nextAction, model, (isTranslationStep?ConfigurableWorkflowActionType.ID_RELEASED_FROM_TRANS:ConfigurableWorkflowActionType.ID_APPROVED), statePrior, ConfigurableWorkflowActionStateType.ID_INTERMEDIATE_APPROVAL, currentAction.getConfigurableWorkflowStep().getState(), approvedBy, nextAction.getActionApprovers(), DateUtil.now(), notes, approveType);
		}else{
			ConfigurableWorkflowActionHistory.generateActionAndNotify(nextAction, model, (isTranslationStep?ConfigurableWorkflowActionType.ID_RELEASED_FROM_TRANS:ConfigurableWorkflowActionType.ID_APPROVED), statePrior, ConfigurableWorkflowActionStateType.ID_INTERMEDIATE_APPROVAL, currentAction.getConfigurableWorkflowStep().getState(), approvedBy, nextAction.getActionApprovers(), DateUtil.now(), notes, approveType, false);
		}

		if(nextStep.getWorkflowStepType()==WorkflowStepType.ID_SUBWORKFLOW){
			Set<ConfigurableWorkflow> subWorkflow = nextStep.getSubworkflows();
			if(subWorkflow!=null && !subWorkflow.isEmpty()){
				for(ConfigurableWorkflow subWF:subWorkflow){
					// Skip the sub-workflow which does not have steps
					if(subWF.findActiveInstance().getWorkflowSteps().isEmpty()){
						continue;
					}

					Map<ConfigurableWorkflowAction, Set<ConfigurableWorkflowAction>> currSubWfActionsMap = WorkflowUtilities.createWFActionMap(model, subWF, startDate, nextAction);
					for(ConfigurableWorkflowAction subWfAction:currSubWfActionsMap.keySet()){
						if(subWfActionsmap.containsKey(subWfAction)){
							subWfActionsmap.get(subWfAction).addAll(currSubWfActionsMap.get(subWfAction));
						}else{
							subWfActionsmap.put(subWfAction, currSubWfActionsMap.get(subWfAction));
						}
					}
				}
			}
		}

		if(model instanceof ApprovableWithWfActions model2){
			model2.replaceWorkflowAction(currentAction, nextAction);
			if(model2 instanceof ContentObject contentObject) {
				if(!subWfActionsmap.isEmpty()){
					model2.updateWorkflowActions(subWfActionsmap);
					// If there is a sub action, create a task for each sub action
					Set<ConfigurableWorkflowAction> subWFActions = subWfActionsmap.get(nextAction);
					if (subWFActions != null) {
						for (ConfigurableWorkflowAction subWFAction : subWFActions) {
							Task subTask = new Task();
							String taskDescription = ApplicationUtil.getMessage("page.text.task.for.subworkflow") + ": " + subWFAction.getConfigurableWorkflow().getName();
							subTask.setRequirement(taskDescription.getBytes());
							subTask.setWorkflowAction(subWFAction);
							ServiceExecutionContext taskCreationContext = CreateOrUpdateTaskService.createContext(contentObject, null, subTask, TaskStatus.ID_IN_WORKFLOW, true, taskDescription);
							Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateTaskService.SERVICE_NAME, CreateOrUpdateTaskService.class);
							service.execute(taskCreationContext);
						}
					}
				}

				Task linkedTask = Task.findByWorkflowAction(currentAction);
				if (linkedTask != null) {
					// If new step is in a translation step, set the task assignee to unassigned
					if (nextStep.isTranslationStep()) {
						linkedTask.getAssignees().clear();
					}

					// Update the task's workflow action
					linkedTask.setWorkflowAction(nextAction);
					linkedTask.save();
				}
			}
		}else {
			model.setWorkflowAction(nextAction);
		}
		model.save();

		// Check if the next step has any sub-workflows and if so skip first step in each SW if required
		boolean skipNextStepInWf = true;
		if(nextStep.isSubworkflowStep() && model instanceof ContentObject contentObject){
			Set<ConfigurableWorkflow> subWorkflows = nextStep.getSubworkflows();
			if(subWorkflows!=null && !subWorkflows.isEmpty()) {
				for (ConfigurableWorkflow subWorkflow : subWorkflows) {
					ConfigurableWorkflowStep firstStep = WorkflowManager.getFirstWorkflowStep(subWorkflow);
					if(contentObject.skipNextWorkflowStep(firstStep)){
						HibernateUtil.getManager().getSession().flush();
						ServiceExecutionContext context = WorkflowApproveService.createContextForSkippingCurrentStep(null, subWorkflow, model);
						Service service = MessagepointServiceFactory.getInstance().lookupService(WorkflowApproveService.SERVICE_NAME, WorkflowApproveService.class);
						service.execute(context);
					}
					else {
						skipNextStepInWf = false; // If any sub-workflow step is not skipped, do not skip the next step in the main workflow
					}
				}
			}
		}

		// Skip the next step if it is translation step and no translation is needed
		if(skipNextStep){
			HibernateUtil.getManager().getSession().flush();
			ConfigurableWorkflow actionToWorkflow = nextStep.getConfigurableWorkflowInstance().getConfigurableWorkflow();
			ServiceExecutionContext context = WorkflowApproveService.createContextForSkippingCurrentStep(null, actionToWorkflow, model);
			Service service = MessagepointServiceFactory.getInstance().lookupService(WorkflowApproveService.SERVICE_NAME, WorkflowApproveService.class);
			service.execute(context);
		}
		// If skip is not required for the next step and subworkflow step was not skipped - generate JSOn export files
		else if(!skipNextStepInWf){
			if(model instanceof ContentObject contentObject){
                //Invoke translation service if needed
				ConfigurableWorkflow targetWorkflow = nextStep.getWorkflowStepType()!=WorkflowStepType.ID_SUBWORKFLOW?currentAction.getConfigurableWorkflow():null;
				ContentObjectData contentObjectData = contentObject.getLatestContentObjectDataWorkingCentric();
				String previousRejectionReason = targetWorkflow!=null?ConfigurableWorkflowActionHistory.findLatestRejectionNotesByContentObjectData(contentObjectData, targetWorkflow):null;
				boolean isRetry = previousRejectionReason != null;
				WorkflowUtilities.sendContentObjectToTranslationService(contentObject, targetWorkflow, isRetry, previousRejectionReason);
			}
		}
	}

	private boolean checkApprovalDetail(ConfigurableWorkflowAction currentAction) {
		ConfigurableWorkflowStep step = currentAction.getConfigurableWorkflowStep();
		if(ApprovalType.ID_APPROVAL_TYPE_ANY_OF == step.getApproveType())
			return true;
		else {//this means it is 'all_of'
			Set<ConfigurableWorkflowApprovalDetail> details = currentAction.getApprovalDetails();
			for (ConfigurableWorkflowApprovalDetail detail : details) {
				if(ConfigurableWorkflowApprovalDetail.APPROVED != detail.getApproved())
					return false;
			}
			return true;
		}
	}

	private ConfigurableWorkflowAction skipCurrentStep(Approvable model, ConfigurableWorkflow actionToWorkflow, Date skippedDate){
		// Retrieve the corresponding workflow action
		ConfigurableWorkflowAction action = null;
		if(model instanceof ApprovableWithWfActions model2){
			action = model2.getActionByWorkflow(actionToWorkflow);
		}else {
			action = model.getWorkflowAction();
		}

		ConfigurableWorkflowApprovalDetail detail = new ConfigurableWorkflowApprovalDetail();
		detail.setWorkflowAction(action);
		detail.setUserId(null);
		detail.setStartedDate(skippedDate);
		detail.setOwner(false);
		action.getApprovalDetails().add(detail);

		detail.setApproved(ConfigurableWorkflowApprovalDetail.SKIPPED);
		detail.setApprovedDate(skippedDate);
		detail.setNotes(null);
		detail.save();

		// Workflow Changes (Translation step skipped)
		AuditEventUtil.auditWorkflowChanged(model, AuditActionType.ID_CHANGE_TRANSLATION_SKIPPED, AuditMetadataBuilder.forWorkflowChanges(action));

		if(model instanceof ApprovableWithWfActions model2){
			return model2.getActionByWorkflow(actionToWorkflow);
		}else {
			return model.getWorkflowAction();
		}
	}

	private ConfigurableWorkflowAction processCurrentApprovalStep(Approvable model, ConfigurableWorkflow actionToWorkflow, User approvedBy, Date approvedDate, String notes, boolean isAutoApproved) {
		// Retrieve the corresponding workflow action
		ConfigurableWorkflowAction action = null;
		if(model instanceof ApprovableWithWfActions model2){
			action = model2.getActionByWorkflow(actionToWorkflow);
		}else {
			action = model.getWorkflowAction();
		}

		ConfigurableWorkflowApprovalDetail detail = null;
		if(action != null) {
			if (approvedBy != null) {
				detail = action.getApprovalDetailByUserId(approvedBy.getId());
			}

			if (detail == null) {// might be a owner override action or a auto approved action
				detail = new ConfigurableWorkflowApprovalDetail();
				detail.setWorkflowAction(action);
				detail.setUserId(approvedBy != null ? approvedBy.getId() : null);
				detail.setStartedDate(approvedDate);
				detail.setOwner(!isAutoApproved);
				action.getApprovalDetails().add(detail);
			}

			if (isAutoApproved) {
				detail.setApproved(ConfigurableWorkflowApprovalDetail.AUTO_APPROVED);
			} else if (action.getConfigurableWorkflowStep().isTranslationStep()) {
				detail.setApproved(ConfigurableWorkflowApprovalDetail.RELEASED_FROM_TRANS);
			} else {
				detail.setApproved(ConfigurableWorkflowApprovalDetail.APPROVED);
			}
			detail.setApprovedDate(approvedDate);
			detail.setNotes(notes);
			detail.save();
		}
		long refObjId = 0;
		if(model instanceof TouchpointSelection) {
			//use the existing service
			List<TouchpointSelection> tpSelections = new ArrayList<>();
			TouchpointSelection ts = (TouchpointSelection) model;
			refObjId = ts.getId();
			tpSelections.add(ts);
			ServiceExecutionContext context = ApproveTouchpointVariantService.createContext(tpSelections, approvedBy, notes, null, false);

			Service service = MessagepointServiceFactory.getInstance().lookupService(ApproveTouchpointVariantService.SERVICE_NAME, ApproveTouchpointVariantService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(ApproveTouchpointVariantService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				sb.append(" requestor = ").append(approvedBy != null ? approvedBy.getUsername() : "null");
				sb.append(" approvals not applied. ");
				log.error(sb.toString());
				throw new RuntimeException(sb.toString());
			}
		}
		// Send approvedbyother email if workflowstep's approval type is any of to other approvers
		ConfigurableWorkflowStep cwfStep = action!=null?action.getConfigurableWorkflowStep():null;
		if(cwfStep != null && cwfStep.getApproveType() == ApprovalType.ID_APPROVAL_TYPE_ANY_OF){
			if(cwfStep.isTranslationStep()){	// Workflow Changes (Translated)
				AuditEventUtil.auditWorkflowChanged(model, AuditActionType.ID_CHANGE_TRANSLATED, AuditMetadataBuilder.forWorkflowChanges(action));
			}else {	// Workflow Changes (Approved)
				MessagepointDeferrable.add(new NotificationRunner(action, model, null,  NotificationEventType.ID_WORKFLOW, refObjId, NotificationActionType.ID_WORKFLOW_APPROVED_BY_OTHERS, notes));
				AuditEventUtil.auditWorkflowChanged(model, AuditActionType.ID_CHANGE_APPROVED, AuditMetadataBuilder.forWorkflowChanges(action));
			}
		}

		if(model instanceof ApprovableWithWfActions model2){
			return model2.getActionByWorkflow(actionToWorkflow);
		}else {
			return model.getWorkflowAction();
		}
	}
	
	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
	}

	public static ServiceExecutionContext createContextForCompletingTranslationServiceStep(ConfigurableWorkflow actionToWorkflow, Approvable... models){
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		WorkflowServiceRequest request = new WorkflowServiceRequest();
		context.setRequest(request);

		String notes = ApplicationUtil.getMessage("page.label.completed");
		Map<Long, String> modelNotes = new HashMap<>();
		for(Approvable approvable : models){
			if(approvable instanceof IdentifiableMessagePointModel) {
				IdentifiableMessagePointModel idMpModel = (IdentifiableMessagePointModel)approvable;
				modelNotes.put(idMpModel.getId(), notes);
			}
		}

		request.setModels(models);
		request.setApprover(null);
		request.setNotes(modelNotes);
		request.setAutoApproved(true);
		request.setSkippedCurrentStep(false);
		request.setWorkflow(actionToWorkflow);
		request.setNotifyUsers(true);

		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}

	public static ServiceExecutionContext createContextForCompletingSubworkflowStep(User approvedBy, Approvable... models){
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		WorkflowServiceRequest request = new WorkflowServiceRequest();
		context.setRequest(request);

		String notes = ApplicationUtil.getMessage("page.label.completed");
		Map<Long, String> modelNotes = new HashMap<>();
		for(Approvable approvable : models){
			if(approvable instanceof IdentifiableMessagePointModel) {
				IdentifiableMessagePointModel idMpModel = (IdentifiableMessagePointModel)approvable;
				modelNotes.put(idMpModel.getId(), notes);
			}
		}

		request.setModels(models);
		request.setApprover(approvedBy);
		request.setNotes(modelNotes);
		request.setAutoApproved(true);
		request.setSkippedCurrentStep(false);
		request.setNotifyUsers(true);

		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}

	public static ServiceExecutionContext createContextForSkippingCurrentStep(User approvedBy, ConfigurableWorkflow actionToWorkflow, Approvable... models){
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		WorkflowServiceRequest request = new WorkflowServiceRequest();
		context.setRequest(request);

		String notes = ApplicationUtil.getMessage("page.label.skipped");
		Map<Long, String> modelNotes = new HashMap<>();
		for(Approvable approvable : models){
			if(approvable instanceof IdentifiableMessagePointModel) {
				IdentifiableMessagePointModel idMpModel = (IdentifiableMessagePointModel)approvable;
				modelNotes.put(idMpModel.getId(), notes);
			}
		}

		request.setModels(models);
		request.setApprover(approvedBy);
		request.setNotes(modelNotes);
		request.setAutoApproved(false);
		request.setSkippedCurrentStep(true);
		request.setWorkflow(actionToWorkflow);
		request.setNotifyUsers(true);

		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}

	public static ServiceExecutionContext createContext(User approvedBy, String notes, boolean autoApproved, ConfigurableWorkflow actionToWorkflow, Approvable... models) {
		Map<Long, String> modelNotes = new HashMap<>();
		for(Approvable approvable : models){
			if(approvable instanceof IdentifiableMessagePointModel) {
				IdentifiableMessagePointModel idMpModel = (IdentifiableMessagePointModel)approvable;
				modelNotes.put(idMpModel.getId(), notes);
			}
		}
		return createContextWithNoteList(approvedBy, modelNotes, autoApproved, true, actionToWorkflow, models);
	}

	public static ServiceExecutionContext createContext(User approvedBy, String notes, boolean autoApproved, Approvable... models) {
		Map<Long, String> modelNotes = new HashMap<>();
		for(Approvable approvable : models){
			if(approvable instanceof IdentifiableMessagePointModel) {
				IdentifiableMessagePointModel idMpModel = (IdentifiableMessagePointModel)approvable;
				modelNotes.put(idMpModel.getId(), notes);
			}
		}
		return createContextWithNoteList(approvedBy, modelNotes, autoApproved, true, null, models);
	}
	
	public static ServiceExecutionContext createContextWithNoteList(User approvedBy, Map<Long, String> notes, boolean autoApproved, boolean notifyUsers, ConfigurableWorkflow actionToWorkflow, Approvable... models) {
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		WorkflowServiceRequest request = new WorkflowServiceRequest();
		context.setRequest(request);
		
		request.setModels(models);
		request.setApprover(approvedBy);
		request.setNotes(notes);
		request.setAutoApproved(autoApproved);
		request.setNotifyUsers(notifyUsers);
		request.setWorkflow(actionToWorkflow);
		
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}
}
