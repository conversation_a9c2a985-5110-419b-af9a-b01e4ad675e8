package com.prinova.messagepoint.query.handler;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.query.DefaultPage;
import com.prinova.messagepoint.query.IPage;

public class ContentObjectAssignedToHandler implements PostQueryHandler {

	private boolean isOrderAsc;
	
	public boolean isOrderAsc() {
		return isOrderAsc;
	}

	public void setOrderAsc(boolean isOrderAsc) {
		this.isOrderAsc = isOrderAsc;
	}
	
	@SuppressWarnings("unchecked")
	public IPage handle(IPage page) {
		List<ContentObject> contentObjects = (List<ContentObject>)page.getList();
		
		Collections.sort(contentObjects, new Comparator<>() {
            public int compare(ContentObject o1, ContentObject o2) {
                String assignedTo1 = o1.getAssignedToUserName();
                String assignedTo2 = o2.getAssignedToUserName();
                if (isOrderAsc()) {
                    return assignedTo1.compareTo(assignedTo2);
                } else {
                    return assignedTo2.compareTo(assignedTo1);
                }
            }
        });
		
        ((DefaultPage)page).setList(contentObjects);
		return page;
	}
}
