package com.prinova.messagepoint.model.admin;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.prinova.messagepoint.model.DescribableMessagePointModel;
import com.prinova.messagepoint.util.HibernateUtil;

public class DateDataValue extends DescribableMessagePointModel {
	
	private static final long serialVersionUID = -9174561061694563133L;
	
	private static final Log log = LogUtil.getLog(DateDataValue.class);
	
	public static final int TYPE_TODAY = 1;
	public static final int TYPE_RUNDATE = 2;
	public static final int TYPE_STARTOFMONTH = 3;
	public static final int TYPE_ENDOFMONTH = 4;
	public static final int TYPE_STARTOFLASTMONTH = 5;
	public static final int TYPE_ENDOFLASTMONTH = 6;
	public static final int TYPE_STARTOFCALENDARYEAR = 7;
	public static final int TYPE_ENDOFCALENDARYEAR = 8;
	public static final int TYPE_STARTOFLASTCALENDARYEAR = 9;
	public static final int TYPE_ENDOFLASTCALENDARYEAR = 10;
	public static final int TYPE_STARTOFCALENDARQUARTER = 11;
	public static final int TYPE_ENDOFCALENDARQUARTER = 12;
	public static final int TYPE_STARTOFLASTCALENDARQUARTER = 13;
	public static final int TYPE_ENDOFLASTCALENDARQUARTER = 14;
	public static final int TYPE_SPECIFICDATE = 15;

	public static final Map<String,String> getValueNameMap() {
		Map<String,String> valueMap = new HashMap<>();
		
		valueMap.put("Today" 						, "Today");
		valueMap.put("RunDate" 						, "Run date");
		valueMap.put("StartOfMonth" 				, "Start of month");
		valueMap.put("EndOfMonth" 					, "End of month");
		valueMap.put("StartOfLastMonth" 			, "Start of last month");
		valueMap.put("EndOfLastMonth" 				, "End of last month");
		valueMap.put("StartOfCalendarYear" 			, "Start of calendar year");
		valueMap.put("EndOfCalendarYear" 			, "End of calendar year");
		valueMap.put("StartOfLastCalendarYear" 		, "Start of last calendar year");
		valueMap.put("EndOfLastCalendarYear" 		, "End of last calendar year");
		valueMap.put("StartOfCalendarQuarter" 		, "Start of calendar quarter");
		valueMap.put("EndOfCalendarQuarter" 		, "End of calendar quarter"); 
		valueMap.put("StartOfLastCalendarQuarter" 	, "Start of last calendar quarter");
		valueMap.put("EndOfLastCalendarQuarter" 	, "End of last calendar quarter");
		valueMap.put("SpecificDate" 				, "Specific date");
		
		return valueMap;
	}

	public static JSONArray getDateValueTypesJSON() {
		List<DateDataValue> dateDataValues = HibernateUtil.getManager().getObjects( DateDataValue.class, MessagepointOrder.asc("name") );
		JSONArray dateValueTypesArray = new JSONArray();
		
		Map<String,String> valueNameMap = getValueNameMap();
		
		try {
			
			for (DateDataValue currentDateValue: dateDataValues) {
				JSONObject dateValueJSON = new JSONObject();
				dateValueJSON.put( "id",	String.valueOf(currentDateValue.getId()) );
				dateValueJSON.put( "value",	currentDateValue.getName() );
				dateValueJSON.put( "label",	valueNameMap.get(currentDateValue.getName()) != null ? valueNameMap.get(currentDateValue.getName()) : "undefined" );
				dateValueTypesArray.put(dateValueJSON);
			}
			
		} catch (JSONException e) {
			log.error("Error: Unable to generate dateValueType JSON: "+e);
		}
		
		return dateValueTypesArray;
	}
	
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + (int) (id ^ (id >>> 32));
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (!(obj instanceof DateDataValue))
			return false;
		final DateDataValue other = (DateDataValue)obj;
		if (id != other.getId())
			return false;
		return true;
	}
}
