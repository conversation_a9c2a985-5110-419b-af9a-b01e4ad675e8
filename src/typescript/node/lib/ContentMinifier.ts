/// <reference path="Types.d.ts" />

'use strict';

import fs = require('fs');
import path = require('path');
import crypto = require('crypto');
import _ = require('underscore');
import md5File = require('md5-file');
import * as CleanCSS from "clean-css";
const { minify } = require("terser");


export class ContentMinifier {

    private obfuscatedPaths: {[key: string]: string} = {};

    public static async minifyJs(file: ProjectFile, includeSourceMaps: boolean): Promise<MinifyOutput> {

        let output: MinifyOutput = {
            code: '',
            map: ''
        };

        try {
            let result = await minify(fs.readFileSync(file.path, "utf8"))
            output.code = result.code;
            output.map = result.map;
        } catch (ex) {
            console.log(`JavaScript parse error: ${file.file}, skipping`);
            output.code = null;
        }

        if (output.code == null) {
            output.code = fs.readFileSync(file.path, 'utf8');
            output.map = null;
        }

        return output;
    };

    public static minifyCss(input: string, filePath: string) : string {
        let result: string = input;

        try {
            let minifier: CleanCSS = new CleanCSS({
                keepSpecialComments: 0,
                advanced: false,
                relativeTo: path.dirname(filePath)
            });

            let minResult = minifier.minify(input);
            result = minResult.styles;

            if (_.isArray(minResult.errors) && minResult.errors.length > 0) {
                console.log("error in file: " + filePath);
                console.log(minResult.errors);
            }
        } catch (e) {
            console.log(e);
            result = input;
        }

        return result;
    }

    public minifyPath = (file: ProjectFile): string => {

        let obfuscatedPath = this.obfuscatedPaths[file.path];

        if (_.isUndefined(obfuscatedPath)) {
            obfuscatedPath = this.md5path(file.path);
            this.obfuscatedPaths[file.path] = obfuscatedPath;
        }

        return obfuscatedPath;
    };

    public getFileContentHashes = (files: Array<ProjectFile>): Array<ProjectFileHash> => {
        let fingerPrintFiles: Array<ProjectFileHash> = [];

        for (let file of files) {
            fingerPrintFiles.push({
                contentHash: md5File.sync(file.path),
                file: file.file,
                path: file.path,
                type: file.type,
            });
        }

        return fingerPrintFiles;
    };

    private md5path = (filePath: string): string => {

        let parts = filePath
            .substr(0, filePath.lastIndexOf(path.sep) + 1)
            .split(path.sep)
            .filter(x => x.length > 0);

        parts = parts
            .map(x => {
                let md5sum = crypto.createHash('md5');
                md5sum.update(x, 'utf8');
                return md5sum.digest('hex').substr(0, 7);
            });

        filePath = parts.reduce((prev, current) => prev + path.sep + current, '');

        return filePath;
    };
}