package com.prinova.messagepoint.model.wrapper;

import java.util.List;
import java.util.stream.Collectors;

import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.dataadmin.LookupTable;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import org.springframework.web.util.HtmlUtils;

import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.common.ContentSelectionStatusType;
import com.prinova.messagepoint.model.common.SyncObjectType;
import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.version.VersionedInstance;
import com.prinova.messagepoint.util.ApplicationUtil;

public class AsyncProjectSyncObjectListVO extends AsyncListVO{
    public static final long CONFLICT_MODEL_CHANGE = 0x10000000L;
    public static final long CONFLICT_CUST_CHANGE   = 0x20000000L;
	private static final IdentifiableMessagePointModel projectStatusWorkingCopy = null;

	private Object							object;
	private SyncObjectType					objectType;
	private Long							objectStatus;

	private String							name;

	private ContentSelectionStatusType		projectStatusActiveCopy;
	private ContentSelectionStatusType		originStatusActiveCopy;

	private ContentSelectionStatusType		projectStatus;
	private ContentSelectionStatusType		originStatus;
	private boolean							syncRequest;
	private boolean							syncFromOrigin;

	private boolean							conflictsActiveCopy = false;
	private boolean							conflictsWorkingCopy = false;
	private boolean                         conflictsExchange = false;

	private boolean                         myTargetWorkingCopy =  true;

	private VersionedInstance				projectInstanceActiveCopy;
	private VersionedInstance				originInstanceActiveCopy;

	private VersionedInstance				projectInstanceWorkingCopy;
	private VersionedInstance				originInstanceWorkingCopy;

	private String                          projectSchema;
	private String                          originSchema;
	private Long                            originNodeId;

	private Long                            projectObjectId;
	private Long                            originObjectId;

	private Long                            sourceObjectId = null;
	private Long                            sourceInstanceId = null;
	private boolean                         sourceHasDependencies = false;
	private List<String>                    sourceDependenciesNeedCreatingIDs = null;
    private List<String>                    sourceDependenciesAlwaysSyncIDs = null;
	private boolean                         targetIsReferenced = false;
	private Long                            targetInstanceId = null;
	private Long                            targetObjectId = null;
	private List<String>                    targetDependenciesIDs = null;

	public AsyncProjectSyncObjectListVO(){
		super();
	}

	// Setters

	public void setObject(Object object) {
		this.object = object;
	}

	public void setObjectType(SyncObjectType objectType) {
		this.objectType = objectType;
	}

	public void setObjectStatus(Long objectStatus) {
		this.objectStatus = objectStatus;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setProjectStatus(ContentSelectionStatusType projectStatus) {
		this.projectStatus = projectStatus;
	}

	public void setOriginStatus(ContentSelectionStatusType originStatus) {
		this.originStatus = originStatus;
	}

	public void setSyncRequest(boolean syncRequest) {
		this.syncRequest = syncRequest;
	}

	public void setSyncFromOrigin(boolean syncFromOrigin) {
		this.syncFromOrigin = syncFromOrigin;
	}

	public void setConflictsActiveCopy(boolean conflictsActiveCopy) {
		this.conflictsActiveCopy = conflictsActiveCopy;
	}

	public void setConflictsWorkingCopy(boolean conflictsWorkingCopy) {
		this.conflictsWorkingCopy = conflictsWorkingCopy;
	}

	public void setMyTargetWorkingCopy(boolean myTargetWorkingCopy) {
	    this.myTargetWorkingCopy = myTargetWorkingCopy;
	}

	public void setProjectStatusActiveCopy(ContentSelectionStatusType projectStatus) {
		this.projectStatusActiveCopy = projectStatus;
	}

	public void setOriginStatusActiveCopy(ContentSelectionStatusType originStatus) {
		this.originStatusActiveCopy = originStatus;
	}

	public void setSourceObjectId(Long sourceObjectId) {
		this.sourceObjectId = sourceObjectId;
	}

	public void setSourceInstanceId(Long sourceInstanceId) {
		this.sourceInstanceId = sourceInstanceId;
	}

    private String getConflictColorCode() {
        String colorCode = "#944";
        if(conflictsExchange) {
/*
            Long conflictStatus = objectStatus & (CONFLICT_MODEL_CHANGE|CONFLICT_CUST_CHANGE);
            if(conflictStatus == CONFLICT_MODEL_CHANGE) colorCode = "#ff4040";
            else if(conflictStatus == CONFLICT_CUST_CHANGE) colorCode = "#ff4040";
            else if(conflictStatus == (CONFLICT_MODEL_CHANGE | CONFLICT_CUST_CHANGE)) colorCode = "#ff4040";
*/
            colorCode = "#ff4040";
        }
        return colorCode;
    }

    private String getConflictDots() {
        String dots = "";
        if(conflictsExchange) {
            dots = "border-bottom: 1px dotted black;";
        }
        return dots;
    }

	// Getters for JSON
	//
	public String getObjectName() {
		String objectNameHTML = "<input type=\"hidden\" class=\"objectTypeId\" value=\""+objectType.getId()+"\" />";
		if (conflictsWorkingCopy || conflictsActiveCopy || !myTargetWorkingCopy || conflictsExchange)
			return objectNameHTML + "<span style='color: " + getConflictColorCode() + ";" + getConflictDots() + "' " + getConflictExchangeMsg() + " >" + objectType.getDisplayText() + "</span>";
		return objectNameHTML + objectType.getDisplayText();
	}

	private String getConflictExchangeMsg() {
	    long conflictStatus = objectStatus & (CONFLICT_MODEL_CHANGE|CONFLICT_CUST_CHANGE);
	    String msg = "";
	    if(conflictsExchange) {
    	    if(conflictStatus == CONFLICT_MODEL_CHANGE) msg = ApplicationUtil.getMessage("page.text.conflict.model.change");
	        else if(conflictStatus == CONFLICT_CUST_CHANGE) msg = ApplicationUtil.getMessage("page.text.conflict.cust.change");
            else if(conflictStatus == (CONFLICT_MODEL_CHANGE | CONFLICT_CUST_CHANGE)) msg = ApplicationUtil.getMessage("page.text.conflict.model.and.cust.change");
    	    msg = "title=\"" + HtmlUtils.htmlEscape(msg) + "\"";
	    }
	    return msg;
	}

	public String getName() {
		String nameHTML = "<div style=\"max-width: 350px;\"><span style=\"white-space: normal;\" class=\"searchContainer\" full_name=\"" + name + "\">";
		if (conflictsWorkingCopy || conflictsActiveCopy || conflictsExchange)
			nameHTML += "<span style='white-space: normal; color: " + getConflictColorCode() + ";" + getConflictDots() + "' >" + name + "</span>";
		else
			nameHTML += name;
		nameHTML += "</span></div>";
		return nameHTML;
	}

	public String getTrueName() {
	    return name.trim();
	}

	private String getIdFromTypeAndIDString(String typeAndIDString) {
	    String [] components = typeAndIDString.split("[-]");
	    return components[1];
    }

	public String getSyncCheckBoxRequest() {
        boolean isNew = false;
        if(syncFromOrigin) {
            if((projectStatusActiveCopy.getId() == ContentSelectionStatusType.ID_NOT_APPLICABLE || projectStatusActiveCopy.getId() == ContentSelectionStatusType.ID_ARCHIVED)
                    && (projectStatus.getId() == ContentSelectionStatusType.ID_NOT_APPLICABLE || projectStatus.getId() == ContentSelectionStatusType.ID_ACTIVATED || projectStatus.getId() == ContentSelectionStatusType.ID_DELETED)) {
                isNew = true;
            }
        } else {
            if((originStatusActiveCopy.getId() == ContentSelectionStatusType.ID_NOT_APPLICABLE || originStatusActiveCopy.getId() == ContentSelectionStatusType.ID_ARCHIVED)
                    && (originStatus.getId() == ContentSelectionStatusType.ID_NOT_APPLICABLE || originStatus.getId() == ContentSelectionStatusType.ID_ACTIVATED || originStatus.getId() == ContentSelectionStatusType.ID_DELETED)) {
                isNew = true;
            }
        }

        String valueAttrExpression = " value='" + objectType.getId() + "_" + ((IdentifiableMessagePointModel)this.object).getId() + "_" + objectStatus;

	    String checkboxHtml = "";

	    try {
            if(targetIsReferenced && (! (object instanceof TargetGroup)) && (! (object instanceof ConditionElement)) && (! (object instanceof ParameterGroup)) && (! (object instanceof DataElementVariable)) && (! (object instanceof DataSource))&& (! (object instanceof DataSourceAssociation))) {
                checkboxHtml = "<span style='color: #944;'><i id=\"targetIsReferenced_" + ((IdentifiableMessagePointModel)this.object).getId() + "\" " +
                        "title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.text.cannot.sync.object.referenced") + "</div>\" " +
                        "reportLabel=\"(" + ApplicationUtil.getMessage("page.text.cannot.sync.object.referenced") + ")\" class=\"fa fa-mp-state txtFmtTip fa-ban\" >" +
                        "&nbsp;" +
                        "</i></span>";
            } else if(myTargetWorkingCopy) {
                // Temporary disable preselect checked.
                boolean usePreselectChecked = false;
                usePreselectChecked = true; // FB14887
                String dependenciesIDsString = null;
                if(sourceHasDependencies && sourceDependenciesNeedCreatingIDs != null && !sourceDependenciesNeedCreatingIDs.isEmpty()) {
                    dependenciesIDsString = sourceDependenciesNeedCreatingIDs.stream().map(typeAndId->getIdFromTypeAndIDString(typeAndId)).collect(Collectors.joining("|"));
                }
                String dependenciesAlwaysSyncIDsString = null;
                if(sourceHasDependencies && sourceDependenciesAlwaysSyncIDs != null && !sourceDependenciesAlwaysSyncIDs.isEmpty()) {
                    dependenciesAlwaysSyncIDsString = sourceDependenciesAlwaysSyncIDs.stream().map(typeAndId->getIdFromTypeAndIDString(typeAndId)).collect(Collectors.joining("|"));
                }
                checkboxHtml = "<input id='objectItemCheck_"+((IdentifiableMessagePointModel)this.object).getId()+"' class='objectItemCheck myTargetCheck' type='checkbox' isSafe=\"" + syncRequest + "\" " + ((usePreselectChecked && syncRequest)?"checked":"") +
                        (objectType.getId() == SyncObjectType.ID_VARIANT ? " onclick='return false'" : "") +
                        " sourceObjectId=\"" + sourceObjectId + "\"" +
                        (dependenciesIDsString == null ? "" : " referencedObjectIDs=\"|" + dependenciesIDsString + "|\"") +
                        (dependenciesAlwaysSyncIDsString == null ? "" : " referencedAlwaysSyncObjectIDs=\"|" + dependenciesAlwaysSyncIDsString + "|\"") +
                        (isNew ? " isNew=true " : "" ) +
                        valueAttrExpression +
                        "' style='' name='selectedObjects'>";
            } else {
                boolean usePreselectChecked = false;
                usePreselectChecked = true; // FB14887
                String dependenciesIDsString = null;
                if(sourceHasDependencies && sourceDependenciesNeedCreatingIDs != null && !sourceDependenciesNeedCreatingIDs.isEmpty()) {
                    dependenciesIDsString = sourceDependenciesNeedCreatingIDs.stream().map(typeAndId->getIdFromTypeAndIDString(typeAndId)).collect(Collectors.joining("|"));
                }
                String dependenciesAlwaysSyncIDsString = null;
                if(sourceHasDependencies && sourceDependenciesAlwaysSyncIDs != null && !sourceDependenciesAlwaysSyncIDs.isEmpty()) {
                    dependenciesAlwaysSyncIDsString = sourceDependenciesAlwaysSyncIDs.stream().map(typeAndId->getIdFromTypeAndIDString(typeAndId)).collect(Collectors.joining("|"));
                }

                checkboxHtml = "<span style='color: #944;'>";

                checkboxHtml += "<i id=\"differentAssignedUser_" + ((IdentifiableMessagePointModel)this.object).getId() + "\" " +
                        "title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.text.touchpoint.content.not.assigned.to.user") + "</div>\" " +
                        "reportLabel=\"(" + ApplicationUtil.getMessage("page.text.touchpoint.content.not.assigned.to.user") + ")\" class=\"fa fa-mp-state txtFmtTip fa-ban\" >" +
                        "&nbsp;" +
                        "</i>";
                checkboxHtml += "<input id='objectItemCheck_"+((IdentifiableMessagePointModel)this.object).getId()+"' style='display: none;' class='objectItemCheck differentUserCheck' type='checkbox' isSafe=\"" + syncRequest + "\" " + ((usePreselectChecked && syncRequest)?"checked":"") +
                        (objectType.getId() == SyncObjectType.ID_VARIANT ? " onclick='return false'" : "") +
                        " sourceObjectId=\"" + sourceObjectId + "\"" +
                        (dependenciesIDsString == null ? "" : " referencedObjectIDs=\"|" + dependenciesIDsString + "|\"") +
                        (dependenciesAlwaysSyncIDsString == null ? "" : " referencedAlwaysSyncObjectIDs=\"|" + dependenciesAlwaysSyncIDsString + "|\"") +
                        (isNew ? " isNew=true " : "" ) +
                        valueAttrExpression +
                        "' style='' name='selectedObjects'>";

                checkboxHtml += "</span>";
            }
        } catch (Exception ex) {
	        throw ex;
        }

		return checkboxHtml;
	}

    public String getSyncCheckBoxHide() {
        boolean isNew = false;
        if(syncFromOrigin) {
            if((projectStatusActiveCopy.getId() == ContentSelectionStatusType.ID_NOT_APPLICABLE || projectStatusActiveCopy.getId() == ContentSelectionStatusType.ID_ARCHIVED)
                    && (projectStatus.getId() == ContentSelectionStatusType.ID_NOT_APPLICABLE || projectStatus.getId() == ContentSelectionStatusType.ID_ACTIVATED || projectStatus.getId() == ContentSelectionStatusType.ID_DELETED)) {
                isNew = true;
            }
        } else {
            if((originStatusActiveCopy.getId() == ContentSelectionStatusType.ID_NOT_APPLICABLE || originStatusActiveCopy.getId() == ContentSelectionStatusType.ID_ARCHIVED)
                    && (originStatus.getId() == ContentSelectionStatusType.ID_NOT_APPLICABLE || originStatus.getId() == ContentSelectionStatusType.ID_ACTIVATED || originStatus.getId() == ContentSelectionStatusType.ID_DELETED)) {
                isNew = true;
            }
        }

        String valueAttrExpression = " value='" + objectType.getId() + "_" + ((IdentifiableMessagePointModel)this.object).getId() + "_" + objectStatus;

        String checkboxHtml = "";

        if(! isNew) {

            if (targetIsReferenced && (!(object instanceof TargetGroup)) && (!(object instanceof ConditionElement)) && (!(object instanceof ParameterGroup)) && (!(object instanceof DataElementVariable)) && (!(object instanceof DataSource))  && (!(object instanceof DataSourceAssociation))  ) {

            } else if (myTargetWorkingCopy) {
                // Temporary disable preselect checked.
                boolean usePreselectChecked = false;
                usePreselectChecked = true; // FB14887
                String dependenciesIDsString = null;
                if (sourceHasDependencies && sourceDependenciesNeedCreatingIDs != null && !sourceDependenciesNeedCreatingIDs.isEmpty()) {
                    dependenciesIDsString = sourceDependenciesNeedCreatingIDs.stream().map(typeAndId -> getIdFromTypeAndIDString(typeAndId)).collect(Collectors.joining("|"));
                }
                String dependenciesAlwaysSyncIDsString = null;
                if(sourceHasDependencies && sourceDependenciesAlwaysSyncIDs != null && !sourceDependenciesAlwaysSyncIDs.isEmpty()) {
                    dependenciesAlwaysSyncIDsString = sourceDependenciesAlwaysSyncIDs.stream().map(typeAndId->getIdFromTypeAndIDString(typeAndId)).collect(Collectors.joining("|"));
                }

                checkboxHtml = "<input id='objectItemHide_" + ((IdentifiableMessagePointModel) this.object).getId() + "' class='objectItemHide myTargetCheck' type='checkbox' isSafe=\"" + syncRequest + "\" " +
                        (objectType.getId() == SyncObjectType.ID_VARIANT ? " onclick='return false'" : "") +
                        " sourceObjectId=\"" + sourceObjectId + "\"" +
                        (dependenciesIDsString == null ? "" : " referencedObjectIDs=\"|" + dependenciesIDsString + "|\"") +
                        (dependenciesAlwaysSyncIDsString == null ? "" : " referencedAlwaysSyncObjectIDs=\"|" + dependenciesAlwaysSyncIDsString + "|\"") +
                        (isNew ? " isNew=true " : "") +
                        valueAttrExpression +
                        "' style='' name='hideObjects'>";
            } else {
                boolean usePreselectChecked = false;
                usePreselectChecked = true; // FB14887
                String dependenciesIDsString = null;
                if (sourceHasDependencies && sourceDependenciesNeedCreatingIDs != null && !sourceDependenciesNeedCreatingIDs.isEmpty()) {
                    dependenciesIDsString = sourceDependenciesNeedCreatingIDs.stream().map(typeAndId -> getIdFromTypeAndIDString(typeAndId)).collect(Collectors.joining("|"));
                }
                String dependenciesAlwaysSyncIDsString = null;
                if(sourceHasDependencies && sourceDependenciesAlwaysSyncIDs != null && !sourceDependenciesAlwaysSyncIDs.isEmpty()) {
                    dependenciesAlwaysSyncIDsString = sourceDependenciesAlwaysSyncIDs.stream().map(typeAndId->getIdFromTypeAndIDString(typeAndId)).collect(Collectors.joining("|"));
                }

                checkboxHtml = "<span style='color: #944;'>";

                checkboxHtml += "<input id='objectItemHide_" + ((IdentifiableMessagePointModel) this.object).getId() + "' style='display: none;' class='objectItemCheck differentUserCheck' type='checkbox' isSafe=\"" + syncRequest + "\" " +
                        (objectType.getId() == SyncObjectType.ID_VARIANT ? " onclick='return false'" : "") +
                        " sourceObjectId=\"" + sourceObjectId + "\"" +
                        (dependenciesIDsString == null ? "" : " referencedObjectIDs=\"|" + dependenciesIDsString + "|\"") +
                        (dependenciesAlwaysSyncIDsString == null ? "" : " referencedAlwaysSyncObjectIDs=\"|" + dependenciesAlwaysSyncIDsString + "|\"") +
                        (isNew ? " isNew=true " : "") +
                        valueAttrExpression +
                        "' style='' name='hideObjects'>";

                checkboxHtml += "</span>";
            }
        }

        return checkboxHtml;
    }

    public String getProjectStatusText() {
	    if (object instanceof ContentObject)
            return CloneHelper.queryInSchema(projectSchema, ()->{
                if(projectObjectId != null) {
                    ContentObject contentObject = ContentObject.findById(projectObjectId);
                    if (contentObject != null) {
                        return insertContentObjectState((ContentObject) contentObject, projectStatusActiveCopy, projectStatus);
                    }
                }
                return "";
            });
        if (object instanceof LookupTable)
            return CloneHelper.queryInSchema(projectSchema, ()->insertLookupTableState((LookupTableInstance) projectInstanceActiveCopy, projectStatusActiveCopy, (LookupTableInstance) projectInstanceWorkingCopy, projectStatus));
		if (projectStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE)
			return projectStatus.getDisplayText();

		return "";
	}

	public String getOriginStatusText() {
        if (object instanceof ContentObject)
            return CloneHelper.queryInSchema(originSchema, ()->{
                if(originObjectId != null) {
                    ContentObject originObject = ContentObject.findById(originObjectId);
                    if (originObject != null) {
                        return insertContentObjectState((ContentObject) originObject, originStatusActiveCopy, originStatus);
                    }
                }
                return "";
            });

        if (object instanceof LookupTable)
            return CloneHelper.queryInSchema(originSchema, ()->insertLookupTableState((LookupTableInstance) originInstanceActiveCopy, originStatusActiveCopy, (LookupTableInstance) originInstanceWorkingCopy, originStatus));

		if (originStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE)
			return originStatus.getDisplayText();

		return "";
	}

/*
	private String insertMessageRemakeHash(String projectSchema, MessageInstance projectInstanceActiveCopy, MessageInstance projectInstanceWorkingCopy, String originSchema, MessageInstance originInstanceActiveCopy, MessageInstance originInstanceWorkingCopy) {
        if (object instanceof Message) {
            String objectType = "message";
//            if(projectInstanceWorkingCopy != null && originInstanceWorkingCopy != null) {
                 return "<div style=\"width: 60px; padding-left: 3px; display: inline-block; text-align: center;\"><button class=\"btn btn-outline-dark buttonTagBtn\" id='objectItemRemakeHash_"+((IdentifiableMessagePointModel)this.object).getId()+"' type='button' onclick='javascript:remakeHash(this);return false;' " +
                 " value='"+objectType + "_" + ((IdentifiableMessagePointModel)this.object).getId() + "_" + objectStatus +"' ></div>";
//            }
        }

	    return "";
	}
*/

    public String getRemakeHash(){
/*
        if (object instanceof Message) {
            return insertMessageRemakeHash(projectSchema, (MessageInstance) projectInstanceActiveCopy, (MessageInstance) projectInstanceWorkingCopy, originSchema, (MessageInstance) originInstanceActiveCopy, (MessageInstance) originInstanceWorkingCopy);
        }

 */
        return "";
    }

    public String getSyncContentCompareNew(){
        return "";
    }

    private String getContentObjectType(ContentObject contentObject) {
        if(contentObject.isMessage()) {
            return "message";
        } else if(contentObject.isLocalSmartText()) {
            return "localSmartText";
        } else if(contentObject.isLocalImage()) {
            return "localImage";
        } else if(contentObject.isGlobalSmartText()) {
            return "smartText";
        } else if(contentObject.isGlobalImage()) {
            return "image";
        }
        return null;
    }


//    public String getSyncContentCompare(){
//		String contentCompareHtml = "";
//        String objectType = "message";
//        if (object instanceof LookupTable)
//            objectType = "lookupTable";
//		if (object instanceof TargetGroup)
//			objectType = "targetGroup";
//		if (object instanceof ConditionElement)
//			objectType = "targetRule";
//		if (object instanceof ParameterGroup)
//			objectType = "parameterGroup";
//        if (object instanceof DocumentSettingsModel)
//            objectType = "documentSettings";
//        if (object instanceof DataElementVariable)
//            objectType = "variable";
//        if (object instanceof DataSource)
//            objectType = "dataSource";
//        if (object instanceof DataSourceAssociation)
//            objectType = "dataCollection";
//        if (object instanceof ContentObject)
//            objectType = getContentObjectType((ContentObject) object);
//
//		if(syncFromOrigin){	// Sync from origin
//			if(object instanceof TargetGroup) {
//				if(projectStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && originStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE) {
//					TargetGroup targetGroup = (TargetGroup) object;
//		            contentCompareHtml += "<i id=\"differencesList_"+ projectObjectId +"\" compareToId=\""+originObjectId+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus + "\" "
//		                    + "class=\"differencesList txtFmtTip fa fa-pencil \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.compare.definition") + "</div>\" " + "></i>";
//				}
//			} else if(object instanceof ConditionElement) {
//				if(projectStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && originStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE) {
//					ConditionElement targetRule = (ConditionElement) object;
//		            contentCompareHtml += "<i id=\"differencesList_"+ projectObjectId +"\" compareToId=\""+originObjectId+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus + "\" "
//		                    + "class=\"differencesList txtFmtTip fa fa-pencil \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.compare.definition") + "</div>\" " + "></i>";
//				}
//			} else if(object instanceof ParameterGroup) {
//				if(projectStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && originStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE) {
//					ParameterGroup parameterGroup = (ParameterGroup) object;
//		            contentCompareHtml += "<i id=\"differencesList_"+ projectObjectId +"\" compareToId=\""+originObjectId+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus + "\" "
//		                    + "class=\"differencesList txtFmtTip fa fa-pencil \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.compare.definition") + "</div>\" " + "></i>";
//				}
//            } else if(object instanceof DocumentSettingsModel) {
//                if(projectStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && originStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE) {
//                    DocumentSettingsModel documentSettings = (DocumentSettingsModel) object;
//                    contentCompareHtml += "<i id=\"differencesList_"+ projectObjectId +"\" compareToId=\""+originObjectId+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus + "\" "
//                            + "class=\"differencesList txtFmtTip fa fa-pencil \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.compare.definition") + "</div>\" " + "></i>";
//                }
//            } else if(object instanceof DataElementVariable) {
//                if(projectStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && originStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE) {
//                    DataElementVariable variable = (DataElementVariable) object;
//                    contentCompareHtml += "<i id=\"differencesList_"+ projectObjectId +"\" compareToId=\""+originObjectId+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus + "\" "
//                            + "class=\"differencesList txtFmtTip fa fa-pencil \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.compare.definition") + "</div>\" " + "></i>";
//                }
//            } else if(object instanceof DataSource) {
//                if(projectStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && originStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE) {
//                    DataSource dataSource = (DataSource) object;
//                    contentCompareHtml += "<i id=\"differencesList_"+ projectObjectId +"\" compareToId=\""+originObjectId+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus + "\" "
//                            + "class=\"differencesList txtFmtTip fa fa-pencil \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.compare.definition") + "</div>\" " + "></i>";
//                }
//            } else if(object instanceof DataSourceAssociation) {
//                if(projectStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && originStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE) {
//                    DataSourceAssociation dataSourceAssociation = (DataSourceAssociation) object;
//                    contentCompareHtml += "<i id=\"differencesList_"+ projectObjectId +"\" compareToId=\""+originObjectId+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus + "\" "
//                            + "class=\"differencesList txtFmtTip fa fa-pencil \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.compare.definition") + "</div>\" " + "></i>";
//                }
//            } else if(object instanceof LookupTable) {
//                if(projectStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && projectStatus.getId() != ContentSelectionStatusType.ID_DELETED && projectStatus.getId() != ContentSelectionStatusType.ID_ACTIVATED) {
//                    if(originStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && originStatus.getId() != ContentSelectionStatusType.ID_DELETED && originStatus.getId() != ContentSelectionStatusType.ID_ACTIVATED) {
//                        if(originStatus.getId() == ContentSelectionStatusType.ID_CHANGED || originStatus.getId() == ContentSelectionStatusType.ID_FIRST_TIME_SYNC || originStatus.getId() == ContentSelectionStatusType.ID_NEW || originStatus.getId() == ContentSelectionStatusType.ID_DIFFERENTLANGUAGES) {
//                            contentCompareHtml += "<i id=\"differencesList_"+projectInstanceWorkingCopy+"\" compareToId=\""+originInstanceWorkingCopy+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus + "\" "
//                                    + "class=\"differencesList txtFmtTip fa fa-pencil \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.status.wip") + "</div>\" " + "></i>";
//                        }
//                    }
//                }
//
//                if(projectStatusActiveCopy.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && projectStatusActiveCopy.getId() != ContentSelectionStatusType.ID_DELETED && projectStatusActiveCopy.getId() != ContentSelectionStatusType.ID_ARCHIVED) {
//                    if(originStatusActiveCopy.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && originStatusActiveCopy.getId() != ContentSelectionStatusType.ID_DELETED && originStatusActiveCopy.getId() != ContentSelectionStatusType.ID_ARCHIVED) {
//                        if(originStatusActiveCopy.getId() == ContentSelectionStatusType.ID_CHANGED || originStatusActiveCopy.getId() == ContentSelectionStatusType.ID_FIRST_TIME_SYNC || originStatusActiveCopy.getId() == ContentSelectionStatusType.ID_NEW || originStatusActiveCopy.getId() == ContentSelectionStatusType.ID_DIFFERENTLANGUAGES) {
//                            contentCompareHtml += "<i id=\"differencesList_"+projectInstanceActiveCopy+"\" compareToId=\""+originInstanceActiveCopy+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus+ "\" "
//                                    + "class=\"differencesList txtFmtTip fa fa-check \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.status.production") + "</div>\" " + "></i>";
//                        }
//                    }
//                }
//			} else if(object instanceof ContentObject) {
//			    if(projectStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && projectStatus.getId() != ContentSelectionStatusType.ID_DELETED && projectStatus.getId() != ContentSelectionStatusType.ID_ACTIVATED) {
//				    if(originStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && originStatus.getId() != ContentSelectionStatusType.ID_DELETED && originStatus.getId() != ContentSelectionStatusType.ID_ACTIVATED) {
//				    	if(originStatus.getId() == ContentSelectionStatusType.ID_CHANGED || originStatus.getId() == ContentSelectionStatusType.ID_FIRST_TIME_SYNC || originStatus.getId() == ContentSelectionStatusType.ID_NEW || originStatus.getId() == ContentSelectionStatusType.ID_DIFFERENTLANGUAGES) {
//				    	    ContentObjectData projectInstanceWorkingCopyData = (ContentObjectData) projectInstanceWorkingCopy;
//				    	    ContentObjectData originInstanceWorkingCopyData = (ContentObjectData) originInstanceWorkingCopy;
//				            contentCompareHtml += "<i id=\"differencesList_"+projectInstanceWorkingCopyData.getContentObject().getId()+"\" dataType=\""+ projectInstanceWorkingCopyData.getDataType() + "\" compareToId=\""+originInstanceWorkingCopyData.getContentObject().getId()+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus + "\" "
//				                    + "class=\"differencesList txtFmtTip fa fa-pencil \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.compare.wip") + "</div>\" " + "></i>";
//				    	}
//			        }
//			    }
//
//			    if(projectStatusActiveCopy.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && projectStatusActiveCopy.getId() != ContentSelectionStatusType.ID_DELETED && projectStatusActiveCopy.getId() != ContentSelectionStatusType.ID_ARCHIVED) {
//				    if(originStatusActiveCopy.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && originStatusActiveCopy.getId() != ContentSelectionStatusType.ID_DELETED && originStatusActiveCopy.getId() != ContentSelectionStatusType.ID_ARCHIVED) {
//			            if(originStatusActiveCopy.getId() == ContentSelectionStatusType.ID_CHANGED || originStatusActiveCopy.getId() == ContentSelectionStatusType.ID_FIRST_TIME_SYNC || originStatusActiveCopy.getId() == ContentSelectionStatusType.ID_NEW || originStatusActiveCopy.getId() == ContentSelectionStatusType.ID_DIFFERENTLANGUAGES) {
//                            ContentObjectData projectInstanceActiveCopyData = (ContentObjectData) projectInstanceActiveCopy;
//                            ContentObjectData originInstanceActiveCopyData = (ContentObjectData) originInstanceActiveCopy;
//			                contentCompareHtml += "<i id=\"differencesList_"+projectInstanceActiveCopyData.getContentObject().getId()+"\" dataType=\""+ projectInstanceActiveCopyData.getDataType() +"\" compareToId=\""+originInstanceActiveCopyData.getContentObject().getId()+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus+ "\" "
//				                    + "class=\"differencesList txtFmtTip fa fa-check \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.compare.production") + "</div>\" " + "></i>";
//			            }
//			        }
//			    }
//			}
//		} else {	// Commit to origin
//			if(object instanceof TargetGroup) {
//				if(projectStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && originStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE) {
//					TargetGroup targetGroup = (TargetGroup) object;
//		            contentCompareHtml += "<i id=\"differencesList_"+ projectObjectId +"\" compareToId=\""+originObjectId+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus + "\" "
//		                    + "class=\"differencesList txtFmtTip fa fa-pencil \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.compare.definition") + "</div>\" " + "></i>";
//				}
//			} else if(object instanceof ConditionElement) {
//				if(projectStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && originStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE) {
//					ConditionElement targetRule = (ConditionElement) object;
//		            contentCompareHtml += "<i id=\"differencesList_"+ projectObjectId +"\" compareToId=\""+originObjectId+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus + "\" "
//		                    + "class=\"differencesList txtFmtTip fa fa-pencil \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.compare.definition") + "</div>\" " + "></i>";
//				}
//			} else if(object instanceof ParameterGroup) {
//				if(projectStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && originStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE) {
//					ParameterGroup parameterGroup = (ParameterGroup) object;
//		            contentCompareHtml += "<i id=\"differencesList_"+ projectObjectId +"\" compareToId=\""+originObjectId+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus + "\" "
//		                    + "class=\"differencesList txtFmtTip fa fa-pencil \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.compare.definition") + "</div>\" " + "></i>";
//				}
//            } else if(object instanceof DocumentSettingsModel) {
//                if(projectStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && originStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE) {
//                    DocumentSettingsModel documentSettings = (DocumentSettingsModel) object;
//                    contentCompareHtml += "<i id=\"differencesList_"+ projectObjectId +"\" compareToId=\""+originObjectId+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus + "\" "
//                            + "class=\"differencesList txtFmtTip fa fa-pencil \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.compare.definition") + "</div>\" " + "></i>";
//                }
//            } else if(object instanceof DataElementVariable) {
//                if(projectStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && originStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE) {
//                    DataElementVariable variable = (DataElementVariable) object;
//                    contentCompareHtml += "<i id=\"differencesList_"+ projectObjectId +"\" compareToId=\""+originObjectId+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus + "\" "
//                            + "class=\"differencesList txtFmtTip fa fa-pencil \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.compare.definition") + "</div>\" " + "></i>";
//                }
//            } else if(object instanceof DataSource) {
//                if(projectStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && originStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE) {
//                    DataSource dataSource = (DataSource) object;
//                    contentCompareHtml += "<i id=\"differencesList_"+ projectObjectId +"\" compareToId=\""+originObjectId+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus + "\" "
//                            + "class=\"differencesList txtFmtTip fa fa-pencil \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.compare.definition") + "</div>\" " + "></i>";
//                }
//            } else if(object instanceof DataSourceAssociation) {
//                if(projectStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && originStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE) {
//                    DataSourceAssociation dataSourceAssociation = (DataSourceAssociation) object;
//                    contentCompareHtml += "<i id=\"differencesList_"+ projectObjectId +"\" compareToId=\""+originObjectId+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus + "\" "
//                            + "class=\"differencesList txtFmtTip fa fa-pencil \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.compare.definition") + "</div>\" " + "></i>";
//                }
//            } else if(object instanceof LookupTable) {
//                if(originStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && originStatus.getId() != ContentSelectionStatusType.ID_DELETED && originStatus.getId() != ContentSelectionStatusType.ID_ACTIVATED) {
//                    if(projectStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && projectStatus.getId() != ContentSelectionStatusType.ID_DELETED && projectStatus.getId() != ContentSelectionStatusType.ID_ACTIVATED) {
//                        if(projectStatus.getId() == ContentSelectionStatusType.ID_CHANGED || projectStatus.getId() == ContentSelectionStatusType.ID_FIRST_TIME_SYNC || projectStatus.getId() == ContentSelectionStatusType.ID_NEW || projectStatus.getId() == ContentSelectionStatusType.ID_DIFFERENTLANGUAGES) {
//                            contentCompareHtml += "<i id=\"differencesList_"+projectInstanceWorkingCopy+"\" compareToId=\""+originInstanceWorkingCopy+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus+ "\" "
//                                    + "class=\"differencesList txtFmtTip fa fa-pencil \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.status.wip") + "</div>\" " + "></i>";
//                        }
//                    }
//                }
//                if(originStatusActiveCopy.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE &&  originStatusActiveCopy.getId() != ContentSelectionStatusType.ID_DELETED && originStatusActiveCopy.getId() != ContentSelectionStatusType.ID_ARCHIVED) {
//                    if(projectStatusActiveCopy.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE &&  projectStatusActiveCopy.getId() != ContentSelectionStatusType.ID_DELETED && projectStatusActiveCopy.getId() != ContentSelectionStatusType.ID_ARCHIVED) {
//                        if(projectStatusActiveCopy.getId() == ContentSelectionStatusType.ID_CHANGED || projectStatusActiveCopy.getId() == ContentSelectionStatusType.ID_FIRST_TIME_SYNC || projectStatusActiveCopy.getId() == ContentSelectionStatusType.ID_NEW || projectStatusActiveCopy.getId() == ContentSelectionStatusType.ID_DIFFERENTLANGUAGES) {
//                            contentCompareHtml += "<i id=\"differencesList_"+projectInstanceActiveCopy+"\" compareToId=\""+originInstanceActiveCopy+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus+ "\" "
//                                    + "class=\"differencesList txtFmtTip fa fa-check \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.status.production") + "</div>\" " + "></i>";
//                        }
//                    }
//                }
//			} else if(object instanceof ContentObject) {
//			    if(originStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && originStatus.getId() != ContentSelectionStatusType.ID_DELETED && originStatus.getId() != ContentSelectionStatusType.ID_ACTIVATED) {
//				    if(projectStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE && projectStatus.getId() != ContentSelectionStatusType.ID_DELETED && projectStatus.getId() != ContentSelectionStatusType.ID_ACTIVATED) {
//	                    if(projectStatus.getId() == ContentSelectionStatusType.ID_CHANGED || projectStatus.getId() == ContentSelectionStatusType.ID_FIRST_TIME_SYNC || projectStatus.getId() == ContentSelectionStatusType.ID_NEW || projectStatus.getId() == ContentSelectionStatusType.ID_DIFFERENTLANGUAGES) {
//                            ContentObjectData projectInstanceWorkingCopyData = (ContentObjectData) projectInstanceWorkingCopy;
//                            ContentObjectData originInstanceWorkingCopyData = (ContentObjectData) originInstanceWorkingCopy;
//	                    	contentCompareHtml += "<i id=\"differencesList_"+projectInstanceWorkingCopyData.getContentObject().getId()+"\" dataType=\""+ projectInstanceWorkingCopyData.getDataType() +"\" compareToId=\""+originInstanceWorkingCopyData.getContentObject().getId()+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus+ "\" "
//	                    			+ "class=\"differencesList txtFmtTip fa fa-pencil \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.compare.wip") + "</div>\" " + "></i>";
//	                    }
//			        }
//			    }
//			    if(originStatusActiveCopy.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE &&  originStatusActiveCopy.getId() != ContentSelectionStatusType.ID_DELETED && originStatusActiveCopy.getId() != ContentSelectionStatusType.ID_ARCHIVED) {
//				    if(projectStatusActiveCopy.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE &&  projectStatusActiveCopy.getId() != ContentSelectionStatusType.ID_DELETED && projectStatusActiveCopy.getId() != ContentSelectionStatusType.ID_ARCHIVED) {
//	                    if(projectStatusActiveCopy.getId() == ContentSelectionStatusType.ID_CHANGED || projectStatusActiveCopy.getId() == ContentSelectionStatusType.ID_FIRST_TIME_SYNC || projectStatusActiveCopy.getId() == ContentSelectionStatusType.ID_NEW || projectStatusActiveCopy.getId() == ContentSelectionStatusType.ID_DIFFERENTLANGUAGES) {
//                            ContentObjectData projectInstanceActiveCopyData = (ContentObjectData) projectInstanceActiveCopy;
//                            ContentObjectData originInstanceActiveCopyData = (ContentObjectData) originInstanceActiveCopy;
//					    	contentCompareHtml += "<i id=\"differencesList_"+projectInstanceActiveCopyData.getContentObject().getId()+"\" dataType=\""+ projectInstanceActiveCopyData.getDataType() +"\" compareToId=\""+originInstanceActiveCopyData.getContentObject().getId()+"\" compareToNodeId=\"" + originNodeId + "\" objectType=\"" + objectType + "\" objectStatus=\"" + objectStatus+ "\" "
//		                            + "class=\"differencesList txtFmtTip fa fa-check \" style=\"padding-left: 5px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.compare.production") + "</div>\" " + "></i>";
//	                    }
//			        }
//			    }
//			}
//		}
//
//		return contentCompareHtml;
//	}

    public String insertContentObjectState(ContentObject contentObject, ContentSelectionStatusType activeStatus, ContentSelectionStatusType workingStatus)
    {
        StringBuilder versioning = new StringBuilder();

        versioning.append( "<div style=\"width: 80px; padding-left: 3px; display: inline-block;\">" );
        if ( contentObject.hasActiveData()) {
            StringBuilder statusHTML = new StringBuilder();
            String reportLabel 	= contentObject.isSuppressed() ? ApplicationUtil.getMessage("page.label.suppressed")  : ApplicationUtil.getMessage("page.label.active");
            String iconClass 	= contentObject.isSuppressed() ? "suppressIconDiv fa-times" : "activeIconDiv fa-check";
            statusHTML.append("<i id=\"activeLink_").append(contentObject.getId()).append("\" reportLabel=\"(").append(reportLabel).append(")\" class=\"fa fa-mp-state txtFmtTip ").append(iconClass).append("\" ").append("title=\"|<div class='txtFmtTipText'>").append(reportLabel).append("</div>\" ").append("></i>");
            if (conflictsActiveCopy || !myTargetWorkingCopy)
                statusHTML.append("<span style='color: #944;'>").append(activeStatus.getDisplayText()).append("</span>");
            else
                statusHTML.append(activeStatus.getDisplayText());

            versioning.append(statusHTML);
        } else if (contentObject.hasArchivedData()) {
            StringBuilder statusHTML = new StringBuilder();
            statusHTML.append("<i id=\"archiveCopyLink_").append(contentObject.getId()).append("\" ").append("title=\"|<div class='txtFmtTipText'>").append(ApplicationUtil.getMessage("page.label.status.archived")).append("</div>\" ").append("reportLabel=\"(").append(ApplicationUtil.getMessage("page.label.status.archived")).append(")\" class=\"archiveIconDiv fa fa-mp-state txtFmtTip fa-archive\" >").append("&nbsp;").append("</i>");

            if (conflictsActiveCopy || !myTargetWorkingCopy)
                statusHTML.append("<span style='color: #944;'>").append(activeStatus.getDisplayText()).append("</span>");
            else
                statusHTML.append(activeStatus.getDisplayText());

            versioning.append(statusHTML);
        } else {
            if (activeStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE)
            {
                StringBuilder statusHTML = new StringBuilder();
                String reportLabel 	= ApplicationUtil.getMessage("page.label.active");
                String iconClass 	= "activeIconDiv fa-check";
                statusHTML.append("<i id=\"activeLink_0\" reportLabel=\"(").append(reportLabel).append(")\" class=\"fa fa-mp-state txtFmtTip ").append(iconClass).append("\" ").append("title=\"|<div class='txtFmtTipText'>").append(reportLabel).append("</div>\" ").append("></i>");
                if (conflictsActiveCopy || !myTargetWorkingCopy)
                    statusHTML.append("<span style='color: #944;'>").append(activeStatus.getDisplayText()).append("</span>");
                else
                    statusHTML.append(activeStatus.getDisplayText());

                versioning.append(statusHTML);
            }
            else
            {
                versioning.append( "<div class=\"blankStatusIcon\">&nbsp;</div>" );
            }
        }

        versioning.append( "</div><div style=\"padding-left: 8px; display: inline-block;\">" );
        if ( contentObject.hasWorkingData() ) {

            StringBuilder statusHTML = new StringBuilder();
            String reportLabel 	= contentObject.isOnHold() ? ApplicationUtil.getMessage("page.label.hold") : ApplicationUtil.getMessage("page.label.in.process");
            String iconClass 	= contentObject.isOnHold() ? "holdIconDiv fa-pause" : "workingCopyIconDiv fa-pencil";
            if ( contentObject.isPendingApproval() && contentObject.getCurrentWorkflowStep() != null) {
                iconClass = "pendingApprovalIconDiv fa fa-clock";
                reportLabel = ApplicationUtil.getMessage("page.label.pending") + " " + contentObject.getCurrentWorkflowStep().getState();
            }

            statusHTML.append("<i id=\"workingCopyLink_").append(contentObject.getId()).append("\" ").append("title=\"|<div class='txtFmtTipText'>").append(reportLabel).append("</div>\" ").append("reportLabel=\"(").append(reportLabel).append(")\" class=\"txtFmtTip fa fa-mp-state ").append(iconClass).append("\" >").append("</i>");
            if (conflictsWorkingCopy || !myTargetWorkingCopy)
                statusHTML.append("<span style='color: #944;'>").append(workingStatus.getDisplayText()).append("</span>");
            else
                statusHTML.append(workingStatus.getDisplayText());

            versioning.append(statusHTML);
        } else {
            if (workingStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE)
            {
                StringBuilder statusHTML = new StringBuilder();
                String reportLabel 	= ApplicationUtil.getMessage("page.label.in.process");
                String iconClass 	= "workingCopyIconDiv fa-pencil";
                statusHTML.append("<i id=\"workingCopyLink_0\" " + "title=\"|<div class='txtFmtTipText'>").append(reportLabel).append("</div>\" ").append("reportLabel=\"(").append(reportLabel).append(")\" class=\"txtFmtTip fa fa-mp-state ").append(iconClass).append("\" >").append("</i>");
                if (conflictsWorkingCopy || !myTargetWorkingCopy)
                    statusHTML.append("<span style='color: #944;'>").append(workingStatus.getDisplayText()).append("</span>");
                else
                    statusHTML.append(workingStatus.getDisplayText());

                versioning.append(statusHTML);
            }
            else
            {
                versioning.append( "<div class=\"blankStatusIcon\">&nbsp;</div>" );
            }
        }

        versioning.append( "</div>" );

        return versioning.toString();
    }


/*
	public String insertMessageState(MessageInstance messageInstanceActiveCopy, ContentSelectionStatusType activeStatus, MessageInstance messageInstanceWorkingCopy, ContentSelectionStatusType workingStatus)
	{
		StringBuffer versioning = new StringBuffer();

		versioning.append( "<div style=\"width: 80px; padding-left: 3px; display: inline-block;\">" );
		if ( messageInstanceActiveCopy != null && messageInstanceActiveCopy.isArchive() ) {

			StringBuffer statusHTML = new StringBuffer();
			statusHTML.append( "<i id=\"archiveCopyLink_"+messageInstanceActiveCopy.getId()+"\" " +
									"title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.status.archived") + "</div>\" " +
									"reportLabel=\"(" + ApplicationUtil.getMessage("page.label.status.archived") + ")\" class=\"archiveIconDiv fa fa-mp-state txtFmtTip fa-archive\" >" +
									"&nbsp;" +
								"</i>" );

			versioning.append(applyCrossLink(statusHTML, messageInstanceActiveCopy, activeStatus));

		} else if ( messageInstanceActiveCopy != null ) {

			StringBuffer statusHTML = new StringBuffer();
			String reportLabel 	= messageInstanceActiveCopy.getModel().getSuppressed() ? ApplicationUtil.getMessage("page.label.suppressed")  : ApplicationUtil.getMessage("page.label.active");
			String iconClass 	= messageInstanceActiveCopy.getModel().getSuppressed() ? "suppressIconDiv fa-times" : "activeIconDiv fa-check";
			statusHTML.append( "<i id=\"activeLink_"+messageInstanceActiveCopy.getId()+"\" reportLabel=\"(" + reportLabel + ")\" class=\"fa fa-mp-state txtFmtTip " + iconClass + "\" " +
									"title=\"|<div class='txtFmtTipText'>" + reportLabel + "</div>\" " +
									"></i>" );
			if (conflictsActiveCopy || !myTargetWorkingCopy)
				statusHTML.append("<span style='color: #944;'>" + activeStatus.getDisplayText() + "</span>");
			else
				statusHTML.append(activeStatus.getDisplayText());

			versioning.append(applyCrossLink(statusHTML, messageInstanceActiveCopy, activeStatus));
		} else {
			if (activeStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE)
			{
				StringBuffer statusHTML = new StringBuffer();
				String reportLabel 	= ApplicationUtil.getMessage("page.label.active");
				String iconClass 	= "activeIconDiv fa-check";
				statusHTML.append( "<i id=\"activeLink_0\" reportLabel=\"(" + reportLabel + ")\" class=\"fa fa-mp-state txtFmtTip " + iconClass + "\" " +
										"title=\"|<div class='txtFmtTipText'>" + reportLabel + "</div>\" " +
										"></i>" );
				if (conflictsActiveCopy || !myTargetWorkingCopy)
					statusHTML.append("<span style='color: #944;'>" + activeStatus.getDisplayText() + "</span>");
				else
					statusHTML.append(activeStatus.getDisplayText());

				versioning.append(statusHTML);
			}
			else
			{
				versioning.append( "<div class=\"blankStatusIcon\">&nbsp;</div>" );
			}
		}

		versioning.append( "</div><div style=\"padding-left: 8px; display: inline-block;\">" );
		if ( messageInstanceWorkingCopy != null ) {

			StringBuffer statusHTML = new StringBuffer();
			String reportLabel 	= messageInstanceWorkingCopy.getModel().getOnHold() ? ApplicationUtil.getMessage("page.label.hold") : ApplicationUtil.getMessage("page.label.in.process");
			String iconClass 	= messageInstanceWorkingCopy.getModel().getOnHold() ? "holdIconDiv fa-pause" : "workingCopyIconDiv fa-pencil";
			if ( messageInstanceWorkingCopy.isPendingApproval() && messageInstanceWorkingCopy.getCurrentWorkflowStep() != null) {
				iconClass = "pendingApprovalIconDiv fa fa-clock";
				reportLabel = ApplicationUtil.getMessage("page.label.pending") + " " + messageInstanceWorkingCopy.getCurrentWorkflowStep().getState();
			}

			statusHTML.append( "<i id=\"workingCopyLink_"+messageInstanceWorkingCopy.getId()+"\" " +
									"title=\"|<div class='txtFmtTipText'>" + reportLabel + "</div>\" " +
									"reportLabel=\"(" + reportLabel + ")\" class=\"txtFmtTip fa fa-mp-state " + iconClass + "\" >" +
								"</i>" );
			if (conflictsWorkingCopy || !myTargetWorkingCopy)
				statusHTML.append("<span style='color: #944;'>" + workingStatus.getDisplayText() + "</span>");
			else
				statusHTML.append(workingStatus.getDisplayText());

			versioning.append(applyCrossLink(statusHTML, messageInstanceWorkingCopy, workingStatus));
		} else {
			if (workingStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE)
			{
				StringBuffer statusHTML = new StringBuffer();
				String reportLabel 	= ApplicationUtil.getMessage("page.label.in.process");
				String iconClass 	= "workingCopyIconDiv fa-pencil";
				statusHTML.append( "<i id=\"workingCopyLink_0\" " +
										"title=\"|<div class='txtFmtTipText'>" + reportLabel + "</div>\" " +
										"reportLabel=\"(" + reportLabel + ")\" class=\"txtFmtTip fa fa-mp-state " + iconClass + "\" >" +
									"</i>" );
				if (conflictsWorkingCopy || !myTargetWorkingCopy)
					statusHTML.append("<span style='color: #944;'>" + workingStatus.getDisplayText() + "</span>");
				else
					statusHTML.append(workingStatus.getDisplayText());

				versioning.append(statusHTML);
			}
			else
			{
				versioning.append( "<div class=\"blankStatusIcon\">&nbsp;</div>" );
			}
		}

		versioning.append( "</div>" );

		return versioning.toString();
	}
*/

/*
	private StringBuffer applyCrossLink(StringBuffer content, IdentifiableMessagePointModel object, ContentSelectionStatusType status) {

		if ( (!object.getObjectSchemaName().equals(projectSchema)) || (status != null && status.getId() == ContentSelectionStatusType.ID_NOT_APPLICABLE) || object == null )
			return content;

		StringBuffer linkHTML = new StringBuffer();

		String link = null;

		if ( object instanceof MessageInstance ) {
			MessageInstance msgInst = (MessageInstance)object;
			Document msgDocument = msgInst.getDocument();
			if(msgDocument != null) {
				if ( msgInst.getIsTouchpointLocal() ) {
					link = "/touchpoints/local_content_list.form?documentId=" + msgDocument.getId() + "&contentObjectId=" + msgInst.getId() +
							   "&statusViewId=" + (msgInst.isWorkingCopy() ? "1" : "2" ) + "&localContext=" + (msgInst.getContentType().getId() <= 0 ? ContentType.TEXT : msgInst.getContentType().getId()) ;
					if ( msgInst.getIsSharedFreeform() )
						link += "&isFreeform=true";
				} else {
					link = "/touchpoints/touchpoint_content_object_list.form?documentId=" + msgDocument.getId() + "&contentObjectId=" + msgInst.getId() +
							   "&statusViewId=" + (msgInst.isWorkingCopy() ? "1" : "2" );
				}
			}
		}

		if ( link != null ) {
			linkHTML.append("<a style=\"text-decoration: underline;\" href=\"javascript:openCrossLink('" + link + "')\">");
			linkHTML.append(	content);
			linkHTML.append("</a>");
		}

		return linkHTML;
	}
*/
    public String insertLookupTableState(LookupTableInstance LookupTableInstanceActiveCopy, ContentSelectionStatusType activeStatus, LookupTableInstance LookupTableInstanceWorkingCopy, ContentSelectionStatusType workingStatus)
    {
        StringBuilder versioning = new StringBuilder();

        versioning.append( "<div style=\"width: 80px; padding-left: 3px; display: inline-block;\">" );
        if ( LookupTableInstanceActiveCopy != null && LookupTableInstanceActiveCopy.isArchive() ) {
            versioning.append("<i id=\"archiveCopyLink_").append(LookupTableInstanceActiveCopy.getId()).append("\" ").append("title=\"|<div class='txtFmtTipText'>").append(ApplicationUtil.getMessage("page.label.status.archived")).append("</div>\" ").append("reportLabel=\"(").append(ApplicationUtil.getMessage("page.label.status.archived")).append(")\" class=\"archiveIconDiv fa fa-mp-state txtFmtTip fa-archive\" >").append("&nbsp;").append("</i>");
        } else if ( LookupTableInstanceActiveCopy != null ) {
            String reportLabel  = ApplicationUtil.getMessage("page.label.active");
            String iconClass    = "activeIconDiv fa-check";
            versioning.append("<i id=\"activeLink_").append(LookupTableInstanceActiveCopy.getId()).append("\" reportLabel=\"(").append(reportLabel).append(")\" class=\"fa fa-mp-state txtFmtTip ").append(iconClass).append("\" ").append("title=\"|<div class='txtFmtTipText'>").append(reportLabel).append("</div>\" ").append("></i>");
            if (conflictsActiveCopy || !myTargetWorkingCopy)
                versioning.append("<span style='color: #944;'>").append(activeStatus.getDisplayText()).append("</span>");
            else
                versioning.append(activeStatus.getDisplayText());
        } else {
            if (activeStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE)
            {
                String reportLabel  = ApplicationUtil.getMessage("page.label.active");
                String iconClass    = "activeIconDiv fa-check";
                versioning.append("<i id=\"activeLink_0\" reportLabel=\"(").append(reportLabel).append(")\" class=\"fa fa-mp-state txtFmtTip ").append(iconClass).append("\" ").append("title=\"|<div class='txtFmtTipText'>").append(reportLabel).append("</div>\" ").append("></i>");
                if (conflictsActiveCopy || !myTargetWorkingCopy)
                    versioning.append("<span style='color: #944;'>").append(activeStatus.getDisplayText()).append("</span>");
                else
                    versioning.append(activeStatus.getDisplayText());
            }
            else
            {
                versioning.append( "<div class=\"blankStatusIcon\">&nbsp;</div>" );
            }
        }

        versioning.append( "</div><div style=\"padding-left: 8px; display: inline-block;\">" );
        if ( LookupTableInstanceWorkingCopy != null ) {
            String reportLabel  = ApplicationUtil.getMessage("page.label.in.process");
            String iconClass    = "workingCopyIconDiv fa-pencil";
            if ( LookupTableInstanceWorkingCopy.isPendingApproval() && LookupTableInstanceWorkingCopy.getCurrentWorkflowStep() != null) {
                iconClass = "pendingApprovalIconDiv fa fa-clock";
                reportLabel = ApplicationUtil.getMessage("page.label.pending") + " " + LookupTableInstanceWorkingCopy.getCurrentWorkflowStep().getState();
            }

            versioning.append("<i id=\"workingCopyLink_").append(LookupTableInstanceWorkingCopy.getId()).append("\" ").append("title=\"|<div class='txtFmtTipText'>").append(reportLabel).append("</div>\" ").append("reportLabel=\"(").append(reportLabel).append(")\" class=\"txtFmtTip fa fa-mp-state ").append(iconClass).append("\" >").append("</i>");
            if (conflictsWorkingCopy || !myTargetWorkingCopy)
                versioning.append("<span style='color: #944;'>").append(workingStatus.getDisplayText()).append("</span>");
            else
                versioning.append(workingStatus.getDisplayText());
        } else {
            if (workingStatus.getId() != ContentSelectionStatusType.ID_NOT_APPLICABLE)
            {
                String reportLabel  = ApplicationUtil.getMessage("page.label.in.process");
                String iconClass    = "workingCopyIconDiv fa-pencil";
                versioning.append("<i id=\"workingCopyLink_0\" " + "title=\"|<div class='txtFmtTipText'>").append(reportLabel).append("</div>\" ").append("reportLabel=\"(").append(reportLabel).append(")\" class=\"txtFmtTip fa fa-mp-state ").append(iconClass).append("\" >").append("</i>");
                if (conflictsWorkingCopy || !myTargetWorkingCopy)
                    versioning.append("<span style='color: #944;'>").append(workingStatus.getDisplayText()).append("</span>");
                else
                    versioning.append(workingStatus.getDisplayText());
            }
            else
            {
                versioning.append( "<div class=\"blankStatusIcon\">&nbsp;</div>" );
            }
        }

        versioning.append( "</div>" );

        return versioning.toString();
    }

    public String getBinding() {

		// return "<input id='listItemCheck_"+((IdentifiableMessagePointModel)this.object).getId()+"' type='checkbox' value='"+((IdentifiableMessagePointModel)this.object).getId()+
		//		"' style='display : none;' name='selectedIds'><input type='hidden' value='on' name='_selectedIds'>";

		return "";
	}

	public String getProjectSchema() {
		return projectSchema;
	}

//	public String getSyncDependenciesFinder() {
//        String objectType = "message";
///*
//        if (object instanceof Message)
//            objectType = "message";
//
// */
//        if (object instanceof LookupTable)
//            objectType = "lookupTable";
//		if (object instanceof TargetGroup)
//			objectType = "targetGroup";
//		if (object instanceof ConditionElement)
//			objectType = "targetRule";
//		if (object instanceof ParameterGroup)
//			objectType = "parameterGroup";
//        if (object instanceof DocumentSettingsModel)
//            objectType = "documentSettings";
//        if (object instanceof DataElementVariable)
//            objectType = "variable";
//        if (object instanceof DataSource)
//            objectType = "dataSource";
//        if (object instanceof DataSourceAssociation)
//            objectType = "dataCollection";
//
//        boolean isNew = false;
//        if(syncFromOrigin) {
//        	if((projectStatusActiveCopy.getId() == ContentSelectionStatusType.ID_NOT_APPLICABLE || projectStatusActiveCopy.getId() == ContentSelectionStatusType.ID_ARCHIVED)
//            && (projectStatus.getId() == ContentSelectionStatusType.ID_NOT_APPLICABLE || projectStatus.getId() == ContentSelectionStatusType.ID_ACTIVATED || projectStatus.getId() == ContentSelectionStatusType.ID_DELETED)) {
//        		isNew = true;
//        	}
//        } else {
//        	if((originStatusActiveCopy.getId() == ContentSelectionStatusType.ID_NOT_APPLICABLE || originStatusActiveCopy.getId() == ContentSelectionStatusType.ID_ARCHIVED)
//            && (originStatus.getId() == ContentSelectionStatusType.ID_NOT_APPLICABLE || originStatus.getId() == ContentSelectionStatusType.ID_ACTIVATED || originStatus.getId() == ContentSelectionStatusType.ID_DELETED)) {
//        		isNew = true;
//        	}
//        }
//
//        boolean isSafe = syncRequest;
//
//		StringBuilder dependenciesFinder = new StringBuilder();
//
//        String dependenciesTypeIDsString = null;
//        if(sourceHasDependencies && sourceDependenciesNeedCreatingIDs != null && sourceDependenciesNeedCreatingIDs.size() > 0) {
//        	dependenciesTypeIDsString = sourceDependenciesNeedCreatingIDs.stream().collect(Collectors.joining(","));
//        }
//        String dependenciesAlwaysSyncTypeIDsString = null;
//        if(sourceHasDependencies && sourceDependenciesAlwaysSyncIDs != null && sourceDependenciesAlwaysSyncIDs.size() > 0) {
//            dependenciesAlwaysSyncTypeIDsString = sourceDependenciesAlwaysSyncIDs.stream().collect(Collectors.joining(","));
//        }
//
//		dependenciesFinder.append( "<div class=\"dependencyContainer\" style=\"margin: auto; width:100%; padding-left: 0px; display: inline-block;\" sourceObjectId=\"" + sourceObjectId + "\" " +
//				((targetObjectId == null) ? "" : (" targetObjectId=\"" + targetObjectId + "\" targetInstanceId=\"" + targetInstanceId + "\" " )) +
//				"isNew=\"" + isNew + "\" isSafe=\"" + isSafe + "\" >" );
//
//		if(object instanceof TargetGroup) {
//			if(sourceHasDependencies) {
//				dependenciesFinder.append( "<i id=\"objectItemFindDependencies_"+((IdentifiableMessagePointModel)this.object).getId()+"\" value='" + objectType + "_" + ((IdentifiableMessagePointModel)this.object).getId() + "_" + objectStatus + "' " +
//						"sourceObjectId=\"" + sourceObjectId + "\" sourceInstanceId=\"" + sourceInstanceId + "\" objectType=\"" + objectType + "\" " +
//						(dependenciesTypeIDsString == null ? "" : "sourceReferencingObjectIds=\"" + dependenciesTypeIDsString + "\"") +
//                        (dependenciesAlwaysSyncTypeIDsString == null ? "" : " sourceReferencingAlwaysSyncObjectIds=\"" + dependenciesAlwaysSyncTypeIDsString + "\"") +
//				        "class=\"findReferencingDependencies txtFmtTip fa fa-box-open \" style=\"padding-left: 0px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.find.dependencies") + "</div>\" " + "></i>"
//		            );
//			}
//
//			if(sourceHasDependencies && targetIsReferenced) {
//				dependenciesFinder.append(" ");
//			}
//
//			if(targetIsReferenced) {
//				String dependenciesIDsString = targetDependenciesIDs.stream().collect(Collectors.joining(","));
//				dependenciesFinder.append( "<i id=\"objectItemFindDependencies_"+((IdentifiableMessagePointModel)this.object).getId()+"\" value='" + objectType + "_" + ((IdentifiableMessagePointModel)this.object).getId() + "_" + objectStatus + "' " +
//						"sourceObjectId=\"" + sourceObjectId + "\" sourceInstanceId=\"" + sourceInstanceId + "\" objectType=\"" + objectType + "\" " +
//						"targetObjectId=\"" + targetObjectId + "\" targetInstanceId=\"" + targetInstanceId + "\" targetReferencedByObjectIds=\"" + dependenciesIDsString + "\" " +
//				        "class=\"findReferencedByDependencies txtFmtTip fa-lg far fa-exclamation-circle \" style=\"padding-left: 0px; color: red;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.find.dependencies") + "</div>\" " + "></i>"
//		            );
//			}
//		} else if(object instanceof ConditionElement) {
//            if(sourceHasDependencies) {
//                dependenciesFinder.append( "<i id=\"objectItemFindDependencies_"+((IdentifiableMessagePointModel)this.object).getId()+"\" value='" + objectType + "_" + ((IdentifiableMessagePointModel)this.object).getId() + "_" + objectStatus + "' " +
//                        "sourceObjectId=\"" + sourceObjectId + "\" sourceInstanceId=\"" + sourceInstanceId + "\" objectType=\"" + objectType + "\" " +
//                        (dependenciesTypeIDsString == null ? "" : "sourceReferencingObjectIds=\"" + dependenciesTypeIDsString + "\"") +
//                        (dependenciesAlwaysSyncTypeIDsString == null ? "" : " sourceReferencingAlwaysSyncObjectIds=\"" + dependenciesAlwaysSyncTypeIDsString + "\"") +
//                        "class=\"findReferencingDependencies txtFmtTip fa fa-box-open \" style=\"padding-left: 0px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.find.dependencies") + "</div>\" " + "></i>"
//                );
//            }
//
//            if(sourceHasDependencies && targetIsReferenced) {
//                dependenciesFinder.append(" ");
//            }
//
//            if(targetIsReferenced) {
//				String dependenciesIDsString = targetDependenciesIDs.stream().collect(Collectors.joining(","));
//				dependenciesFinder.append( "<i id=\"objectItemFindDependencies_"+((IdentifiableMessagePointModel)this.object).getId()+"\" value='" + objectType + "_" + ((IdentifiableMessagePointModel)this.object).getId() + "_" + objectStatus + "' " +
//						"sourceObjectId=\"" + sourceObjectId + "\" sourceInstanceId=\"" + sourceInstanceId + "\" objectType=\"" + objectType + "\" " +
//						"targetObjectId=\"" + targetObjectId + "\" targetInstanceId=\"" + targetInstanceId + "\" targetReferencedByObjectIds=\"" + dependenciesIDsString + "\" " +
//				        "class=\"findReferencedByDependencies txtFmtTip fa-lg far fa-exclamation-circle \" style=\"padding-left: 0px; color: red;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.find.dependencies") + "</div>\" " + "></i>"
//		            );
//			}
//		} else if(object instanceof ParameterGroup) {
//            if(sourceHasDependencies) {
//                dependenciesFinder.append( "<i id=\"objectItemFindDependencies_"+((IdentifiableMessagePointModel)this.object).getId()+"\" value='" + objectType + "_" + ((IdentifiableMessagePointModel)this.object).getId() + "_" + objectStatus + "' " +
//                        "sourceObjectId=\"" + sourceObjectId + "\" sourceInstanceId=\"" + sourceInstanceId + "\" objectType=\"" + objectType + "\" " +
//                        (dependenciesTypeIDsString == null ? "" : "sourceReferencingObjectIds=\"" + dependenciesTypeIDsString + "\"") +
//                        (dependenciesAlwaysSyncTypeIDsString == null ? "" : " sourceReferencingAlwaysSyncObjectIds=\"" + dependenciesAlwaysSyncTypeIDsString + "\"") +
//                        "class=\"findReferencingDependencies txtFmtTip fa fa-box-open \" style=\"padding-left: 0px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.find.dependencies") + "</div>\" " + "></i>"
//                );
//            }
//
//            if(sourceHasDependencies && targetIsReferenced) {
//                dependenciesFinder.append(" ");
//            }
//
//            if(targetIsReferenced) {
//				String dependenciesIDsString = targetDependenciesIDs.stream().collect(Collectors.joining(","));
//				dependenciesFinder.append( "<i id=\"objectItemFindDependencies_"+((IdentifiableMessagePointModel)this.object).getId()+"\" value='" + objectType + "_" + ((IdentifiableMessagePointModel)this.object).getId() + "_" + objectStatus + "' " +
//						"sourceObjectId=\"" + sourceObjectId + "\" sourceInstanceId=\"" + sourceInstanceId + "\" objectType=\"" + objectType + "\" " +
//						"targetObjectId=\"" + targetObjectId + "\" targetInstanceId=\"" + targetInstanceId + "\" targetReferencedByObjectIds=\"" + dependenciesIDsString + "\" " +
//				        "class=\"findReferencedByDependencies txtFmtTip fa-lg far fa-exclamation-circle \" style=\"padding-left: 0px; color: red;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.find.dependencies") + "</div>\" " + "></i>"
//		            );
//			}
//        } else if(object instanceof DocumentSettingsModel) {
//            if(targetIsReferenced) {
//                String dependenciesIDsString = targetDependenciesIDs.stream().collect(Collectors.joining(","));
//                dependenciesFinder.append( "<i id=\"objectItemFindDependencies_"+((IdentifiableMessagePointModel)this.object).getId()+"\" value='" + objectType + "_" + ((IdentifiableMessagePointModel)this.object).getId() + "_" + objectStatus + "' " +
//                        "sourceObjectId=\"" + sourceObjectId + "\" sourceInstanceId=\"" + sourceInstanceId + "\" objectType=\"" + objectType + "\" " +
//                        "targetObjectId=\"" + targetObjectId + "\" targetInstanceId=\"" + targetInstanceId + "\" targetReferencedByObjectIds=\"" + dependenciesIDsString + "\" " +
//                        "class=\"findReferencedByDependencies txtFmtTip fa-lg far fa-exclamation-circle \" style=\"padding-left: 0px; color: red;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.find.dependencies") + "</div>\" " + "></i>"
//                );
//            }
//        } else if(object instanceof DataElementVariable) {
//            if(sourceHasDependencies) {
//                dependenciesFinder.append( "<i id=\"objectItemFindDependencies_"+((IdentifiableMessagePointModel)this.object).getId()+"\" value='" + objectType + "_" + ((IdentifiableMessagePointModel)this.object).getId() + "_" + objectStatus + "' " +
//                        "sourceObjectId=\"" + sourceObjectId + "\" sourceInstanceId=\"" + sourceInstanceId + "\" objectType=\"" + objectType + "\" " +
//                        (dependenciesTypeIDsString == null ? "" : "sourceReferencingObjectIds=\"" + dependenciesTypeIDsString + "\"") +
//                        (dependenciesAlwaysSyncTypeIDsString == null ? "" : " sourceReferencingAlwaysSyncObjectIds=\"" + dependenciesAlwaysSyncTypeIDsString + "\"") +
//                        "class=\"findReferencingDependencies txtFmtTip fa fa-box-open \" style=\"padding-left: 0px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.find.dependencies") + "</div>\" " + "></i>"
//                );
//            }
//
//            if(sourceHasDependencies && targetIsReferenced) {
//                dependenciesFinder.append(" ");
//            }
//
//            if(targetIsReferenced) {
//                String dependenciesIDsString = targetDependenciesIDs.stream().collect(Collectors.joining(","));
//                dependenciesFinder.append( "<i id=\"objectItemFindDependencies_"+((IdentifiableMessagePointModel)this.object).getId()+"\" value='" + objectType + "_" + ((IdentifiableMessagePointModel)this.object).getId() + "_" + objectStatus + "' " +
//                        "sourceObjectId=\"" + sourceObjectId + "\" sourceInstanceId=\"" + sourceInstanceId + "\" objectType=\"" + objectType + "\" " +
//                        "targetObjectId=\"" + targetObjectId + "\" targetInstanceId=\"" + targetInstanceId + "\" targetReferencedByObjectIds=\"" + dependenciesIDsString + "\" " +
//                        "class=\"findReferencedByDependencies txtFmtTip fa-lg far fa-exclamation-circle \" style=\"padding-left: 0px; color: red;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.find.dependencies") + "</div>\" " + "></i>"
//                );
//            }
//        } else if(object instanceof DataSource) {
//            if(targetIsReferenced) {
//                String dependenciesIDsString = targetDependenciesIDs.stream().collect(Collectors.joining(","));
//                dependenciesFinder.append( "<i id=\"objectItemFindDependencies_"+((IdentifiableMessagePointModel)this.object).getId()+"\" value='" + objectType + "_" + ((IdentifiableMessagePointModel)this.object).getId() + "_" + objectStatus + "' " +
//                        "sourceObjectId=\"" + sourceObjectId + "\" sourceInstanceId=\"" + sourceInstanceId + "\" objectType=\"" + objectType + "\" " +
//                        "targetObjectId=\"" + targetObjectId + "\" targetInstanceId=\"" + targetInstanceId + "\" targetReferencedByObjectIds=\"" + dependenciesIDsString + "\" " +
//                        "class=\"findReferencedByDependencies txtFmtTip fa-lg far fa-exclamation-circle \" style=\"padding-left: 0px; color: red;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.find.dependencies") + "</div>\" " + "></i>"
//                );
//            }
//        } else if(object instanceof DataSourceAssociation) {
//            if(sourceHasDependencies) {
//                dependenciesFinder.append( "<i id=\"objectItemFindDependencies_"+((IdentifiableMessagePointModel)this.object).getId()+"\" value='" + objectType + "_" + ((IdentifiableMessagePointModel)this.object).getId() + "_" + objectStatus + "' " +
//                        "sourceObjectId=\"" + sourceObjectId + "\" sourceInstanceId=\"" + sourceInstanceId + "\" objectType=\"" + objectType + "\" " +
//                        (dependenciesTypeIDsString == null ? "" : "sourceReferencingObjectIds=\"" + dependenciesTypeIDsString + "\"") +
//                        (dependenciesAlwaysSyncTypeIDsString == null ? "" : " sourceReferencingAlwaysSyncObjectIds=\"" + dependenciesAlwaysSyncTypeIDsString + "\"") +
//                        "class=\"findReferencingDependencies txtFmtTip fa fa-box-open \" style=\"padding-left: 0px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.find.dependencies") + "</div>\" " + "></i>"
//                );
//            }
//
//            if(sourceHasDependencies && targetIsReferenced) {
//                dependenciesFinder.append(" ");
//            }
//
//            if(targetIsReferenced) {
//                String dependenciesIDsString = targetDependenciesIDs.stream().collect(Collectors.joining(","));
//                dependenciesFinder.append( "<i id=\"objectItemFindDependencies_"+((IdentifiableMessagePointModel)this.object).getId()+"\" value='" + objectType + "_" + ((IdentifiableMessagePointModel)this.object).getId() + "_" + objectStatus + "' " +
//                        "sourceObjectId=\"" + sourceObjectId + "\" sourceInstanceId=\"" + sourceInstanceId + "\" objectType=\"" + objectType + "\" " +
//                        "targetObjectId=\"" + targetObjectId + "\" targetInstanceId=\"" + targetInstanceId + "\" targetReferencedByObjectIds=\"" + dependenciesIDsString + "\" " +
//                        "class=\"findReferencedByDependencies txtFmtTip fa-lg far fa-exclamation-circle \" style=\"padding-left: 0px; color: red;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.find.dependencies") + "</div>\" " + "></i>"
//                );
//            }
//		} else {
//			if(targetIsReferenced) {
//				String dependenciesIDsString = targetDependenciesIDs.stream().collect(Collectors.joining(","));
//				dependenciesFinder.append( "<i id=\"objectItemFindDependencies_"+((IdentifiableMessagePointModel)this.object).getId()+"\" value='" + objectType + "_" + ((IdentifiableMessagePointModel)this.object).getId() + "_" + objectStatus + "' " +
//						"sourceObjectId=\"" + sourceObjectId + "\" sourceInstanceId=\"" + sourceInstanceId + "\" objectType=\"" + objectType + "\" " +
//						"targetObjectId=\"" + targetObjectId + "\" targetInstanceId=\"" + targetInstanceId + "\" targetReferencedByObjectIds=\"" + dependenciesIDsString + "\" " +
//				        "class=\"findReferencedByDependencies txtFmtTip fa fa-box-open \" style=\"padding-left: 0px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.find.dependencies") + "</div>\" " + "></i>"
//		            );
//			} else if(sourceHasDependencies) {
//				if(objectType.equalsIgnoreCase("message") || objectType.equalsIgnoreCase("smartText")) {
//					dependenciesFinder.append( "<i id=\"objectItemFindDependencies_"+((IdentifiableMessagePointModel)this.object).getId()+"\" value='" + objectType + "_" + ((IdentifiableMessagePointModel)this.object).getId() + "_" + objectStatus + "' " +
//						"sourceObjectId=\"" + sourceObjectId + "\" sourceInstanceId=\"" + sourceInstanceId + "\" objectType=\"" + objectType + "\" " +
//						(dependenciesTypeIDsString == null ? "" : "sourceReferencingObjectIds=\"" + dependenciesTypeIDsString + "\"") +
//                            (dependenciesAlwaysSyncTypeIDsString == null ? "" : " sourceReferencingAlwaysSyncObjectIds=\"" + dependenciesAlwaysSyncTypeIDsString + "\"") +
//				        "class=\"findReferencingDependencies txtFmtTip fa fa-box-open \" style=\"padding-left: 0px;\" title=\"|<div class='txtFmtTipText'>" + ApplicationUtil.getMessage("page.label.find.dependencies") + "</div>\" " + "></i>"
//		            );
//				}
//			}
//		}
//
//        dependenciesFinder.append("</div>");
//		return dependenciesFinder.toString();
//	}

	public void setProjectSchema(String projectSchema) {
		this.projectSchema = projectSchema;
	}

	public String getOriginSchema() {
		return originSchema;
	}

	public void setOriginSchema(String originSchema) {
		this.originSchema = originSchema;
	}

	public Long getOriginNodeId() {
		return originNodeId;
	}

	public void setOriginNodeId(Long originNodeId) {
		this.originNodeId = originNodeId;
	}

    public void setProjectInstanceActiveCopy(VersionedInstance projectInstanceActiveCopy) {
        this.projectInstanceActiveCopy = projectInstanceActiveCopy;
    }

    public void setOriginInstanceActiveCopy(VersionedInstance originInstanceActiveCopy) {
        this.originInstanceActiveCopy = originInstanceActiveCopy;
    }

    public void setProjectInstanceWorkingCopy(VersionedInstance projectInstanceWorkingCopy) {
        this.projectInstanceWorkingCopy = projectInstanceWorkingCopy;
    }

    public void setOriginInstanceWorkingCopy(VersionedInstance originInstanceWorkingCopy) {
        this.originInstanceWorkingCopy = originInstanceWorkingCopy;
    }

    public boolean isConflictsExchange() {
        return conflictsExchange;
    }

    public void setConflictsExchange(boolean conflictsExchange) {
        this.conflictsExchange = conflictsExchange;
    }

	public void setSourceHasDependencies(boolean hasDependencies) {
		this.sourceHasDependencies = hasDependencies;
	}

	public void setSourceDependenciesNeedCreatingIDs(List<String> dependenciesIDs) {
		this.sourceDependenciesNeedCreatingIDs = dependenciesIDs;
	}

    public void setSourceAlwaysSyncDependenciesIDs(List<String> dependenciesIDs) {
        this.sourceDependenciesAlwaysSyncIDs = dependenciesIDs;
    }

	public void setTargetIsReferenced(boolean targetIsReferenced) {
		this.targetIsReferenced = targetIsReferenced;
	}

	public void setTargetObjectId(long targetObjectId) {
		this.targetObjectId = targetObjectId;
	}

	public void setTargetInstanceId(Long targetInstanceId) {
		this.targetInstanceId = targetInstanceId;
	}

	public void setTargetDependenciesIDs(List<String> targetDependenciesIDs) {
		this.targetDependenciesIDs = targetDependenciesIDs;
	}

	public Long getProjectObjectId() {
		return projectObjectId;
	}

	public void setProjectObjectId(Long projectObjectId) {
		this.projectObjectId = projectObjectId;
	}

	public Long getOriginObjectId() {
		return originObjectId;
	}

	public void setOriginObjectId(Long originObjectId) {
		this.originObjectId = originObjectId;
	}
}