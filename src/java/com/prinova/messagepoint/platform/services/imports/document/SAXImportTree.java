package com.prinova.messagepoint.platform.services.imports.document;

import java.util.HashMap;
import java.util.Map;

import com.prinova.messagepoint.util.ApplicationLanguageUtils;

public final class SAXImportTree
{
    private final String tagName;
    private final Map<String, SAXImportTree> children = new HashMap<>();
    private final Class<?extends MessagepointImportHandler> handlerclass;
    
    public SAXImportTree( String name )
    {
        tagName = name;
        handlerclass = null;
    }
    public SAXImportTree( String name, Class<?extends MessagepointImportHandler> handler )
    {
        this.handlerclass = handler;
        tagName = name;
    }
    
    public SAXImportTree addChild( String name )
    {
        SAXImportTree child = new SAXImportTree(name);

        children.put(name, child);
        return child;
    }
    public SAXImportTree addChild( String name, Class<?extends MessagepointImportHandler> handler )
    {
        SAXImportTree child = new SAXImportTree(name, handler);

        children.put(name, child);
        return child;
    }
    
    public SAXImportTree getChild( String tagname )
    {
        if ( children.containsKey( tagname ) )
            return children.get(tagname);
        return null;
    }
    
    public String getName()
    {
        return tagName;
    }
    
    public Class<?extends MessagepointImportHandler> getHandlerClass()
    {
        return handlerclass;
    }
    
    @Override
    public String toString()
    {
        StringBuffer sb = new StringBuffer("\n");
        sb.append("<?xml version=\"1.0\" encoding=\"" + ApplicationLanguageUtils.XML_ENCODING + "\"?>\n");
        
        toBuffer(0, sb);
        
        return sb.toString();
    }
    
    private void toBuffer( int depth, StringBuffer sb )
    {
        indentString(sb, depth);
        sb.append("<").append(tagName);


        if ( handlerclass != null )
            sb.append(" handler=\"").append(handlerclass.getSimpleName()).append("\"");

        if ( children.isEmpty() )
        {
            sb.append("/>\n");
            return;
        }
        else
        {
            sb.append(">\n");
        }
        
        for( SAXImportTree child : children.values() )
        {
            child.toBuffer(depth + 1, sb); 
        }
        if ( !children.isEmpty() )
        {
            indentString(sb, depth);
            sb.append("</").append(tagName).append(">\n");
        }
    }

    private static void indentString( StringBuffer sb, int depth )
    {
        for ( int index = 0; index < depth; ++index )
        {
            sb.append("    ");
        }
    }
}
