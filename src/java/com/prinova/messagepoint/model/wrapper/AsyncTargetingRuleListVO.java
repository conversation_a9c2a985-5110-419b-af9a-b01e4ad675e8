package com.prinova.messagepoint.model.wrapper;

import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.util.ApplicationUtil;

public class AsyncTargetingRuleListVO extends AsyncListVO{
	
	private ConditionElement			rule;
	private String						name;
	private int							numConditions;
	private AsyncListVOIFrameData		iFrameData;
	private TargetingRuleListVOFlags	flags;
	
	public AsyncTargetingRuleListVO(){
		super();
	}

	public void setTargetingRule(ConditionElement rule) {
		this.rule = rule;
	}

	public void setName(String name) {
		this.name = name;
	}	
	public String getName() {
		String viewURL = ApplicationUtil.getWebRoot() + "dataadmin/condition_element_edit.form";
		String nameHTML = "<a id=\"actionLabel_"+ this.rule.getId() +"\" itemName=\"ITEM_NAME\" href=\""+viewURL+"?elementid=" + this.rule.getId() + "\" class=\"dataTableLink dataTableItemName d-inline-block w-100 text-truncate\">" + name + "</a>";
		nameHTML = nameHTML.replace("ITEM_NAME",name);
		return "<div class=\"position-relative mt--2\"><div class=\"text-truncate-wrapper\" title=\"" + name + "\">" + nameHTML + "</div></div>";
	}
	
	public int getNumConditions() {
		return numConditions;
	}

	public void setNumConditions(int numConditions) {
		this.numConditions = numConditions;
	}

	public AsyncListVOIFrameData getIFrameData() {
		return iFrameData;
	}

	public void setIFrameData(AsyncListVOIFrameData iFrameData) {
		this.iFrameData = iFrameData;
	}

	public String getBinding() {
		return "<input id='listItemCheck_"+this.rule.getId()+"' type='checkbox' value='"+this.rule.getId()+
				"' style='display : none;' name='selectedIds'><input type='hidden' value='on' name='_selectedIds'>";
	}
	
	public TargetingRuleListVOFlags getFlags() {
		return flags;
	}

	public void setFlags(TargetingRuleListVOFlags flags) {
		this.flags = flags;
	}

	public static class TargetingRuleListVOFlags{
		private boolean	canUpdate;
		private boolean canDelete;

		public boolean isCanUpdate() {
			return canUpdate;
		}
		public void setCanUpdate(boolean canUpdate) {
			this.canUpdate = canUpdate;
		}
		public boolean isCanDelete() {
			return canDelete;
		}
		public void setCanDelete(boolean canDelete) {
			this.canDelete = canDelete;
		}
	}
}