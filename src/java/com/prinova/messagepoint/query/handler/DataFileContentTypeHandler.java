package com.prinova.messagepoint.query.handler;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import com.prinova.messagepoint.model.testing.DataFile;
import com.prinova.messagepoint.query.DefaultPage;
import com.prinova.messagepoint.query.IPage;
import com.prinova.messagepoint.util.ApplicationUtil;

public class DataFileContentTypeHandler implements PostQueryHandler {

	private boolean isOrderAsc;
	
	public boolean isOrderAsc() {
		return isOrderAsc;
	}

	public void setOrderAsc(boolean isOrderAsc) {
		this.isOrderAsc = isOrderAsc;
	}
	
	@SuppressWarnings("unchecked")
	public IPage handle(IPage page) {
		List<DataFile> dataFiles = (List<DataFile>)page.getList();
		
		Collections.sort(dataFiles, new Comparator<>() {
            public int compare(DataFile o1, DataFile o2) {
                String t1 = getContentType(o1);
                String t2 = getContentType(o2);
                if (isOrderAsc()) {
                    return t1.compareTo(t2);
                } else {
                    return t2.compareTo(t1);
                }
            }
        });
		
        ((DefaultPage)page).setList(dataFiles);
		return page;
	}
	
	private String getContentType(DataFile dataFile) {
		return ApplicationUtil.getMessage(dataFile.getSourceType().getName());
	}	
}
