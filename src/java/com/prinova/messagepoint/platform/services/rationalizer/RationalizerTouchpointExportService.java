package com.prinova.messagepoint.platform.services.rationalizer;

import ai.mpr.marcie.ingestion.IngestionSupportedFileTypesEnum;
import ai.mpr.marcie.ingestion.input.VariablesTypeEnum;
import com.prinova.messagepoint.model.metadata.MetadataFormItem;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocument;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent.ContentIdAndZoneConnector;
import com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.platform.services.export.RationalizerTextHashUtil;
import com.prinova.messagepoint.platform.services.rationalizer.dto.ContentConfigDto;
import com.prinova.messagepoint.platform.services.rationalizer.dto.ContentStyleConfigDto;
import com.prinova.messagepoint.platform.services.rationalizer.dto.DeliveryDto;
import com.prinova.messagepoint.platform.services.rationalizer.dto.EmbeddedContentConfigDto;
import com.prinova.messagepoint.platform.services.rationalizer.dto.MessageConfigDto;
import com.prinova.messagepoint.platform.services.rationalizer.dto.TouchpointExportSelectionNode;
import com.prinova.messagepoint.platform.services.rationalizer.dto.TouchpointExportSelectionTree;
import com.prinova.messagepoint.platform.services.rationalizer.dto.TouchpointLanguageDto;
import com.prinova.messagepoint.platform.services.rationalizer.dto.TouchpointZonesConfigDto;
import com.prinova.messagepoint.platform.services.rationalizer.dto.UserVariableDataElementDto;
import com.prinova.messagepoint.platform.services.rationalizer.dto.UserVariableDto;
import com.prinova.messagepoint.platform.services.rationalizer.dto.VersionConfigDto;
import com.prinova.messagepoint.platform.services.rationalizer.dto.ZoneConfigDto;
import com.prinova.messagepoint.util.RandomGUID;
import com.prinova.messagepoint.util.RationalizerUtil;
import com.prinova.messagepoint.util.StringUtil;
import com.prinova.messagepoint.util.jstree.RationalizerNavigationJsonSerializer;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.prinova.messagepoint.platform.services.export.RationalizerTextHashUtil.generateUniqueLongId;

public class RationalizerTouchpointExportService {
    private static final String ZONE_CONNECTOR = "zone_connector";
    private static final String VARIABLES_CONNECTOR = "Variables";
    public static final int SELECTION_NODE_NAME_MAX_LENGTH = 96;
    public static final int MESSAGE_NAME_MAX_LENGTH = 256;
    public static final String VAR_MATCH_ERR = "VAR_MATCH_ERR";

    private TouchpointLanguageDto defaultLanguage;

    public TouchpointExportSelectionTree createRationalizerExportTree(RationalizerApplication application, TouchpointZonesConfigDto touchpointZonesConfigDto,
                                                                      String rationalizerDocFormItemId, TouchpointExportSelectionTree messagepointSelectionTree) {
        TouchpointExportSelectionTree rationalizerSelectionTree = new TouchpointExportSelectionTree();
        RationalizerNavigationJsonSerializer rationalizerNavigationJsonSerializer = new RationalizerNavigationJsonSerializer(application);
        String[] ids = rationalizerDocFormItemId.split(",");
        rationalizerNavigationJsonSerializer.setSelectedNodeId(Long.parseLong(ids[ids.length-1]));
        JSONArray treeStructureJson = rationalizerNavigationJsonSerializer.getTreeStructureJson();
        List<Long> processedContentIdsList = new ArrayList<>();
        List<String> processedNodesNameList = new ArrayList<>();
        TouchpointExportSelectionNode root = createEmptyRootNode();
        root.setSelectedInTree(true);
        root.setDepthInTree(0);
        rationalizerSelectionTree.setTouchpointExportSelectionNode(root);

        // create variables
        createUserVariables(application, rationalizerSelectionTree, messagepointSelectionTree);
        // create embedded contents
        rationalizerSelectionTree.setEmbeddedContentConfigDtoList(createEmbeddedContents(application, rationalizerSelectionTree));

        // retrieve master/ root object
        JSONObject rootElement = treeStructureJson.getJSONObject(0);
        AtomicBoolean masterNodeSelected = new AtomicBoolean(false);
        if(rootElement.get("id").toString().equals(rationalizerDocFormItemId)) {
            masterNodeSelected.set(true);
        }
        JSONArray childrenForMasterNode = getChildrenForCurrentNode(rootElement);
        if (childrenForMasterNode.isEmpty()) {
            root.setMessageConfigDtoList(
                    createMessagesForRootNode(root, application, processedContentIdsList,
                            touchpointZonesConfigDto, rationalizerSelectionTree));
            return rationalizerSelectionTree;
        }

        if (!childrenForMasterNode.isEmpty()) {
            for (int i = 0; i < childrenForMasterNode.length(); i++) {
                JSONObject crtJsonObject = childrenForMasterNode.getJSONObject(i);
                createSelectionForCrtObjectRecursive(root, crtJsonObject, application, processedContentIdsList, processedNodesNameList, touchpointZonesConfigDto,
                            rationalizerDocFormItemId, masterNodeSelected.get(), rationalizerSelectionTree);
            }
        }

        return rationalizerSelectionTree;
    }

    private void  createSelectionForCrtObjectRecursive(TouchpointExportSelectionNode crtNode, JSONObject crtObject, RationalizerApplication application, List<Long> processedContentIdsList, List<String> processedNodesNameList,
                                                      TouchpointZonesConfigDto touchpointZonesConfigDto, String rationalizerDocFormItemId, boolean includeNodes,
                                                       TouchpointExportSelectionTree touchpointExportSelectionTree) {
        TouchpointExportSelectionNode touchpointExportSelectionNode = new TouchpointExportSelectionNode();
        touchpointExportSelectionNode.setParent(crtNode);
        if (CollectionUtils.isEmpty(crtNode.getChildren())) {
            crtNode.setChildren(new LinkedList<>());
        }

        Long metadataIdForCurrentNode = getMetadataIdForCurrentNode(crtObject);
        String idForCurrentNode = crtObject.getString("id");
        JSONArray childrenForCurrentNode = getChildrenForCurrentNode(crtObject);
        MetadataFormItem metadataFormItem = MetadataFormItem.findById(metadataIdForCurrentNode);
        if(metadataFormItem != null && (metadataFormItem.getValue() == null || metadataFormItem.getValue().isEmpty())){
            String metadataName = metadataFormItem.getItemDefinition().getName();
            if(!touchpointExportSelectionTree.getMetadataWithNoValues().contains(metadataName)) {
                touchpointExportSelectionTree.getMetadataWithNoValues().add(metadataName);
            }
        }
        if(idForCurrentNode.equals(rationalizerDocFormItemId) || includeNodes) {
            List<Long> contentIds = computeContentIdsByDocFormItemId(application.getId(), idForCurrentNode, childrenForCurrentNode);
            String name = constructTouchpointExportNodeName(metadataFormItem, processedNodesNameList, true);
            touchpointExportSelectionNode.setName(name);
            touchpointExportSelectionNode.setId(metadataIdForCurrentNode);
            touchpointExportSelectionNode.setDepthInTree((int) idForCurrentNode.chars().filter(ch -> ch == ',').count());
            if (!contentIds.isEmpty()) {
                touchpointExportSelectionNode.setMessageConfigDtoList(
                        createMessages(touchpointExportSelectionNode, contentIds, touchpointZonesConfigDto, processedContentIdsList,  touchpointExportSelectionTree));
            }
            touchpointExportSelectionNode.setSelectedInTree(true);
            selectParentsInTree(touchpointExportSelectionNode);
            crtNode.getChildren().add(touchpointExportSelectionNode);
        } else {
            String name = constructTouchpointExportNodeName(metadataFormItem, processedNodesNameList, false);
            touchpointExportSelectionNode.setName(name);
            touchpointExportSelectionNode.setId(metadataIdForCurrentNode);
            touchpointExportSelectionNode.setDepthInTree((int) idForCurrentNode.chars().filter(ch -> ch == ',').count());
            crtNode.getChildren().add(touchpointExportSelectionNode);
        }

        if (!childrenForCurrentNode.isEmpty()) {
            for (int i = 0; i < childrenForCurrentNode.length(); i++) {
                JSONObject crtJsonObject = childrenForCurrentNode.getJSONObject(i);
                createSelectionForCrtObjectRecursive(touchpointExportSelectionNode, crtJsonObject, application,
                        processedContentIdsList, processedNodesNameList, touchpointZonesConfigDto, rationalizerDocFormItemId,
                        idForCurrentNode.equals(rationalizerDocFormItemId) || includeNodes, touchpointExportSelectionTree);
            }
        }
    }

    private String constructTouchpointExportNodeName(MetadataFormItem metadataFormItem, List<String> processedNodesNameList, boolean isPartOfSelectedNodePath) {
        String name = metadataFormItem != null && StringUtils.isNotEmpty(metadataFormItem.getValue())  ? metadataFormItem.getValue() : "NO VALUE";
        if (name.length() > SELECTION_NODE_NAME_MAX_LENGTH) {
            name = name.substring(0, SELECTION_NODE_NAME_MAX_LENGTH - 1);
        }
        name = removeExtensionFronVariantName(name);
        name = stripIllegalCharsFromName(name);

        int i = 1;
        String finalName = name;
        if(isPartOfSelectedNodePath && name.contains("NO VALUE")) {
            while(processedNodesNameList.contains(finalName)) {
                finalName = name + " " + i;
                i++;
            }

            processedNodesNameList.add(finalName);
        }

        return finalName;
    }

    private void selectParentsInTree(TouchpointExportSelectionNode touchpointExportSelectionNode) {
        TouchpointExportSelectionNode parent = touchpointExportSelectionNode.getParent();
        while(parent != null) {
            parent.setSelectedInTree(true);
            parent = parent.getParent();
        }
    }

    private void createUserVariables(RationalizerApplication application, TouchpointExportSelectionTree touchpointExportSelectionTree, TouchpointExportSelectionTree messagepointSelectionTree) {
        Set<String> allVariables = new LinkedHashSet<>();

        // Content Variables
        List<ContentIdAndZoneConnector> contentIdAndZoneConnectorList = RationalizerDocumentContent.findAllContentsWithZoneConnectorForApplication(application);
        if (CollectionUtils.isNotEmpty(contentIdAndZoneConnectorList)) {
            final List<Long> contentIds = contentIdAndZoneConnectorList.stream()
                    .map(el -> el.getContentId())
                    .collect(Collectors.toList());

            final Set<String> documentContentVariables = RationalizerDocumentContent.findMetadataValuesByContentIdsAndPrimaryConnector(
                    contentIds,
                    VARIABLES_CONNECTOR
            );
            if (CollectionUtils.isNotEmpty(documentContentVariables)) {
                allVariables.addAll(documentContentVariables);
            }
        }

        // Shared object - Smart text variables
        List<RationalizerSharedContent> sharedContentList = RationalizerSharedContent.findByApplication(application);
        if (CollectionUtils.isNotEmpty(sharedContentList)) {
            final List<Long> sharedContentIds = sharedContentList.stream()
                    .map(sharedContent -> sharedContent.getId())
                    .collect(Collectors.toList());
            Set<String> sharedContentsVariables = RationalizerSharedContent.findMetadataValueBySharedContentIdAndPrimaryConnector(
                    sharedContentIds,
                    VARIABLES_CONNECTOR
            );
            if (CollectionUtils.isNotEmpty(sharedContentsVariables)) {
                allVariables.addAll(sharedContentsVariables);
            }
        }

        if (CollectionUtils.isEmpty(allVariables)) {
            return;
        }

        allVariables.stream()
                .filter(StringUtils::isNotEmpty)
                .forEach(element -> createUserVariables(element, touchpointExportSelectionTree, messagepointSelectionTree.getUserVariableDtoList()));
    }

    private void createUserVariables(String variablesValue, TouchpointExportSelectionTree touchpointExportSelectionTree, List<UserVariableDto> messagepointUserVariableDtos) {
        final String variableWithoutVarTag = createVariableWithoutVarTag(variablesValue);
        boolean foundUserVariable = touchpointExportSelectionTree.getUserVariableDtoList().stream().anyMatch(object -> object.getName().equalsIgnoreCase(variableWithoutVarTag));
        // get the corresponding messagepoint variable
        UserVariableDto messagepointVariable = messagepointUserVariableDtos.stream().filter(var -> var.getName().equalsIgnoreCase(variableWithoutVarTag)).findFirst().orElse(null);
        if (!foundUserVariable) {
            if (messagepointVariable != null) {
                // use the messagepoint variable
                touchpointExportSelectionTree.addUserVariable(messagepointVariable);
            } else {
                // create a new rationalizer variable
                UserVariableDto userVariableDto = createUserVariableDto(variableWithoutVarTag);
                touchpointExportSelectionTree.addUserVariable(userVariableDto);
            }
        }

    }

    private String createVariableWithoutVarTag(String variable) {
        List<VariablesTypeEnum> variablesTypeEnumList = Arrays.stream(VariablesTypeEnum.values()).collect(Collectors.toList());
        variablesTypeEnumList.sort(VariablesTypeEnum.reversedTagLengthComparator);
        for (VariablesTypeEnum varType : variablesTypeEnumList) {
            String unescapeCommaVariable = variable.replace("\\,", ",");
            if(unescapeCommaVariable.startsWith(varType.getStartTag()) && unescapeCommaVariable.endsWith(varType.getEndTag())) {
                variable = unescapeCommaVariable.substring(varType.getStartTag().length(), unescapeCommaVariable.length()-varType.getEndTag().length());
            }
        }

        return variable;
    }

    private UserVariableDto createUserVariableDto(String variableName) {
        UserVariableDto userVariableDto = new UserVariableDto();
        userVariableDto.setId(generateUniqueLongId());
        userVariableDto.setDna(RandomGUID.getGUID().toUpperCase());
        userVariableDto.setName(variableName);
        userVariableDto.setAttributes(createVariableAttributesMap());
        userVariableDto.getAttributes().put("id", String.valueOf(userVariableDto.getId()));
        userVariableDto.getAttributes().put("dna", userVariableDto.getDna());
        userVariableDto.setApplication(UserVariableDto.VAR_APP_RATIONALIZER);

        UserVariableDataElementDto dataElementDto = new UserVariableDataElementDto();
        dataElementDto.setValue(variableName);
        dataElementDto.setAttributes(createDataElementAttributesMap());

        userVariableDto.setDataElementDto(dataElementDto);
        return userVariableDto;
    }

    private Map<String, String> createDataElementAttributesMap() {
        Map<String, String> dataElementAttributes = new HashMap<>();
        dataElementAttributes.put("id", generateUniqueLongId().toString());
        dataElementAttributes.put("referencedatasource", "Rationalizer Reference");
        return dataElementAttributes;
    }

    private Map<String, String> createVariableAttributesMap() {
        Map<String, String> attributesMap = new HashMap<>();
        attributesMap.put("id", "");
        attributesMap.put("guid", RandomGUID.getGUID().toLowerCase());
        attributesMap.put("dna", "");
        attributesMap.put("referencevariable", "true");
        attributesMap.put("rulesenabled", "false");
        attributesMap.put("contentenabled", "true");
        attributesMap.put("expressionvariable", "false");
        attributesMap.put("scriptvariable", "false");
        attributesMap.put("systemvariable", "false");
        attributesMap.put("typeid", "1");

        return attributesMap;
    }

    private List<MessageConfigDto> createMessages(TouchpointExportSelectionNode parent, List<Long> contentIds,
                                                  TouchpointZonesConfigDto touchpointZonesConfigDto, List<Long> processedContentIdsList, TouchpointExportSelectionTree touchpointExportSelectionTree) {
        List<MessageConfigDto> result = new LinkedList<>();
        Map<String, TreeMap<Integer, Long>> zonesToContentIds = constructZonesToContentsMapping(contentIds, processedContentIdsList);

        for (Map.Entry<String, TreeMap<Integer, Long>> crtEntry : zonesToContentIds.entrySet()) {
            boolean exportToSingleMessage = touchpointZonesConfigDto.isExportToSingleMessage(crtEntry.getKey());
            if (exportToSingleMessage) {
                if (touchpointZonesConfigDto.isCombineWithinDocument()) {
                    result.addAll(createCombinedMessagesWithinDocument(parent, crtEntry.getKey(), crtEntry.getValue(), touchpointZonesConfigDto, touchpointExportSelectionTree));
                } else {
                    MessageConfigDto message = createCombinedMessage(parent, crtEntry.getKey(), crtEntry.getValue(), touchpointZonesConfigDto, touchpointExportSelectionTree);
                    message.setParent(parent);
                    result.add(message);
                }
            } else {
                if(crtEntry.getValue().values() != null) {
                    int messageNo = 0;
                    for (Map.Entry<Integer, Long> crtContentId : crtEntry.getValue().entrySet()) {
                        MessageConfigDto message = createMessage(parent, crtContentId.getValue(), crtContentId.getKey(), crtEntry.getKey(),
                                touchpointZonesConfigDto, touchpointExportSelectionTree);
                        String name = message.getVersionConfigDto().getName();
                        name = name + " - " + messageNo;
                        name = stripIllegalCharsFromName(name);

                        message.getVersionConfigDto().setName(name);
                        result.add(message);
                        messageNo++;
                    }
                }
            }
        }

        return result;
    }

    private List<MessageConfigDto> createCombinedMessagesWithinDocument(TouchpointExportSelectionNode parent, String zoneConnectorValue, TreeMap<Integer, Long> contentIds,
                                                                        TouchpointZonesConfigDto touchpointZonesConfigDto, TouchpointExportSelectionTree touchpointExportSelectionTree) {
        List<MessageConfigDto> result = new LinkedList<>();
        Map<RationalizerDocument, List<RationalizerDocumentContent>> documentToContentsMap = constructDocumentToContentsMap(contentIds);
        MessageConfigDto messageConfigDto;
        for (Map.Entry<RationalizerDocument, List<RationalizerDocumentContent>> crtEntry : documentToContentsMap.entrySet()) {
            messageConfigDto = createCombinedMessageForDoc(parent, zoneConnectorValue, crtEntry.getKey().getName(), crtEntry.getValue(), touchpointZonesConfigDto, touchpointExportSelectionTree);
            messageConfigDto.setParent(parent);
            result.add(messageConfigDto);
        }
        return result;
    }

    private Map<RationalizerDocument, List<RationalizerDocumentContent>> constructDocumentToContentsMap(TreeMap<Integer, Long> contentIds) {
        Map<RationalizerDocument, List<RationalizerDocumentContent>> result = new HashMap<>();

        if(contentIds.values() != null) {
            for (Long crtId : contentIds.values()) {
                RationalizerDocumentContent crtContent = RationalizerDocumentContent.findById(crtId);
                if (crtContent == null) {
                    continue;
                }

                List<RationalizerDocumentContent> contentsForCurrentDoc = result.get(crtContent.getRationalizerDocument());
                if (CollectionUtils.isNotEmpty(contentsForCurrentDoc)) {
                    contentsForCurrentDoc.add(crtContent);
                } else {
                    contentsForCurrentDoc = new LinkedList<>();
                    contentsForCurrentDoc.add(crtContent);
                    result.put(crtContent.getRationalizerDocument(), contentsForCurrentDoc);
                }
            }
        }

        return result;
    }

    private MessageConfigDto createCombinedMessage(TouchpointExportSelectionNode parent,String zoneConnectorValue, TreeMap<Integer, Long> contentIds,
                                                   TouchpointZonesConfigDto touchpointZonesConfigDto, TouchpointExportSelectionTree touchpointExportSelectionTree) {
        MessageConfigDto messageConfigDto = new MessageConfigDto();
        VersionConfigDto versionConfigDto = new VersionConfigDto();
        Long messageId = generateUniqueLongId();
        messageConfigDto.setId(messageId);
        String messageName = construcMessageName(parent, zoneConnectorValue, touchpointZonesConfigDto);
        versionConfigDto.setName(messageName);

        messageConfigDto.setRationalizerDocumentContentName(" ");
        messageConfigDto.setRationalizerDocumentName(" ");
        versionConfigDto.setContentConfigDtoList(new LinkedList<>());
        String zoneRefId = extractZoneRefId(zoneConnectorValue, touchpointZonesConfigDto.getZonesSet());
        versionConfigDto.setDeliveryDto(new DeliveryDto(zoneRefId, "Mandatory"));

        ContentConfigDto contentCfgDto;
        if(contentIds.values() != null) {
            RationalizerDocumentContent content;
            contentCfgDto = new ContentConfigDto(RationalizerTextHashUtil.generateUniqueLongId(), "");
            for (Long crtId : contentIds.values()) {
                content = RationalizerDocumentContent.findById(crtId);
                contentCfgDto.setContentHtml(contentCfgDto.getContentHtml() + createContentParagraphTagString(content, touchpointExportSelectionTree));
                Document document = Jsoup.parse(contentCfgDto.getContentHtml());
                document.outputSettings().prettyPrint(false);
                if(document != null) {
                    populateVariablePositionAndIdentifierMap(document,touchpointExportSelectionTree.getUserVariablesLookupMap(), contentCfgDto);
                    String processedContent = processRationalizerContent(document, contentCfgDto.getContentHtml(), touchpointExportSelectionTree);
                    contentCfgDto.setProcessedContent(processedContent);
                    // extract styles from span tag(s) from current content/ message
                    Map<Integer, ContentStyleConfigDto> contentStylesMap = contentCfgDto.extractContentStyles(document.select("span[style]"));
                    touchpointExportSelectionTree.getContentStylesMap().putAll(contentStylesMap);
                }
            }
            String language = "en";
            if(this.defaultLanguage != null) {
                language = this.defaultLanguage.getCode();
            }
            contentCfgDto.addAttribute("language", language);
            versionConfigDto.getContentConfigDtoList().add(contentCfgDto);
        }

        versionConfigDto.setParent(messageConfigDto);
        messageConfigDto.setVersionConfigDto(versionConfigDto);
        return messageConfigDto;
    }

    private String processRationalizerContent(Document document, String contentHtml, TouchpointExportSelectionTree touchpointExportSelectionTree) {
        if (!StringUtil.isEmptyOrNull(contentHtml) && contentHtml.contains("var")) {
            document.getElementsByTag("var").forEach(element -> {
                String elementText = element.text();
                if (elementText.equals("RENDERED_ITEM")) {
                    String elementId = element.attr("id");
                    String elementDna = element.attr("dna");
                    String key = computKeyByElementVariableIdOrDna(elementId, elementDna);

                    if (!StringUtil.isEmptyOrNull(key)) {
                        String type = element.attr("type");
                        if (type.equals(ContentObjectContentUtil.VAR_TYPE_EMBEDDED_CONTENT)) {
                            List<EmbeddedContentConfigDto> embeddedContentConfigDtoList = touchpointExportSelectionTree.getEmbeddedContentConfigDtoList();
                            boolean embeddedContentNotFound = true;
                            for (EmbeddedContentConfigDto embeddedContentConfigDto : embeddedContentConfigDtoList) {
                                if(StringUtil.isEmptyOrNull(elementId)) {
                                    if (embeddedContentConfigDto.getDna().equals(elementDna)) {
                                        element.html(embeddedContentConfigDto.getVersionConfigDto().getName());
                                        embeddedContentNotFound = false;
                                        break;
                                    }
                                } else {
                                    if (embeddedContentConfigDto.getId() == Long.parseLong(elementId)) {
                                        element.html(embeddedContentConfigDto.getVersionConfigDto().getName());
                                        embeddedContentNotFound = false;
                                        break;
                                    }
                                }
                            }
                            if(embeddedContentNotFound) {
                                element.html("SMART TEXT NOT FOUND");
                            }
                        } else if (type.equals(ContentObjectContentUtil.VAR_TYPE_VARIABLE)) {
                            Set<String> keySet = touchpointExportSelectionTree.getUserVariablesLookupMap().keySet()
                                    .stream()
                                    .filter(s -> s.contains(key))
                                    .collect(Collectors.toSet());
                            if (CollectionUtils.isNotEmpty(keySet)) {
                                element.text(touchpointExportSelectionTree.getUserVariablesLookupMap().get(keySet.iterator().next()).getName());
                            } else {
                                element.html("VARIABLE NOT FOUND");
                            }
                        }
                    }
                }
            });
            return ContentObjectContentUtil.formatBody(document);
        }

        return contentHtml;
    }
    private String computKeyByElementVariableIdOrDna(String id, String dna) {
        String key = null;

        if(!StringUtil.isEmptyOrNull(id)) {
            key = "[" + id + "]";
        }

        if(!StringUtil.isEmptyOrNull(dna)) {
            key += "[" + dna + "]";
        }

        return key;
    }


    private String extractZoneRefId(String zoneConnectorValue, Set<ZoneConfigDto> zoneSet) {
        if (CollectionUtils.isEmpty(zoneSet) || StringUtils.isEmpty(zoneConnectorValue)) {
            return null;
        }

        for (ZoneConfigDto crtZone : zoneSet) {
            if (zoneConnectorValue.equals(crtZone.getZoneName()) || zoneConnectorValue.equals(crtZone.getZoneFriendlyName())) {
                return Long.toString(crtZone.getZoneId());
            }
        }

        return null;
    }

    private MessageConfigDto createCombinedMessageForDoc(TouchpointExportSelectionNode parent,String zoneConnectorValue, String documentName, List<RationalizerDocumentContent> contents,
                                                         TouchpointZonesConfigDto touchpointZonesConfigDto, TouchpointExportSelectionTree touchpointExportSelectionTree) {
        MessageConfigDto messageConfigDto = new MessageConfigDto();
        VersionConfigDto versionConfigDto = new VersionConfigDto();
        Long messageId = generateUniqueLongId();
        messageConfigDto.setId(messageId);
        String messageName = construcMessageName(parent, zoneConnectorValue, touchpointZonesConfigDto, documentName);

        if (messageName.length() > MESSAGE_NAME_MAX_LENGTH) {
            messageName = messageName.substring(0, MESSAGE_NAME_MAX_LENGTH - 1);
        }
        messageConfigDto.setRationalizerDocumentContentName(" ");
        messageConfigDto.setRationalizerDocumentName(documentName);
        versionConfigDto.setName(messageName);
        versionConfigDto.setContentConfigDtoList(new LinkedList<>());
        String zoneRefId = extractZoneRefId(zoneConnectorValue, touchpointZonesConfigDto.getZonesSet());
        versionConfigDto.setDeliveryDto(new DeliveryDto(zoneRefId, "Mandatory"));

        ContentConfigDto contentCfgDto = new ContentConfigDto(RationalizerTextHashUtil.generateUniqueLongId(), "");
        Collections.sort(contents, new Comparator<>() {
            public int compare(RationalizerDocumentContent o1, RationalizerDocumentContent o2) {
                return o1.getOrder().compareTo(o2.getOrder());
            }
        });
        for (RationalizerDocumentContent crtContent : contents) {
            contentCfgDto.setContentHtml(contentCfgDto.getContentHtml() + createContentParagraphTagString(crtContent, touchpointExportSelectionTree));
            Document document = Jsoup.parse(contentCfgDto.getContentHtml());
            if(document != null) {
                populateVariablePositionAndIdentifierMap(document, touchpointExportSelectionTree.getUserVariablesLookupMap(), contentCfgDto);
                String processedContent = processRationalizerContent(document, contentCfgDto.getContentHtml(), touchpointExportSelectionTree);
                contentCfgDto.setProcessedContent(processedContent);
                // extract styles from span tag(s) from current content/ message
                Map<Integer, ContentStyleConfigDto> contentStylesMap = contentCfgDto.extractContentStyles(document.select("span[style]"));
                touchpointExportSelectionTree.getContentStylesMap().putAll(contentStylesMap);
            }
        }
        String language = "en";
        if(this.defaultLanguage != null) {
            language = this.defaultLanguage.getCode();
        }
        contentCfgDto.addAttribute("language", language);
        versionConfigDto.getContentConfigDtoList().add(contentCfgDto);
        versionConfigDto.setParent(messageConfigDto);
        messageConfigDto.setVersionConfigDto(versionConfigDto);
        return messageConfigDto;
    }

    private Map<String, TreeMap<Integer, Long>> constructZonesToContentsMapping(List<Long> contentIds, List<Long> processedContentIdsList) {
        Map<String, TreeMap<Integer, Long>> result = new HashMap<>();

        if (CollectionUtils.isEmpty(contentIds)) {
            return result;
        }

        List<RationalizerDocumentContent> rationalizerDocumentContentList = RationalizerDocumentContent.findByIds(contentIds);
        rationalizerDocumentContentList.forEach( rationalizerDocumentContent -> {
            String zoneConnectorValue = retrieveZoneConnectorValue(rationalizerDocumentContent);
            if (StringUtils.isNotEmpty(zoneConnectorValue)) {
                TreeMap<Integer, Long> contentIdsForCurrentZone = result.get(zoneConnectorValue);
                if (contentIdsForCurrentZone != null) {
                    int order = rationalizerDocumentContent.getOrder();
                    if(contentIdsForCurrentZone.containsKey(order)){
                       order = contentIdsForCurrentZone.descendingKeySet().first() + 1;
                    }
                    contentIdsForCurrentZone.put(order, rationalizerDocumentContent.getId());
                } else {
                    contentIdsForCurrentZone = new TreeMap<>();
                    contentIdsForCurrentZone.put(rationalizerDocumentContent.getOrder(), rationalizerDocumentContent.getId());
                    result.put(zoneConnectorValue, contentIdsForCurrentZone);
                }
                processedContentIdsList.add(rationalizerDocumentContent.getId());
            }
        });

        return result;
    }

    private String retrieveZoneConnectorValue(RationalizerDocumentContent rationalizerDocumentContent) {
        return rationalizerDocumentContent.findMetadataValueByItemDefinitionPrimaryConnector(ZONE_CONNECTOR);
    }

    private TouchpointExportSelectionNode createEmptyRootNode() {
        TouchpointExportSelectionNode root = new TouchpointExportSelectionNode();
        root.setId(-9L);
        root.setName("Master");

        return root;
    }

    private List<MessageConfigDto> createMessagesForRootNode(TouchpointExportSelectionNode parent, RationalizerApplication application, List<Long> processedContentIdsList,
                                                             TouchpointZonesConfigDto touchpointZonesConfigDto, TouchpointExportSelectionTree touchpointExportSelectionTree) {
        List<MessageConfigDto> result = new ArrayList<>();
        List<ContentIdAndZoneConnector> contentIdAndZoneConnectorList = RationalizerDocumentContent.findAllContentsWithZoneConnectorForApplication(application);
        if (CollectionUtils.isEmpty(contentIdAndZoneConnectorList)) {
            return result;
        }
        Set<Long> processedContentIdsSet = new LinkedHashSet<>(processedContentIdsList);

        contentIdAndZoneConnectorList = contentIdAndZoneConnectorList.stream()
                        .filter(contentIdAndZoneConnector -> !processedContentIdsSet.contains(contentIdAndZoneConnector.getContentId()))
                                .collect(Collectors.toCollection(LinkedList::new));
        int order = 0;
        for (ContentIdAndZoneConnector element : contentIdAndZoneConnectorList) {
            order = order+1;
            MessageConfigDto message = createMessage(
                    parent,
                    element.getContentId(),
                    order,
                    element.getZoneConnector(),
                    touchpointZonesConfigDto,
                    touchpointExportSelectionTree
            );
            if (message != null) {
                result.add(message);
            }
        };

        return result;
    }

    private MessageConfigDto createMessage(TouchpointExportSelectionNode parent, Long id, Integer order, String zoneConnectorValue,
                                           TouchpointZonesConfigDto touchpointZonesConfigDto, TouchpointExportSelectionTree touchpointExportSelectionTree) {
        RationalizerDocumentContent content = RationalizerDocumentContent.findById(id);
        MessageConfigDto messageConfigDto = new MessageConfigDto();
        messageConfigDto.setId(content.getId());
        messageConfigDto.setRationalizerDocumentContentName(content.getName());
        messageConfigDto.setRationalizerDocumentName(content.getRationalizerDocument().getName());
        VersionConfigDto versionConfigDto = new VersionConfigDto();

        String messageName = construcMessageName(parent, zoneConnectorValue, touchpointZonesConfigDto);
        versionConfigDto.setName(messageName);

        String zoneRefId = extractZoneRefId(zoneConnectorValue, touchpointZonesConfigDto.getZonesSet());
        versionConfigDto.setDeliveryDto(new DeliveryDto(zoneRefId, "Mandatory"));
        messageConfigDto.setVersionConfigDto(versionConfigDto);
        ContentConfigDto contentCfgDto = new ContentConfigDto(content.getId(), createContentParagraphTagString(content, touchpointExportSelectionTree));
        Document document = Jsoup.parse(contentCfgDto.getContentHtml());
        document.outputSettings().prettyPrint(false);
        if(document != null) {
            populateVariablePositionAndIdentifierMap(document,touchpointExportSelectionTree.getUserVariablesLookupMap(), contentCfgDto);
            String processedContent = processRationalizerContent(document, contentCfgDto.getContentHtml(), touchpointExportSelectionTree);
            contentCfgDto.setProcessedContent(processedContent);
            // extract styles from span tag(s) from current content/ message
            Map<Integer, ContentStyleConfigDto> contentStylesMap = contentCfgDto.extractContentStyles(document.select("span[style]"));
            touchpointExportSelectionTree.getContentStylesMap().putAll(contentStylesMap);
        }
        String language = "en";
        if(this.defaultLanguage != null) {
            language = this.defaultLanguage.getCode();
        }
        contentCfgDto.addAttribute("language", language);
        versionConfigDto.setContentConfigDtoList(Collections.singletonList(contentCfgDto));
        versionConfigDto.setParent(messageConfigDto);
        messageConfigDto.setParent(parent);

        return messageConfigDto;
    }

    private String construcMessageName(TouchpointExportSelectionNode parent, String zoneConnectorValue, TouchpointZonesConfigDto touchpointZonesConfigDto) {
        return construcMessageName(parent, zoneConnectorValue, touchpointZonesConfigDto, "");
    }

    private String construcMessageName(TouchpointExportSelectionNode parent, String zoneConnectorValue, TouchpointZonesConfigDto touchpointZonesConfigDto, String documentName) {
        int depthInTree = parent.getDepthInTree();
        String variantName = parent.getName();
        variantName = removeExtensionFronVariantName(variantName);
        variantName = stripIllegalCharsFromName(variantName);
        String documentNameForMessage = "";
        if(StringUtils.isNotEmpty(documentName)) {
            documentNameForMessage = removeExtensionFronVariantName(documentName);
            documentNameForMessage = stripIllegalCharsFromName(documentNameForMessage);
        }
        ZoneConfigDto zone = touchpointZonesConfigDto.getZonesSet().stream().filter(z -> z.getZoneName().equals(zoneConnectorValue)).findFirst().orElse(null);
        String zoneFriendlyName = zone != null ? zone.getZoneFriendlyName() : zoneConnectorValue;
        String messageName = depthInTree + " - " + zoneFriendlyName + " - " + variantName;
        if(StringUtils.isNotEmpty(documentNameForMessage) && !variantName.equalsIgnoreCase(documentNameForMessage)) {
            messageName = depthInTree + " - " + zoneFriendlyName + " - " + variantName  + " - " + documentNameForMessage;
        }

        messageName = stripIllegalCharsFromName(messageName);
        return messageName;
    }

    private String stripIllegalCharsFromName(String messageName) {
        return messageName.replaceAll("[^a-zA-Z0-9_\\-' ]", "");
    }

    private String removeExtensionFronVariantName(String variantName) {
        for(IngestionSupportedFileTypesEnum extension : IngestionSupportedFileTypesEnum.values()) {
            if(variantName.toLowerCase().endsWith("." + extension.getValue()) ) {
                variantName = variantName.substring(0, variantName.length() - extension.getValue().length() - 1);
            }
        }
        return variantName;
    }

    private void populateVariablePositionAndIdentifierMap(Document document, HashMap<String, UserVariableDto> userVariablesLookupMap, ContentConfigDto contentCfgDto){
        Elements mprVariables = document.select("mpr_variable");
        AtomicInteger counter = new AtomicInteger(0);
        mprVariables.forEach(element -> {
            String variableName = element.text();
            Map.Entry<String, UserVariableDto> entry = userVariablesLookupMap.entrySet().stream().filter(stringUserVariableDtoEntry ->
                    (stringUserVariableDtoEntry.getValue().getName().equalsIgnoreCase(variableName))).findFirst().orElse(null);
            if(entry != null) {
                contentCfgDto.getVariablePositionAndIdentifierMap().put(counter.incrementAndGet(), entry.getKey());
            } else {
                contentCfgDto.getVariablePositionAndIdentifierMap().put(counter.incrementAndGet(), VAR_MATCH_ERR  + variableName);
            }
        });
    }

    private List<EmbeddedContentConfigDto> createEmbeddedContents(RationalizerApplication application, TouchpointExportSelectionTree rationalizerSelectionTree) {
        List<EmbeddedContentConfigDto> result = new LinkedList<>();
        List<RationalizerSharedContent> sharedContentList = RationalizerSharedContent.findByApplication(application);
        if (CollectionUtils.isEmpty(sharedContentList)) {
            return result;
        }
        HashMap<String, UserVariableDto> userVariablesLookupMap = rationalizerSelectionTree.getUserVariablesLookupMap();
        sharedContentList.forEach(sharedContent -> {
            EmbeddedContentConfigDto embeddedContentConfigDto = new EmbeddedContentConfigDto();
            embeddedContentConfigDto.setId(sharedContent.getId());
            embeddedContentConfigDto.setDna(RandomGUID.getGUID().toUpperCase());
            VersionConfigDto versionConfigDto = new VersionConfigDto();
            versionConfigDto.setName(sharedContent.getName());
            versionConfigDto.setMetatags(sharedContent.getMetatags());
            versionConfigDto.setContentConfigDtoList(createContentsForSharedContent(sharedContent, userVariablesLookupMap, rationalizerSelectionTree));
            versionConfigDto.setParent(embeddedContentConfigDto);
            embeddedContentConfigDto.setVersionConfigDto(versionConfigDto);
            result.add(embeddedContentConfigDto);
        });

        return result;
    }

    private List<ContentConfigDto> createContentsForSharedContent(RationalizerSharedContent crtSharedContent, HashMap<String, UserVariableDto> userVariablesLookupMap, TouchpointExportSelectionTree rationalizerSelectionTree) {
        List<ContentConfigDto> result = new LinkedList<>();
        if (CollectionUtils.isEmpty(crtSharedContent.getRationalizerDocumentContents())) {
            return result;
        }
        String markupContent = crtSharedContent.computeNormalizedMarkupContent();
        ContentConfigDto contentCfgDto = new ContentConfigDto(crtSharedContent.getId(), markupContent);

        Document markupContentDocument = Jsoup.parse(markupContent);
        populateVariablePositionAndIdentifierMap(markupContentDocument, userVariablesLookupMap, contentCfgDto);

        String language = "en";
        if(this.defaultLanguage != null) {
            language = this.defaultLanguage.getCode();
        }
        contentCfgDto.addAttribute("language", language);
        result.add(contentCfgDto);
        Document document = Jsoup.parse(contentCfgDto.getContentHtml());
        if(document != null) {
            Map<Integer, ContentStyleConfigDto> contentStylesMap = contentCfgDto.extractContentStyles(document.select("span[style]"));
            rationalizerSelectionTree.getContentStylesMap().putAll(contentStylesMap);
        }

        return result;
    }

    private JSONArray getChildrenForCurrentNode(JSONObject jsonObject) {
        Object children = jsonObject.get("children");
        if (children instanceof JSONArray) {
            return (JSONArray) children;
        }
        return new JSONArray();
    }

    private Long getMetadataIdForCurrentNode(JSONObject jsonObject) {
        String treePath = jsonObject.getString("id");
        String[] idsArray = treePath.split(",");
        return idsArray.length > 0 ? Long.valueOf(idsArray[idsArray.length - 1]) : null;
    }

    private List<Long> computeContentIdsByDocFormItemId(long applicationId, String rationalizerDocFormItemId, JSONArray childrenForCurrentNode) {
        List<Long> docContentIds = new ArrayList<>();

        if (childrenForCurrentNode == null || childrenForCurrentNode.isEmpty()) {
            Map<MetadataFormItemDefinition, MetadataFormItem> rationalizerNavTreePathMap = MetadataFormItem.findRationalizerNavTreePathMap(applicationId, rationalizerDocFormItemId);
            Set<Long> contentIds = RationalizerUtil.getNavTreeFilteredContents(applicationId, rationalizerNavTreePathMap);
            if (!docContentIds.isEmpty()) {
                docContentIds.retainAll(contentIds);
            } else {
                docContentIds.addAll(contentIds);
            }
        } else {
            boolean hasNoValueChild = false;
            for (int i = 0; i < childrenForCurrentNode.length(); i++) {
                JSONObject crtJsonObject = childrenForCurrentNode.getJSONObject(i);
                if(crtJsonObject.getString("text").contains("NO VALUE")) {
                    hasNoValueChild = true;
                    break;
                }
            }
            if (hasNoValueChild) {
                Map<MetadataFormItemDefinition, MetadataFormItem> rationalizerNavTreePathMap = MetadataFormItem.findRationalizerNavTreePathMap(applicationId, rationalizerDocFormItemId);
                Set<Long> contentIds = RationalizerUtil.getNavTreeFilteredContents(applicationId, rationalizerNavTreePathMap);
                if (!docContentIds.isEmpty()) {
                    docContentIds.retainAll(contentIds);
                } else {
                    docContentIds.addAll(contentIds);
                }

                if (childrenForCurrentNode != null && !childrenForCurrentNode.isEmpty()) {
                    for (int i = 0; i < childrenForCurrentNode.length(); i++) {
                        JSONObject crtJsonObject = childrenForCurrentNode.getJSONObject(i);
                        String idForCurrentNode = crtJsonObject.getString("id");
                        List<Long> contentIdsForCurrentChild = computeContentIdsByDocFormItemId(applicationId, idForCurrentNode);
                        if (!contentIdsForCurrentChild.isEmpty()) {
                            docContentIds.removeAll(contentIdsForCurrentChild);
                        }
                    }
                }
            }
        }

        return docContentIds;
    }

    private List<Long> computeContentIdsByDocFormItemId(long applicationId, String rationalizerDocFormItemId) {
        List<Long> docContentIds = new ArrayList<>();
        Map<MetadataFormItemDefinition, MetadataFormItem> rationalizerNavTreePathMap = MetadataFormItem.findRationalizerNavTreePathMap(applicationId, rationalizerDocFormItemId);
        Set<Long> contentIds = RationalizerUtil.getNavTreeFilteredContents(applicationId, rationalizerNavTreePathMap);
        if (!docContentIds.isEmpty()) {
            docContentIds.retainAll(contentIds);
        } else {
            docContentIds.addAll(contentIds);
        }
        return docContentIds;
    }


    private String createContentParagraphTagString(RationalizerDocumentContent rationalizerDocumentContent, TouchpointExportSelectionTree rationalizerTree) {
        RationalizerSharedContent rationalizerSharedContent = rationalizerDocumentContent.computeRationalizerSharedContent();

        if (rationalizerSharedContent == null) {
            return computeMarkupVersionOfContent(rationalizerDocumentContent.computeNormalizedMarkupContent());
        }
        String dna = "";
        for (EmbeddedContentConfigDto sharedContent : rationalizerTree.getEmbeddedContentConfigDtoList()){
            if(sharedContent.getId() == rationalizerSharedContent.getId()){
                dna = sharedContent.getDna();
            }
        }
        StringBuilder contentCdata = new StringBuilder();
        contentCdata.append("<p><var class=\"staticContentItem mceNonEditable embedded_content_tag actionVariable\" type=\"4\"");
        contentCdata.append(" dna=\"");
        contentCdata.append(dna);
        contentCdata.append("\" id=\"");
        contentCdata.append(rationalizerSharedContent.getId());
        contentCdata.append("\">");
        contentCdata.append("RENDERED_ITEM");
        contentCdata.append("</var></p>");
        return contentCdata.toString();
    }

    private String computeMarkupVersionOfContent(String contentMarkup) {
        if (StringUtil.isEmptyOrNull(contentMarkup)) {
            return null;
        }

        // TODO - any kind of html tag not only the below / if content has a table ??
        if (contentMarkup.contains("</p>") || contentMarkup.contains("</div>") || contentMarkup.contains("</li>")) {
            return contentMarkup;
        }

        return "<p>" + contentMarkup + "</p>";
    }

    public void setDefaultLanguage(TouchpointLanguageDto defaultLanguage) {
        this.defaultLanguage = defaultLanguage;
    }
}
