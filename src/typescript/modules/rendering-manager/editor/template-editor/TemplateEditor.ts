import {AbstractContentEditor} from "../AbstractContentEditor";
import {ConnectedSidebar} from "../../containers/connected/ConnectedSidebar";
import {ZoneTemplateContainer} from "../../containers/right/ZoneTemplateContainer";
import {Logger} from "../../debugger/Logger";
import {ZoneTemplateContentChangeIF} from "./ZoneTemplateContentChangeIF";
import {EditorConfigIF} from "../EditorConfigIF";
import {PostEditorInitCallback} from "../../util/CommonTypes";
import {ContentEditorIF} from "../ContentEditorIF";

export class TemplateEditor extends AbstractContentEditor implements ContentEditorIF, ZoneTemplateContentChangeIF {

    private zoneTemplateContainer: ZoneTemplateContainer;

    constructor(contentEditorConfig: EditorConfigIF) {
        super(contentEditorConfig);
        this.zoneTemplateContainer = new ZoneTemplateContainer(this.getZone());
        this.zoneTemplateContainer.setZoneTemplateChangeListener(this);
    }

    protected doStartEdit(postInitCallback?: PostEditorInitCallback): void {
        Logger.renderingManager().info(`Start editing template for content ${this.getContent().getZoneContentPieceId()}`);

        this.getContent().getZone().getJqueryElement().addClass("image-zone-editor-highlight");
        location.href = `#${this.getContent().getZone().getComponentId()}`;
        ConnectedSidebar.getInstance().showComponent(this.zoneTemplateContainer);

        if (postInitCallback && postInitCallback.callback) {
            postInitCallback.callback(this);
        }
    }

    protected doStopEdit(): void {
        Logger.renderingManager().info(`Remove editor template for content ${this.getContent().getZoneContentPieceId()}`);

        if (this.getContent()
            && this.getContent().getZone()
            && this.getContent().getZone().getJqueryElement()
        ) {
            this.getContent().getZone().getJqueryElement().removeClass("image-zone-editor-highlight");
        }

        ConnectedSidebar.getInstance().closeSidebar();
    }

    onTemplateChanged(newContent: JQuery): void {
        this.setContentChanged(true);
        var wrapper = this.getZone().getJqueryElement().children("div[data-message-wrapper='true']:first");
        if (wrapper.length) {
            wrapper.empty().append(newContent);
        } else {
            this.getZone().replaceJqueryContent(newContent);
        }
        $(`#${this.getZone().getComponentId()}`).replaceWith(this.getContent().getJqueryElement());
    }
}