package com.prinova.licence.webapp.web;

import javax.servlet.http.HttpServletRequest;

import org.springframework.validation.Errors;
import org.springframework.validation.ValidationUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.licence.webapp.actions.RepresentativeAction;
import com.prinova.licence.webapp.utils.EmailUtil;

public class CreateRepresentativeController extends MessagepointController {
	
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		return new Command();
	}

	protected ModelAndView onSubmit(Object command) throws Exception {
		Command c = (Command)command;
		RepresentativeAction.createNew(c.getName(), c.getEmailAddress(), c.getPhoneNumber());
		return new ModelAndView(new RedirectView(getSuccessView(), true));
	}
	
	public static class Command {
		private String name;
		private String emailAddress;
		private String phoneNumber;
		public String getName() {
			return name;
		}
		public void setName(String name) {
			this.name = name;
		}
		public String getEmailAddress() {
			return emailAddress;
		}
		public void setEmailAddress(String emailAddress) {
			this.emailAddress = emailAddress;
		}
		public String getPhoneNumber() {
			return phoneNumber;
		}
		public void setPhoneNumber(String phoneNumber) {
			this.phoneNumber = phoneNumber;
		}
		
	}
	
	public static class Validator implements org.springframework.validation.Validator{
		public boolean supports(Class arg0) {
			return Command.class.isAssignableFrom(arg0);
		}
		
		public void validate(Object object, Errors errors) {
			Command command = (Command)object;
			ValidationUtils.rejectIfEmptyOrWhitespace(errors, "name", null, "Please specify the representative name.");
			ValidationUtils.rejectIfEmptyOrWhitespace(errors, "emailAddress", null, "Please enter an email address.");
			String email = command.getEmailAddress();
			if(!EmailUtil.isValidEmailAddress(email)){
				errors.rejectValue("emailAddress", "invalidemailaddress", "Please enter an valid email address. ");
			}
		}
	}	
}
