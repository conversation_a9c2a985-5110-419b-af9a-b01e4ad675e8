




# Chapter 1: Setting your bundle delivery options

_Bundle delivery_ is domain and sub-domain-specific. The bundle delivery option sets the parameters for delivering job bundles from Messagepoint to your DE or Production Manager server. The page lists all bundle delivery options that are accessed in the domain. It allows you to:

  * Determine how job bundles are automatically downloaded to the DE or Production Manager server.

  * Create the bundle name convention using the file name pattern if using SFTP or SCP as your _Communication Type_.




If you do not set a bundle delivery option, Messagepoint checks for the default endpoint by going through your ancestor domains. If a default endpoint cannot be found, the _STOP_ option is chosen. and you can download the bundle from the _Job Center_.

Once you have set the options, you can test to see if Messagepoint can access the server using the parameters that you entered.

The Bundle delivery list is accessed from S _etup > Setup > Bundle Delivery_. It consists of the following fields:

Field |  Description  
---|---  
Name |  The name of the bundle delivery option. _Note_ : The Name is mandatory and has to be unique.  
Description |  A description of the delivery option.  
Communication Type |  The method of downloading the files from Messagepoint to the DE or DEWS server: SFTP SCP DEWS STOP _Note_ : STOP means that the bundle is not downloaded. You download the bundle from the Job Center. For more information, see [[_Downloading a bundle_](Downloading a bundle.htm)](Files in the DE install files for zOS.htm#_bookmark59).  
URL |  The URL of the DE or Production Manager server. _Note_ : The URL must include the port. _Note_ : No URL is required if STOP is selected.  
Notification emails |  Lists the email addresses of the users who are on the notification list.  
Default |  Indicates if the server is the default server for the domain.
﻿




## Managing the bundle deliveries options

You can use the tool bar buttons or the right-click menu to manage your servers from the list.

The following commands are available using the tool bar buttons or the right-click menu:

Button |  Action  
---|---  
Edit |  _Edit_. Edits the delivery option’s parameters. For more information, see [Modifying a bundle’s delivery parameters](Modifying a bundles delivery parameters.htm#_Ref89935516).  
More |  _Delete_. Removes the bundle delivery option from Messagepoint. For more information, see [Deleting a delivery option](Deleting a delivery option.htm#_Ref89935578).
﻿




## Bundle naming options

The default job bundle name pattern uses the following format:

{jobId}_{instance}_{company}_{jobType}.zip

If you are using SFTP or SCP as your transfer protocol for downloading your job bundles, you can modify the job bundle name pattern using filename variables. You can use the following filename variables in renaming the job bundles:

  * {podId} _\- pod id_

  * {company}_\- company_

  * {instance} _\- instance_

  * {instancetype} - instance type - production, sandbox

  * {touchpoint} - touchpoint name

  * {jobId} - job id

  * {jobType} - job type

  * {date_yyyy} - current date - year (yyyy)

  * {date_yy} - current date - year (yy)

  * {date_mm} - current date - month (mm)

  * {date_dd} - current date - day (dd)

  * {time_hh} - current time - hours (hh)

  * {time_mm} - current time - minutes (mm)

  * {time_ss} - current time - seconds (ss)



﻿




## Adding a bundle delivery option

The bundle delivery settings are for the domain and its sub-domains. You must set at least one bundle delivery option for each server that you are using. Each server can have multiple delivery options as you can create a separate _Bundle Delivery_ profile for each job type. For example, simulations could have a separate bundle delivery option from production. This allows you to set a specific filename pattern by job type if you are using SFTP or SCP.

To add a bundle delivery:

  1. Go to _Setup > Setup > Bundle Delivery_.

  2. Click _Add Bundle Delivery_.

  3. Enter the _Name_ of the delivery option.




_Note_ : The Name is mandatory and must be unique.

  4. Add a _Description_. (Optional)

  5. Enter the _File name pattern_. (Optional)




The field displays default filename pattern, which is the default Production Manager pattern. The file name is only editable for SFTP and SCP types. 

  * You can also click _Show variables_ to see the list of _Filename Variables_ that you can use.



  6. Select the _Notification Emails_ check box if you want email notifications sent out.

  7. Enter the email addresses for those users who are to receive notifications as a comma- delimited list in one of the following fields:



  * Emails (To)

  * Emails (Cc)




_Note_ : If you want notifications sent only if the bundle delivery fails, select the _Notify on error only_ check box.

  8. Set the _Status_ for each appropriate job type by selecting the _Enabled_ check box.




You do not have to select _Enabled_ for all job types. For example, you may want to run your Tests in the cloud and not on premise or use a specific server or delivery type for specific job types.

  9. You must ensure that each selected job type has one _Default_ bundle delivery option.




_Note_ : Job types may have more than one bundle delivery option. Ensure that only one of the options is marked as Default. If you have only one bundle delivery option, then all of the job types using that option must be marked as Default.

_Note_ : We recommend that you do not select Default for Test if you are using STOP as the default Communication Type.

  10. Configure the Server information:



  * Select the Communication Type from the drop-down list:



  * SFTP

  * SCP

  * DEWS

  * STOP




_Note_ : STOP means that the bundle is not downloaded. You download the bundle from the Job Center. For more information, see [[Downloading a bundle](Downloading a bundle.htm#_Ref89936133)](Files in the DE install files for zOS.htm#_bookmark59).

  * Enter the server’s _URL_ in the _Server_ field.

  * Enter the _Port_ number for the server.

  * Enter the _Path_ to where you want the bundle to be delivered. If you selected:



  * _SFTP_. You set the relative path to the client folder.

  * _SCP_. You can access any folder on the file folder.



  11. Set the _Authentication_ for accessing the server:




_Note_ : Authentication can be either key-based or username and password.

  * To use Key-based Authentication:

  * Select the Key-based authentication check box.

  * Enter the _Username_.

  * On the _SSH Key_ slide toggle select either:



  * _Generate_. The key is automatically created and uploaded when you click _Save_.

  * _Upload_. You need to upload _Public_ and Private keys.




_Note_ : When using key-based authentication there is an option for two-factor authentication. When enabled, the bundle delivery configuration connects to the remote server using key-based authentication and enters a password if prompted by the target server.

If using the two factor authentication option, after generating or uploading the keys:

  * Select the Two-Factor Authentication check box.

  * Enter the Password.



  * To use username and password for authentication:



  * Enter the _Username_.

  * Enter the _Password_.



  12. Click _Test Connection_.




This tests that the entered information is correct and if Messagepoint can connect to the server.

  13. Save.




This saves the bundle delivery.
﻿




## Modifying a bundle’s delivery parameters

To modify a bundle’s delivery parameters:

  1. Select the option.



  2. Click _Edit_.

  3. Make the necessary changes.

  4. Click:



  * _Save_. This saves the bundle delivery.

  * _Test Connection_. This tests if the entered information is correct and if Messagepoint can connect to the server.



﻿




## Deleting a delivery option

To delete a delivery option:

  1. Select the option.

  2. Click _Delete_.

  3. Click _Continue_.



