package com.prinova.messagepoint.reports.processor.modelprocessor;

import java.util.Set;

import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.deliveryevent.DeliveryEvent;
import com.prinova.messagepoint.model.scenario.AbstractScenario;

public interface ModelProcessor<T extends IdentifiableMessagePointModel> {
	
	public String getQuery(T model);
	
	public int getItemType();
	public String getReportField();
	
	public Set<T> getModels( AbstractScenario scenario, DeliveryEvent event);
}
