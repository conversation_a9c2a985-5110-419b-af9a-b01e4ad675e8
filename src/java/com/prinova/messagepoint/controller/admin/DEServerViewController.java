package com.prinova.messagepoint.controller.admin;

import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.admin.deserver.DEServer;
import com.prinova.messagepoint.model.admin.deserver.DEServerAvailability;
import com.prinova.messagepoint.model.admin.deserver.DEServerBundleTypeState;
import com.prinova.messagepoint.model.admin.proxylogin.Pod;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.HttpRequestUtil;
import com.prinova.messagepoint.util.JSONUtils;
import org.json.JSONObject;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.net.URI;
import java.text.MessageFormat;
import java.util.List;
import java.util.Map;

public class DEServerViewController extends MessagepointController implements Serializable {

	private static final long serialVersionUID = 6159626656207109284L;

	public static final int ACTION_UPDATE 		= 1;

	public static final String REQ_PARAM_SERVER_ID = "deServerId";
	public static final String REQ_PARAM_ACTION = "action";
	private static final String REQ_PARAM_BRANCH_ID = "branchId";

	private String editView;
	
	
	public String getEditView() {
		return editView;
	}
	public void setEditView(String editView) {
		this.editView = editView;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}

    protected Command formBackingObject(HttpServletRequest request) {
		HttpRequestUtil.saveBackToListURL(request, HttpRequestUtil.getAllParamenterFromRequest(request), HttpRequestUtil.HTTP_ATTRIBUTE_BACKURL_VARIABLE);
    	long serverId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_SERVER_ID, -1);
    	long branchId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_BRANCH_ID, -1);

    	DEServer server = DEServer.findById(serverId, Branch.findById(branchId));

    	Command command = new Command();
		command.setServer(server);

		return command;
    }
    
    protected ModelAndView onSubmit(HttpServletRequest request,
            						HttpServletResponse response,
            						Object commandObj,
            						BindException errors) throws Exception {
		int action = ServletRequestUtils.getIntParameter(request, REQ_PARAM_ACTION, -1);
		long serverdId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_SERVER_ID, -1);
		long branchId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_BRANCH_ID, -1);

		Map<String, Object> parms = PodUtils.getContextMapParms(request);

		parms.put(REQ_PARAM_SERVER_ID, serverdId);
		parms.put(REQ_PARAM_BRANCH_ID, branchId);
		
		switch (action) {
			case (ACTION_UPDATE): {
				return new ModelAndView(new RedirectView(getEditView()), parms);
			} 
			default: {
				break;
			}
		}
		return null;
    }

	public static class Command {
		private DEServer server;
		private boolean updatePermitted = false;
		private Pod pod;
		private String serverUrl;

		public Pod getPod() {
			return pod;
		}

		public void setPod(Pod pod) {
			this.pod = pod;
		}

		public DEServer getServer() {
			return server;
		}
		public void setServer(DEServer server) {
			this.server = server;

			if (server.getBundleTypeState() == null) {
				server.setBundleTypeState(new DEServerBundleTypeState());
			}
		}

		public String getServerUrl() {

			URI uri = URI.create(getServer().getUrl());

			return MessageFormat.format("{0}://{1}{2}{3}", uri.getScheme(), uri.getHost(), uri.getPort() != -1 ? ":" + uri.getPort() : "", uri.getPath());
		}

		public void setServerUrl(String serverUrl) {
			this.serverUrl = serverUrl;
		}

		public boolean isEnabledConnected() {
			return server.getBundleTypeState().isCommunicationProofEnabled();
		}

		public boolean isDefaultConnected() {
			return server.getBundleTypeState().isCommunicationProofDefault();
		}

		public boolean isEnabledProduction() {
			return server.getBundleTypeState().isProductionEnabled();
		}

		public boolean isDefaultProduction() {
			return server.getBundleTypeState().isProductionDefault();
		}

		public boolean isEnabledTest() {
			return server.getBundleTypeState().isTestEnabled();
		}

		public boolean isDefaultTest() {
			return server.getBundleTypeState().isTestDefault();
		}

		public boolean isEnabledSimulation() {
			return server.getBundleTypeState().isSimulationEnabled();
		}

		public boolean isDefaultSimulation() {
			return server.getBundleTypeState().isSimulationDefault();
		}

		public String getAvaliabilityMessageCode() {
			switch (server.getAvailability()) {

				case DEServerAvailability.AVAILABLE:
					return "page.label.online";
				case DEServerAvailability.UNAVAILABLE:
					return "page.label.unavailable";
				case DEServerAvailability.UNAVAILABLE_REFUSED:
					return "error.connection.refused";
				case DEServerAvailability.UNAVAILABLE_CREDENTIALS:
					return "page.label.unavailable.credentials";
				case DEServerAvailability.UNAVAILABLE_TIMEOUT:
					return "page.label.unavailable.connection";
				case DEServerAvailability.NOT_TESTED:
					return "page.label.not.tested";
				default:
					return "page.label.unavailable";
			}
		}

		public boolean isEmailNotify() {
			try {
				JSONObject emails = server.getNotificationEmailsAsJson();
				return !emails.getJSONArray("to").isEmpty() || !emails.getJSONArray("cc").isEmpty();
			} catch (Exception e) {
				return false;
			}
		}

		public String getToEmails() {
			try {
				List<String> emails = JSONUtils.getJSONArrayAsList(server.getNotificationEmailsAsJson().getJSONArray("to"), String.class);
				return String.join(", ", emails);
			} catch (Exception e) {
				return "";
			}
		}

		public String getCcEmails() {
			try {
				List<String> emails = JSONUtils.getJSONArrayAsList(server.getNotificationEmailsAsJson().getJSONArray("cc"), String.class);
				return String.join(", ", emails);
			} catch (Exception e) {
				return "";
			}
		}
	}
}