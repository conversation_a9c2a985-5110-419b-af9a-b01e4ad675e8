package com.prinova.messagepoint.controller.tpadmin;

import java.io.Serializable;
import java.util.List;

import com.prinova.messagepoint.model.ComplexValue;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.DocumentChannelEditVO;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.admin.deserver.DEServer;
import com.prinova.messagepoint.model.clickatell.ClickatellConfiguration;
import com.prinova.messagepoint.model.dialogue.DialogueConfiguration;
import com.prinova.messagepoint.model.eMessaging.EMessagingConfiguration;
import com.prinova.messagepoint.model.exacttarget.ExactTargetConfiguration;
import com.prinova.messagepoint.model.file.CompositionFileSet;
import com.prinova.messagepoint.model.ftp.FtpConfiguration;
import com.prinova.messagepoint.model.gmc.GMCConfiguration;
import com.prinova.messagepoint.model.nativecomposition.NativeCompositionConfiguration;
import com.prinova.messagepoint.model.sefas.SefasConfiguration;
import com.prinova.messagepoint.model.sefas.MPHCSConfiguration;
import com.prinova.messagepoint.model.sendmail.SendmailConfiguration;
import com.prinova.messagepoint.util.HibernateDeproxyUtil;

public class DocumentChannelEditWrapper implements Serializable{

	private static final long serialVersionUID = 5697614816925671802L;

	private long 					channelId;
	private long 					connectorId;
	private long 					qualificationOutputId 	= -1L;
	private int						outputFileTypeId		= OutputFileType.ID_PDF;
	private Boolean 				startsOnOddPage			= false;
	private Boolean 				duplexOutput			= false;
	private String 					xmlFileName;
	private int 					formatType;
	private String 					pubFileName;					// Dialogue input file
	private String 					workflowFileName;				// GMC input file
	private boolean 				supportsStyles 			= true;
	private boolean					legacyDxfMode			= false;
	private boolean					mixedDxfTaggedText		= false;
	private boolean					runTimeDxf				= false;
	private boolean 				controlStyles 			= true;
	private int						textStyleCompositionType		= StyleCompositionType.ID_NAME;
	private int						paragraphStyleCompositionType	= StyleCompositionType.ID_NAME;
	private int						listStyleCompositionType		= StyleCompositionType.ID_NAME;
	private int						listStyleControlType			= StyleControlType.ID_FULL;
	private int						colorOutputFormatType			= ColorOutputFormatType.ID_CMYK;
	private boolean					restrictedQuotes		= true;
	private boolean					templateControl			= false;
	private boolean					accessibility			= false;
	private Integer					linespacePosition		= 2;
	private boolean					tablePaddingInPts		= true;
	private String 					customerDriverInputFilename;
	private DataElementVariable		customerEmailAddressVariable;  	// Email channel specific
	private DataElementVariable		customerPhoneNumberVariable;  	// SMS channel specific
	private DataElementVariable		customerKeyVariable;  			// Exact Target specific
	private CompositionFileSet		compositionFileSet;
	private String					compositionVersion;
	private String					outputFilename;
	private String					outputDocumentTitle;
	private Boolean					validateProductionBundle	= false;
	private Boolean					escapeTagsInDriverData		= true;
	private Boolean					convertTableBorderPxToPts	= false;
	private Boolean					evalNotEqualOnMissingTag	= true;
	private Boolean					playEmptyAggFirstLastVar	= true;
	private Boolean					removeZeroFromStyleConnector = true;
	private Boolean					compTimeParentTagging 		= true;
	private Boolean					dataGroupExpressionVarProc 	= true;
	private Boolean					scriptVarAppliesUndefined	= true;
	private Boolean					correctParagraphTextStyles	= true;
	private Boolean					fixInlineTargetingStyles	= true;
	private Boolean					preserveDataWhitespace		= true;
	private Boolean					nbspComposedAsSpace			= true;
	private Boolean					normalizeImageLibrary		= true;

	private Boolean					normalizeEmbeddedContent	= true;
	private Boolean					gmcSpanToTTag				= false;
	private Boolean					blueUnderlineLinks			= false;
	private Boolean					unalteredZonePDFPassthrough	= true;

	// FTP Connector
	private long 					serverId;
	private String 					webURL;
	private String 					recipientFileLocation;
	private String 					recipientFileName;
	private boolean 				isEmbedded;
	
	// In Cloud Configuration
	private boolean					executeInCloudTest;
	private boolean					executeInCloudPreview;
	private boolean					executeInCloudProof;
	private boolean					executeInCloudSimulation;
	
	// Dews scripts
	private String 					preQualEngineScript;
	private String 					postQualEngineScript;
	private String 					postConnectorScript;
	
	private boolean					applyFilenamesToBundledImages;
	private String					filenameSeparatorForBundledImages;
	private boolean					playMessageOnEmptyVar;
	
	// Override Remote Server Settings
	private boolean 				overrideRemote;
	private String					remoteServerIP;
	private String					remoteServerPort;
	private String					remoteServerUser;
	private String					remoteServerPassword;
	private String 					DEServerGuid;
	private List<DEServer> 			DEServers;
	private String					bundleNameOverride;

	private String 					appliedDeVersion;
	
	private int						inputCharacterEncoding;
	private int						outputCharacterEncoding;
	
	private boolean					isAdvancedComposition;
	private boolean					applyFillableForms;
	
	private Document				document;
	private DocumentChannelEditVO	documentChannelEditVO;

	private boolean						overrideSMTP;
	private String						smtpHost;
	private String						smtpPort;
	private String						smtpSecurity;
	private String						smtpAccount;
	private String						smtpPassword;
	private String						smtpCustomHeader;
	private int 						useDefaultImage;

	public DocumentChannelEditWrapper(){
		super();
	}

	public void loadConnectorInformation() {
		
		ConnectorConfiguration connConfiguration = document.getConnectorConfiguration();
		// load connector information
		if (connConfiguration != null && connConfiguration.getConnector() != null) {
			setConnectorId(connConfiguration.getConnector().getId());
			setChannelId(connConfiguration.getConnector().getChannel().getId());
			setCustomerDriverInputFilename(connConfiguration.getCustomerDriverInputFileName());
			setExecuteInCloudPreview(connConfiguration.isExecuteInCloudPreview());
			setExecuteInCloudProof(connConfiguration.isExecuteInCloudProof());
			setExecuteInCloudSimulation(connConfiguration.isExecuteInCloudSimulation());
			setExecuteInCloudTest(connConfiguration.isExecuteInCloudTest());
			setApplyFilenamesToBundledImages(connConfiguration.getApplyFilenamesToBundledImages());
			setFilenameSeparatorForBundledImages(connConfiguration.getFilenameSeparatorForBundledImages());
			setPlayMessageOnEmptyVar(connConfiguration.getPlayMessageOnEmptyVar());
			setOverrideRemote(connConfiguration.isOverrideRemote());
			setRemoteServerIP(connConfiguration.getRemoteServerIP());
			setRemoteServerPort(connConfiguration.getRemoteServerPort());
			setRemoteServerUser(connConfiguration.getRemoteServerUser());
			setRemoteServerPassword(connConfiguration.getRemoteServerPassword());
			setDEServerGuid(connConfiguration.getDEServerGuid());
			setBundleNameOverride(connConfiguration.getBundleNameOverride());
			setAppliedDeVersion(connConfiguration.getAppliedDeVersion());
			setInputCharacterEncoding(connConfiguration.getInputCharacterEncoding());
			setOutputCharacterEncoding(connConfiguration.getOutputCharacterEncoding());
			setPreQualEngineScript(connConfiguration.getPreQualEngineScript());
			setPostQualEngineScript(connConfiguration.getPostQualEngineScript());
			setPostConnectorScript(connConfiguration.getPostConnectorScript());
			setUseDefaultImage(connConfiguration.getUseDefaultImage());

			ComplexValue outputFilename= connConfiguration.getOutputFilename();
			if( outputFilename != null )
				setOutputFilename(outputFilename.getViewContent());
			
			ComplexValue outputDocumentTitle= connConfiguration.getOutputDocumentTitle();
			if( outputDocumentTitle != null )
				setOutputDocumentTitle(outputDocumentTitle.getViewContent());
			
			setValidateProductionBundle(connConfiguration.getValidateProductionBundle());
			setEscapeTagsInDriverData(connConfiguration.getEscapeTagsInDriverData());
			setConvertTableBorderPxToPts(connConfiguration.getConvertTableBorderPxToPts());
			setEvalNotEqualOnMissingTag(connConfiguration.getEvalNotEqualOnMissingTag());
			setPlayEmptyAggFirstLastVar(connConfiguration.getPlayEmptyAggFirstLastVar());
			setRemoveZeroFromStyleConnector(connConfiguration.getRemoveZeroFromStyleConnector());
			setCompTimeParentTagging(connConfiguration.getCompTimeParentTagging());
			setDataGroupExpressionVarProc(connConfiguration.getDataGroupExpressionVarProc());
			setScriptVarAppliesUndefined(connConfiguration.getScriptVarAppliesUndefined());
			setBlueUnderlineLinks(connConfiguration.getBlueUnderlineLinks());
			setCorrectParagraphTextStyles(connConfiguration.getCorrectParagraphTextStyles());
			setFixInlineTargetingStyles(connConfiguration.getFixInlineTargetingStyles());
			setPreserveDataWhitespace(connConfiguration.getPreserveDataWhitespace());
			setNbspComposedAsSpace(connConfiguration.getNbspComposedAsSpace());
			setNormalizeImageLibrary(connConfiguration.getNormalizeImageLibrary());
			setNormalizeEmbeddedContent(connConfiguration.getNormalizeEmbeddedContent());
			setListStyleControlType(connConfiguration.getListStyleControlType());
			setColorOutputFormatType(connConfiguration.getColorOutputFormatType());
			setGmcSpanToTTag(connConfiguration.getGmcSpanToTTag());
			setUnalteredZonePDFPassthrough(connConfiguration.getUnalteredZonePDFPassthrough());

			if(connConfiguration.getQualificationOutput() != null)
				setQualificationOutputId(connConfiguration.getQualificationOutput().getId());

			// force hibernate to deproxy this object
			connConfiguration = HibernateDeproxyUtil.deproxy(connConfiguration, ConnectorConfiguration.class);
			if (connConfiguration instanceof GenericConnectorConfiguration) {
				setFormatType(((GenericConnectorConfiguration) connConfiguration).getFormatType());
				setXmlFileName(((GenericConnectorConfiguration) connConfiguration).getFileName());

				document.getConnectorConfigWrapper().setPreProcessXSLTFileName(((GenericConnectorConfiguration) connConfiguration).getPreProcessXSLTFile());
				document.getConnectorConfigWrapper().setPostProcessXSLTFileName(((GenericConnectorConfiguration) connConfiguration).getPostProcessXSLTFile());
			}
			if (connConfiguration instanceof DialogueConfiguration) {
				setPubFileName( ((DialogueConfiguration) connConfiguration).getPubFile() );
				setSupportsStyles(((DialogueConfiguration) connConfiguration).isSupportsStyles());
				setLegacyDxfMode(((DialogueConfiguration) connConfiguration).isLegacyDxfMode());
				setMixedDxfTaggedText( ((DialogueConfiguration) connConfiguration).isMixedDxfTaggedText());
				setRunTimeDxf( ((DialogueConfiguration) connConfiguration).isRunTimeDxf());
				setCompositionFileSet( ((DialogueConfiguration) connConfiguration).getCompositionFileSet() );
				setCompositionVersion(((DialogueConfiguration) connConfiguration).getCompositionVersion());
			} else if (connConfiguration instanceof GMCConfiguration) {
				setWorkflowFileName( ((GMCConfiguration) connConfiguration).getWorkflowFile() );
				setCompositionFileSet( ((GMCConfiguration) connConfiguration).getCompositionFileSet() );
				setCompositionVersion(((GMCConfiguration) connConfiguration).getCompositionVersion());
				setControlStyles(((GMCConfiguration) connConfiguration).getControlStyles());
				setTextStyleCompositionType(((GMCConfiguration) connConfiguration).getTextStyleCompositionType());
				setParagraphStyleCompositionType(((GMCConfiguration) connConfiguration).getParagraphStyleCompositionType());
				setListStyleCompositionType(((GMCConfiguration) connConfiguration).getListStyleCompositionType());
				setRestrictedQuotes(((GMCConfiguration) connConfiguration).getRestrictedQuotes());
			} else if (connConfiguration instanceof EMessagingConfiguration) {
				if (connConfiguration.getConnector().getChannel().isSMS())
					setCustomerPhoneNumberVariable( ((EMessagingConfiguration) connConfiguration).getCustomerPhoneNumberVariable() );
				else
					setCustomerEmailAddressVariable( ((EMessagingConfiguration) connConfiguration).getCustomerEmailAddressVariable() );
			} else if (connConfiguration instanceof SendmailConfiguration) {
				setCustomerEmailAddressVariable( ((SendmailConfiguration) connConfiguration).getCustomerEmailAddressVariable() );
				setOverrideSMTP( ((SendmailConfiguration) connConfiguration).isOverrideSMTP() );
				setSmtpHost( ((SendmailConfiguration) connConfiguration).getSmtpHost() );
				setSmtpPort( ((SendmailConfiguration) connConfiguration).getSmtpPort() );
				setSmtpSecurity( ((SendmailConfiguration) connConfiguration).getSmtpSecurity() );
				setSmtpAccount( ((SendmailConfiguration) connConfiguration).getSmtpAccount() );
				setSmtpPassword( ((SendmailConfiguration) connConfiguration).getSmtpPassword() );
				setSmtpCustomHeader( ((SendmailConfiguration) connConfiguration).getSmtpCustomHeader() );
			} else if (connConfiguration instanceof ExactTargetConfiguration) {
				setCustomerEmailAddressVariable( ((ExactTargetConfiguration) connConfiguration).getCustomerEmailAddressVariable() );
				setCustomerKeyVariable( ((ExactTargetConfiguration) connConfiguration).getCustomerKeyVariable() );
			} else if (connConfiguration instanceof ClickatellConfiguration) {
				setCustomerPhoneNumberVariable( ((ClickatellConfiguration) connConfiguration).getCustomerPhoneNumberVariable() );
			} else if (connConfiguration instanceof FtpConfiguration) {
				setServerId(((FtpConfiguration) connConfiguration).getServerId());
				setWebURL(((FtpConfiguration) connConfiguration).getWebURL());
				setRecipientFileLocation(((FtpConfiguration) connConfiguration).getRecipientFileLocation());
				ComplexValue fileNameValue= ((FtpConfiguration) connConfiguration).getRecipientFileComplexValue();
				if( fileNameValue != null) {
					setRecipientFileName(fileNameValue.getEncodedValue());
				}
				setIsEmbedded(((FtpConfiguration) connConfiguration).getIsEmbedded());
			}
			else if ( connConfiguration instanceof NativeCompositionConfiguration ) {
				setAdvancedComposition( ((NativeCompositionConfiguration) connConfiguration).isAdvancedComposition() );
				setApplyFillableForms( ((NativeCompositionConfiguration) connConfiguration).isApplyFillableForms() );
				setOutputFileTypeId( ((NativeCompositionConfiguration) connConfiguration).getOutputFileType() );
				setDuplexOutput( ((NativeCompositionConfiguration) connConfiguration).getDuplexOutput() );
				setStartsOnOddPage( ((NativeCompositionConfiguration) connConfiguration).getStartsOnOddPage() );
			}
			else if ( connConfiguration instanceof SefasConfiguration ) {
				setCompositionFileSet( ((SefasConfiguration) connConfiguration).getCompositionFileSet() );
				setCompositionVersion(((SefasConfiguration) connConfiguration).getCompositionVersion());
				setOutputFileTypeId( ((SefasConfiguration) connConfiguration).getOutputFileType() );
				setDuplexOutput( ((SefasConfiguration) connConfiguration).getDuplexOutput() );
				setStartsOnOddPage( ((SefasConfiguration) connConfiguration).getStartsOnOddPage() );
				setTemplateControl( ((SefasConfiguration) connConfiguration).getTemplateControl());
				setAccessibility( ((SefasConfiguration) connConfiguration).getAccessibility());
				setLinespacePosition( ((SefasConfiguration) connConfiguration).getLinespacePosition());
				setTablePaddingInPts( ((SefasConfiguration) connConfiguration).getTablePaddingInPts());

			}
			else if ( connConfiguration instanceof MPHCSConfiguration ) {
				setCompositionFileSet( ((MPHCSConfiguration) connConfiguration).getCompositionFileSet() );
				setCompositionVersion(((MPHCSConfiguration) connConfiguration).getCompositionVersion());
				setOutputFileTypeId( ((MPHCSConfiguration) connConfiguration).getOutputFileType() );
				setDuplexOutput( ((MPHCSConfiguration) connConfiguration).getDuplexOutput() );
				setStartsOnOddPage( ((MPHCSConfiguration) connConfiguration).getStartsOnOddPage() );
				setTemplateControl( ((MPHCSConfiguration) connConfiguration).getTemplateControl());
				setAccessibility( ((MPHCSConfiguration) connConfiguration).getAccessibility());
				setLinespacePosition( ((MPHCSConfiguration) connConfiguration).getLinespacePosition());
				setTablePaddingInPts( ((MPHCSConfiguration) connConfiguration).getTablePaddingInPts());
			}
		}

		setDEServers(DEServer.findAll());
	}

	public void setDocument(Document refDocument){
		document = refDocument;
	}

	public Document getDocument(){
		return document;
	}

	public boolean isSupportsStyles() {
		return supportsStyles;
	}
	public void setSupportsStyles(boolean supportsStyles) {
		this.supportsStyles = supportsStyles;
	}

	public boolean isLegacyDxfMode() {
		return legacyDxfMode;
	}
	public void setLegacyDxfMode(boolean legacyDxfMode) {
		this.legacyDxfMode = legacyDxfMode;
	}

	public boolean isMixedDxfTaggedText() {
		return mixedDxfTaggedText;
	}
	public void setMixedDxfTaggedText(boolean mixedDxfTaggedText) {
		this.mixedDxfTaggedText = mixedDxfTaggedText;
	}

	public boolean isRunTimeDxf() {
		return runTimeDxf;
	}

	public void setRunTimeDxf(boolean runTimeDxf) {
		this.runTimeDxf = runTimeDxf;
	}

	public boolean getTemplateControl() { return templateControl; }
	public void setTemplateControl(boolean templateControl) { this.templateControl = templateControl; }

	public boolean getAccessibility() { return accessibility; }
	public void setAccessibility(boolean accessibility) { this.accessibility = accessibility; }

	public Integer getLinespacePosition() { return linespacePosition; }
	public void setLinespacePosition(Integer linespacePosition) { this.linespacePosition = linespacePosition; }

	public int getListStyleControlType() {return listStyleControlType; }
	public void setListStyleControlType(int listStyleControlType) { this.listStyleControlType = listStyleControlType; }

	public int getColorOutputFormatType() {return colorOutputFormatType; }
	public void setColorOutputFormatType(int colorOutputFormatType) { this.colorOutputFormatType = colorOutputFormatType; }

	public boolean getTablePaddingInPts() { return tablePaddingInPts; }
	public void setTablePaddingInPts(boolean tablePaddingInPts) { this.tablePaddingInPts = tablePaddingInPts; }

	public boolean getControlStyles() {
		return controlStyles;
	}
	public void setControlStyles(boolean controlStyles) {
		this.controlStyles = controlStyles;
	}

	public int getTextStyleCompositionType() {
		return textStyleCompositionType;
	}
	public void setTextStyleCompositionType(int textStyleCompositionType) {
		this.textStyleCompositionType = textStyleCompositionType;
	}

	public int getParagraphStyleCompositionType() {
		return paragraphStyleCompositionType;
	}
	public void setParagraphStyleCompositionType(int paragraphStyleCompositionType) {
		this.paragraphStyleCompositionType = paragraphStyleCompositionType;
	}

	public int getListStyleCompositionType() {
		return listStyleCompositionType;
	}
	public void setListStyleCompositionType(int listStyleCompositionType) {
		this.listStyleCompositionType = listStyleCompositionType;
	}

	public boolean getRestrictedQuotes() {
		return restrictedQuotes;
	}
	public void setRestrictedQuotes(boolean restrictedQuotes) {
		this.restrictedQuotes = restrictedQuotes;
	}

	// ** Field in use to store remote file location folder. The filename itself is coming from Data Resource filename. 
	public String getCustomerDriverInputFilename() {
		return customerDriverInputFilename;
	}
	public void setCustomerDriverInputFilename(String customerDriverInputFilename) {
		this.customerDriverInputFilename = customerDriverInputFilename;
	}

	public DataElementVariable getCustomerEmailAddressVariable() {
		return customerEmailAddressVariable;
	}
	public void setCustomerEmailAddressVariable(
			DataElementVariable customerEmailAddressVariable) {
		this.customerEmailAddressVariable = customerEmailAddressVariable;
	}

	public DataElementVariable getCustomerPhoneNumberVariable() {
		return customerPhoneNumberVariable;
	}
	public void setCustomerPhoneNumberVariable(DataElementVariable customerPhoneNumberVariable) {
		this.customerPhoneNumberVariable = customerPhoneNumberVariable;
	}

	public DataElementVariable getCustomerKeyVariable() {
		return customerKeyVariable;
	}
	public void setCustomerKeyVariable(
			DataElementVariable customerKeyVariable) {
		this.customerKeyVariable = customerKeyVariable;
	}

	public CompositionFileSet getCompositionFileSet() {
		return compositionFileSet;
	}
	public void setCompositionFileSet(CompositionFileSet compositionFileSet) {
		this.compositionFileSet = compositionFileSet;
	}

	public String getCompositionVersion() {
		return compositionVersion;
	}

	public void setCompositionVersion(String compositionVersion) {
		this.compositionVersion = compositionVersion;
	}

	public String getOutputFilename() {
		return outputFilename;
	}

	public void setOutputFilename(String outputFilename) {
		this.outputFilename = outputFilename;
	}
	
	public String getOutputDocumentTitle() {
		return outputDocumentTitle;
	}

	public void setOutputDocumentTitle(String outputDocumentTitle) {
		this.outputDocumentTitle = outputDocumentTitle;
	}

	public String getXmlFileName() {
		return xmlFileName;
	}
	public void setXmlFileName(String xmlFileName) {
		this.xmlFileName = xmlFileName;
	}

	public int getFormatType() {
		return formatType;
	}
	public void setFormatType(int formatType) {
		this.formatType = formatType;
	}

	public String getPubFileName() {
		return pubFileName;
	}
	public void setPubFileName(String pubFileName) {
		this.pubFileName = pubFileName;
	}

	public String getWorkflowFileName() {
		return workflowFileName;
	}
	public void setWorkflowFileName(String workflowFileName) {
		this.workflowFileName = workflowFileName;
	}

	public long getChannelId() {
		return channelId;
	}
	public void setChannelId(long channelId) {
		this.channelId = channelId;
	}

	public long getConnectorId() {
		return connectorId;
	}
	public void setConnectorId(long connectorId) {
		this.connectorId = connectorId;
	}

	public void setQualificationOutputId(long qualificationOutputId) {
		this.qualificationOutputId = qualificationOutputId;
	}
	public long getQualificationOutputId() {
		return qualificationOutputId;
	}

	public int getOutputFileTypeId() {
		return outputFileTypeId;
	}
	public void setOutputFileTypeId(int outputFileTypeId) {
		this.outputFileTypeId = outputFileTypeId;
	}
	
	public Boolean getStartsOnOddPage() {
		return startsOnOddPage;
	}
	public void setStartsOnOddPage(Boolean startsOnOddPage) {
		this.startsOnOddPage = startsOnOddPage;
	}

	public Boolean getDuplexOutput() {
		return duplexOutput;
	}
	public void setDuplexOutput(Boolean duplexOutput) {
		this.duplexOutput = duplexOutput;
	}

	public long getServerId() {
		return serverId;
	}

	public void setServerId(long serverId) {
		this.serverId = serverId;
	}

	public String getWebURL() {
		return webURL;
	}

	public void setWebURL(String webURL) {
		this.webURL = webURL;
	}

	public String getRecipientFileLocation() {
		return recipientFileLocation;
	}

	public void setRecipientFileLocation(String recipientFileLocation) {
		this.recipientFileLocation = recipientFileLocation;
	}

	public String getRecipientFileName() {
		return recipientFileName;
	}

	public void setRecipientFileName(String recipientFileName) {
		this.recipientFileName = recipientFileName;
	}

	public boolean getIsEmbedded() {
		return isEmbedded;
	}

	public void setIsEmbedded(boolean isEmbedded) {
		this.isEmbedded = isEmbedded;
	}

	public boolean isExecuteInCloudTest() {
		return executeInCloudTest;
	}

	public void setExecuteInCloudTest(boolean executeInCloudTest) {
		this.executeInCloudTest = executeInCloudTest;
	}

	public boolean isExecuteInCloudPreview() {
		return executeInCloudPreview;
	}

	public void setExecuteInCloudPreview(boolean executeInCloudPreview) {
		this.executeInCloudPreview = executeInCloudPreview;
	}

	public boolean isExecuteInCloudProof() {
		return executeInCloudProof;
	}

	public void setExecuteInCloudProof(boolean executeInCloudProof) {
		this.executeInCloudProof = executeInCloudProof;
	}

	public boolean isExecuteInCloudSimulation() {
		return executeInCloudSimulation;
	}

	public void setExecuteInCloudSimulation(boolean executeInCloudSimulation) {
		this.executeInCloudSimulation = executeInCloudSimulation;
	}

	public boolean getApplyFilenamesToBundledImages() {
		return applyFilenamesToBundledImages;
	}

	public void setApplyFilenamesToBundledImages(boolean applyFilenamesToBundledImages) {
		this.applyFilenamesToBundledImages = applyFilenamesToBundledImages;
	}

	public String getFilenameSeparatorForBundledImages() {
		return filenameSeparatorForBundledImages;
	}

	public void setFilenameSeparatorForBundledImages(String filenameSeparatorForBundledImages) {
		this.filenameSeparatorForBundledImages = filenameSeparatorForBundledImages;
	}

	public boolean getPlayMessageOnEmptyVar() {
		return playMessageOnEmptyVar;
	}

	public void setPlayMessageOnEmptyVar(boolean playMessageOnEmptyVar) {
		this.playMessageOnEmptyVar = playMessageOnEmptyVar;
	}

	public boolean isOverrideRemote() {
		return overrideRemote;
	}

	public void setOverrideRemote(boolean overrideRemote) {
		this.overrideRemote = overrideRemote;
	}

	public String getRemoteServerIP() {
		return remoteServerIP;
	}

	public void setRemoteServerIP(String remoteServerIP) {
		this.remoteServerIP = remoteServerIP;
	}

	public String getRemoteServerPort() {
		return remoteServerPort;
	}

	public void setRemoteServerPort(String remoteServerPort) {
		this.remoteServerPort = remoteServerPort;
	}
	public String getRemoteServerUser() {
		return remoteServerUser;
	}

	public void setRemoteServerUser(String remoteServerUser) {
		this.remoteServerUser = remoteServerUser;
	}

	public String getRemoteServerPassword() {
		return remoteServerPassword;
	}

	public void setRemoteServerPassword(String remoteServerPassword) {
		this.remoteServerPassword = remoteServerPassword;
	}

	public String getAppliedDeVersion() {
		return appliedDeVersion;
	}

	public void setAppliedDeVersion(String appliedDeVersion) {
		this.appliedDeVersion = appliedDeVersion;
	}

	public int getInputCharacterEncoding() {
		return inputCharacterEncoding;
	}

	public void setInputCharacterEncoding(int inputCharacterEncoding) {
		this.inputCharacterEncoding = inputCharacterEncoding;
	}

	public int getOutputCharacterEncoding() {
		return outputCharacterEncoding;
	}

	public void setOutputCharacterEncoding(int outputCharacterEncoding) {
		this.outputCharacterEncoding = outputCharacterEncoding;
	}

	public boolean isAdvancedComposition() {
		return isAdvancedComposition;
	}

	public void setAdvancedComposition(boolean isAdvancedComposition) {
		this.isAdvancedComposition = isAdvancedComposition;
	}

	public boolean isApplyFillableForms() {
		return applyFillableForms;
	}

	public void setApplyFillableForms(boolean applyFillableForms) {
		this.applyFillableForms = applyFillableForms;
	}

	public String getPreQualEngineScript() {
		return preQualEngineScript;
	}

	public void setPreQualEngineScript(String preQualEngineScript) {
		this.preQualEngineScript = preQualEngineScript;
	}

	public String getPostQualEngineScript() {
		return postQualEngineScript;
	}

	public void setPostQualEngineScript(String postQualEngineScript) {
		this.postQualEngineScript = postQualEngineScript;
	}

	public String getPostConnectorScript() {
		return postConnectorScript;
	}

	public void setPostConnectorScript(String postConnectorScript) {
		this.postConnectorScript = postConnectorScript;
	}

	public DocumentChannelEditVO getDocumentChannelEditVO() {
		return documentChannelEditVO;
	}

	public void setDocumentChannelEditVO(Document document) {
		this.documentChannelEditVO = new DocumentChannelEditVO(document);
	}

	public Boolean getValidateProductionBundle() {
		return validateProductionBundle;
	}

	public void setValidateProductionBundle(Boolean validateProductionBundle) {
		this.validateProductionBundle = validateProductionBundle;
	}

	public Boolean getEscapeTagsInDriverData() {
		return escapeTagsInDriverData;
	}

	public void setEscapeTagsInDriverData(Boolean escapeTagsInDriverData) {
		this.escapeTagsInDriverData = escapeTagsInDriverData;
	}

	public Boolean getConvertTableBorderPxToPts() {
		return  convertTableBorderPxToPts;
	}

	public void setConvertTableBorderPxToPts(Boolean convertTableBorderPxToPts) {
		this.convertTableBorderPxToPts = convertTableBorderPxToPts;
	}

	public Boolean getEvalNotEqualOnMissingTag() {	return evalNotEqualOnMissingTag; }
	public void setEvalNotEqualOnMissingTag(Boolean evalNotEqualOnMissingTag) {
		this.evalNotEqualOnMissingTag = evalNotEqualOnMissingTag;
	}

	public Boolean getPlayEmptyAggFirstLastVar() { return playEmptyAggFirstLastVar;	}
	public void setPlayEmptyAggFirstLastVar(Boolean playEmptyAggFirstLastVar) {
		this.playEmptyAggFirstLastVar = playEmptyAggFirstLastVar;
	}

	public Boolean getRemoveZeroFromStyleConnector() { return removeZeroFromStyleConnector;	}
	public void setRemoveZeroFromStyleConnector(Boolean removeZeroFromStyleConnector) {
		this.removeZeroFromStyleConnector = removeZeroFromStyleConnector;
	}

	public Boolean getCompTimeParentTagging() { return compTimeParentTagging; }
	public void setCompTimeParentTagging(Boolean compTimeParentTagging) {
		this.compTimeParentTagging = compTimeParentTagging;
	}

	public Boolean getDataGroupExpressionVarProc() { return dataGroupExpressionVarProc; }
	public void setDataGroupExpressionVarProc(Boolean dataGroupExpressionVarProc) {
		this.dataGroupExpressionVarProc = dataGroupExpressionVarProc;
	}

	public Boolean getScriptVarAppliesUndefined() { return scriptVarAppliesUndefined; }
	public void setScriptVarAppliesUndefined(Boolean scriptVarAppliesUndefined) {
		this.scriptVarAppliesUndefined = scriptVarAppliesUndefined;
	}

	public Boolean getCorrectParagraphTextStyles() { return correctParagraphTextStyles; }
	public void setCorrectParagraphTextStyles(Boolean correctParagraphTextStyles) {
		this.correctParagraphTextStyles = correctParagraphTextStyles;
	}

	public Boolean getFixInlineTargetingStyles() { return fixInlineTargetingStyles; }
	public void setFixInlineTargetingStyles(Boolean fixInlineTargetingStyles) {
		this.fixInlineTargetingStyles = fixInlineTargetingStyles;
	}

	public Boolean getPreserveDataWhitespace() { return preserveDataWhitespace; }
	public void setPreserveDataWhitespace(Boolean preserveDataWhitespace) {
		this.preserveDataWhitespace = preserveDataWhitespace;
	}

	public Boolean getNbspComposedAsSpace() { return nbspComposedAsSpace; }
	public void setNbspComposedAsSpace(Boolean nbspComposedAsSpace) {
		this.nbspComposedAsSpace = nbspComposedAsSpace;
	}

	public Boolean getNormalizeImageLibrary() { return normalizeImageLibrary; }
	public void setNormalizeImageLibrary(Boolean normalizeImageLibrary) {
		this.normalizeImageLibrary = normalizeImageLibrary;
	}

	public Boolean getNormalizeEmbeddedContent() { return normalizeEmbeddedContent; }

	public void setNormalizeEmbeddedContent(Boolean normalizeEmbeddedContent) {
		this.normalizeEmbeddedContent = normalizeEmbeddedContent;
	}

	public Boolean getGmcSpanToTTag() { return gmcSpanToTTag; }
	public void setGmcSpanToTTag(Boolean gmcSpanToTTag) {
		this.gmcSpanToTTag = gmcSpanToTTag;
	}

	public Boolean getBlueUnderlineLinks() { return blueUnderlineLinks; }
	public void setBlueUnderlineLinks(Boolean blueUnderlineLinks) {
		this.blueUnderlineLinks = blueUnderlineLinks;
	}

	public Boolean getUnalteredZonePDFPassthrough() { return unalteredZonePDFPassthrough; }
	public void setUnalteredZonePDFPassthrough(Boolean unalteredZonePDFPassthrough) {
		this.unalteredZonePDFPassthrough = unalteredZonePDFPassthrough;
	}

	public boolean isOverrideSMTP() {
		return overrideSMTP;
	}

	public void setOverrideSMTP(boolean overrideSMTP) {
		this.overrideSMTP = overrideSMTP;
	}

	public String getSmtpHost() {
		return smtpHost;
	}

	public void setSmtpHost(String smtpHost) {
		this.smtpHost = smtpHost;
	}

	public String getSmtpPort() {
		return smtpPort;
	}

	public void setSmtpPort(String smtpPort) {
		this.smtpPort = smtpPort;
	}

	public String getSmtpSecurity() {
		return smtpSecurity;
	}

	public void setSmtpSecurity(String smtpSecurity) {
		this.smtpSecurity = smtpSecurity;
	}

	public String getSmtpAccount() {
		return smtpAccount;
	}

	public void setSmtpAccount(String smtpAccount) {
		this.smtpAccount = smtpAccount;
	}

	public String getSmtpPassword() {
		return smtpPassword;
	}

	public void setSmtpPassword(String smtpPassword) {
		this.smtpPassword = smtpPassword;
	}

	public String getSmtpCustomHeader() {
		return smtpCustomHeader;
	}

	public void setSmtpCustomHeader(String smtpCustomHeader) {
		this.smtpCustomHeader = smtpCustomHeader;
	}

	public String getDEServerGuid() {
		return DEServerGuid;
	}

	public void setDEServerGuid(String DEServerGuid) {
		this.DEServerGuid = DEServerGuid;
	}

	public List<DEServer> getDEServers() {
		return DEServers;
	}

	public void setDEServers(List<DEServer> DEServers) {
		this.DEServers = DEServers;
	}

	public String getBundleNameOverride() {
		return bundleNameOverride;
	}

	public void setBundleNameOverride(String bundleNameOverride) {
		this.bundleNameOverride = bundleNameOverride;
	}

	public int getUseDefaultImage() {
		return useDefaultImage;
	}

	public void setUseDefaultImage(int useDefaultImage) {
		this.useDefaultImage = useDefaultImage;
	}
}
