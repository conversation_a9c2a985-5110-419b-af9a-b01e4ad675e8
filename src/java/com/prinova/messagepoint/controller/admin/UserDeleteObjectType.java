package com.prinova.messagepoint.controller.admin;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import com.prinova.messagepoint.model.common.StaticType;
import com.prinova.messagepoint.util.ApplicationUtil;

public class UserDeleteObjectType extends StaticType {


	public static final int ID_WIDGET_TYPE_MY_MESSAGES								= 1;
	public static final int ID_WIDGET_TYPE_MY_MESSAGE_APPROVALS						= 2;
	public static final int ID_WIDGET_TYPE_MY_SMART_TEXT							= 3;
	public static final int ID_WIDGET_TYPE_MY_SMART_TEXT_APPROVALS					= 4;
	public static final int ID_WIDGET_TYPE_MY_IMAGES								= 5;
	public static final int ID_WIDGET_TYPE_MY_IMAGE_APPROVALS						= 6;
	public static final int ID_WIDGET_TYPE_MY_VARIANTS								= 7;
	public static final int ID_WIDGET_TYPE_MY_VARIANT_APPROVALS						= 8;
	public static final int ID_WIDGET_TYPE_MY_RECENTLY_COMPLETED_TESTS				= 9;
	public static final int ID_WIDGET_TYPE_MY_RECENTLY_COMPLETED_PROOFS				= 10;
	public static final int ID_WIDGET_TYPE_MY_TASKS									= 11;
	public static final int ID_WIDGET_TYPE_MY_TASK_APPROVALS						= 12;
	public static final int ID_WIDGET_TYPE_METADATA									= 13;
	public static final int ID_WIDGET_TYPE_CONTENT									= 14;
	public static final int ID_WIDGET_TYPE_MY_TRANSLATIONS							= 15;

	public static final String MESSAGE_CODE_WIDGET_TYPE_MY_MESSAGES 				= "page.label.widget.type.my.messages";
	public static final String MESSAGE_CODE_WIDGET_TYPE_MY_MESSAGE_APPROVALS 		= "page.label.widget.type.my.message.approvals";
	public static final String MESSAGE_CODE_WIDGET_TYPE_MY_SMART_TEXT		 		= "page.label.widget.type.my.smart.text";
	public static final String MESSAGE_CODE_WIDGET_TYPE_MY_SMART_TEXT_APPROVALS 	= "page.label.widget.type.my.smart.text.approvals";
	public static final String MESSAGE_CODE_WIDGET_TYPE_MY_IMAGES 					= "page.label.widget.type.my.images";
	public static final String MESSAGE_CODE_WIDGET_TYPE_MY_IMAGE_APPROVALS 			= "page.label.widget.type.my.image.approvals";
	public static final String MESSAGE_CODE_WIDGET_TYPE_MY_VARIANTS 				= "page.label.widget.type.my.variants";
	public static final String MESSAGE_CODE_WIDGET_TYPE_MY_VARIANT_APPROVALS 		= "page.label.widget.type.my.variant.approvals";
	public static final String MESSAGE_CODE_WIDGET_TYPE_MY_RECENTLY_COMPLETED_TESTS 	= "page.label.widget.type.my.recently.completed.tests";
	public static final String MESSAGE_CODE_WIDGET_TYPE_MY_RECENTLY_COMPLETED_PROOFS 	= "page.label.widget.type.my.recently.completed.proofs";
	public static final String MESSAGE_CODE_WIDGET_TYPE_MY_TASKS 					= "page.label.widget.type.my.tasks";
	public static final String MESSAGE_CODE_WIDGET_TYPE_MY_TASK_APPROVALS		 	= "page.label.widget.type.my.task.approvals";
	public static final String MESSAGE_CODE_WIDGET_TYPE_METADATA 					= "page.label.widget.type.metadata";
	public static final String MESSAGE_CODE_WIDGET_TYPE_CONTENT					 	= "page.label.widget.type.content";
	public static final String MESSAGE_CODE_WIDGET_TYPE_MY_TRANSLATIONS			 	= "page.label.widget.type.my.translations";
	
	private int								ranking;
	private boolean 						containsActions							= false;
	private List<UserDeleteActionType> 		actionsList								= new ArrayList<>();
	private boolean 						containsFilter							= false;
	private boolean 						containsSearch							= false;
	
	
	public UserDeleteObjectType(){
		super();
	}

	public UserDeleteObjectType(Integer id) {
		super();
		switch (id) {
			case ID_WIDGET_TYPE_MY_MESSAGES:
				this.setId(ID_WIDGET_TYPE_MY_MESSAGES);
				this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_MESSAGES));
				this.setDisplayMessageCode(MESSAGE_CODE_WIDGET_TYPE_MY_MESSAGES);
				this.setContainsFilter(true);
				break;
			case ID_WIDGET_TYPE_MY_MESSAGE_APPROVALS:
				this.setId(ID_WIDGET_TYPE_MY_MESSAGE_APPROVALS);
				this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_MESSAGE_APPROVALS));
				this.setDisplayMessageCode(MESSAGE_CODE_WIDGET_TYPE_MY_MESSAGE_APPROVALS);
				this.setContainsActions(true);
				this.setContainsFilter(true);
				this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_APPROVE));
				this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_REJECT));
				break;
			case ID_WIDGET_TYPE_MY_SMART_TEXT:
				this.setId(ID_WIDGET_TYPE_MY_SMART_TEXT);
				this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_SMART_TEXT));
				this.setDisplayMessageCode(MESSAGE_CODE_WIDGET_TYPE_MY_SMART_TEXT);
				this.setContainsFilter(true);
				break;
			case ID_WIDGET_TYPE_MY_SMART_TEXT_APPROVALS:
				this.setId(ID_WIDGET_TYPE_MY_SMART_TEXT_APPROVALS);
				this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_SMART_TEXT_APPROVALS));
				this.setDisplayMessageCode(MESSAGE_CODE_WIDGET_TYPE_MY_SMART_TEXT_APPROVALS);
				this.setContainsActions(true);
				this.setContainsFilter(true);
				this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_APPROVE));
				this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_REJECT));
				break;
			case ID_WIDGET_TYPE_MY_IMAGES:
				this.setId(ID_WIDGET_TYPE_MY_IMAGES);
				this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_IMAGES));
				this.setDisplayMessageCode(MESSAGE_CODE_WIDGET_TYPE_MY_IMAGES);
				this.setContainsFilter(true);
				break;
			case ID_WIDGET_TYPE_MY_IMAGE_APPROVALS:
				this.setId(ID_WIDGET_TYPE_MY_IMAGE_APPROVALS);
				this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_IMAGE_APPROVALS));
				this.setDisplayMessageCode(MESSAGE_CODE_WIDGET_TYPE_MY_IMAGE_APPROVALS);
				this.setContainsActions(true);
				this.setContainsFilter(true);
				this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_APPROVE));
				this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_REJECT));
				break;
			case ID_WIDGET_TYPE_MY_VARIANTS:
				this.setId(ID_WIDGET_TYPE_MY_VARIANTS);
				this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_VARIANTS));
				this.setDisplayMessageCode(MESSAGE_CODE_WIDGET_TYPE_MY_VARIANTS);
				this.setContainsFilter(true);
				break;
			case ID_WIDGET_TYPE_MY_VARIANT_APPROVALS:
				this.setId(ID_WIDGET_TYPE_MY_VARIANT_APPROVALS);
				this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_VARIANT_APPROVALS));
				this.setDisplayMessageCode(MESSAGE_CODE_WIDGET_TYPE_MY_VARIANT_APPROVALS);
				this.setContainsActions(true);
				this.setContainsFilter(true);
				this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_APPROVE));
				this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_REJECT));
				break;
			case ID_WIDGET_TYPE_MY_TASKS:
				this.setId(ID_WIDGET_TYPE_MY_TASKS);
				this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_TASKS));
				this.setDisplayMessageCode(MESSAGE_CODE_WIDGET_TYPE_MY_TASKS);
				this.setContainsActions(true);
				this.setContainsFilter(true);
				this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_MARK_COMPLETE));
				break;
			case ID_WIDGET_TYPE_MY_TASK_APPROVALS:
				this.setId(ID_WIDGET_TYPE_MY_TASK_APPROVALS);
				this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_TASK_APPROVALS));
				this.setDisplayMessageCode(MESSAGE_CODE_WIDGET_TYPE_MY_TASK_APPROVALS);
				this.setContainsActions(true);
				this.setContainsFilter(true);
				this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_APPROVE));
				this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_REJECT));
				break;
		
		}
	}
	
	public UserDeleteObjectType(String name) {
		super();

		if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_MESSAGES))) {
			this.setId(ID_WIDGET_TYPE_MY_MESSAGES);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_MESSAGES));
			this.setDisplayMessageCode(MESSAGE_CODE_WIDGET_TYPE_MY_MESSAGES);
			this.setContainsFilter(true);
		}
		else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_MESSAGE_APPROVALS))) {
			this.setId(ID_WIDGET_TYPE_MY_MESSAGE_APPROVALS);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_MESSAGE_APPROVALS));
			this.setDisplayMessageCode(MESSAGE_CODE_WIDGET_TYPE_MY_MESSAGE_APPROVALS);
			this.setContainsActions(true);
			this.setContainsFilter(true);
			this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_APPROVE));
			this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_REJECT));
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_SMART_TEXT))) {
			this.setId(ID_WIDGET_TYPE_MY_SMART_TEXT);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_SMART_TEXT));
			this.setDisplayMessageCode(MESSAGE_CODE_WIDGET_TYPE_MY_SMART_TEXT);
			this.setContainsFilter(true);
		}
		else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_SMART_TEXT_APPROVALS))) {
			this.setId(ID_WIDGET_TYPE_MY_SMART_TEXT_APPROVALS);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_SMART_TEXT_APPROVALS));
			this.setDisplayMessageCode(MESSAGE_CODE_WIDGET_TYPE_MY_SMART_TEXT_APPROVALS);
			this.setContainsActions(true);
			this.setContainsFilter(true);
			this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_APPROVE));
			this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_REJECT));
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_IMAGES))) {
			this.setId(ID_WIDGET_TYPE_MY_IMAGES);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_IMAGES));
			this.setDisplayMessageCode(MESSAGE_CODE_WIDGET_TYPE_MY_IMAGES);
			this.setContainsFilter(true);
		}else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_IMAGE_APPROVALS))) {
			this.setId(ID_WIDGET_TYPE_MY_IMAGE_APPROVALS);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_IMAGE_APPROVALS));
			this.setDisplayMessageCode(MESSAGE_CODE_WIDGET_TYPE_MY_IMAGE_APPROVALS);
			this.setContainsActions(true);
			this.setContainsFilter(true);
			this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_APPROVE));
			this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_REJECT));
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_VARIANTS))) {
			this.setId(ID_WIDGET_TYPE_MY_VARIANTS);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_VARIANTS));
			this.setDisplayMessageCode(MESSAGE_CODE_WIDGET_TYPE_MY_VARIANTS);
			this.setContainsFilter(true);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_VARIANT_APPROVALS))) {
			this.setId(ID_WIDGET_TYPE_MY_VARIANT_APPROVALS);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_VARIANT_APPROVALS));
			this.setDisplayMessageCode(MESSAGE_CODE_WIDGET_TYPE_MY_VARIANT_APPROVALS);
			this.setContainsActions(true);
			this.setContainsFilter(true);
			this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_APPROVE));
			this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_REJECT));
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_TASKS))) { 
			this.setId(ID_WIDGET_TYPE_MY_TASKS);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_TASKS));
			this.setDisplayMessageCode(MESSAGE_CODE_WIDGET_TYPE_MY_TASKS);
			this.setContainsActions(true);
			this.setContainsFilter(true);
			this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_MARK_COMPLETE));
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_TASK_APPROVALS))) { 
			this.setId(ID_WIDGET_TYPE_MY_TASK_APPROVALS);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WIDGET_TYPE_MY_TASK_APPROVALS));
			this.setDisplayMessageCode(MESSAGE_CODE_WIDGET_TYPE_MY_TASK_APPROVALS);
			this.setContainsActions(true);
			this.setContainsFilter(true);
			this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_APPROVE));
			this.getActionsList().add(new UserDeleteActionType(UserDeleteActionType.ID_ACTION_TYPE_REJECT));
		}
	}
	
	public static List<UserDeleteObjectType> listAll() {
		List<UserDeleteObjectType> allWidgetTypes = new ArrayList<>();
		
		UserDeleteObjectType widget = null;

		widget = new UserDeleteObjectType(ID_WIDGET_TYPE_MY_MESSAGES);
		widget.setRanking(12);
		allWidgetTypes.add(widget);
		widget = new UserDeleteObjectType(ID_WIDGET_TYPE_MY_MESSAGE_APPROVALS);
		widget.setRanking(4);
		allWidgetTypes.add(widget);
		widget = new UserDeleteObjectType(ID_WIDGET_TYPE_MY_SMART_TEXT);
		widget.setRanking(13);
		allWidgetTypes.add(widget);
		widget = new UserDeleteObjectType(ID_WIDGET_TYPE_MY_SMART_TEXT_APPROVALS);
		widget.setRanking(6);
		allWidgetTypes.add(widget);
		widget = new UserDeleteObjectType(ID_WIDGET_TYPE_MY_IMAGES);
		widget.setRanking(14);
		allWidgetTypes.add(widget);
		widget = new UserDeleteObjectType(ID_WIDGET_TYPE_MY_IMAGE_APPROVALS);
		widget.setRanking(7);
		allWidgetTypes.add(widget);
		widget = new UserDeleteObjectType(ID_WIDGET_TYPE_MY_VARIANTS);
		widget.setRanking(11);
		allWidgetTypes.add(widget);
		widget = new UserDeleteObjectType(ID_WIDGET_TYPE_MY_VARIANT_APPROVALS);
		widget.setRanking(5);
		allWidgetTypes.add(widget);
		widget = new UserDeleteObjectType(ID_WIDGET_TYPE_MY_TASKS);
		widget.setRanking(3);
		allWidgetTypes.add(widget);
		widget = new UserDeleteObjectType(ID_WIDGET_TYPE_MY_TASK_APPROVALS);
		widget.setRanking(8);
		allWidgetTypes.add(widget);
		
		// Sort by the ranking
		allWidgetTypes.sort(new Comparator<>() {
            @Override
            public int compare(UserDeleteObjectType o1, UserDeleteObjectType o2) {
                if (o1.getRanking() < o2.getRanking()) {
                    return -1;
                } else if (o1.getRanking() > o2.getRanking()) {
                    return 1;
                } else {
                    return 0;
                }
            }
        });
		return allWidgetTypes;
	}

	public int getRanking() {
		return ranking;
	}

	public void setRanking(int ranking) {
		this.ranking = ranking;
	}

	public boolean isContainsActions() {
		return containsActions;
	}

	public void setContainsActions(boolean containsActions) {
		this.containsActions = containsActions;
	}
	
	public List<UserDeleteActionType> getActionsList() {
		return actionsList;
	}

	public void setActionsList(List<UserDeleteActionType> actionsList) {
		this.actionsList = actionsList;
	}

	public boolean isContainsFilter() {
		return containsFilter;
	}

	public void setContainsFilter(boolean containsFilter) {
		this.containsFilter = containsFilter;
	}

	public boolean isContainsSearch() {
		return containsSearch;
	}

	public void setContainsSearch(boolean containsSearch) {
		this.containsSearch = containsSearch;
	}
}
