package com.prinova.messagepoint.platform.services.backgroundtask.sync;

public enum SyncState {
    CREATED(0),
    STARTED(2),
    COMPLETED(4),
    ERROR(8),
    SKIPPED(16);

    private final Integer state;

    SyncState(Integer state) {
        this.state = state;
    }

    public Integer getState() {
        return state;
    }

    public static SyncState getSyncState(Integer state){
        for(SyncState syncState: SyncState.values()){
            if(syncState.getState().equals(state)){
                return syncState;
            }
        }
        return null;
    }

    public Boolean isError(){
        return state.equals(SyncState.ERROR.state);
    }

    public Boolean isSkipped(){
        return state.equals(SyncState.SKIPPED.state);
    }

    public Boolean isCompleted(){
        return state.equals(SyncState.COMPLETED.state);
    }

}
