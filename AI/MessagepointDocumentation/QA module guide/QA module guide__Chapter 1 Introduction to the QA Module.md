




# Chapter 1: Introduction to the QA Module

The Messagepoint QA Module is an add-on module for Messagepoint that provides advanced quality assurance capabilities to help teams ensure the accuracy of long, complex documents.

Messagepoint comes with workflow, proofing and testing capabilities that provide control at the individual content object level. The Messagepoint QA Module workflow addresses an entire document.

The Messagepoint QA Module provides real-time visibility and control to ensure the accuracy of document changes. It would typically be used for any quality assurance process that:

  * Involves long or complex documents such as disclosures, policies, contracts, welcome kits, Evidence of Coverage.

  * Requires robust workflow to coordinate across and manage large or distributed teams involved in the review cycle.

  * Encompasses large complex projects such as re-rewrite or re-branding projects.




The Messagepoint QA Module enables teams to confidently manage the quality assurance process for critical documents where accuracy is paramount. Key capabilities include the ability to:

  * Compare documents side by side against previous versions.

  * Capture and track annotations and mark-up made by reviewers to show exactly what was changed, by whom and when.

  * Configure reporting and dashboards to provide managers and team members with real-time visibility into the status of the program exactly as they need it.

  * Manage and configure the robust workflow engine to match your QA process.

  * Control tasks and programs with advanced task management and issue logging.



﻿




## Browsers Supported

Messagepoint supports the following browsers:

  * Firefox 40 or higher.

  * Chrome 42 or higher.

  * Microsoft Edge.




_Note_ : Ensure that your browser’s pop-up blocker and ad blocker are disabled for your Messagepoint URL. Both can interfere with Messagepoint’s display and functionality.
﻿




## File Formats Supported

Flowing text documents, like PDF and Word, are the recommended file formats to upload into the QA Module. 

Formats, like PPT and Excel, are not recommended for upload, even if converted to PDF. The nature of these formats is unlike flowing text documents. The results are unlikely to be useful and may vary.

Format |  QA Module Support  
---|---  
PDF |  Yes  
MS Word |  Yes; comments are stripped; all revisions are accepted upon upload.  
MS Excel |  Not supported  
MS PowerPoint |  No  
Text Files |  Customer must convert to PDF prior to upload.  
HTML |  Customer must convert to PDF.  
XML |  No  
JSON |  No  
AFP |  Customer must convert to PDF.
﻿




## QA Module Whitelisting Information

For a better user experience, Messagepoint strongly recommends whitelisting your learning and production QA Module web sites. Depending on your configuration, you may need to request whitelisting on your company firewall and/or your company VPN(s).

  * The public IP address to whitelist is *************.




Whitelisting allows network traffic from the QA Module sites to arrive quickly without being blocked, processed, or decrypted. What you are saying by whitelisting is that network traffic from the QA Module website is from a trusted source and may be let through unimpeded by security checks.

This is important because the QA Module specializes in the processing of large PDF test files. These test files can be hundreds of pages long. When these large files are run through the QA Module compare process, the compare files can easily reach 20-45 MB in size.

Without whitelisting, users are likely to see a PDF viewing process time out after several minutes with a JavaScript error similar to this:

![QA Module Whitelisting JavaScript error.](../Resources/Images/QA module guide/QA Module Whitelisting Information_445x363.png)

With whitelisting, compare screens like the one above typically load successfully in 5-15 seconds, depending on the size of the PDFs.
﻿




## Logging into the QA Module

To log into the QA Module:

  1. In your browser, enter the QA Module URL.



  * You may want to bookmark the URL to save you time in subsequent logins.



  1. Enter your _Username_.

  2. Enter your _Password_.

  3. Click the bar under the password field.



﻿




## The QA Module Interface

After login we have our first look at the QA Module. There is a header bar across the top. Under the header bar is the dashboard area with various widgets. We will discuss each area in the sections that follow.

![The QA Module interface after log-in.](../Resources/Images/QA module guide/The QA Module interface_465x222.png)
﻿




### The header bar

#### Top left

The top left bar includes the logo and Company drop-down. Also, there is an arrow to hide the side panel. When the side panel is hidden, the arrow points right. You can click on the right arrow to engage the panel.

![The top left of the header bar.](../Resources/Images/QA module guide/The Header Bar_172x119.png)

  * If you hover over the logo area¸ you can see the version of the software.




#### Top middle

The top middle includes a search bar.

![The top middle of the header bar.](../Resources/Images/QA module guide/The Header Bar_1_444x194.png)

You can use the search window to locate files, folders, and issues. Using the drop down menu you can specify the match for you search. You can search All matches or matches only on Title, Tags, Attribute Values, Attribute Names, Status, Comments, Issue ID, or Document Objects. 

You can add attributes and tags to folders, files, and compares (in the _Tags & Attributes_ panel) to use in this search.

#### Top right

The top right includes icons to access Tasks and Notifications. Your initials appear along with a Logout option.

![The top right of the header bar.](../Resources/Images/QA module guide/The Header Bar_2_97x29.png)
﻿




### Side panel

The left side panel contains the QA Module areas that you can access. You can select the areas you want to include in the side panel. You can also hide the side panel when you’re not using it. 
﻿




### Customize the side panel

You can organize the sections in the side panel and select the sections you want to include. 

_Note_ : Although you can add all sections to the side panel, your role will determine what actions you can perform in each section or if you can even access a section. The License section can only be accessed by users with a role that includes “All Admin Actions”.

By default, Dashboard, Files, and Reports menu items will display. 

![The side panel with the default dashboards.](../Resources/Images/QA module guide/Customize the side panel_163x94.png)

At the bottom of the side panel, there is a gear to access _Sections_. You can determine which sections appear in your side panel, given your permissions.

![The gear in the side panel to access Sections.](../Resources/Images/QA module guide/Customize the side panel_1_170x26.png)

The side panel options will appear. Check the box beside the section you want to include in the side panel and then click _Done_. This closes the side panel options and returns you back to the side panel view. Drag the sections to arrange them in the order they will appear in the side panel. 

_Note_ : Workspaces when selected will always appear at the top of the column. 

![Navigation List Visibility panel showing the options for the side panel.](../Resources/Images/QA module guide/Customize the side panel_2_111x291.png)
﻿




### Show or hide the side panel

You can keep the side panel open all the time or hide it when you’re not using it. The next time you login, your choice to show or hide the side panel will be retained. 

The first time you login to QA, the side panel is open. To hide the side panel, click the left arrow. To show the side panel, the right arrow. 
﻿




## The Dashboard

The Dashboard is your home page and the location for various widgets that help you do your job. These include _My Active Tasks_ and _Issue Overview_ , among others. Widgets reflect the latest activity. As a user starts working, the widgets will show the latest information.

It contains useful widgets for reviewing status and work assignments.

A sample dashboard is displayed here:

![Sample of the Dashboard.](../Resources/Images/QA module guide/The Dashboard_451x199.png)

Other widgets and layouts can be applied by using the blue buttons.

![Blue buttons on the Dashboard to Add widgets and change the Layout.](../Resources/Images/QA module guide/Apply other widgets and layouts.png)
﻿




### Adding widgets

Widgets provide different views of the actions and activities occurring in your environment. You can change the view of your dashboard by adding widgets. 

To add widgets:

  1. Click _Add_ _widget_.



  2. Select a category and widget from the left column.

  3. With the widget in the right pane, click _Add Widget_.



﻿




### Moving widgets

After you add widgets, you may want to move them around on your Dashboard.

To move a widget:

  1. Hover over the widget name until you get the hand icon.



  2. Drag and drop the widget to the desired location.



﻿




### Change the layout

Layouts can be one, two, or three columns. The _Layout_ drop-down lets you change the view of your dashboard, such as the number of columns in your view. 

To change the layout:

  1. Click the _Layout_ drop-down.

  2. Select a layout.



﻿




### Title bar icons

Some widgets include a search icon in the title bar. All widgets include a vertical ellipsis (kebab ![Kebab menu icon](../Resources/Images/QA module guide/Title bar icons_12x12.png)) for options. The options change based on available actions for the specific widget.

  * Hover over the kebab (![Kebab menu icon](../Resources/Images/QA module guide/Title bar icons_12x12.png)) until you see the hand icon. Then click.




![Example of filter options available by clicking on the kebab menu icon.](../Resources/Images/QA module guide/Title bar icons_1_415x96.png)
﻿




### Issue Overview

The Issue Overview dashboard widget provides options for selecting a specific issue form and grouping issues to view the number of _Completed_ issues and _Total_ issues for the group.

  * The default _Group by_ option is _Filename_.




![The Issue Overview dashboard widget.](../Resources/Images/QA module guide/Issue Overview_446x181.png)

To see the issues:

  1. Select a specific issue form or _All forms._

  2. Select the grouping.



  3. Click on a row to see details.

  4. In the details, click _issue number_ to open the document to a specific issue.




_Note_ : Issues without all required answer will be visible in details view but the issue icon displays grey. When a user other than the creator tries to open the issue a warning message will display. “Selected Issue no longer exists on this file or its form has outstanding required fields.” 

  1. Click _Back_ to return home in the widget.




#### Filtering in the Dashboard widget

The Issue Overview dashboard widget offers 4 levels of filtering shown in the below screen shot:

![](../Resources/Images/QA module guide/Issue Overview filter options_495x97.png)

  * The top right menu enables the widget to filter on specific issues of interest.

  * The _Select Form_ menu enables the widget to show issues for all forms or a specific form of interest - allowing users to home in specific classes of issues.

  * The _Group by_ menu enables the widget to group issues in a lot of flexible ways - and then when you click on a row in the chart, you see a report for the specific group of issues you’re interested in.

  * The search function is accessible using the magnifying glass icon at the top right.




#### Filtering in detail view

Further sorting and filtering capabilities are available in the detail view.

![](../Resources/Images/QA module guide/Issue Overview filter detail view_492x174.png)

  * Click on a specific status, creator, or assignee to filter the list to just those issues. You can select one in each category.

  * Click _Show All_ to wrap the _By Status_ , _By Creator_ , and _By Assigned_ lists, if you need to see them all.



﻿




### Report

The _Report_ dashboard widget has a drop-down with list of reports created and shared with users. The report you select on your dashboard becomes the default report on the dashboard.

  * You can change the report by clicking on the _x_ beside the current Title, then selecting the drop-down arrow and choosing from the list.




![The Report dashboard widget.](../Resources/Images/QA module guide/Report_438x77.png)

To change or select a report:

  1. Select the _x_ beside the report title.



  2. Select the report.

  3. Click _Load Report_.




When you load a report, it will be there the next time you log in.

_Note_ : To change reports click the x again and make another selection.
﻿




### Favorite Items

The Favorite Items dashboard widget displays items you have marked as favorites. Folders, files, and reports can be marked as favorites.

_Note_ : No object is listed until you mark one as a favorite. 

![The Favorite Items dashboard widget.](../Resources/Images/QA module guide/Favorite Items_434x42.png)

  * Objects marked as favorites also appear under the _Files_ link.




![Image of Favorites in the side panel.](../Resources/Images/QA module guide/Favorite Items_1_139x38.png)

To mark a file, folder, or report as a favorite:

  * Click the star icon next to the file or folder to mark it as a favorite.




_Note_ : The favorites marking stays with the version. If you mark version 1 as a favorite, version 2 is not a favorite unless also marked.
﻿




### My Active Tasks

The _My Active Tasks_ dashboard widget displays active tasks for you and groups to which you are assigned.

![My Active Tasks dashboard widget.](../Resources/Images/QA module guide/My Active Tasks_444x55.png)
﻿




### Files By Status

The _Files by Status_ dashboard widget displays how many files are in each status category. This is a view of current file status in the workflow of the most recent version of a document.

![The Files By Status dashboard widget.](../Resources/Images/QA module guide/Files By Status_440x242.png)

  * You can set the chart type from the kebab (![Kebab menu icon](../Resources/Images/QA module guide/Title bar icons.png)).



