package com.prinova.messagepoint.model.workflow;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.PrivateModelImpl;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.version.VersionStatus;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import org.hibernate.query.NativeQuery;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class ConfigurableWorkflow extends PrivateModelImpl {

	private static final long serialVersionUID = -5913968823993270011L;
	
	public static final int SHARED_CONTENT_WORKFLOW					= 3; // TODO Remove
	public static final int INSERT_WORKFLOW 						= 4;
	public static final int INSERT_SCHEDULE_WORKFLOW 				= 5;
	public static final int TAG_WORKFLOW 							= 6;
	public static final int ATTACHMENT_WORKFLOW 					= 7;
	public static final int TOUCHPOINT_TARGETING_WORKFLOW 			= 8;
	public static final int GLOBAL_SMART_TEXT_WORKFLOW 				= 11;
	public static final int GLOBAL_IMAGE_WORKFLOW 					= 12;
	public static final int LOOKUP_TABLE_WORKFLOW					= 14;
	public static final int PROJECT_TASK_WORKFLOW					= 15;
	public static final int WORKFLOW_LIBRARY_WORKFLOW				= 16;

	private Set<ConfigurableWorkflowInstance> 	workflowInstances = new HashSet<>();
	private Document 							document;
	private Set<Document> 						workflowLibraryDocuments	= new HashSet<>();
	
	private int 								modelType;
	private Integer								usageType;
	
	// Public default constructor
	//
	public ConfigurableWorkflow()
	{
	}

	// Copy constructor (not public) for cloning 
	//
	protected ConfigurableWorkflow(ConfigurableWorkflow cloneFrom, Document clonedDocument)
	{
		super(cloneFrom);

        this.document = clonedDocument;

        if (cloneFrom.workflowInstances != null) {
			for (ConfigurableWorkflowInstance workflowInstance : cloneFrom.workflowInstances) {
				if (workflowInstance.getStatus() != null && workflowInstance.getStatus().getId() != VersionStatus.VERSION_HISTORICAL)
				{
					this.workflowInstances.add(CloneHelper.clone(workflowInstance, o->o.clone(this)));
				}
			}
		}
		
        this.workflowLibraryDocuments = CloneHelper.assignAlreadyClonedObject(cloneFrom.workflowLibraryDocuments);
        
		this.modelType = cloneFrom.modelType;
		this.usageType = cloneFrom.usageType;
	}

	@Override
	public Object clone()
	{
		return new ConfigurableWorkflow(this, this.document);
	}

	public ConfigurableWorkflow clone(Document clonedDocument)
	{
		ConfigurableWorkflow clone = new ConfigurableWorkflow(this, clonedDocument);
		return clone;
	}
	
	@Override
	public int hashCode() {
		return getGuid().hashCode();
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (!(obj instanceof ConfigurableWorkflow))
			return false;
		final ConfigurableWorkflow other = (ConfigurableWorkflow) obj;
		return (getGuid().equals(other.getGuid()));
	}
		
	public String toString() {
		StringBuilder sb = new StringBuilder("Configurable Workflow ID:");
		sb.append(getId());
		return sb.toString();
	}
	
	public static ConfigurableWorkflow createNewInstance() {
		ConfigurableWorkflow workflow = new ConfigurableWorkflow();
		return workflow;
	}
	
	public int getModelType() {
		return modelType;
	}
	public void setModelType(int modelType) {
		this.modelType = modelType;
	}
	public Set<ConfigurableWorkflowInstance> getWorkflowInstances() {
		return workflowInstances;
	}
	public void setWorkflowInstances(Set<ConfigurableWorkflowInstance> workflowInstances) {
		this.workflowInstances = workflowInstances;
	}
	public Document getDocument() {
		return document;
	}
	public void setDocument(Document document) {
		this.document = document;
	}
	public Set<Document> getWorkflowLibraryDocuments() {
		return workflowLibraryDocuments;
	}

	public void setWorkflowLibraryDocuments(Set<Document> workflowLibraryDocuments) {
		this.workflowLibraryDocuments = workflowLibraryDocuments;
	}

	public Integer getUsageType() {
		return usageType;
	}
	public void setUsageType(Integer usageType) {
		this.usageType = usageType;
	}
	
	/**
	 * Retrieve the active instance of the workflow
     */
	public ConfigurableWorkflowInstance findActiveInstance(){
		Set<ConfigurableWorkflowInstance> instances = getWorkflowInstances();
		for (ConfigurableWorkflowInstance instance : instances) {
			if(VersionStatus.VERSION_PRODUCTION == instance.getStatus().getId())
				return instance;
		}
		return null;
	}
	
	public String getOwner(){
		ConfigurableWorkflowInstance workflowInstance = this.findActiveInstance();
		if(workflowInstance != null && workflowInstance.getOwnerId() != null){
			return User.findById(workflowInstance.getOwnerId())!=null?User.findById(workflowInstance.getOwnerId()).getName():"";
		}else{
			return "";
		}
	}

	public String getDisplayName(){
		if(this.getName()==null || this.getName().isEmpty()){
			return "(" + ApplicationUtil.getMessage("page.label.default.workflow") + ")";
		}else{
			return this.getName();
		}
	}
	
	public static List<Long> findAllVisibleIds(List<Long> docIds){
		String query = 	"SELECT DISTINCT	workflow_library_id " +
						"FROM 				workflow_library_document ";
		if ( docIds != null && !docIds.isEmpty() )
			query +=    "WHERE 				document_id in (:documentIds)";
		
        NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
        if ( docIds != null && !docIds.isEmpty() ){
        	sqlQuery.setParameterList("documentIds", docIds);
        }
        List ids = sqlQuery.list();
        List<Long> workflowLibraryIds = new ArrayList<>();
        for(Object idObj : ids)
        	workflowLibraryIds.add(((BigInteger)idObj).longValue());

        return workflowLibraryIds;
	}

	public static List<ConfigurableWorkflow> findLegacyWFByDocumentId(long documentId) {
		List<MessagepointCriterion> criterionList = new ArrayList<>();
		criterionList.add(MessagepointRestrictions.eq("document.id", documentId));
		criterionList.add(MessagepointRestrictions.ne("modelType", ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW));
		return HibernateUtil.getManager().getObjectsAdvanced(ConfigurableWorkflow.class, criterionList);
	}
	
	public static List<ConfigurableWorkflow> findByDocumentId(long documentId) {
		List<MessagepointCriterion> criterionList = new ArrayList<>();
		criterionList.add(MessagepointRestrictions.eq("document.id", documentId));
		return HibernateUtil.getManager().getObjectsAdvanced(ConfigurableWorkflow.class, criterionList);
	}
	
	public static List<ConfigurableWorkflow> findByModelType(int modelType){
		List<MessagepointCriterion> criterionList = new ArrayList<>();
		criterionList.add(MessagepointRestrictions.eq("modelType", modelType));
		return HibernateUtil.getManager().getObjectsAdvanced(ConfigurableWorkflow.class, criterionList, MessagepointOrder.asc("id"));
	}
	
	public static List<ConfigurableWorkflow> findByNameAndModelType(String name, int modelType) {
		List<MessagepointCriterion> criterionList = new ArrayList<>();
		criterionList.add(MessagepointRestrictions.eq("name", name).ignoreCase());
		criterionList.add(MessagepointRestrictions.eq("modelType", modelType));
		return HibernateUtil.getManager().getObjectsAdvanced(ConfigurableWorkflow.class, criterionList, MessagepointOrder.asc("id"));
	}
	
	public static List<ConfigurableWorkflow> findByDocumentModelAndUsageTypes(long documentId, int modelType, int usageType){
		List<MessagepointCriterion> criterionList = new ArrayList<>();
		criterionList.add(MessagepointRestrictions.eq("modelType", modelType));
		if(usageType > 0){
			if(usageType != WorkflowUsageType.ID_USAGE_TYPE_ALL){
				criterionList.add(MessagepointRestrictions.or(MessagepointRestrictions.eq("usageType", usageType), MessagepointRestrictions.eq("usageType", WorkflowUsageType.ID_USAGE_TYPE_ALL)));
			}else {
				criterionList.add(MessagepointRestrictions.eq("usageType", WorkflowUsageType.ID_USAGE_TYPE_ALL));
			}
		}
		List<ConfigurableWorkflow> allWorkflows = HibernateUtil.getManager().getObjectsAdvanced(ConfigurableWorkflow.class, criterionList, MessagepointOrder.asc("id"));
		if(documentId > 0){
			List<ConfigurableWorkflow> workflowsFilteredByDocument = new ArrayList<>();
			for(ConfigurableWorkflow workflow : allWorkflows){
				if(workflow.getWorkflowLibraryDocuments().contains(Document.findById(documentId))){
					workflowsFilteredByDocument.add(workflow);
				}else if(workflow.getUsageType()==WorkflowUsageType.ID_USAGE_TYPE_ALL){
					workflowsFilteredByDocument.add(workflow);
				}
			}
			return workflowsFilteredByDocument;
		}
		return allWorkflows;
	}
	
	public static ConfigurableWorkflow getSharedContentWorkflow() {
		List<ConfigurableWorkflow> wfs = findByModelType(SHARED_CONTENT_WORKFLOW);
		if(!wfs.isEmpty())
			return wfs.get(0);
		return null;
	}
	
	public static ConfigurableWorkflow getGlobalSmartTextWorkflow() {
		List<ConfigurableWorkflow> wfs = findByModelType(GLOBAL_SMART_TEXT_WORKFLOW);
		if(!wfs.isEmpty())
			return wfs.get(0);
		return null;
	}
	
	public static ConfigurableWorkflow getGlobalImageWorkflow() {
		List<ConfigurableWorkflow> wfs = findByModelType(GLOBAL_IMAGE_WORKFLOW);
		if(!wfs.isEmpty())
			return wfs.get(0);
		return null;
	}
	
	public static ConfigurableWorkflow getLookupTableWorkflow() {
		List<ConfigurableWorkflow> wfs = findByModelType(LOOKUP_TABLE_WORKFLOW);
		if(!wfs.isEmpty())
			return wfs.get(0);
		return null;
	}	
	
	/**
	 * Retrieve the workflow by the given document and model type, if document id is not valid, return the shared
	 * content workflow.
     */
	public static ConfigurableWorkflow findByDocumentAndModelType(long documentId, int modelType) {
		List<ConfigurableWorkflow> workflows = null;
		if(documentId != -1){	// Message and selection workflows
			workflows = findByDocumentId(documentId);
			for (ConfigurableWorkflow workflow : workflows) {
				if(modelType == workflow.getModelType()){
					return workflow;
				}
			}			
		}else{	// Shared content workflow
			workflows = findByModelType(modelType);
			if(!workflows.isEmpty()){
				return workflows.get(0);
			}
		}
		return null;
	}
	
	public static ConfigurableWorkflow findById(long id) {
		return HibernateUtil.getManager().getObject(ConfigurableWorkflow.class, id);
	}

	public List<Document> findAllDocumentsAreReferencing(){
		List<Document> allDocumentsAreReferencing = new ArrayList<>();
		if(this.getModelType() == ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW){
			String query = "";
			if(this.getUsageType() == WorkflowUsageType.ID_USAGE_TYPE_VARIANT){
				query = "SELECT ts.id FROM touchpoint_selection ts WHERE ts.wf_id = ?";
			}else if(this.getUsageType() == WorkflowUsageType.ID_USAGE_TYPE_CONNECTED){
				query = "SELECT ts.id FROM touchpoint_selection ts WHERE ts.connected_wf_id = ?";
			}else if(this.getUsageType() == WorkflowUsageType.ID_USAGE_TYPE_MESSAGE){
				query = "SELECT d.id FROM document d WHERE d.message_wf_id = ?";
			}
			NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
			sqlQuery.setParameter(1, this.getId());
			List<?> ids = sqlQuery.list();
			for (Object idObj : ids) {
				long objectId = ((BigInteger) idObj).longValue();
				Document document = null;
				if(this.getUsageType() == WorkflowUsageType.ID_USAGE_TYPE_VARIANT ||
						this.getUsageType() == WorkflowUsageType.ID_USAGE_TYPE_CONNECTED){
					TouchpointSelection ts = TouchpointSelection.findById(objectId);
					document = ts.getDocument();
				}else if(this.getUsageType() == WorkflowUsageType.ID_USAGE_TYPE_MESSAGE){
					document = Document.findById(objectId);
				}

				if(document != null && !allDocumentsAreReferencing.contains(document) && !document.isRemoved()){
					allDocumentsAreReferencing.add(document);
				}
			}
		}
		return allDocumentsAreReferencing;
	}

	public boolean isReferenced(){
		if(this.getModelType() == ConfigurableWorkflow.PROJECT_TASK_WORKFLOW){
			String query = "SELECT p.id FROM project p, workflow_instance wfi WHERE p.wf_instance_id = wfi.id AND wfi.workflow_id = ?";
			NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
			sqlQuery.setParameter(1, this.getId());
			List<?> ids = sqlQuery.list();
			if(!ids.isEmpty()){
				return true;
			}else{
				return false;
			}
		}else if(this.getModelType() == ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW){
			String query = "";
			if(this.getUsageType() == WorkflowUsageType.ID_USAGE_TYPE_VARIANT){
				query = "SELECT ts.id FROM touchpoint_selection ts WHERE ts.wf_id = ?";
			}else if(this.getUsageType() == WorkflowUsageType.ID_USAGE_TYPE_CONNECTED){
				query = "SELECT ts.id FROM touchpoint_selection ts WHERE ts.connected_wf_id = ?";
			}else if(this.getUsageType() == WorkflowUsageType.ID_USAGE_TYPE_MESSAGE){
				query = "SELECT d.id FROM document d WHERE d.message_wf_id = ?";
			}else if(this.getUsageType() == WorkflowUsageType.ID_USAGE_TYPE_RATIONALIZER){
				query = "SELECT ra.id FROM rationalizer_application ra WHERE ra.rationalizer_wf_id = ?";
			}else if(this.getUsageType() == WorkflowUsageType.ID_USAGE_TYPE_ALL){
				query = "SELECT wf.wf_step_id FROM workflow_step_subwfs wf WHERE wf.wf_id = ?";
			}
			NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
			sqlQuery.setParameter(1, this.getId());
			List<?> ids = sqlQuery.list();
			if(!ids.isEmpty()){
				return true;
			}else{
				return false;
			}
		}else{
			return false;
		}
	}

	/**
	 * Check if the workflow is currently used by any assets
	 */
	public static boolean isCurrentlyUsedByAssets(long workflowId) {
		String query = 	"	SELECT DISTINCT co.id FROM object_wf_action_map owam LEFT JOIN content_object co ON owam.model_id = co.id LEFT JOIN workflow_action wa ON owam.wf_action_id = wa.id " +
						"	LEFT JOIN workflow_step ws ON wa.wf_step_id = ws.id LEFT JOIN workflow_instance wi ON ws.wf_instance_id = wi.id " +
						"   WHERE co.state_id = 29 AND wi.workflow_id = :workflowId " +
						"   UNION " +
						"   SELECT DISTINCT ts.id FROM touchpoint_selection ts LEFT JOIN workflow_action wa ON ts.wf_action_id = wa.id " +
						"   LEFT JOIN workflow_step ws ON wa.wf_step_id = ws.id LEFT JOIN workflow_instance wi ON ws.wf_instance_id = wi.id " +
						"   WHERE ts.own_content_ready = true AND wi.workflow_id = :workflowId " +
						"   UNION " +
						"   SELECT DISTINCT c.id FROM communication c LEFT JOIN workflow_action wa ON c.wf_action_id = wa.id " +
						"   LEFT JOIN workflow_step ws ON wa.wf_step_id = ws.id LEFT JOIN workflow_instance wi ON ws.wf_instance_id = wi.id " +
						"   WHERE wi.workflow_id = :workflowId " +
						"   UNION " +
						"   SELECT DISTINCT rdc.id FROM rationalizer_document_content rdc LEFT JOIN workflow_action wa ON rdc.wf_action_id = wa.id " +
						"   LEFT JOIN workflow_step ws ON wa.wf_step_id = ws.id LEFT JOIN workflow_instance wi ON ws.wf_instance_id = wi.id " +
						"   WHERE wi.workflow_id = :workflowId ";
		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
		sqlQuery.setParameter("workflowId", workflowId);
		List<?> ids = sqlQuery.list();
		return !ids.isEmpty();
	}
}