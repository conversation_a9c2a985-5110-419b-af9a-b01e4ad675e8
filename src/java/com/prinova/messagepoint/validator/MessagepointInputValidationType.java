package com.prinova.messagepoint.validator;

public class MessagepointInputValidationType {

	private String label;
	private Boolean mandatory;
	private String mandatoryErrorCode;
	private Integer minLength;
	private String minLengthErrorCode;
	private Integer maxLength;
	private String maxLengthErrorCode;
	private String restrictedCharsErrorCode;
	private String restrictedCharsList;
	private String restrictedCharsRegex;

	public MessagepointInputValidationType() {
		super();
	}
	
	public MessagepointInputValidationType(MessagepointInputValidationEntry validationEntry) {
		if (validationEntry.getLabel() != null && !validationEntry.getLabel().isEmpty()) {
			label = validationEntry.getLabel();
		} else {
			throw new RuntimeException("Label must be provided for " + validationEntry.getPropertyName() + " property.");
		}
		if (validationEntry.getRestrictedCharsList() != null && !validationEntry.getRestrictedCharsList().isEmpty()) {
			restrictedCharsList = validationEntry.getRestrictedCharsList();
		} else {
			if (validationEntry.getPropertyType() != null && validationEntry.getPropertyType().getRestrictedCharsList() != null) {
				restrictedCharsList = validationEntry.getPropertyType().getRestrictedCharsList();
			} else {
				throw new RuntimeException("restrictedCharsList must be provided for " + validationEntry.getPropertyName() + " property.");
			}
		}
		if (validationEntry.getMandatory() != null) {
			mandatory = validationEntry.getMandatory().booleanValue();
		} else {
			if (validationEntry.getPropertyType() != null && validationEntry.getPropertyType().getMandatory() != null) {
				mandatory = validationEntry.getPropertyType().getMandatory().booleanValue();
			} else {
				mandatory = false;
			}
		}
		if (validationEntry.getMandatoryErrorCode() != null) {
			mandatoryErrorCode = validationEntry.getMandatoryErrorCode();
		} else {
			if (validationEntry.getPropertyType() != null && validationEntry.getPropertyType().getMandatoryErrorCode() != null) {
				mandatoryErrorCode = validationEntry.getPropertyType().getMandatoryErrorCode();
			} else {
				mandatoryErrorCode = "error.input.mandatory";
			}
		}
		if (validationEntry.getMinLength() != null) {
			minLength = validationEntry.getMinLength().intValue();
		} else {
			if (validationEntry.getPropertyType() != null && validationEntry.getPropertyType().getMinLength() != null) {
				minLength = validationEntry.getPropertyType().getMinLength().intValue();
			} else {
				throw new RuntimeException("minLength must be provided for either " + validationEntry.getPropertyName() + " property itself or its PropertyType.");
			}
		}
		if (validationEntry.getMinLengthErrorCode() != null) {
			minLengthErrorCode = validationEntry.getMinLengthErrorCode();
		} else {
			if (validationEntry.getPropertyType() != null && validationEntry.getPropertyType().getMinLengthErrorCode() != null) {
				minLengthErrorCode = validationEntry.getPropertyType().getMinLengthErrorCode();
			} else {
				minLengthErrorCode = "error.input.minlength";
			}
		}
		if (validationEntry.getMaxLength() != null) {
			maxLength = validationEntry.getMaxLength().intValue();
		} else {
			if (validationEntry.getPropertyType() != null && validationEntry.getPropertyType().getMaxLength() != null) {
				maxLength = validationEntry.getPropertyType().getMaxLength().intValue();
			} else {
				throw new RuntimeException("maxLength must be provided for either " + validationEntry.getPropertyName() + " property itself or its PropertyType.");
			}
		}
		if (validationEntry.getMaxLengthErrorCode() != null) {
			maxLengthErrorCode = validationEntry.getMaxLengthErrorCode();
		} else {
			if (validationEntry.getPropertyType() != null && validationEntry.getPropertyType().getMaxLengthErrorCode() != null) {
				maxLengthErrorCode = validationEntry.getPropertyType().getMaxLengthErrorCode();
			} else {
				maxLengthErrorCode = "error.input.maxlength";
			}
		}
		if (validationEntry.getRestrictedCharsRegex() != null) {
			restrictedCharsRegex = validationEntry.getRestrictedCharsRegex();
		} else {
			if (validationEntry.getPropertyType() != null && validationEntry.getPropertyType().getRestrictedCharsRegex() != null) {
				restrictedCharsRegex = validationEntry.getPropertyType().getRestrictedCharsRegex();
			} else {
				throw new RuntimeException("charRestrictionRegex must be provided for either " + validationEntry.getPropertyName() + " property itself or its PropertyType.");
			}
		}
		if (validationEntry.getRestrictedCharsErrorCode() != null) {
			restrictedCharsErrorCode = validationEntry.getRestrictedCharsErrorCode();
		} else {
			if (validationEntry.getPropertyType() != null && validationEntry.getPropertyType().getRestrictedCharsErrorCode() != null) {
				restrictedCharsErrorCode = validationEntry.getPropertyType().getRestrictedCharsErrorCode();
			} else {
				restrictedCharsErrorCode = "error.input.charrestriction";
			}
		}
	}
	
	public Boolean getMandatory() {
		return mandatory;
	}
	public void setMandatory(Boolean mandatory) {
		this.mandatory = mandatory;
	}
	public String getMandatoryErrorCode() {
		return mandatoryErrorCode;
	}
	public void setMandatoryErrorCode(String mandatoryErrorCode) {
		this.mandatoryErrorCode = mandatoryErrorCode;
	}
	public Integer getMinLength() {
		return minLength;
	}
	public void setMinLength(Integer minLength) {
		this.minLength = minLength;
	}
	public String getMinLengthErrorCode() {
		return minLengthErrorCode;
	}
	public void setMinLengthErrorCode(String minLengthErrorCode) {
		this.minLengthErrorCode = minLengthErrorCode;
	}
	public Integer getMaxLength() {
		return maxLength;
	}
	public void setMaxLength(Integer maxLength) {
		this.maxLength = maxLength;
	}
	public String getMaxLengthErrorCode() {
		return maxLengthErrorCode;
	}
	public void setMaxLengthErrorCode(String maxLengthErrorCode) {
		this.maxLengthErrorCode = maxLengthErrorCode;
	}
	public String getRestrictedCharsErrorCode() {
		return restrictedCharsErrorCode;
	}

	public void setRestrictedCharsErrorCode(String restrictedCharsErrorCode) {
		this.restrictedCharsErrorCode = restrictedCharsErrorCode;
	}

	public String getRestrictedCharsList() {
		return restrictedCharsList;
	}

	public void setRestrictedCharsList(String restrictedCharsList) {
		this.restrictedCharsList = restrictedCharsList;
	}

	public String getRestrictedCharsRegex() {
		return restrictedCharsRegex;
	}

	public void setRestrictedCharsRegex(String restrictedCharsRegex) {
		this.restrictedCharsRegex = restrictedCharsRegex;
	}

	public String getLabel() {
		return label;
	}
	public void setLabel(String label) {
		this.label = label;
	}
}
