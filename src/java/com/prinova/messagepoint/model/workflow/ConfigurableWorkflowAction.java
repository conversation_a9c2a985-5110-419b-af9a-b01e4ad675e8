package com.prinova.messagepoint.model.workflow;

import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.UserNameComparator;
import com.prinova.messagepoint.model.admin.ItemType;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.version.Approvable;
import com.prinova.messagepoint.platform.services.imports.ImportJSONContentServiceReturnCode;
import com.prinova.messagepoint.util.*;
import org.hibernate.query.NativeQuery;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;

import java.math.BigInteger;
import java.text.DateFormatSymbols;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.IntStream;

public class ConfigurableWorkflowAction extends IdentifiableMessagePointModel {

	private static final long serialVersionUID = -8766086777704902228L;
	
	private ConfigurableWorkflowStep configurableWorkflowStep;
	private ConfigurableWorkflowAction previousAction;
	private ConfigurableWorkflowAction nextAction;
	private Date releaseForApprovalDate; //same date as release for approval
	private Approvable model;
	private Set<ConfigurableWorkflowApprovalDetail> approvalDetails = new HashSet<>();
	private boolean active;
	private boolean dueByNotified;
	private String requestGuid;
	private Integer serviceCode;
	
	// Public default constructor
	//
	public ConfigurableWorkflowAction(){
	}

	// Copy constructor (not public) for cloning 
	//
	protected ConfigurableWorkflowAction(ConfigurableWorkflowAction cloneFrom, Approvable clonedModel){
		super(cloneFrom);
		
        this.model = clonedModel;

        this.configurableWorkflowStep = cloneFrom.configurableWorkflowStep;
		this.previousAction = cloneFrom.previousAction;
		this.nextAction = cloneFrom.nextAction;
		this.releaseForApprovalDate = cloneFrom.releaseForApprovalDate;
		
		if (cloneFrom.approvalDetails != null) {
			for (ConfigurableWorkflowApprovalDetail approvalDetail : cloneFrom.approvalDetails) {
				this.approvalDetails.add((ConfigurableWorkflowApprovalDetail) approvalDetail.clone(this));
			}
		}
		
		this.active = cloneFrom.active;
		this.dueByNotified = cloneFrom.dueByNotified;
	}

    public static ConfigurableWorkflowAction findByRequestGUID(String requestGUID) {
		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("requestGuid", requestGUID));

		List<ConfigurableWorkflowAction> list = HibernateUtil.getManager().getObjectsAdvanced(ConfigurableWorkflowAction.class, critList);

		if(list == null || list.isEmpty())
			return null;
		return list.get(0);
    }

    @Override
	public Object clone(){
		return new ConfigurableWorkflowAction(this, this.model);
	}
	
	public ConfigurableWorkflowAction clone(Approvable clonedModel){
		ConfigurableWorkflowAction clone = new ConfigurableWorkflowAction(this, clonedModel);
		return clone;
	}
	
	public ConfigurableWorkflowStep getConfigurableWorkflowStep() {
		return configurableWorkflowStep;
	}
	public void setConfigurableWorkflowStep(ConfigurableWorkflowStep configurableWorkflowStep) {
		this.configurableWorkflowStep = configurableWorkflowStep;
	}
	public ConfigurableWorkflowAction getPreviousAction() {
		return previousAction;
	}
	public void setPreviousAction(ConfigurableWorkflowAction previousAction) {
		this.previousAction = previousAction;
	}
	public ConfigurableWorkflowAction getNextAction() {
		return nextAction;
	}
	public void setNextAction(ConfigurableWorkflowAction nextAction) {
		this.nextAction = nextAction;
	}
	public Date getReleaseForApprovalDate() {
		return releaseForApprovalDate;
	}
	public void setReleaseForApprovalDate(Date releaseForApprovalDate) {
		this.releaseForApprovalDate = releaseForApprovalDate;
	}
	public Approvable getModel() {
		return model;
	}
	public void setModel(Approvable model) {
		this.model = model;
	}
	public Set<ConfigurableWorkflowApprovalDetail> getApprovalDetails() {
		return approvalDetails;
	}
	public List<ConfigurableWorkflowApprovalDetail> getApprovalDetailsWithoutOwner() {
		List<ConfigurableWorkflowApprovalDetail> approverlist = new ArrayList<>();
		Set<ConfigurableWorkflowApprovalDetail> details = getApprovalDetails();
		for (ConfigurableWorkflowApprovalDetail detail : details) {
			if(!detail.isOwner())
				approverlist.add(detail);
		}
		return approverlist;
	}
	public void setApprovalDetails(Set<ConfigurableWorkflowApprovalDetail> approvalDetails) {
		this.approvalDetails = approvalDetails;
	}

	public ConfigurableWorkflow getConfigurableWorkflow(){
		if(this.getConfigurableWorkflowStep() == null){
			if(model instanceof ContentObject contentObject){
                if (contentObject.isGlobalSmartText()) {
					return ConfigurableWorkflow.getGlobalSmartTextWorkflow();
				} else if (contentObject.isGlobalImage()) {
					return ConfigurableWorkflow.getGlobalImageWorkflow();
				} else {
					return contentObject.getFirstDocumentDelivery().getMessageWorkflow();
				}
			}else if(model instanceof TouchpointSelection touchpointSelection){
				return touchpointSelection.getWorkflow();
			}else if(model instanceof LookupTableInstance){
				return ConfigurableWorkflow.getLookupTableWorkflow();
			}else if(model instanceof Communication communication){
                return communication.getDocument().getConnectedWorkflow();
			}else if(model instanceof RationalizerDocumentContent rationalizerDocumentContent){
				return rationalizerDocumentContent.getRationalizerWorkflow();
			}
		}
		return this.getConfigurableWorkflowStep().getConfigurableWorkflowInstance().getConfigurableWorkflow();
	}

	public ConfigurableWorkflowApprovalDetail getApprovalDetailByUserId(long userId) {
		Set<ConfigurableWorkflowApprovalDetail> details = this.getApprovalDetails();
		for (ConfigurableWorkflowApprovalDetail detail : details) {
			if(userId == detail.getUserId())
				return detail;
		}
		return null;
	}
	
	public ConfigurableWorkflowApprovalDetail getApprovalDetailByApproved() {

		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.or(MessagepointRestrictions.eq("approved", ConfigurableWorkflowApprovalDetail.APPROVED), MessagepointRestrictions.eq("approved", ConfigurableWorkflowApprovalDetail.AUTO_APPROVED)));
		critList.add(MessagepointRestrictions.eq("workflowAction", this));
		List<MessagepointOrder> orders = new ArrayList<>();
		orders.add(MessagepointOrder.desc("approvedDate"));
		List<ConfigurableWorkflowApprovalDetail> details = HibernateUtil.getManager().getObjectsAdvanced(ConfigurableWorkflowApprovalDetail.class, critList, orders);
		if(details != null && !details.isEmpty())
			return details.get(0);
		return null;
	}
	
	public boolean isActive() {
		return active;
	}
	public void setActive(boolean active) {
		this.active = active;
	}	
	
	public boolean isDueByNotified() {
		return dueByNotified;
	}
	public void setDueByNotified(boolean dueByNotified) {
		this.dueByNotified = dueByNotified;
	}

	public String getRequestGuid() {
		return requestGuid;
	}
	public void setRequestGuid(String requestGuid) {
		this.requestGuid = requestGuid;
	}

	public Integer getServiceCode() {
		return serviceCode;
	}
	public void setServiceCode(Integer serviceCode) {
		this.serviceCode = serviceCode;
	}

	public String getServiceCodeMessage() {
		String message = "";
		if (serviceCode != null) {
			ConfigurableWorkflowActionError returnCode = ConfigurableWorkflowActionError.fromCode(serviceCode);
			if(returnCode != null && returnCode != ImportJSONContentServiceReturnCode.SUCCESSFUL){
				message = returnCode.getMessage() + (returnCode.isRetry()? ". Retrying..." : "");
			}
		}
		return message;
	}

	public static ConfigurableWorkflowAction findById(long id) {
		return HibernateUtil.getManager().getObject(ConfigurableWorkflowAction.class, id);
	}
	
	public static List<ConfigurableWorkflowAction> findAllByModel(Approvable model){
		Map<String, Object> paramsMap = new HashMap<>();
		paramsMap.put("model_type", ItemType.getTypeId(model));
		paramsMap.put("model_id", ((IdentifiableMessagePointModel)model).getId());
		String query = "select * from workflow_action where model_type = :model_type AND model_id = :model_id ORDER BY created DESC";
		return HibernateUtil.getManager().getObjectsOfSpecifiedClassByNativeQuery(ConfigurableWorkflowAction.class, query, paramsMap);
	}
	
	public static ConfigurableWorkflowAction findLatestActionByModel(Approvable model) {
		Map<String, Object> paramsMap = new HashMap<>();
		paramsMap.put("model_type", ItemType.getTypeId(model));
		paramsMap.put("model_id", ((IdentifiableMessagePointModel)model).getId());
		String query = "select * from workflow_action where model_type = :model_type AND model_id = :model_id ORDER BY created DESC";
		List<ConfigurableWorkflowAction> actions = HibernateUtil.getManager().getObjectsOfSpecifiedClassByNativeQuery(ConfigurableWorkflowAction.class, query, paramsMap);

		if(actions == null || actions.isEmpty())
			return null;
		return actions.get(0);
	}
	
	public static ConfigurableWorkflowAction findLatestActiveActionByModel(Approvable model) {
		Map<String, Object> paramsMap = new HashMap<>();
		paramsMap.put("model_type", ItemType.getTypeId(model));
		paramsMap.put("model_id", ((IdentifiableMessagePointModel)model).getId());
		String query = "select * from workflow_action where model_type = :model_type AND model_id = :model_id AND is_active = true ORDER BY created DESC";
		List<ConfigurableWorkflowAction> actions = HibernateUtil.getManager().getObjectsOfSpecifiedClassByNativeQuery(ConfigurableWorkflowAction.class, query, paramsMap);

		if(actions == null || actions.isEmpty())
			return null;
		return actions.get(0);
	}
	
	public static List<ConfigurableWorkflowAction> getActionList(Approvable model){
		List<ConfigurableWorkflowAction> actionList = new ArrayList<>();
		// Add the workflow action list
		ConfigurableWorkflowAction currentAction =  model.getWorkflowAction();
		if(currentAction == null){	// WC or being rejected before
			currentAction = ConfigurableWorkflowAction.findLatestActionByModel(model);
		}
		
		while(currentAction != null){
			actionList.add(0, currentAction);
			currentAction = currentAction.getPreviousAction();
		}
		return actionList;
	}

	/**
	 * If the action is a sub-workflow action, find the sub-workflow actions
     */
	public List<ConfigurableWorkflowAction> findSubworkflowActions(){
		ConfigurableWorkflowAction nextAction = this.getNextAction();
		Map<String, Object> paramsMap = new HashMap<>();
		if(nextAction!=null) {
			paramsMap.put("next_action_id", nextAction.getId());
		}
		paramsMap.put("current_action_id", this.getId());
		paramsMap.put("model_type", ItemType.getTypeId(model));
		paramsMap.put("model_id", ((IdentifiableMessagePointModel)model).getId());
		String query = "SELECT * FROM workflow_action WHERE id > :current_action_id" +
				(nextAction!=null?" AND id < :next_action_id ":"") +
				" AND next_action_id IS NULL AND model_id = :model_id AND model_type = :model_type ORDER BY created DESC";
		return HibernateUtil.getManager().getObjectsOfSpecifiedClassByNativeQuery(ConfigurableWorkflowAction.class, query, paramsMap);
	}

	public List<ConfigurableWorkflowApprovalDetail> getApprovalDetailsSorted() {
		List<ConfigurableWorkflowApprovalDetail> approvalDetailsSorted = new ArrayList<>(approvalDetails);
		Collections.sort(approvalDetailsSorted, new Comparator<>() {
            public int compare(ConfigurableWorkflowApprovalDetail o1, ConfigurableWorkflowApprovalDetail o2) {
                if (o1.getUserId() != null && o2.getUserId() != null) {
                    User user1 = User.findById(o1.getUserId());
                    User user2 = User.findById(o2.getUserId());
                    String userName1 = (user1 != null) ? user1.getName() : "";
                    String userName2 = (user2 != null) ? user2.getName() : "";
                    return userName1.compareTo(userName2);
                }

                return 0;
            }
        });

		return approvalDetailsSorted;
	}

	/**
	 * Retrieve the approvers list who can approve this message for the current step/action
     */
	public List<User> getActionApprovers(){
		ConfigurableWorkflowStep wfStep = this.getConfigurableWorkflowStep();
		if(wfStep != null){
			List<User> approvers = new ArrayList<>();
			for(User approver : wfStep.getApprovalUsers()){
				// Force the Hibernate to deproxy every user on the list
				approver = HibernateDeproxyUtil.deproxy(approver, User.class);
				approvers.add(approver);
			}
			Collections.sort(approvers, new UserNameComparator());
			return approvers;
		}else{
			return new ArrayList<>();
		}
	}
	
	/**
	 * Check whether the action is being automatically approved.
     */
	public boolean isActionAutoApproved(){
		for(ConfigurableWorkflowApprovalDetail detail : approvalDetails){
			if(detail.getApproved() == ConfigurableWorkflowApprovalDetail.AUTO_APPROVED){
				return true;
			}
		}
		return false;
	}

	/**
	 * Check whether the action is being skipped.
     */
	public boolean isActionSkipped(){
		for(ConfigurableWorkflowApprovalDetail detail : approvalDetails){
			if(detail.getApproved() == ConfigurableWorkflowApprovalDetail.SKIPPED){
				return true;
			}
		}
		return false;
	}
	
	/**
	 * Check whether the action is being manually approved.
     */
	public boolean isActionManualApproved(){
		if(configurableWorkflowStep == null){	// No step activate action
			// Should be only one approval detail
			for(ConfigurableWorkflowApprovalDetail detail : approvalDetails){
				if(detail.getApproved() != ConfigurableWorkflowApprovalDetail.APPROVED){
					return false;
				}
			}			
			return true;
		}else{
			if(configurableWorkflowStep.getApproveType() == ApprovalType.ID_APPROVAL_TYPE_ALL_OF){	// All of
				boolean allApprove = true;
				for(ConfigurableWorkflowApprovalDetail detail : approvalDetails){
					if(detail.isOwner() && detail.getApproved() == ConfigurableWorkflowApprovalDetail.APPROVED){
						return true;
					}
					if(detail.getApproved() != ConfigurableWorkflowApprovalDetail.APPROVED){
						allApprove = false;
					}
				}
				return allApprove;
			}else{	// Any of
				for(ConfigurableWorkflowApprovalDetail detail : approvalDetails){
					if(detail.getApproved() == ConfigurableWorkflowApprovalDetail.APPROVED){
						return true;
					}
				}
				return false;
			}
		}
	}
	
	public boolean isActionApproved(){
		return isActionAutoApproved() || isActionManualApproved() || isActionSkipped();
	}
	
	/**
	 * Check whether the action is being rejected.
     */
	public boolean isActionRejected(){
		return approvalDetails.stream().anyMatch(detail -> detail.getApproved() == ConfigurableWorkflowApprovalDetail.REJECTED);
	}

	/**
	 * Check whether the action is failed from translation service.
     */
	public boolean isActionFailedWithoutRetrying(){
		ConfigurableWorkflowStep currentStep = this.getConfigurableWorkflowStep();
		if(currentStep != null && currentStep.getWorkflowStepType() == WorkflowStepType.ID_TRANSLATION && this.getServiceCode() != null){
			ConfigurableWorkflowActionError importReturnCode = ConfigurableWorkflowActionError.fromCode(this.getServiceCode());
			if(importReturnCode != null && importReturnCode.getCode() != ImportJSONContentServiceReturnCode.SUCCESSFUL.getCode() && !importReturnCode.isRetry()) {
				return true;
			}
		}
		return false;
	}

	public boolean isActionRetried(){
		ConfigurableWorkflowStep currentStep = this.getConfigurableWorkflowStep();
		if(currentStep != null && currentStep.getWorkflowStepType() == WorkflowStepType.ID_TRANSLATION && this.getServiceCode() != null){
			ConfigurableWorkflowActionError importReturnCode = ConfigurableWorkflowActionError.fromCode(this.getServiceCode());
			return importReturnCode != null && importReturnCode.isRetry();
		}
		return false;
	}

	/**
	 * Check whether the action is being aborted.
     */
	public boolean isActionAborted(){
		for(ConfigurableWorkflowApprovalDetail detail : approvalDetails){
			if(detail.getApproved() == ConfigurableWorkflowApprovalDetail.ABORTED){
				return true;
			}
		}
		return false;
	}

	/**
	 * Check whether the action is being aborted.
     */
	public boolean isActionReleasedFromTranslation(){
		for(ConfigurableWorkflowApprovalDetail detail : approvalDetails){
			if(detail.getApproved() == ConfigurableWorkflowApprovalDetail.RELEASED_FROM_TRANS){
				return true;
			}
		}
		return false;
	}
	
	/**
	 * Check whether the action has already been approved by the given user.
     */
	public boolean isActionApprovedByUser(User approver){
		for(ConfigurableWorkflowApprovalDetail detail : approvalDetails){
			if(detail.getUserId() != null && detail.getUserId() == approver.getId() && detail.getApproved() == ConfigurableWorkflowApprovalDetail.APPROVED){
				return true;
			}
		}
		return false;
	}

	public boolean isUserInApprovalList(User approver, int stepType){
		return isUserInApprovalList(approver, new int[]{stepType});
	}

	public boolean isUserInApprovalList(User approver, int[] stepTypes){
		ConfigurableWorkflowStep wfStep = this.getConfigurableWorkflowStep();
		if(wfStep != null && stepTypes != null && IntStream.of(stepTypes).anyMatch(n -> n == wfStep.getWorkflowStepType())){
			for(User approverInList : this.getActionApprovers()){
				if(approverInList.getId() == approver.getId()){
					return true;
				}
			}
		}

		return false;
	}
	
	/**
	 * Check whether the action approve type is "All of" and not all of the approvers ( 0 < # approvers < # of All approvers)
     */
	public boolean isActionPartialApproved(){
		int numApproved = 0;
		for(ConfigurableWorkflowApprovalDetail detail : approvalDetails){
			if(detail.getUserId() != null && detail.getApproved() == ConfigurableWorkflowApprovalDetail.APPROVED){
				numApproved++;
			}
		}
		return (this.getConfigurableWorkflowStep().getApproveType() == ApprovalType.ID_APPROVAL_TYPE_ALL_OF) && (numApproved>0 && numApproved<approvalDetails.size());
	}

	public boolean isSubworkflowAction(){
		ObjectWorkflowActionAssociation wfActionAsso = ObjectWorkflowActionAssociation.findByAction(this);
        return wfActionAsso != null && wfActionAsso.getParentAction() != null && wfActionAsso.getParentAction().getConfigurableWorkflowStep() != null &&
                wfActionAsso.getParentAction().getConfigurableWorkflowStep().isSubworkflowStep();
    }
	
	/**
	 * Calculate the time remaining before the due by time
     */
	public String getDueByRemainingStr(){
		String remainingStr = "";
		if(this.getConfigurableWorkflowStep() != null && this.getConfigurableWorkflowStep().isDueBy()){
			long currentTime = System.currentTimeMillis();
			Date nextEventDate = this.getConfigurableWorkflowStep().getNextEventDate(this.getUpdated());
			if(nextEventDate == null){
				return remainingStr;
			}
			long remainTime = nextEventDate.getTime() - currentTime;
			if(remainTime > 0){
				long rDays = remainTime/DateUtil.ONE_DAY;
				long rHours = (remainTime%DateUtil.ONE_DAY)/DateUtil.ONE_HOUR;
				long rMinutes = (remainTime%DateUtil.ONE_DAY%DateUtil.ONE_HOUR)/DateUtil.ONE_MINUTE;
				remainingStr += ((rDays==0)?"":(rDays+"d")) + ((rHours==0)?"":(rHours+"h")) + rMinutes+"m " + ApplicationUtil.getMessage("page.text.remaining");
			}else{
				remainingStr += "Past Due";
			}
		}
		return remainingStr;
	}
	
	/**
	 * Calculate the due by timestamp (e.g., ex. Jan 1, 2012 09:30)
     */
	public String getDueByStr(){
		String dueByStr = "";
		if(this.getConfigurableWorkflowStep() != null && this.getConfigurableWorkflowStep().isDueBy()){
			Date nextEventDate = this.getConfigurableWorkflowStep().getNextEventDate(this.getUpdated());
			if(nextEventDate == null){
				return dueByStr;
			}
			Locale locale = new Locale(UserUtil.getAppLangCodeForPrincipal());
			SimpleDateFormat df = new SimpleDateFormat("MMM dd, yyyy HH:mm", DateUtil.formatAbbricationMonths( new DateFormatSymbols(locale) ));
			dueByStr += df.format(nextEventDate.getTime());
		}
		return dueByStr;		
	}
	
	/**
	 * Find all ids of workflow actions whose approvers contain the given user
     */
	public static List<Long> findAllIdsForUser(User user){
		String query = "SELECT wa.id FROM workflow_action wa, approval_detail ad WHERE wa.id = ad.wf_action_id AND ad.user_id = ?";
        NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
        sqlQuery.setParameter(1, user.getId());
        List<?> ids = sqlQuery.list();
        List<Long> waIds = new ArrayList<>();
        for(Object idObj : ids){
        	waIds.add(((BigInteger)idObj).longValue());
        }
        return waIds;		
	}

	public void filterLangLocales(List<MessagepointLocale> langLocales, Long defaultLocaleId){
		User principal = UserUtil.getPrincipalUser();
		List<User> approvers = this.getActionApprovers();
		// If the user is not an approver, clear the list then return
		if(!approvers.contains(principal)){
			langLocales.clear();
			return;
		}
		List<MessagepointLocale> filteredList = new ArrayList<>();
		Set<MessagepointLocale> langLocalesInTranslationStep = this.getConfigurableWorkflowStep().getLanguages();
		boolean allowEditDefaultLang = this.getConfigurableWorkflowStep().isAllowEditDefaultLanguage();
		for(MessagepointLocale langLocale : langLocales){
			boolean isDefaultLangLocale = langLocale.getId() == defaultLocaleId;
			if(isDefaultLangLocale){	// Default language
				// If it is default language and default language is not allowed
				if (!allowEditDefaultLang) {
					continue;
				}
			}else {		// Non-default language
				// If the language is not in the step's language list
				if (!langLocalesInTranslationStep.contains(langLocale)) {
					continue;
				}
			}
			filteredList.add(langLocale);
		}

		langLocales.clear();
		langLocales.addAll(filteredList);
	}

	public String findModelType(){
		String type = "";
		if (model instanceof ContentObject) {
			ContentObject contentObject = (ContentObject) model;
			if (contentObject.getIsTouchpointLocal()) {
				type += ApplicationUtil.getMessage("page.label.local") + " ";
			}
			if (contentObject.isDynamicVariantEnabled()) {
				type += ApplicationUtil.getMessage("page.label.dynamic") + " ";
			} else if (contentObject.isStructuredContentEnabled()) {
				type += ApplicationUtil.getMessage("page.label.structured") + " ";
			} else {
				type += ApplicationUtil.getMessage("page.label.static") + " ";
			}
			if (contentObject.getIsSharedFreeform()) {
				type += ApplicationUtil.getMessage("page.label.smart.canvas");
			} else if (contentObject.getIsTouchpointLocal() && contentObject.getContentType().getId() == 1) {
				type += ApplicationUtil.getMessage("page.label.smart.text");
			} else {
				type += ApplicationUtil.getMessage("page.label.message");
			}
		} else if (model instanceof TouchpointSelection) {
			type += ApplicationUtil.getMessage("page.label.touchpoint.selection");
		}
		return type;
	}
}
