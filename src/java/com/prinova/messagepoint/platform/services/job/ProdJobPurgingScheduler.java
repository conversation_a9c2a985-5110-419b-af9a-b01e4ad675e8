package com.prinova.messagepoint.platform.services.job;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.model.CommunicationProductionEvent;
import com.prinova.messagepoint.model.DocumentProductionEvent;
import com.prinova.messagepoint.model.FilerootManagementProfile;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditMetadataBuilder;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.communication.CommunicationMiniProductionEvent;
import com.prinova.messagepoint.model.deliveryevent.DeliveryEvent;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.PropertyUtils;
import org.apache.commons.logging.Log;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public class ProdJobPurgingScheduler extends MessagePointRunnable {
    private static final Log log = LogUtil.getLog(ProdJobPurgingScheduler.class);

    private Date startTime;
    private long threadId;
    private boolean notifyStop;

    @Override
    public void performMainProcessing() {
        long currentThreadId = this.getOwningThread().getId();
        if(this.threadId != currentThreadId){
            this.startTime = DateUtil.now();
            this.threadId = currentThreadId;
            notifyStop = false;
            log.info("ProdJobPurgingScheduler starts at: " + this.startTime);
        }

        String purgeSystem = PropertyUtils.getRuntimeProperty("pod.purge.system.prod.job");
        if((purgeSystem != null && purgeSystem.equalsIgnoreCase("disabled")) || this.shouldStop()) {
            return;
        }

        boolean isSchedulerEnabled = FilerootManagementProfile.getFilerootManagementProfile().getPurgeExpiredProdJobs();
        if(isSchedulerEnabled) {
            log.info("Entering ProdJobPurgingScheduler.performMainProcessing() for schema " + Node.getCurrentNodeSchemaName() + " ...");

            this.doWork();

            HibernateUtil.getManager().getSession().flush();

            log.info("Finished ProdJobPurgingScheduler.performMainProcessing() for schema " + Node.getCurrentNodeSchemaName() + ".");
        }
    }

    private void doWork(){
        int prodJobExpiryDuration = FilerootManagementProfile.getFilerootManagementProfile().getProdJobExpiryDuration();
        Date targetDate = JobPurgingSchedulerUtils.getTargetExpiryDate(prodJobExpiryDuration);
        Date notifiedDate = DateUtil.nextDay(targetDate);

        // DocumentProductionEvent
        List<DocumentProductionEvent> docProdEventlist = HibernateUtil.getManager().getObjects(DocumentProductionEvent.class);
        List<DeliveryEvent> desToBeRemoved = new ArrayList<>();
        for (DocumentProductionEvent documentProductionEvent : docProdEventlist) {
            // Jump out of scheduled job if exceed the period
            if (this.shouldStop()) {
                return;
            }

            Collection<DeliveryEvent> deliveryEvents = documentProductionEvent.getDeliveryEvents();
            desToBeRemoved.clear();
            if (deliveryEvents != null) {
                for (DeliveryEvent deliveryEvent : deliveryEvents) {
                    // Jump out of scheduled job if exceed the period
                    if (this.shouldStop()) {
                        return;
                    }
                    Date updated = deliveryEvent.getUpdated();
                    if (updated == null)
                        updated = deliveryEvent.getCreated();

                    String assetName = "Delivery Event: " + (documentProductionEvent.getName() != null ? documentProductionEvent.getName() : "") +
                            " (Job #: " + (deliveryEvent.getJob() != null ? deliveryEvent.getJob().getId() : "null") + ")";

                    if (updated != null) {
                        if (updated.before(notifiedDate) && updated.after(targetDate)) {
                            // Auditing
                            String action = "This object will be removed tomorrow";
                            AuditEventUtil.push(AuditEventType.ID_CLEANUP, AuditObjectType.ID_DELIVERY_EVENT, assetName, documentProductionEvent.getId(),
                                    AuditActionType.ID_PURGE_NOTIFICATION, AuditMetadataBuilder.forSimpleAuditMessage(action, null, null));
                        } else if (updated.before(targetDate)) {
                            desToBeRemoved.add(deliveryEvent);
                            JobPurgingSchedulerUtils.removeDeliveryEvent(deliveryEvent);
                            // Auditing
                            AuditEventUtil.push(AuditEventType.ID_CLEANUP, AuditObjectType.ID_DELIVERY_EVENT, assetName, documentProductionEvent.getId(),
                                    AuditActionType.ID_PURGE, null);
                        }
                    }
                }
                deliveryEvents.removeAll(desToBeRemoved);
            }

            if (deliveryEvents == null || deliveryEvents.isEmpty()) {
                documentProductionEvent.setOutputFilename("");
                documentProductionEvent.setOutputPath("");
                documentProductionEvent.setError(false);
                documentProductionEvent.setComplete(false);
                documentProductionEvent.save();
            }
        }

        // Remove orphaned delivery events
        if (this.shouldStop()) {
            return;
        }
        JobPurgingSchedulerUtils.removeOrphanedDeliveryEvents(DocumentProductionEvent.class, startTime);
        if (this.shouldStop()) {
            return;
        }
        // Remove the orphaned jobs
        JobPurgingSchedulerUtils.removeOrphanedJobs(DocumentProductionEvent.class, startTime);

        // CommunicationProductionEvent
        List<CommunicationProductionEvent> commProdEventlist = HibernateUtil.getManager().getObjects(CommunicationProductionEvent.class);
        for (CommunicationProductionEvent commProductionEvent : commProdEventlist) {
            // Jump out of scheduled job if exceed the period
            if (this.shouldStop()) {
                return;
            }

            Collection<DeliveryEvent> deliveryEvents = commProductionEvent.getDeliveryEvents();
            desToBeRemoved.clear();
            if (deliveryEvents != null) {
                for (DeliveryEvent deliveryEvent : deliveryEvents) {
                    // Jump out of scheduled job if exceed the period
                    if (this.shouldStop()) {
                        return;
                    }
                    Date updated = deliveryEvent.getUpdated();
                    String assetName = "Delivery Event: " + (commProductionEvent.getName() != null ? commProductionEvent.getName() : "") +
                            " (Job #: " + (deliveryEvent.getJob() != null ? deliveryEvent.getJob().getId() : "null") + ")";
                    if (updated.before(notifiedDate) && updated.after(targetDate)) {
                        // Auditing
                        String action = "This object will be removed tomorrow";
                        AuditEventUtil.push(AuditEventType.ID_CLEANUP, AuditObjectType.ID_DELIVERY_EVENT, assetName, commProductionEvent.getId(),
                                AuditActionType.ID_PURGE_NOTIFICATION, AuditMetadataBuilder.forSimpleAuditMessage(action, null, null));
                    } else if (updated.before(targetDate)) {
                        desToBeRemoved.add(deliveryEvent);
                        JobPurgingSchedulerUtils.removeDeliveryEvent(deliveryEvent);
                        // Auditing
                        AuditEventUtil.push(AuditEventType.ID_CLEANUP, AuditObjectType.ID_DELIVERY_EVENT, assetName, commProductionEvent.getId(),
                                AuditActionType.ID_PURGE, null);
                    }
                }
                deliveryEvents.removeAll(desToBeRemoved);
            }

            if (deliveryEvents == null || deliveryEvents.isEmpty()) {
                commProductionEvent.setOutputFilename("");
                commProductionEvent.setOutputPath("");
                commProductionEvent.setError(false);
                commProductionEvent.setComplete(false);
                commProductionEvent.save();
            }
        }

        // Remove orphaned delivery events
        if (this.shouldStop()) {
            return;
        }
        JobPurgingSchedulerUtils.removeOrphanedDeliveryEvents(CommunicationProductionEvent.class, startTime);

        // Remove the orphaned jobs
        if (this.shouldStop()) {
            return;
        }
        JobPurgingSchedulerUtils.removeOrphanedJobs(CommunicationProductionEvent.class, startTime);

        // CommunicationMiniProductionEvent
        List<CommunicationMiniProductionEvent> commMiniProdEventlist = HibernateUtil.getManager().getObjects(CommunicationMiniProductionEvent.class);
        for (CommunicationMiniProductionEvent commMiniProductionEvent : commMiniProdEventlist) {
            // Jump out of scheduled job if exceed the period
            if (this.shouldStop()) {
                return;
            }

            Collection<DeliveryEvent> deliveryEvents = commMiniProductionEvent.getDeliveryEvents();
            desToBeRemoved.clear();
            if (deliveryEvents != null) {
                for (DeliveryEvent deliveryEvent : deliveryEvents) {
                    // Jump out of scheduled job if exceed the period
                    if (this.shouldStop()) {
                        return;
                    }
                    Date updated = deliveryEvent.getUpdated();
                    String assetName = "Delivery Event: " + (commMiniProductionEvent.getName() != null ? commMiniProductionEvent.getName() : "") +
                            " (Job #: " + (deliveryEvent.getJob() != null ? deliveryEvent.getJob().getId() : "null") + ")";
                    if (updated.before(notifiedDate) && updated.after(targetDate)) {
                        // Auditing
                        String action = "This object will be removed tomorrow";
                        AuditEventUtil.push(AuditEventType.ID_CLEANUP, AuditObjectType.ID_DELIVERY_EVENT, assetName, commMiniProductionEvent.getId(),
                                AuditActionType.ID_PURGE_NOTIFICATION, AuditMetadataBuilder.forSimpleAuditMessage(action, null, null));
                    } else if (updated.before(targetDate)) {
                        desToBeRemoved.add(deliveryEvent);
                        JobPurgingSchedulerUtils.removeDeliveryEvent(deliveryEvent);
                        // Auditing
                        AuditEventUtil.push(AuditEventType.ID_CLEANUP, AuditObjectType.ID_DELIVERY_EVENT, assetName, commMiniProductionEvent.getId(),
                                AuditActionType.ID_PURGE, null);
                    }
                }
                deliveryEvents.removeAll(desToBeRemoved);
            }

            if (deliveryEvents == null || deliveryEvents.isEmpty()) {
                commMiniProductionEvent.setOutputFilename("");
                commMiniProductionEvent.setOutputPath("");
                commMiniProductionEvent.setError(false);
                commMiniProductionEvent.setComplete(false);
                commMiniProductionEvent.save();
            }
        }

        // Remove orphaned delivery events
        if (this.shouldStop()) {
            return;
        }
        JobPurgingSchedulerUtils.removeOrphanedDeliveryEvents(CommunicationMiniProductionEvent.class, startTime);

        // Remove the orphaned jobs
        if (this.shouldStop()) {
            return;
        }
        JobPurgingSchedulerUtils.removeOrphanedJobs(CommunicationMiniProductionEvent.class, startTime);
    }

    private boolean shouldStop(){
        boolean shouldStop = JobPurgingSchedulerUtils.shouldScheduledJobStop(startTime, FilerootManagementProfile.prodJobPeriod);
        if(shouldStop && !notifyStop){
            log.info("ProdJobPurgingScheduler ends at: " + DateUtil.now());
            notifyStop = true;
        }
        return shouldStop;
    }
}
