package com.prinova.messagepoint.platform.services.admin;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.model.DataExpirationSchedule;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.platform.services.job.JobPurgingSchedulerUtils;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.PropertyUtils;
import org.apache.commons.logging.Log;

import java.util.Date;

public class SyncDataExpirationScheduler extends MessagePointRunnable {
    public static final String SYNC_SUPPORT_DATA_PURGE_NAME = "SYNC_SUPPORT_DATA";

    private static final Log log = LogUtil.getLog(SyncDataExpirationScheduler.class);
    private Date startTime;
    private long threadId;
    private boolean notifyStop;

    @Override
    public void performMainProcessing() throws Exception {
        long currentThreadId = this.getOwningThread().getId();
        Node currentNode = Node.getCurrentNode();

        if(this.threadId != currentThreadId){
            this.startTime = DateUtil.now();
            this.threadId = currentThreadId;
            notifyStop = false;
            debug("SyncDataExpirationScheduler starts at: " + this.startTime + " for schema " + currentNode.getSchemaName());
        }

        String purgeSystem = PropertyUtils.getRuntimeProperty("pod.purge.system.sync.data");
        if((purgeSystem != null && purgeSystem.equalsIgnoreCase("disabled")) || this.shouldStop()) {
            return;
        }

        DataExpirationSchedule dataExpirationSchedule = DataExpirationSchedule.findById(DataExpirationSchedule.SYNC_SUPPORT_DATA_PURGE_ID);
        if(isEnabled(dataExpirationSchedule)){
            purgeSyncObject(dataExpirationSchedule, currentNode);
            purgeSyncWorker(dataExpirationSchedule, currentNode);
            purgeSyncRequest(dataExpirationSchedule, currentNode);
            purgeSyncProcess(dataExpirationSchedule, currentNode);
        }

        debug("SyncDataExpirationScheduler finished for schema " + currentNode.getSchemaName());
    }

    private boolean shouldStop(){
        int canRunForInSeconds = 3600 * 8; // 8 hours in seconds
        boolean shouldStop = shouldScheduledJobStop(startTime, canRunForInSeconds);
        if(shouldStop && !notifyStop){
            debug("SyncDataExpirationScheduler stopped at: " + DateUtil.now());
            notifyStop = true;
        }
        return shouldStop;
    }

    private boolean shouldScheduledJobStop(Date startTime, long jobPeriodInSeconds){
        if(startTime != null) {
            Date finishTime = DateUtil.now();
            long timeElapsedInSecs = DateUtil.secondsBetween(startTime, finishTime);
            return timeElapsedInSecs >= jobPeriodInSeconds;
        }else{
            return false;
        }
    }

    private Boolean isEnabled(DataExpirationSchedule schedule){
        if(schedule != null){
            return schedule.getEnabled();
        }

        return false;
    }

    private void purgeSyncProcess(DataExpirationSchedule schedule, Node currentNode){
        if(shouldStop()){
            return;
        }

        long numberRemovedRecords = 0;
        try{
            Date targetDate = JobPurgingSchedulerUtils.getTargetExpiryDate(schedule.getDuration());
            numberRemovedRecords = HibernateUtil.getManager().getSession()
                    .createQuery("delete from SyncProcess where created < :date")
                    .setParameter("date", targetDate)
                    .executeUpdate();
            info("SyncDataExpirationScheduler deleted " + numberRemovedRecords + " from SYNC_PROCESS table in schema " + currentNode.getSchemaName());
        }
        catch(Exception ex){
            error("SyncDataExpirationScheduler failed to purge SYNC_PROCESS table due to an error", ex);
        }
    }

    private void purgeSyncRequest(DataExpirationSchedule schedule, Node currentNode){
        if(shouldStop()){
            return;
        }

        long numberRemovedRecords = 0;
        try{
            Date targetDate = JobPurgingSchedulerUtils.getTargetExpiryDate(schedule.getDuration());
            numberRemovedRecords = HibernateUtil.getManager().getSession()
                    .createQuery("delete from SyncRequest where created < :date")
                    .setParameter("date", targetDate)
                    .executeUpdate();
            info("SyncDataExpirationScheduler deleted " + numberRemovedRecords + " from SYNC_REQUEST table in schema " + currentNode.getSchemaName());
        }
        catch(Exception ex){
            error("SyncDataExpirationScheduler failed to purge SYNC_REQUEST table due to an error", ex);
        }
    }

    private void purgeSyncWorker(DataExpirationSchedule schedule, Node currentNode){
        if(shouldStop()){
            return;
        }

        long numberRemovedRecords = 0;
        try{
            Date targetDate = JobPurgingSchedulerUtils.getTargetExpiryDate(schedule.getDuration());
            numberRemovedRecords = HibernateUtil.getManager().getSession()
                    .createQuery("delete from SyncWorker where created < :date")
                    .setParameter("date", targetDate)
                    .executeUpdate();
            info("SyncDataExpirationScheduler deleted " + numberRemovedRecords + " from SYNC_WORKER table in schema " + currentNode.getSchemaName());
        }
        catch(Exception ex){
            error("SyncDataExpirationScheduler failed to purge SYNC_WORKER table due to an error", ex);
        }
    }

    private void purgeSyncObject(DataExpirationSchedule schedule, Node currentNode){
        if(shouldStop()){
            return;
        }

        long numberRemovedRecords = 0;
        try{
            Date targetDate = JobPurgingSchedulerUtils.getTargetExpiryDate(schedule.getDuration());
            numberRemovedRecords = HibernateUtil.getManager().getSession()
                    .createQuery("delete from SyncObject where created < :date")
                    .setParameter("date", targetDate)
                    .executeUpdate();
            info("SyncDataExpirationScheduler deleted " + numberRemovedRecords + " from SYNC_OBJECT table in schema " + currentNode.getSchemaName());
        }
        catch(Exception ex){
            error("SyncDataExpirationScheduler failed to purge SYNC_OBJECT table due to an error", ex);
        }
    }

    private void info(String message){
        log.info(message);
    }

    private void error(String message, Exception ex){
        log.error(message, ex);
    }
    private void debug(String message){
        log.debug(message);
    }

}
