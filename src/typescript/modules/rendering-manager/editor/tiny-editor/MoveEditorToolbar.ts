import {EditorToolbar} from "../toolbar/EditorToolbar";
import * as $ from "jquery";
import {TextContentEditor} from "./TextContentEditor";

export class MoveEditorToolbar {
    public constructor(
        private editorId: string,
        private correctScrollPosition: () => void,
        private isEditorStopped: () => boolean,
        private callback : () => void,
    ) {
    }

    public execute() {
        this.moveEditorToolbar();
    }

    private moveEditorToolbar(): void {
        const toolbarLocation = EditorToolbar.getJqueryElement();
        toolbarLocation.empty();
        let editorInstanceFilter = `[${TextContentEditor.EDITOR_ID_ATTR}="${this.editorId}"]`;
        let crtRetry = 0;
        let maxRetries = 150;
        let retryInterval = 100;
        this.moveEditorToolbarImpl(
            editorInstanceFilter,
            crtRetry,
            maxRetries,
            retryInterval
        );
    }

    private moveEditorToolbarImpl(
        editorInstanceFilter: string,
        crtRetry: number,
        maxRetries: number,
        retryInterval: number
    ): void {
        crtRetry++;
        if (crtRetry > maxRetries) {
            return;
        }

        if ($(editorInstanceFilter).length === 0) {
            console.log(`Editor instance '${editorInstanceFilter}' was not found at retry count: ${crtRetry}. Stopping retries for moving editor toolbar.`);
            return;
        }

        const toolbar = $(".mce-floatpanel").last();
        if (toolbar.length) {
            toolbar.addClass(TextContentEditor.TOOLBAR_CLASS);
            const toolbarLocation = EditorToolbar.getJqueryElement();
            toolbarLocation.empty();

            toolbarLocation.addClass(EditorToolbar.TOOLBAR_LOCATION_CLASS);
            toolbarLocation.append(toolbar);
            // Marcie panel moved to footer in Connected layout - no longer attached to toolbar
            // toolbarLocation.append(`<div id="${TextContentEditor.MARCIE_LOCATION_ID}"></div>`);
            // Don't override width - let our Connected styling take precedence
            // toolbar.children('div').css('width', '');
            setTimeout(this.correctScrollPosition, 100);

            if (this.callback) {
                this.callback();
            }

            return;
        }

        if (this.isEditorStopped()) {
            return;
        }

        setTimeout(this.moveEditorToolbarImplCallback.bind(
            this,
            editorInstanceFilter,
            crtRetry,
            maxRetries,
            retryInterval
        ), retryInterval);
    }

    private moveEditorToolbarImplCallback (
        editorInstanceFilterParam: string,
        crtRetryParam: number,
        maxRetriesParam: number,
        retryIntervalParam: number
    ): void {
        this.moveEditorToolbarImpl(
            editorInstanceFilterParam,
            crtRetryParam,
            maxRetriesParam,
            retryIntervalParam
        );
    }
}