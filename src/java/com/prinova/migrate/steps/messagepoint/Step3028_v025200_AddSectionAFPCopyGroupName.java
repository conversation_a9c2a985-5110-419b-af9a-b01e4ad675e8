package com.prinova.migrate.steps.messagepoint;

import com.prinova.migrate.MigrationStep;
import com.prinova.migrate.models.ColumnDefinition;

import java.sql.Connection;

public class Step3028_v025200_AddSectionAFPCopyGroupName extends MigrationStep {
    @Override
    public void migrate(Connection conn) throws Exception {
        if ( !columnExists(conn, "document_section", "afp_copy_group_name") ) {
            addColumn(conn, "document_section", ColumnDefinition.createString("afp_copy_group_name", 8, true));
        }
    }
}
