<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
<msgpt:HeaderNew title="page.label.admin" viewType="<%= MessagepointHeader.ViewType.VIEW %>">
	<msgpt:Script src="includes/javascript/dataadmin.js" />
</msgpt:HeaderNew>

<c:set var="securitySettingsEditPerm" value="false" scope="request" />
<msgpt:IfAuthGranted authority="ROLE_PROVIDER_SYSTEM_ADMIN_EDIT" >
	<c:set var="securitySettingsEditPerm" value="true" scope="request" />
</msgpt:IfAuthGranted>
<msgpt:IfAuthGranted authority="ROLE_MASTER_USER" >
	<c:set var="securitySettingsEditPerm" value="true" scope="request" />
</msgpt:IfAuthGranted>

<msgpt:BodyNew>
	<msgpt:BannerNew  edit="false" tab="<%= NavigationTab.TAB_ID_ADMIN %>" />
	<msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_ADMIN %>" />
	<msgpt:LowerContainer>
		<msgpt:DropDownMenu />
		<msgpt:ContentPanel>
			<msgpt:ContentData title="page.label.security.settings">
					<msgpt:FormLayout labelWidth="400px" vertical="true" columns="2">
						<!-- HEADER: General Sign In Settings -->
						<msgpt:FormHeader label="page.label.sign.in.settings"/>
						<!-- Lockout on Invalid Sign In -->
						<msgpt:FormField label="page.label.track.invalid.sign.ins">
							<fmtSpring:message code="${ (securitySettings.trackFlag)? 'page.label.yes' : 'page.label.no' }"/>
						</msgpt:FormField>
						<!-- Maximum Sign In Attempts -->
						<c:if test="${securitySettings.trackFlag}">
							<tr><td align="right">
								<span class="listItemArrow">&nbsp;</span> 
								<fmtSpring:message code="page.label.lock.out.after" />&nbsp;
							</td>
							<td align="left">
								<c:out value="${(securitySettings.maxAttempts)}"/>&nbsp;
								<fmtSpring:message code="page.label.attempts" />
							</td></tr>
						</c:if>
						
						<msgpt:FormField label="page.label.admin.systemproperties.password_reset_keepalive">
							<c:out value="${securitySettings.pwResetKeepAlive}" />
						</msgpt:FormField>
						
						<msgpt:FormField label="page.label.admin.systemproperties.password_expires">
							<fmtSpring:message code="${ (securitySettings.pwExpires)? 'page.label.yes' : 'page.label.no' }" />
						</msgpt:FormField>
						<c:if test="${securitySettings.pwExpires}">
							<msgpt:FormField label="">&nbsp;</msgpt:FormField>
							<tr><td align="right">
								<span class="listItemArrow">&nbsp;</span> 
								<fmtSpring:message code="page.label.admin.systemproperties.password_expire_after" />&nbsp;
							</td>
							<td align="left">
								<c:out value="${(securitySettings.pwExpireDays)}"/>&nbsp;
								<fmtSpring:message code="page.label.days" />
							</td></tr>														
						</c:if>
						
						<msgpt:FormField label="page.label.admin.systemproperties.prevent_repeated_password">
							<fmtSpring:message code="${ (securitySettings.preventRepeatedPw)? 'page.label.yes' : 'page.label.no' }" />
						</msgpt:FormField>
						<c:if test="${securitySettings.preventRepeatedPw}">
							<tr><td align="right">
								<span class="listItemArrow">&nbsp;</span> 
								<fmtSpring:message code="page.label.admin.systemproperties.password_history_entries" />&nbsp;
							</td>
							<td align="left">
								<c:out value="${(securitySettings.pwHistoryEntries)}"/>
							</td></tr>													
						</c:if>
						<msgpt:FormField label="page.label.admin.systemproperties.password_limit_reuse_period">
							<fmtSpring:message code="${ (securitySettings.pwLimitReusePeriod)? 'page.label.yes' : 'page.label.no' }" />
						</msgpt:FormField>
						<c:if test="${securitySettings.pwLimitReusePeriod}">
							<tr><td align="right">
								<span class="listItemArrow">&nbsp;</span> 
								<fmtSpring:message code="page.label.admin.systemproperties.password_limit_reuse_period_months" />&nbsp;
							</td>
							<td align="left">
								<c:out value="${(securitySettings.pwLimitMonths)}"/>
							</td></tr>													
						</c:if>
						
						<msgpt:FormField label="page.label.admin.systemproperties.session_timeout">
							<c:out value="${securitySettings.sessionExpireMins}" />
						</msgpt:FormField>
						<!-- Soft Deactivation -->
						<msgpt:FormField label="page.label.admin.security.settings.soft.deactivation.enabled">
							<fmtSpring:message code="${ (securitySettings.softDeactivationEnabled)? 'page.label.yes' : 'page.label.no' }" />
							<c:if test="${securitySettings.softDeactivationEnabled}">
							<tr><td align="right">
								<span class="listItemArrow">&nbsp;</span> 
								<fmtSpring:message code="page.label.admin.security.settings.soft.deactivation.limit.days" />&nbsp;
							</td>
							<td align="left">
								<c:out value="${(securitySettings.softDeactivationLimitDays)}"/>
							</td></tr>														
						</c:if>
						</msgpt:FormField>
						<!-- Hard Deactivation -->
						<msgpt:FormField label="page.label.admin.security.settings.hard.deactivation.enabled">
							<fmtSpring:message code="${ (securitySettings.hardDeactivationEnabled)? 'page.label.yes' : 'page.label.no' }" />
							<c:if test="${securitySettings.hardDeactivationEnabled}">
							<tr><td align="right">
								<span class="listItemArrow">&nbsp;</span> 
								<fmtSpring:message code="page.label.admin.security.settings.hard.deactivation.limit.days" />&nbsp;
							</td>
							<td align="left">
								<c:out value="${(securitySettings.hardDeactivationLimitDays)}"/>
							</td></tr>														
						</c:if>
						</msgpt:FormField>
					</msgpt:FormLayout >

					<!--  User/Password Settings -->
					<msgpt:FormLayout labelWidth="400px" columns="2" vertical="true">
						<!-- Login/Password Settings -->
						<msgpt:FormHeader label="page.label.user.id.settings" />
						
						<!-- Minimum Username Length -->
						<msgpt:FormField label="page.label.minimum.length">
							<c:out value="${ securitySettings.usernameMinLength }"/>
						</msgpt:FormField>

						<!-- Alphanumeric Only -->
						<msgpt:FormField label="page.label.alphanumeric.only">
							<fmtSpring:message code="${ (securitySettings.alphanumericOnly)? 'page.label.yes' : 'page.label.no' }"/>
						</msgpt:FormField>

						<!-- Maximum Username Length -->
						<msgpt:FormField label="page.label.maximum.length">
							<c:out value="${ securitySettings.usernameMaxLength }"/>
						</msgpt:FormField>

					</msgpt:FormLayout>

					<msgpt:FormLayout labelWidth="400px" columns="2" vertical="true">
						<!-- Special Username/Password Properties -->
						<msgpt:FormHeader label="page.label.password.settings" />

						<!-- Minimum Password Length -->
						<msgpt:FormField label="page.label.minimum.length">
							<c:out value="${ securitySettings.minLength }"/>
						</msgpt:FormField>

						<!-- Uppercase Letter Req -->
						<msgpt:FormField label="page.label.password.special.uppercase">
							<fmtSpring:message code="${ (securitySettings.requiresUppercase)? 'page.label.yes' : 'page.label.no' }"/>
						</msgpt:FormField>

						<!-- Maximum Password Length -->
						<msgpt:FormField label="page.label.maximum.length">
							<c:out value="${ securitySettings.maxLength }"/>
						</msgpt:FormField>

						<!-- Lowercase Letter Req -->
						<msgpt:FormField label="page.label.password.special.lowercase">
							<fmtSpring:message code="${ (securitySettings.requiresLowercase)? 'page.label.yes' : 'page.label.no' }"/>
						</msgpt:FormField>

						<!-- Numeral Req -->
						<msgpt:FormField label="page.label.password.special.numeral">
							<fmtSpring:message code="${ (securitySettings.requiresNumeral)? 'page.label.yes' : 'page.label.no' }"/>
						</msgpt:FormField>

						<!-- Symbol Req -->
						<msgpt:FormField label="page.label.password.special.symbol">
							<fmtSpring:message code="${ (securitySettings.requiresSymbol)? 'page.label.yes' : 'page.label.no' }"/>
						</msgpt:FormField>

						<!-- No Repeat Char -->
						<msgpt:FormField label="page.label.password.special.avoid.repeats">
							<fmtSpring:message code="${ (securitySettings.norepeats)? 'page.label.yes' : 'page.label.no' }"/>
						</msgpt:FormField>

					</msgpt:FormLayout>
					
					<c:if test="${securitySettingsEditPerm}">
						<msgpt:DataTable>
							<msgpt:TableButtons>
								<msgpt:Button label="page.label.edit" URL="password_security_edit.form" flowControl="true"/>
							</msgpt:TableButtons>
						</msgpt:DataTable>
					</c:if>
					
			</msgpt:ContentData>
		</msgpt:ContentPanel>
	</msgpt:LowerContainer>
</msgpt:BodyNew>
</msgpt:Html5>