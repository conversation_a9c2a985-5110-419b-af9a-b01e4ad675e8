#!/usr/bin/env python3
"""
Incremental Messagepoint Codebase Indexer
- Only indexes new/changed files
- Fault-tolerant with resume capability
- Cost-effective by avoiding re-indexing
"""

import os
import sys
import yaml
import json
import time
import hashlib
import sqlite3
from pathlib import Path
from typing import List, Dict, Any, Optional, Set
import requests
import weaviate
import weaviate.classes as wvc
from datetime import datetime

class IncrementalIndexer:
    def __init__(self, config_path: str = None, openai_api_key: str = None):
        """Initialize incremental indexer with state tracking"""
        if config_path is None:
            config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'weaviate_config.yaml')
        
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        self.openai_api_key = openai_api_key or "********************************************************************************************************************************************************************"
        
        # Initialize Weaviate client
        self.client = weaviate.connect_to_custom(
            http_host=self.config['weaviate']['host'],
            http_port=self.config['weaviate']['port'],
            http_secure=False,
            grpc_host=self.config['weaviate']['host'],
            grpc_port=50051,
            grpc_secure=False,
            auth_credentials=wvc.init.Auth.api_key(self.config['weaviate']['api_key']),
            skip_init_checks=True
        )
        
        self.codebase_root = self._find_codebase_root()
        
        # State tracking database
        self.state_db_path = os.path.join(os.path.dirname(__file__), '..', 'state', 'indexing_state.db')
        os.makedirs(os.path.dirname(self.state_db_path), exist_ok=True)
        self.init_state_db()
        
        # Performance tracking
        self.session_stats = {
            'files_processed': 0,
            'chunks_created': 0,
            'files_skipped': 0,
            'total_cost': 0.0,
            'start_time': time.time()
        }
        
    def _find_codebase_root(self) -> str:
        """Find the root of the Messagepoint codebase"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        while current_dir != '/':
            if os.path.exists(os.path.join(current_dir, 'src', 'java')):
                return current_dir
            current_dir = os.path.dirname(current_dir)
        return os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
    
    def init_state_db(self):
        """Initialize SQLite database for tracking file states"""
        self.conn = sqlite3.connect(self.state_db_path)
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS file_states (
                file_path TEXT PRIMARY KEY,
                file_hash TEXT NOT NULL,
                last_modified REAL NOT NULL,
                chunks_count INTEGER NOT NULL,
                last_indexed REAL NOT NULL,
                status TEXT DEFAULT 'completed'
            )
        ''')
        
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS indexing_sessions (
                session_id TEXT PRIMARY KEY,
                start_time REAL NOT NULL,
                end_time REAL,
                files_processed INTEGER DEFAULT 0,
                chunks_created INTEGER DEFAULT 0,
                total_cost REAL DEFAULT 0.0,
                status TEXT DEFAULT 'running'
            )
        ''')
        
        self.conn.commit()
    
    def get_file_hash(self, file_path: str) -> str:
        """Get MD5 hash of file content"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception:
            return ""
    
    def is_file_changed(self, file_path: str) -> bool:
        """Check if file has changed since last indexing"""
        rel_path = os.path.relpath(file_path, self.codebase_root)
        current_hash = self.get_file_hash(file_path)
        current_mtime = os.path.getmtime(file_path)
        
        cursor = self.conn.execute(
            'SELECT file_hash, last_modified FROM file_states WHERE file_path = ?',
            (rel_path,)
        )
        result = cursor.fetchone()
        
        if not result:
            return True  # New file
        
        stored_hash, stored_mtime = result
        return current_hash != stored_hash or current_mtime != stored_mtime
    
    def get_files_to_process(self) -> List[str]:
        """Get list of files that need indexing (new or changed)"""
        files_to_process = []
        
        for root, dirs, files in os.walk(self.codebase_root):
            # Filter directories
            dirs[:] = [d for d in dirs if d not in self.config['indexing']['exclude_dirs']]
            
            for file in files:
                file_path = os.path.join(root, file)
                
                if self.should_index_file(file_path) and self.is_file_changed(file_path):
                    files_to_process.append(file_path)
        
        return files_to_process
    
    def should_index_file(self, file_path: str) -> bool:
        """Check if file should be indexed"""
        file_ext = Path(file_path).suffix
        
        if file_ext not in self.config['indexing']['file_extensions']:
            return False
        
        for exclude_dir in self.config['indexing']['exclude_dirs']:
            if exclude_dir in file_path:
                return False
        
        return True
    
    def remove_old_chunks(self, file_path: str):
        """Remove old chunks for a file from Weaviate (simplified for first run)"""
        rel_path = os.path.relpath(file_path, self.codebase_root)

        # Check if this file was previously indexed
        cursor = self.conn.execute(
            'SELECT chunks_count FROM file_states WHERE file_path = ? AND status = "completed"',
            (rel_path,)
        )
        result = cursor.fetchone()

        if result and result[0] > 0:
            print(f"🔄 File {rel_path} was previously indexed with {result[0]} chunks - will be replaced")

        # For now, skip actual deletion to avoid gRPC issues
        # In production, this would use HTTP API to delete old chunks
    
    def get_embedding(self, text: str) -> List[float]:
        """Get embedding with cost tracking"""
        url = "https://api.openai.com/v1/embeddings"
        headers = {
            "Authorization": f"Bearer {self.openai_api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "input": text[:8000],
            "model": "text-embedding-3-small"
        }
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            tokens = result['usage']['total_tokens']
            cost = tokens * 0.0001 / 1000
            self.session_stats['total_cost'] += cost
            return result['data'][0]['embedding']
        else:
            raise Exception(f"API error: {response.status_code}")
    
    def classify_service_generation(self, content: str, file_path: str) -> str:
        """Simple classification"""
        content_lower = content.lower()
        file_path_lower = file_path.lower()
        
        if any(p.lower() in content_lower for p in ['ResponseDto', '@RestController']):
            return "clean"
        elif any(p.lower() in file_path_lower for p in ['legacy', 'old']):
            return "legacy"
        else:
            return "messy"
    
    def extract_business_domain(self, content: str, file_path: str) -> str:
        """Extract business domain"""
        content_lower = content.lower()
        file_path_lower = file_path.lower()
        
        domains = ['variable', 'user', 'touchpoint', 'task', 'company']
        for domain in domains:
            if domain in content_lower or domain in file_path_lower:
                return domain
        return "unknown"
    
    def process_file_incrementally(self, file_path: str) -> int:
        """Process a single file with state tracking"""
        rel_path = os.path.relpath(file_path, self.codebase_root)
        
        try:
            # Remove old chunks first
            self.remove_old_chunks(file_path)
            
            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Simple chunking
            lines = content.split('\n')
            chunk_size = 100
            chunks_created = 0
            
            for i in range(0, len(lines), chunk_size):
                chunk_lines = lines[i:i + chunk_size]
                chunk_content = '\n'.join(chunk_lines)
                
                if not chunk_content.strip():
                    continue
                
                # Get embedding
                embedding = self.get_embedding(chunk_content)
                
                # Prepare data
                data_object = {
                    "content": chunk_content,
                    "file_path": rel_path,
                    "service_generation": self.classify_service_generation(chunk_content, rel_path),
                    "business_domain": self.extract_business_domain(chunk_content, rel_path),
                    "file_type": Path(file_path).suffix,
                    "chunk_hash": hashlib.md5(chunk_content.encode()).hexdigest()
                }
                
                # Add to Weaviate
                collection = self.client.collections.get("CodeChunk")
                collection.data.insert(
                    properties=data_object,
                    vector=embedding
                )
                
                chunks_created += 1
                time.sleep(0.3)  # Rate limiting
            
            # Update state database
            file_hash = self.get_file_hash(file_path)
            current_time = time.time()
            
            self.conn.execute('''
                INSERT OR REPLACE INTO file_states 
                (file_path, file_hash, last_modified, chunks_count, last_indexed, status)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (rel_path, file_hash, os.path.getmtime(file_path), chunks_created, current_time, 'completed'))
            
            self.conn.commit()
            
            return chunks_created
            
        except Exception as e:
            print(f"❌ Error processing {rel_path}: {e}")
            
            # Mark as failed in state database
            self.conn.execute('''
                INSERT OR REPLACE INTO file_states 
                (file_path, file_hash, last_modified, chunks_count, last_indexed, status)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (rel_path, "", os.path.getmtime(file_path), 0, time.time(), 'failed'))
            
            self.conn.commit()
            return 0
    
    def run_incremental_indexing(self, max_files: int = None):
        """Run incremental indexing with resume capability"""
        session_id = f"session_{int(time.time())}"
        
        print(f"🚀 Starting incremental indexing session: {session_id}")
        
        # Get files that need processing
        files_to_process = self.get_files_to_process()
        
        if max_files:
            files_to_process = files_to_process[:max_files]
        
        print(f"📊 Files to process: {len(files_to_process)}")
        
        if not files_to_process:
            print("✅ No files need indexing - all up to date!")
            return
        
        # Record session start
        self.conn.execute('''
            INSERT INTO indexing_sessions (session_id, start_time)
            VALUES (?, ?)
        ''', (session_id, self.session_stats['start_time']))
        self.conn.commit()
        
        # Process files
        for i, file_path in enumerate(files_to_process, 1):
            rel_path = os.path.relpath(file_path, self.codebase_root)
            print(f"📄 [{i}/{len(files_to_process)}] Processing: {rel_path}")
            
            chunks = self.process_file_incrementally(file_path)
            
            self.session_stats['files_processed'] += 1
            self.session_stats['chunks_created'] += chunks
            
            # Progress update every 10 files
            if i % 10 == 0:
                elapsed = time.time() - self.session_stats['start_time']
                print(f"📊 Progress: {i}/{len(files_to_process)} files, {self.session_stats['chunks_created']} chunks, ${self.session_stats['total_cost']:.4f} cost ({elapsed:.1f}s)")
        
        # Record session completion
        end_time = time.time()
        self.conn.execute('''
            UPDATE indexing_sessions 
            SET end_time = ?, files_processed = ?, chunks_created = ?, total_cost = ?, status = ?
            WHERE session_id = ?
        ''', (end_time, self.session_stats['files_processed'], self.session_stats['chunks_created'], 
              self.session_stats['total_cost'], 'completed', session_id))
        self.conn.commit()
        
        elapsed = end_time - self.session_stats['start_time']
        print(f"\n✅ Incremental indexing complete!")
        print(f"📊 Files processed: {self.session_stats['files_processed']}")
        print(f"📊 Chunks created: {self.session_stats['chunks_created']}")
        print(f"💰 Session cost: ${self.session_stats['total_cost']:.4f}")
        print(f"⏱️  Time: {elapsed:.1f}s")
        
        self.client.close()
        self.conn.close()

def main():
    import argparse

    parser = argparse.ArgumentParser(description='Incremental indexing')
    parser.add_argument('--max-files', type=int, default=50, help='Maximum files to process (default: 50, use 0 for unlimited)')

    args = parser.parse_args()
    max_files = None if args.max_files == 0 else args.max_files

    indexer = IncrementalIndexer()
    indexer.run_incremental_indexing(max_files=max_files)

if __name__ == "__main__":
    main()
