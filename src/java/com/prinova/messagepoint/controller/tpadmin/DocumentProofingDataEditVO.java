package com.prinova.messagepoint.controller.tpadmin;

import java.io.Serializable;

import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.DataResource;

public class DocumentProofingDataEditVO implements Serializable {

	private static final long serialVersionUID = -1545600549388851797L;

	private TouchpointSelection touchpointSelection;
	private int treeNodeLevel;
	private boolean isValid = true;
	private String status;
	private DataResource dataResource;
	private String recepientRangeFrom;
	private String recepientRangeTo;
	
	public TouchpointSelection getTouchpointSelection() {
		return touchpointSelection;
	}
	public void setTouchpointSelection(TouchpointSelection touchpointSelection) {
		this.touchpointSelection = touchpointSelection;
	}
	public int getTreeNodeLevel() {
		return treeNodeLevel;
	}
	public void setTreeNodeLevel(int treeNodeLevel) {
		this.treeNodeLevel = treeNodeLevel;
	}
	public boolean isValid() {
		return isValid;
	}
	public void setValid(boolean isValid) {
		this.isValid = isValid;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public DataResource getDataResource() {
		return dataResource;
	}
	public void setDataResource(DataResource dataResource) {
		this.dataResource = dataResource;
	}
	public String getRecepientRangeFrom() {
		return recepientRangeFrom;
	}
	public int getRecepientRangeFromAsInteger() {
		if (getRecepientRangeFrom() != null && !getRecepientRangeFrom().trim().equals("")) {
			return Integer.valueOf(getRecepientRangeFrom());
		} 
		return 1;
	}
	public void setRecepientRangeFrom(String recepientRangeFrom) {
		this.recepientRangeFrom = recepientRangeFrom;
	}
	public String getRecepientRangeTo() {
		return recepientRangeTo;
	}
	public int getRecepientRangeToAsInteger() {
		if (getRecepientRangeTo() != null && !getRecepientRangeTo().trim().equals("")) {
			return Integer.valueOf(getRecepientRangeTo());
		} 
		return 1;
	}
	public void setRecepientRangeTo(String recepientRangeTo) {
		this.recepientRangeTo = recepientRangeTo;
	}
}