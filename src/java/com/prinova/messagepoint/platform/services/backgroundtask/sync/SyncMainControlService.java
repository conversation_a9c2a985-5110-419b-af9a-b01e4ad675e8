package com.prinova.messagepoint.platform.services.backgroundtask.sync;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.admin.sync.SyncProcess;
import com.prinova.messagepoint.model.util.SyncTouchpointUtil;
import com.prinova.messagepoint.platform.services.backgroundtask.sync.workers.AbstractSyncWorker;
import com.prinova.messagepoint.platform.services.backgroundtask.sync.workers.SyncWorkerResponse;
import com.prinova.messagepoint.platform.services.backgroundtask.sync.workers.SyncWorkerType;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import static com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncStarterBackgroundTaskStatus.*;


public class SyncMainControlService {
    private SyncRequest syncRequest;
    private SyncProcessResponse syncProcessResponse = new SyncProcessResponse();

    @Transactional(propagation = Propagation.REQUIRED, readOnly = false/*, rollbackFor = java.lang.Throwable.class*/)
    public SyncProcessResponse start(SyncRequest syncRequest) {
        this.syncRequest = syncRequest;

        Node sourceInstance = syncRequest.getSourceSchema() == null ? Node.getCurrentNode() : Node.findBySchema(syncRequest.getSourceSchema());
        Node targetInstance = syncRequest.getTargetSchema() == null ? Node.getCurrentNode() : Node.findBySchema(syncRequest.getTargetSchema());

        info("SyncMainControlTask started!");

        info("Source document: [" + syncRequest.getSourceDocument().getId() + "] " + syncRequest.getSourceDocument().getName() + ", " +
             "source instance: " + sourceInstance.getName() + ", " +
             "source schema: " + syncRequest.getSourceSchema() + ", " +
             "source domain: " + sourceInstance.getBranch().getName()
        );

        info("Target document : [" + syncRequest.getTargetDocument().getId() + "] " + syncRequest.getTargetDocument().getName() + ", " +
             "target instance: " + targetInstance.getName() + ", " +
             "target schema: " + syncRequest.getTargetSchema() + ", " +
             "target domain: " + targetInstance.getBranch().getName()
        );

        SyncTouchpointUtil.commitAndContinue();

        updateSyncProcessState(SyncState.STARTED.getState());

        progressStatusPollingBackgroundTask(5);
        updateSyncProcessStatus(
                syncRequest.getBackgroundTaskGuid(),
                SYNC_PROCESS_STAGE_START_PROCESSING,
                5,
                null,
                null,
                null
        );

        executeSyncWorkers();

        progressStatusPollingBackgroundTask(100);
        updateSyncProcessStatus(
                syncRequest.getBackgroundTaskGuid(),
                SYNC_PROCESS_STAGE_UPDATING_DOCUMENT,
                100,
                null,
                null,
                ""
        );
        setDocumentTimestamps();
        setDocumentContentChangedFlag();

        // If there are any workers that have completed with errors - fail sync process.
        // This can be adjusted in future to be completed with warning instead
        if(syncProcessResponse.getWorkersCompletedWithErrors() > 0){
            syncProcessResponse.setFailed(true);
        }

        SyncTouchpointUtil.commitAndContinue();

        return syncProcessResponse;
    }

    public SyncProcessResponse getSyncProcessResponse() {
        return syncProcessResponse;
    }

    /**
     * Call all sync workers and catch any exceptions
     */
    private void executeSyncWorkers(){
        try {
            performSync(SyncWorkerType.SYNC_DATA_SOURCES_WORKER);
            performSync(SyncWorkerType.SYNC_LOOKUP_TABLES_WORKER);
            performSync(SyncWorkerType.SYNC_DATA_ELEMENT_VARIABLES_WORKER);
            performSync(SyncWorkerType.SYNC_METADATA_TEMMPLATE_WORKER);
            performSync(SyncWorkerType.SYNC_DOCUMENT_SETTINGS_WORKER);
            performSync(SyncWorkerType.SYNC_DATA_COLLECTION_WORKER);
            performSync(SyncWorkerType.SYNC_DATA_FILES_WORKER);
            performSync(SyncWorkerType.SYNC_DATA_RESOURCE_WORKER);
            performSync(SyncWorkerType.SYNC_PARAMETER_GROUPS_WORKER);
            performSync(SyncWorkerType.SYNC_TARGET_RULES_WORKER);
            performSync(SyncWorkerType.SYNC_TARGET_GROUPS_WORKER);
            performSync(SyncWorkerType.SYNC_TEXT_STYLE_FONT_WORKER);
            performSync(SyncWorkerType.SYNC_TEXT_STYLE_WORKER);
            performSync(SyncWorkerType.SYNC_LIST_STYLES_WORKER);
            performSync(SyncWorkerType.SYNC_PARAGRAPH_STYLES_WORKER);
            performSync(SyncWorkerType.SYNC_DOCUMENT_ALTERNATELAYOUTS_WORKER);
            performSync(SyncWorkerType.SYNC_COMPOSITION_PACKAGE_WORKER);
            performSync(SyncWorkerType.SYNC_TOUCHPOINT_VARIANT_WORKER);
            performSync(SyncWorkerType.SYNC_CONTENT_OBJECTS_WORKER);
            performSync(SyncWorkerType.SYNC_CHANNEL_CONFIGURATION_WORKER);
            performSync(SyncWorkerType.SYNC_ASSOCIATE_REFERENCED_OBJECTS_WORKER);
        } catch (Exception e) {
            error("SyncMainControlTask caught an exception trying process worker, stopping sync process.", e);
            syncProcessResponse.setFailed(true);
        }
    }

    private void updateSyncProcessState(Integer state){
        SyncProcess syncProcess = syncRequest.getSyncProcess();
        syncProcess.setState(state);
        syncProcess.save();
    }

    /**
     * Call sync() function on a specific worker type
     */
    private void performSync(SyncWorkerType workerType) throws Exception {
        info("Starting "+ workerType);

        // Instantiate Sync Worker implementation class of specific type and call sync() function on it
        AbstractSyncWorker worker = workerType.instantiateWorkerClass(syncRequest);

        try {
            HibernateUtil.getManager().getSession().flush();
        } catch (Exception e) {
            throw e;
        }

        SyncTouchpointUtil.commitAndContinue();

        SyncWorkerResponse response = worker.sync();

        SyncTouchpointUtil.commitAndContinue();

        processWorkerResponse(response, worker);
        info("Completed "+ workerType);
        progressStatusPollingBackgroundTask(5);

        updateSyncProcessStatus(
                syncRequest.getBackgroundTaskGuid(),
                SYNC_PROCESS_STAGE_FINISHED_SYNCING,
                syncRequest.getStatusPollingBackgroundTask().getProgressInPercentInThread(),
                null,
                response.getObjectsProcessedSuccessfully(),
                workerType.getLabel()
        );
    }

    private void progressStatusPollingBackgroundTask(float percentage){
        StatusPollingBackgroundTask pollingTask = syncRequest.getStatusPollingBackgroundTask();
        int currentPercent = pollingTask.getProgressInPercent();
        pollingTask.setProgressInPercentInThread(Math.max(0, Math.min(100, (int) Math.ceil(currentPercent + percentage))));
        pollingTask.save();
    }

    /**
     * Analyze worker response and increment count of processed and failed objects, log worker response to a log file
     */
    private void processWorkerResponse(SyncWorkerResponse syncWorkerResponse, AbstractSyncWorker worker){
        if(syncWorkerResponse.isSuccessful()){
            syncProcessResponse.incrementWorkersCompletedSuccessfully();
        }

        if(syncWorkerResponse.isCompletedWithErrors() || syncWorkerResponse.isFailed()){
            syncProcessResponse.incrementWorkersCompletedWithErrors();
        }

        // Log worker response to log file
        logWorkerResponse(syncWorkerResponse, worker);
    }

    /**
     * Update document in context (project) with checkout/checkin timestamps
     */
    private void setDocumentTimestamps(){
        if(syncRequest.getTargetSchema().equals(syncRequest.getSourceSchema())) {
            Document target = syncRequest.getTargetDocument();
            Document source = syncRequest.getSourceDocument();

            if (target.getOriginObject() != null && target.getOriginObject().getId() == source.getId()) {
                target.setCheckoutTimestamp(new Timestamp(System.currentTimeMillis()));
            }
            else if(source.getOriginObject() != null && source.getOriginObject().getId() == target.getId()){
                target.setCheckinTimestamp(new Timestamp(System.currentTimeMillis()));
            }
            target.save();
        }
    }

    /**
     * Update document's content changed flag
     */
    private void setDocumentContentChangedFlag(){
        Document target = CloneHelper.queryInSchema(syncRequest.getTargetSchema(), () -> Document.findById(syncRequest.getTargetDocumentId()));

        if(target.isTpContentChanged()){
            target.setTpContentChanged(true);
            target.save();
        }
    }

    private void logWorkerResponse(SyncWorkerResponse syncResponse, AbstractSyncWorker worker){
        info("Worker "+ worker.getType() + " completed. "
                + syncResponse.getObjectsProcessedSuccessfully() + " objects processed successfully, "
                + syncResponse.getObjectsProcessedWithError() + " objects processed with error, "
                + syncResponse.getObjectsSkipped() + " objects skipped"
        );
    }

    protected void info(String message){
        LogUtil.getLog(SyncMainControlService.class).info("\uD83C\uDF44: Sync 2.0 [" + syncRequest.getSyncProcess().getId() + "]:" + message);
    }

    protected void error(String message, Exception ex){
        LogUtil.getLog(SyncMainControlService.class).error("\uD83C\uDF44: Sync 2.0 [" + syncRequest.getSyncProcess().getId() + "]:" + message, ex);
    }

}
