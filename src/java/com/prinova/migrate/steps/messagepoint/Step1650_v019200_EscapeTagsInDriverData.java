package com.prinova.migrate.steps.messagepoint;

import java.sql.Connection;

import com.prinova.migrate.MigrationStep;
import com.prinova.migrate.models.ColumnDefinition;

public class Step1650_v019200_EscapeTagsInDriverData extends MigrationStep
{
    @Override
    public void migrate(Connection conn) throws Exception
    {

        if ( !columnExists( conn, "generic_configuration", "escape_driver_data_tags" ) ) {
            addColumn(conn, "generic_configuration", ColumnDefinition.createBoolean("escape_driver_data_tags", true));
            executeUpdate(conn, "UPDATE generic_configuration SET escape_driver_data_tags = 1");
            alterColumn(conn, "generic_configuration", ColumnDefinition.createBoolean("escape_driver_data_tags", false));
        }

        if ( !columnExists( conn, "dialogue_configuration", "escape_driver_data_tags" ) ) {
            addColumn(conn, "dialogue_configuration", ColumnDefinition.createBoolean("escape_driver_data_tags", true));
            executeUpdate(conn, "UPDATE dialogue_configuration SET escape_driver_data_tags = 1");
            alterColumn(conn, "dialogue_configuration", ColumnDefinition.createBoolean("escape_driver_data_tags", false));
        }

        if ( !columnExists( conn, "gmc_configuration", "escape_driver_data_tags" ) ) {
            addColumn(conn, "gmc_configuration", ColumnDefinition.createBoolean("escape_driver_data_tags", true));
            executeUpdate(conn, "UPDATE gmc_configuration SET escape_driver_data_tags = 1");
            alterColumn(conn, "gmc_configuration", ColumnDefinition.createBoolean("escape_driver_data_tags", false));
        }

        if ( !columnExists( conn, "emessaging_configuration", "escape_driver_data_tags" ) ) {
            addColumn(conn, "emessaging_configuration", ColumnDefinition.createBoolean("escape_driver_data_tags", true));
            executeUpdate(conn, "UPDATE emessaging_configuration SET escape_driver_data_tags = 1");
            alterColumn(conn, "emessaging_configuration", ColumnDefinition.createBoolean("escape_driver_data_tags", false));
        }

        if ( !columnExists( conn, "sendmail_configuration", "escape_driver_data_tags" ) ) {
            addColumn(conn, "sendmail_configuration", ColumnDefinition.createBoolean("escape_driver_data_tags", true));
            executeUpdate(conn, "UPDATE sendmail_configuration SET escape_driver_data_tags = 1");
            alterColumn(conn, "sendmail_configuration", ColumnDefinition.createBoolean("escape_driver_data_tags", false));
        }

        if ( !columnExists( conn, "exacttarget_configuration", "escape_driver_data_tags" ) ) {
            addColumn(conn, "exacttarget_configuration", ColumnDefinition.createBoolean("escape_driver_data_tags", true));
            executeUpdate(conn, "UPDATE exacttarget_configuration SET escape_driver_data_tags = 1");
            alterColumn(conn, "exacttarget_configuration", ColumnDefinition.createBoolean("escape_driver_data_tags", false));
        }

        if ( !columnExists( conn, "clickatell_configuration", "escape_driver_data_tags" ) ) {
            addColumn(conn, "clickatell_configuration", ColumnDefinition.createBoolean("escape_driver_data_tags", true));
            executeUpdate(conn, "UPDATE clickatell_configuration SET escape_driver_data_tags = 1");
            alterColumn(conn, "clickatell_configuration", ColumnDefinition.createBoolean("escape_driver_data_tags", false));
        }

        if ( !columnExists( conn, "ftp_configuration", "escape_driver_data_tags" ) ) {
            addColumn(conn, "ftp_configuration", ColumnDefinition.createBoolean("escape_driver_data_tags", true));
            executeUpdate(conn, "UPDATE ftp_configuration SET escape_driver_data_tags = 1");
            alterColumn(conn, "ftp_configuration", ColumnDefinition.createBoolean("escape_driver_data_tags", false));
        }

        if ( !columnExists( conn, "native_comp_configuration", "escape_driver_data_tags" ) ) {
            addColumn(conn, "native_comp_configuration", ColumnDefinition.createBoolean("escape_driver_data_tags", true));
            executeUpdate(conn, "UPDATE native_comp_configuration SET escape_driver_data_tags = 1");
            alterColumn(conn, "native_comp_configuration", ColumnDefinition.createBoolean("escape_driver_data_tags", false));
        }

    }
}