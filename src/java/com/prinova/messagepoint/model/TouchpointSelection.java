package com.prinova.messagepoint.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.prinova.messagepoint.controller.content.ContentCompareUtils;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.common.ArchiveType;
import com.prinova.messagepoint.model.common.TouchpointSelectionListFilterType;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.model.email.TemplateModifier;
import com.prinova.messagepoint.model.email.TemplateVariant;
import com.prinova.messagepoint.model.message.content.TouchpointContentObjectContentSelection;
import com.prinova.messagepoint.model.metadata.MetadataForm;
import com.prinova.messagepoint.model.proof.Proof;
import com.prinova.messagepoint.model.proof.ProofDefinition;
import com.prinova.messagepoint.model.proof.ProofDefinitionStatus;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.util.SyncTouchpointUtil;
import com.prinova.messagepoint.model.version.Approvable;
import com.prinova.messagepoint.model.version.VersionStatus;
import com.prinova.messagepoint.model.workflow.*;
import com.prinova.messagepoint.model.workgroup.Workgroup;
import com.prinova.messagepoint.model.workgroup.WorkgroupNameComparator;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.query.handler.PostQueryHandler;
import com.prinova.messagepoint.query.service.HibernatePaginationService;
import com.prinova.messagepoint.query.service.PaginationServiceResponse;
import com.prinova.messagepoint.tag.layout.TxtFmtTag;
import com.prinova.messagepoint.util.*;
import com.prinova.messagepoint.util.jstree.TouchpointSelectionWrapper;
import com.prinova.messagepoint.util.jstree.TreeNodeSource;
import org.apache.commons.logging.Log;
import org.hibernate.query.NativeQuery;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import javax.persistence.criteria.JoinType;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.Serializable;
import java.math.BigInteger;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class TouchpointSelection extends MessagepointSelection implements Approvable, Serializable, SegmentationAnalyzable, TreeNodeSource {

	private static final long serialVersionUID = -4486725956887791478L;

	private static final Log log = LogUtil.getLog(TouchpointSelection.class);

	public static final int SEARCH_TYPE_NAME 		= 1;
	public static final int SEARCH_TYPE_VALUES		= 2;

	private boolean 					fullyVisible 			= true;
	private Set<User> 					visibleUsers 			= new HashSet<>();
	private boolean 					connectedFullyVisible 	= true;
	private Set<User> 					connectedVisibleUsers 	= new HashSet<>();

	private boolean 					ownContentReadyForApproval;
	private Long 						assigneeId;

	private ConfigurableWorkflow 		workflow;
	private ConfigurableWorkflow 		connectedWorkflow;

	private ConfigurableWorkflowAction 	workflowAction;
	private Set<ProofDefinition> 		proofDefinitions 		= new HashSet<>();
	private Set<Workgroup> 				workgroups 				= new HashSet<>();
	private Set<Approval> 				approvals 				= new HashSet<>();
	private Date 						contentLastUpdated;
	private Long 						contentLastUpdatedBy;
	private Date 						latestProductionDate;
	private int 						archiveTypeId; // ArchiveType
	private String						segmentationData;

	private Set<TemplateModifier> 		templateModifiers 		= new HashSet<>();
	private	TemplateVariant				templateVariant;
	private Document					alternateLayout;

	private MetadataForm				metadataForm;
	private boolean						inheritMetadata			= true;
	private Map<Long, TouchpointSelection> pgTnIdToTpSelectionMap = null;

	private boolean                     needRecalculateHash = true;
	private String                      sha256Hash;
    private String                      attributesHash;
    private String                      activeCopyHash;
    private LanguageContentHash         activeCopyLanguageContentHash = null;
    private String                      workingCopyHash;
    private LanguageContentHash         workingCopyLanguageContentHash = null;

	// Public default constructor
	//
	public TouchpointSelection()
	{
	}

	// Copy constructor (not public) for cloning
	//
	protected TouchpointSelection(TouchpointSelection cloneFrom, Document clonedDocument)
	{
		super(cloneFrom, clonedDocument);

		this.save();

		this.fullyVisible = cloneFrom.fullyVisible;
		this.connectedFullyVisible = cloneFrom.connectedFullyVisible;
		this.ownContentReadyForApproval = cloneFrom.ownContentReadyForApproval;

        if(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
    		this.assigneeId = cloneFrom.assigneeId;

    		if(cloneFrom.workflowAction != null){
    			this.workflowAction = cloneFrom.workflowAction.clone(this);
    		}
/*
    		if (cloneFrom.proofDefinitions != null) {
    			for (ProofDefinition proofDefinition : cloneFrom.proofDefinitions) {
    				this.proofDefinitions.add(proofDefinition.clone(this));
    			}
    		}
*/
        	if ( cloneFrom.workgroups != null )
        	{
    	    	this.workgroups.addAll(cloneFrom.workgroups);
        	}

    		if (cloneFrom.approvals != null) {
    			for (Approval approval : cloneFrom.approvals) {
    				this.approvals.add((Approval) approval.clone());
    			}
    		}

    		this.contentLastUpdated = cloneFrom.contentLastUpdated;
            this.contentLastUpdatedBy = cloneFrom.contentLastUpdatedBy;
		} else {
            this.assigneeId = CloneHelper.getRequestor().getId();

            if(cloneFrom.workflowAction != null){
                this.workflowAction = CloneHelper.clone(cloneFrom.workflowAction, o->o.clone(this));
            }
/*
            if (cloneFrom.proofDefinitions != null) {
                for (ProofDefinition proofDefinition : cloneFrom.proofDefinitions) {
                    this.proofDefinitions.add(CloneHelper.clone(proofDefinition, o->o.clone(this)));
                }
            }
*/
            if ( cloneFrom.workgroups != null )
            {
                Set<Workgroup> clonedWorkgroups = CloneHelper.assign(cloneFrom.workgroups);
                CloneHelper.execInSaveSession(()->{
                    this.workgroups.addAll(clonedWorkgroups);
                });
            }
//
//            this.approvals.add((Approval) approval.clone());
//
            this.contentLastUpdated = cloneFrom.contentLastUpdated;
            this.contentLastUpdatedBy = CloneHelper.getRequestor().getId();
		}

		this.latestProductionDate = cloneFrom.latestProductionDate;
		this.archiveTypeId = cloneFrom.archiveTypeId;

        if(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
            this.visibleUsers.addAll(cloneFrom.visibleUsers);
            this.connectedVisibleUsers.addAll(cloneFrom.connectedVisibleUsers);
        } else {
            if(! cloneFrom.isFullyVisible()) {
                CloneHelper.execInSaveSession(()->{
                    this.visibleUsers.add(CloneHelper.getRequestor());
                });
            }
            if(! cloneFrom.isConnectedFullyVisible()) {
                CloneHelper.execInSaveSession(()->{
                    this.connectedVisibleUsers.add(CloneHelper.getRequestor());
                });
            }
        }

        if(this.getObjectSchemaName().equalsIgnoreCase(cloneFrom.getObjectSchemaName())
            && clonedDocument.getId() == cloneFrom.getDocument().getId()) {
            for (TemplateModifier templateModifier : cloneFrom.templateModifiers) {
                TouchpointSelection referencingTouchpointSelection = templateModifier.getReferencingTouchpointSelection();
                TouchpointSelection clonedReferencingTouchpointSelection = CloneHelper.getAlreadyClonedObject(referencingTouchpointSelection);
                TemplateModifier clonedTemplateModifier = CloneHelper.clone(templateModifier, tm -> {
                    TemplateModifier clonedTM = tm.clone(clonedDocument, this, clonedReferencingTouchpointSelection);
                    return clonedTM;
                });
                CloneHelper.execInSaveSession(() -> {
                    clonedTemplateModifier.save();
                    this.templateModifiers.add(clonedTemplateModifier);
                });
            }
        }
		this.templateVariant = null;/*CloneHelper.assign(cloneFrom.templateVariant); Handled in templateVariant*/
		this.segmentationData = cloneFrom.segmentationData;

		this.alternateLayout = null; //CloneHelper.assign(cloneFrom.alternateLayout);

		if ( cloneFrom.metadataForm != null ) {
			this.metadataForm = CloneHelper.clone(cloneFrom.metadataForm, o->o.clone(true));
			this.metadataForm.save();
		}

		this.inheritMetadata = cloneFrom.inheritMetadata;
	}

	@Override
	public Object clone()
	{
		return new TouchpointSelection(this, this.getDocument());
	}

	public TouchpointSelection clone(Document document)
	{
	    return new TouchpointSelection(this, document);
/*
		TouchpointSelection clone = (TouchpointSelection) this.clone();
		clone.setDocument(document);
        if(document != null && (this.getDocument() == null || document.getId() != this.getDocument().getId())) {
	        clone.setCheckoutTimestamp(document.getCheckoutTimestamp());
		    clone.cloneDna();
		}
		return clone;
*/
	}

	@Override
	public void preDelete() {
		super.preDelete();

	}

	public Set<TemplateModifier> getTemplateModifiers() {
		return templateModifiers;
	}

	public void setTemplateModifiers(Set<TemplateModifier> templateModifiers) {
		this.templateModifiers = templateModifiers;
	}

	public TemplateVariant getTemplateVariant() {
		return templateVariant;
	}

	public void setTemplateVariant(TemplateVariant templateVariant) {
		this.templateVariant = templateVariant;
	}

	public List<User> getVisibleUsersSorted() {
		List<User> approvalUsers = new ArrayList<>(getVisibleUsers());
		Collections.sort(approvalUsers, new UserNameComparator());
		return approvalUsers;
	}

	public List<User> getVisibleConnectedUsersSorted() {
		List<User> connectedApprovalUsers = new ArrayList<>(getConnectedVisibleUsers());
		Collections.sort(connectedApprovalUsers, new UserNameComparator());
		return connectedApprovalUsers;
	}

	public Set<User> getVisibleUsers() {
		return visibleUsers;
	}

	public void setVisibleUsers(Set<User> visibleUsers) {
		this.visibleUsers = visibleUsers;
	}

	public boolean isFullyVisible() {
		return fullyVisible;
	}

	public void setFullyVisible(boolean fullyVisible) {
		this.fullyVisible = fullyVisible;
	}

	public boolean isConnectedFullyVisible() {
		return connectedFullyVisible;
	}

	public void setConnectedFullyVisible(boolean connectedFullyVisible) {
		this.connectedFullyVisible = connectedFullyVisible;
	}

	public Set<User> getConnectedVisibleUsers() {
		return connectedVisibleUsers;
	}

	public void setConnectedVisibleUsers(Set<User> connectedVisibleUsers) {
		this.connectedVisibleUsers = connectedVisibleUsers;
	}

	@SuppressWarnings("rawtypes")
	public boolean hasVisibleChildrenForUser(User user) {

		long userId = UserUtil.getPrincipalUserId();
		String query	= "SELECT DISTINCT pgtnc1.id FROM pg_tree_node pgtn " +
						"INNER JOIN touchpoint_selection tps 		ON tps.pg_tree_node_id = pgtn.id " +
						"LEFT OUTER JOIN tp_sel_visible_user tpvu 	ON tpvu.tp_selection_id = tps.id " +

						"LEFT OUTER JOIN pg_tree_node pgtnc1 		ON pgtnc1.parent_node_id = pgtn.id " +
						"LEFT OUTER JOIN touchpoint_selection tps2 	ON tps2.pg_tree_node_id = pgtnc1.id " +
						"LEFT OUTER JOIN tp_sel_visible_user tpvu2 	ON tpvu2.tp_selection_id = tps2.id " +

						"LEFT OUTER JOIN pg_tree_node pgtnc2 		ON pgtnc2.parent_node_id = pgtnc1.id " +
						"LEFT OUTER JOIN touchpoint_selection tps3 	ON tps3.pg_tree_node_id = pgtnc2.id " +
						"LEFT OUTER JOIN tp_sel_visible_user tpvu3 	ON tpvu3.tp_selection_id = tps3.id " +

						"LEFT OUTER JOIN pg_tree_node pgtnc3 		ON pgtnc3.parent_node_id = pgtnc2.id " +
						"LEFT OUTER JOIN touchpoint_selection tps4 	ON tps4.pg_tree_node_id = pgtnc3.id " +
						"LEFT OUTER JOIN tp_sel_visible_user tpvu4 	ON tpvu4.tp_selection_id = tps4.id " +

						"LEFT OUTER JOIN pg_tree_node pgtnc4 		ON pgtnc4.parent_node_id = pgtnc3.id " +
						"LEFT OUTER JOIN touchpoint_selection tps5 	ON tps5.pg_tree_node_id = pgtnc4.id " +
						"LEFT OUTER JOIN tp_sel_visible_user tpvu5 	ON tpvu5.tp_selection_id = tps5.id " +

						"LEFT OUTER JOIN pg_tree_node pgtnc5 		ON pgtnc5.parent_node_id = pgtnc4.id " +
						"LEFT OUTER JOIN touchpoint_selection tps6 	ON tps6.pg_tree_node_id = pgtnc5.id " +
						"LEFT OUTER JOIN tp_sel_visible_user tpvu6 	ON tpvu6.tp_selection_id = tps6.id " +

						"WHERE 	( 	(tps2.fully_visible = true AND tps2.archive_type_id != 1) OR tpvu2.user_id = " + userId + " 	OR " +
						"			(tps3.fully_visible = true AND tps3.archive_type_id != 1) OR tpvu3.user_id = " + userId + " 	OR " +
						"			(tps4.fully_visible = true AND tps4.archive_type_id != 1) OR tpvu4.user_id = " + userId + " 	OR " +
						"			(tps5.fully_visible = true AND tps5.archive_type_id != 1) OR tpvu5.user_id = " + userId + " 	OR " +
						"			(tps6.fully_visible = true AND tps6.archive_type_id != 1) OR tpvu6.user_id = " + userId + " ) " +
						"     	 	AND pgtn.id = " + getParameterGroupTreeNode().getId();

        NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
        List ids = sqlQuery.list();

        return !ids.isEmpty();
	}

	public boolean isOwnContentReadyForApproval() {
		return ownContentReadyForApproval;
	}

	public void setOwnContentReadyForApproval(boolean ownContentReadyForApproval) {
		this.ownContentReadyForApproval = ownContentReadyForApproval;
	}

	public Long getAssigneeId() {
		return assigneeId;
	}

	public void setAssigneeId(Long id) {
		this.assigneeId = id;
	}

	public String getAssigneeName() {
		if (assigneeId != null)
		{
			User user = User.findById(assigneeId);
			if (user != null)
				return user.getName();
		}
		return "";
	}

	public ConfigurableWorkflowStep getCurrentWorkflowStep() {
		if(getWorkflowAction() == null && getWorkflowAction().getConfigurableWorkflowStep() == null){
			return null;
		}else{
			return getWorkflowAction().getConfigurableWorkflowStep();
		}
	}

	public Set<ProofDefinition> getProofDefinitions() {
		return proofDefinitions;
	}

	public void setProofDefinitions(Set<ProofDefinition> proofDefinitions) {
		this.proofDefinitions = proofDefinitions;
	}

	public TouchpointSelection getParent() {
		ParameterGroupTreeNode pgTreeNode = this.getParameterGroupTreeNode().getParentNode();
		if (pgTreeNode == null) {
			return null;
		} else {
			return (getPgTnIdToTpSelectionMap() != null && getPgTnIdToTpSelectionMap().containsKey(pgTreeNode.getId()))? getPgTnIdToTpSelectionMap().get(pgTreeNode.getId()) : TouchpointSelection.findByPgTreeNodeId(pgTreeNode.getId());
		}
	}

	public ProofDefinition getProofDefinitionForLanguage(String langCode) {
		for (ProofDefinition proofDefinition : getProofDefinitions()) {
			if (proofDefinition.getLanguage().trim().equals(langCode.trim())) {
				return proofDefinition;
			}
		}
		return null;
	}

	public Map<Long, Boolean> getCanProofForLanguage(List<MessagepointLocale> langLocales) {
		Map<Long, Boolean> getCanProofForLanguage = new HashMap<>();
		boolean validSelectorData = getHasSelectorValues() || this.isMaster();
		boolean hasMessage = hasProofMessages(null);
		for (MessagepointLocale locale : langLocales) {
			ProofDefinition proofDefinition = getProofDefinitionForLanguage(locale.getLanguageCode());
			getCanProofForLanguage.put(locale.getId(),  validSelectorData &&
														proofDefinition != null &&
														proofDefinition.getDataResource() != null &&
														proofDefinition.getStatusId() != ProofDefinitionStatus.ID_INVALID &&
														hasMessage);
		}
		return getCanProofForLanguage;
	}
	public boolean hasProofMessages(User requestor) {
		return ContentObjectZonePriorityUtil.hasProofMessages(this);
	}
	public Boolean getCanProofForAnyLanguage() {
		List<MessagepointLocale> languages = this.getDocument().getTouchpointLanguagesAsLocales();
		Map<Long, Boolean> canProofForLanguage = getCanProofForLanguage(languages);
		for (Long localeId : canProofForLanguage.keySet())
			if (canProofForLanguage.get(localeId))
				return true;
		return false;
	}

	public Proof getLatestCompletedProof() {
		List<Proof> tpProofs = Proof.findAllCompletedProofs(this);
		if (tpProofs.iterator().hasNext()) {
			return tpProofs.iterator().next();
		}
		return null;
	}

	public Proof getLatestActiveProofForLang(String langCode) {
		return getLatestActiveProofForLang(langCode, false);
	}
	public Proof getLatestActiveProofForLang(String langCode, boolean applyChannelContext) {
		List<Proof> activeProofs = Proof.findActiveProofsForLang(this, langCode, applyChannelContext);
		if (activeProofs.iterator().hasNext()) {
			return activeProofs.iterator().next();
		} else {
			return null;
		}
	}

	public Proof getLatestCompletedProofForLang(String langCode) {
		return getLatestCompletedProofForLang(langCode, false);
	}
	public Proof getLatestCompletedProofForLang(String langCode, Boolean applyChannelContext) {
		List<Proof> tpProofs = Proof.findAllCompletedProofsForLang(this, langCode, applyChannelContext);
		if (tpProofs.iterator().hasNext()) {
			return tpProofs.iterator().next();
		}
		return null;
	}

	public Set<Workgroup> getWorkgroups() {
		return workgroups;
	}

	public List<Workgroup> getWorkgroupsSorted() {
		List<Workgroup> workgroupsSorted = new ArrayList<>(getWorkgroups());
		Collections.sort(workgroupsSorted, new WorkgroupNameComparator());
		return workgroupsSorted;
	}

	public void setWorkgroups(Set<Workgroup> workgroups) {
		this.workgroups = workgroups;
	}

	public Date getContentLastUpdated() {
		return contentLastUpdated;
	}
	public String getContentLastUpdatedStr() {
		if (getContentLastUpdated() == null)
			return ApplicationUtil.getMessage("page.label.none");

		DateFormat df = SimpleDateFormat.getDateInstance();
		return df.format(getContentLastUpdated());
	}

	public void setContentLastUpdated(Date contentLastUpdated) {
		this.contentLastUpdated = contentLastUpdated;
	}

	public Long getContentLastUpdatedBy() {
		return contentLastUpdatedBy;
	}

	public void setContentLastUpdatedBy(Long contentLastUpdatedBy) {
		this.contentLastUpdatedBy = contentLastUpdatedBy;
	}

	public String getStatus() {
		if(this.getWorkflowAction() ==  null)
			return "Working Copy";
		if(this.getWorkflowAction() != null && this.getWorkflowAction().isActive())
			return "Active";
		if(getWorkflowAction() != null && getWorkflowAction().getConfigurableWorkflowStep() != null){
			return "Pending " + getWorkflowAction().getConfigurableWorkflowStep().getState();
		} else {
			return "";
		}
	}

	public List<User> getCurrentStateUsers() {
		List<User> list = new ArrayList<>();
		if (this.isDeleted()) return list;
		if (this.isWIP() || isActive()) {
			if (this.isMaster()) {
				list = User.findEnabledUsersByPermission(Permission.ROLE_TOUCHPOINT_SELECTIONS_EDIT);
			} else {
				list = User.findEnabledUsersByPermission(Permission.ROLE_TOUCHPOINT_SELECTIONS_EDIT);
			}
		} else {
			//list = getCurrentWorkflowStep().getNextStep().getApprovalUsersSorted();
			list = new ArrayList<>();
			List<User> usersWithPermission = new ArrayList<>();
			if (this.isMaster()) {
				usersWithPermission = User.findEnabledUsersByPermission(Permission.ROLE_TOUCHPOINT_SELECTIONS_APPROVE);
			} else {
				usersWithPermission = User.findEnabledUsersByPermission(Permission.ROLE_TOUCHPOINT_SELECTIONS_APPROVE);
			}
			list.retainAll(usersWithPermission);
		}
		// Put back Super User if this message is assigned to Super User
		User superUser = User.findById(1L);
		if(superUser != null)
			if(this.getAssignedToUserName().equals(superUser.getName()) && !list.contains(superUser))
				list.add(superUser);
		return list;
	}

	public List<User> getNextStateUsers() {
		List<User> list = new ArrayList<>();
		if (getCurrentWorkflowStep().getNextStep() != null && getCurrentWorkflowStep().getNextStep().getNextStep() != null) {
			list = getCurrentWorkflowStep().getNextStep().getNextStep().getApprovalUsersSorted();
		}
		List<User> usersWithPermission = new ArrayList<>();
		usersWithPermission = User.findEnabledUsersByPermission(Permission.ROLE_TOUCHPOINT_SELECTIONS_APPROVE);
		list.retainAll(usersWithPermission);

		return list;
	}

	public boolean isVisible(User user) {
		return fullyVisible || getVisibleUsers().contains(user) || user.isPermitted(Permission.ROLE_TOUCHPOINT_SELECTIONS_ADMIN_SETUP);
	}
	public boolean getIsVisible() {
		return isVisible(UserUtil.getPrincipalUser());
	}

	@Override
	public int hashCode() {
		return (getObjectSchemaName() + " " + getGuid()).hashCode();
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (!(obj instanceof TouchpointSelection))
			return false;
		final TouchpointSelection other = (TouchpointSelection) obj;
		if (! getObjectSchemaName().equals(other.getObjectSchemaName()))
			return false;
		return (getGuid().equals(other.getGuid()));
	}

	public static TouchpointSelection createMasterSelection(Document document, ParameterGroup parameterGroup, Long userId) {
		TouchpointSelection masterSelection = new TouchpointSelection();
		masterSelection.setDocument(document);
		masterSelection.setAssigneeId(userId);
		String masterTreeNodeName = "Master";
		ParameterGroupTreeNode masterTreeNode = ParameterGroupTreeNode.createMasterTreeNode(parameterGroup, masterTreeNodeName, document);
		masterSelection.setParameterGroupTreeNode(masterTreeNode);
		return masterSelection;
	}

	public boolean isDeleted() {
		return (getArchiveTypeId() == ArchiveType.ID_DELETED);
	}

	public boolean isWIP() {
		if(isDeleted()){
			return false;
		}
		if(getWorkflowAction() == null){
			return true;
		}else{
			// The activating thread is still running
			if(this.appliesNoStepWorkflow() && this.isActivatingStillRunning()){
				return true;
			}
			// Current action action is being rejected
			return this.getWorkflowAction().isActionRejected();
		}
	}

	public boolean isReleasedForApproval() {
		if(isDeleted()){
			return false;
		}
		if(getWorkflowAction() == null){
			return false;
		}else{
			if(this.getWorkflowAction().isActionRejected()){	// Current action action is being rejected
				return false;
			}else if(this.getWorkflowAction().isActive() && this.getWorkflowAction().isActionApproved()){
				return false;
			}else{
				return true;
			}
		}
	}

	public boolean isAwaitingApproval() {
		if(isDeleted()){
			return false;
		}
		if(this.getWorkflowAction() == null){
			// If the workflow has no step, it is always awating for approval
			if (TouchpointSelection.findRootWorkflow(this) == null)
			{
				return true;
			}
			ConfigurableWorkflowInstance wfInstance = TouchpointSelection.findRootWorkflow(this).findActiveInstance();
			if(wfInstance == null || wfInstance.getWorkflowSteps() == null || wfInstance.getWorkflowSteps().isEmpty()){
				return true;
			}else{
				return false;
			}
		}else{
			if(this.getWorkflowAction().isActionRejected()){
				return false;
			}else{
				if(this.getWorkflowAction().isActive() && this.getWorkflowAction().isActionApproved()){	// Final action
					return false;
				}else{
					return true;
				}
			}
		}
	}

	public boolean appliesNoStepWorkflow() {
		ConfigurableWorkflowAction wfAction = this.getWorkflowAction();
		ConfigurableWorkflowInstance wfInst = null;
		if( wfAction != null ) {	// Touchpoint selection participate in previous workflow before.
			if ( wfAction.isActionRejected() ) {
				wfInst = TouchpointSelection.findRootWorkflow(this).findActiveInstance();
			} else {
				if(wfAction.getConfigurableWorkflowStep() != null) // By activate (Workflow does not have step)
					wfInst = wfAction.getConfigurableWorkflowStep().getConfigurableWorkflowInstance();
			}
		} else {
			wfInst = TouchpointSelection.findRootWorkflow(this).findActiveInstance();
		}
		return wfInst == null || wfInst.getWorkflowSteps() == null || wfInst.getWorkflowSteps().isEmpty();
	}

	public boolean isAwaitingFinalApproval() {
		if( isDeleted() )
			return false;
		if ( this.getWorkflowAction() == null ) {
			return false;
		} else {
			// Final action
			if( this.getWorkflowAction().getNextAction() == null )
				return true;
			else
				return false;
		}
	}

	public boolean isActive() {
		if(isDeleted()){
			return false;
		}
		if(getWorkflowAction() == null){
			return false;
		}else{
			if(this.getWorkflowAction().isActive()){
				return true;
			}else{
				return false;
			}
		}
	}

	public boolean getIsActive() {
		return isActive();
	}

	public boolean getIsInactive() {
		return !isActive();
	}

	public boolean hasWorkingCopy() {
		return isWIP() || isAwaitingApproval() || (this.appliesNoStepWorkflow() && isActivatingStillRunning());
	}
	public boolean getHasWorkingCopy() {
		return hasWorkingCopy();
	}

	public boolean getHasActiveCopy() {
		return (getLatestProductionDate() != null && !isDeleted());
	}

	public boolean getIsMine() {
		if (this.getAssigneeId() == null)
			return false;
		return this.getAssigneeId().longValue() == UserUtil.getPrincipalUserId();
	}

	public ConfigurableWorkflowStep getWIPStep() {
		return WorkflowManager.getFirstWorkflowStep(TouchpointSelection.findRootWorkflow(this));
	}

	public boolean canUpdate(User requestor) {
		List<User> currentStateUsers = getCurrentStateUsers();
		if (currentStateUsers == null || currentStateUsers.isEmpty()) {
			return false;
		}

		List<Long> currentStateUserIds = new ArrayList<>();
		for (User user : currentStateUsers)
		{
			currentStateUserIds.add(user.getId());
		}

		if (getAssigneeId() != null && currentStateUserIds.contains(getAssigneeId())) {
			return (getAssigneeId() == requestor.getId());
		} else {
			return (currentStateUsers.contains(requestor));
		}
	}

	public boolean canCreateWorkingCopy(User requestor) {
		if (!this.getHasWorkingCopy() && !this.isDeleted()) {
			List<User> list = new ArrayList<>();
			if (this.isMaster()) {
				list = User.findEnabledUsersByPermission(Permission.ROLE_TOUCHPOINT_SELECTIONS_EDIT);
			} else {
				list = User.findEnabledUsersByPermission(Permission.ROLE_TOUCHPOINT_SELECTIONS_EDIT);
			}
			if (list.contains(requestor)) {
				return true;
			}
		}
		return false;
	}

	public boolean canDiscardWorkingCopy(User requestor) {
		if (this.getHasWorkingCopy() && !this.isDeleted()) {
			List<User> list = new ArrayList<>();
			if (this.isMaster()) {
				list = User.findEnabledUsersByPermission(Permission.ROLE_TOUCHPOINT_SELECTIONS_EDIT);
			} else {
				list = User.findEnabledUsersByPermission(Permission.ROLE_TOUCHPOINT_SELECTIONS_EDIT);
			}
			if (list.contains(requestor)) {
				return true;
			}
		}
		return false;
	}

	/**
	 * It is used to "archive" selections that are active (or working copies with associated active copies)
     */
	public boolean canRemove(User requestor) {
		if (!this.isMaster() && !this.isDeleted() && this.getLatestProductionDate() != null) {
			if (requestor.isPermitted(Permission.ROLE_LICENCED_TOUCHPOINT_SELECTIONS_ARCHIVE)) {
				return true;
			}
		}
		return false;

	}

	/**
	 * It is used to remove selections that have never been activated
     */
	public boolean canDelete(User requestor){
		if (!this.isMaster() && !this.isDeleted() && this.getLatestProductionDate() == null) {
			if (requestor.isPermitted(Permission.ROLE_LICENCED_TOUCHPOINT_SELECTIONS_EDIT)) {
				return true;
			}
		}
		return false;
	}

	public VersionStatus getVersionStatus() {
		if (isWIP()) {
			return VersionStatus.findById(VersionStatus.VERSION_WIP);
		}
		if (isActive()) {
			return VersionStatus.findById(VersionStatus.VERSION_PRODUCTION);
		}
		if (isAwaitingApproval()) {
			return VersionStatus.findById(VersionStatus.VERSION_WAITING_APPROVAL);
		}
		return null;
	}

	public static TouchpointSelection findById(long id) {
		return HibernateUtil.getManager().getObject(TouchpointSelection.class, id);
	}

	@SuppressWarnings("unchecked")
	public static TouchpointSelection findByPgTreeNodeId(long pgTreeNodeId) {
		Map<String, Object> params = new HashMap<>();
		params.put("treeNodeId", pgTreeNodeId);

		StringBuilder query = new StringBuilder();
		query.append("SELECT 		ts ");
		query.append("FROM 			TouchpointSelection ts ");
		query.append("INNER JOIN 	ts.parameterGroupTreeNode pgtn ");
		query.append("WHERE 		pgtn.id = :treeNodeId ");
		List<TouchpointSelection> list = (List<TouchpointSelection>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
		if (list != null && !list.isEmpty()) {
			return list.iterator().next();
		}
		return null;
	}

	@SuppressWarnings("unchecked")
	public static TouchpointSelection findByGuid(String guid)
	{
		Map<String, Object> params = new HashMap<>();
		params.put("guid", guid);

		StringBuilder query = new StringBuilder();
		query.append("SELECT 		ts ");
		query.append("FROM 			TouchpointSelection ts ");
		query.append("WHERE 		ts.guid = :guid ");

		List<TouchpointSelection> list = (List<TouchpointSelection>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
		if (list != null && !list.isEmpty()) {
			return list.iterator().next();
		}
		return null;
	}

    public static TouchpointSelection findByDnaAndDocument(TouchpointSelection fellow, Document document)
    {
        if (document != null && fellow != null)
        {
            String dna = fellow.getDna();
            return findByDnaAndDocument(dna, document);
        }

        return null;
    }

    public static TouchpointSelection findByDnaAndDocument(String dna, Document document)
    {
        Map<String, Object> params = new HashMap<>();
        params.put("dna", dna);
        params.put("document", document);

        StringBuilder query = new StringBuilder();
        query.append("SELECT        ts ");
        query.append("FROM          TouchpointSelection ts ");
        query.append("WHERE         ts.dna = :dna AND ts.document = :document");

        List<TouchpointSelection> list = (List<TouchpointSelection>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
        if (list != null && !list.isEmpty()) {
            return list.iterator().next();
        }

        return null;
    }

    public static List<TouchpointSelection> findAllByDocument(Document document){
		if (document != null){
			return HibernateUtil.getManager().getObjectsAdvanced(TouchpointSelection.class, MessagepointRestrictions.eq("document.id", document.getId()));
		}else{
			return null;
		}
	}

	public List<TouchpointSelection> getAllChildrenAndGrandchildren() {
		List<TouchpointSelection> result = new ArrayList<>();
		List<TouchpointSelection> childrenSelections = this.getChildrenOrderByName();

		if (childrenSelections != null && !childrenSelections.isEmpty())
		{
			result.addAll(childrenSelections);

			for (TouchpointSelection childSelection : childrenSelections) {
				if ( childSelection.isDeleted() )
					continue;

				result.addAll(childSelection.getAllChildrenAndGrandchildren());
			}
		}

		return result;
	}

	public List<TouchpointSelection> getAllChildrenAndGrandchildrenOrderByHierarchy() {
		List<TouchpointSelection> result = new ArrayList<>();
		List<TouchpointSelection> childrenSelections = this.getChildrenOrderByName();

		if(!this.isDeleted()) {
			result.add(this);
		}

		if (childrenSelections != null && !childrenSelections.isEmpty())
		{
			for (TouchpointSelection childSelection : childrenSelections) {
				result.addAll(childSelection.getAllChildrenAndGrandchildrenOrderByHierarchy());
			}
		}

		return result;
	}


	public List<TouchpointSelection> getChildrenOrderByName() {
		StringBuilder query = new StringBuilder();

		query.append("SELECT ts.ID from TOUCHPOINT_SELECTION ts ");
		query.append("INNER JOIN PG_TREE_NODE pgtn ON pgtn.ID = ts.PG_TREE_NODE_ID ");
		query.append("WHERE pgtn.PARENT_NODE_ID = ").append(getParameterGroupTreeNode().getId());
		query.append(" AND ts.ARCHIVE_TYPE_ID != " + ArchiveType.ID_DELETED);
		query.append(" AND ts.DOCUMENT_ID is not null ");
		query.append(" ORDER BY pgtn.name COLLATE \"C\" ASC ");

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query.toString());

		List<TouchpointSelection> list = new ArrayList<>();
		List ids = sqlQuery.list();
		for ( Object idObj : ids )
			list.add( TouchpointSelection.findById(((BigInteger)idObj).longValue()) );

		return list;
	}

	public List<TouchpointSelection> getDeletedChildrenOrderByName() {
		StringBuilder query = new StringBuilder();

		query.append("SELECT ts.ID from TOUCHPOINT_SELECTION ts ");
		query.append("INNER JOIN PG_TREE_NODE pgtn ON pgtn.ID = ts.PG_TREE_NODE_ID ");
		query.append("WHERE pgtn.PARENT_NODE_ID = ").append(getParameterGroupTreeNode().getId());
		query.append(" AND ts.ARCHIVE_TYPE_ID = " + ArchiveType.ID_DELETED);
		query.append(" ORDER BY pgtn.name COLLATE \"C\" ASC ");

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query.toString());

		List<TouchpointSelection> list = new ArrayList<>();
		List ids = sqlQuery.list();
		for ( Object idObj : ids )
			list.add( TouchpointSelection.findById(((BigInteger)idObj).longValue()) );

		return list;
	}

	public Set<TouchpointSelection> getDependingAncesters() {
		List<ParameterGroupTreeNode> references = ContentObjectAssociation.findDependingTpParameterGroupTreeNodesForWorkingDataType(getParameterGroupTreeNode());
		Set<TouchpointSelection> dependingAncesters = new HashSet<>();

		for (ParameterGroupTreeNode pgtn : references) {
			dependingAncesters.add(TouchpointSelection.findByPgTreeNodeId(pgtn.getId()));
		}

		return dependingAncesters;
	}

	public boolean hasChildren() {
		if (this.getChildrenOrderByName() != null && !this.getChildrenOrderByName().isEmpty()) {
			return true;
		} else {
			return false;
		}
	}

	public List<TouchpointSelection> getAncesters() {
		return getAncesters(null);
	}

	public List<TouchpointSelection> getAncesters(ParameterGroupTreeNode topParameterGroupTreeNode) {
		List<TouchpointSelection> ancesters = new ArrayList<>();

		ParameterGroupTreeNode treeMode = getParameterGroupTreeNode();
		ParameterGroupTreeNode parentTreeMode = null;
		if (treeMode != topParameterGroupTreeNode)
			parentTreeMode = treeMode.getParentNode();

		while (parentTreeMode != null) {
			TouchpointSelection parent = TouchpointSelection.findByPgTreeNodeId(parentTreeMode.getId());
			if (!parent.isDeleted())
				ancesters.add(parent);
			if (parentTreeMode != topParameterGroupTreeNode)
				parentTreeMode = parentTreeMode.getParentNode();
			else
				parentTreeMode = null;
		}

		Collections.reverse(ancesters);

		return ancesters;
	}
	public List<TouchpointSelection> getInvertedAncesters() {
		List<TouchpointSelection> ancesters = new ArrayList<>();

		ParameterGroupTreeNode parentTreeMode = getParameterGroupTreeNode().getParentNode();
		while (parentTreeMode != null) {
			TouchpointSelection parent = TouchpointSelection.findByPgTreeNodeId(parentTreeMode.getId());
			ancesters.add(parent);
			parentTreeMode = parentTreeMode.getParentNode();
		}
		return ancesters;
	}

	public List<TouchpointSelection> getSiblings() {
		return getSiblings(null);
	}

	public List<TouchpointSelection> getSiblings(ParameterGroupTreeNode topParameterGroupTreeNode) {
		List<TouchpointSelection> siblings = new ArrayList<>();

		ParameterGroupTreeNode treeMode = getParameterGroupTreeNode();
		ParameterGroupTreeNode parentTreeMode = null;
		if (treeMode != topParameterGroupTreeNode)
			parentTreeMode = treeMode.getParentNode();

		if(parentTreeMode != null) {
			/*
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("treeNodeId", parentTreeMode.getId());
			params.put("tsId", getId());

			StringBuffer query = new StringBuffer();
			query.append("SELECT 		ts ");
			query.append("FROM 			TouchpointSelection ts ");
			query.append("INNER JOIN 	ts.parameterGroupTreeNode pgtn ");
			query.append("INNER JOIN 	pgtn.parentNode pn ");
			query.append("WHERE 		pn.id = :treeNodeId ");
			query.append("AND 			ts.id != :tsId ");
			query.append("AND 			ts.archiveTypeId = 0 ");
			query.append("ORDER BY		pgtn.name asc ");
			 */

			StringBuilder query = new StringBuilder();
			query.append("SELECT ts.ID from TOUCHPOINT_SELECTION ts ");
			query.append("INNER JOIN PG_TREE_NODE pgtn ON pgtn.ID = ts.PG_TREE_NODE_ID ");
			query.append("WHERE pgtn.PARENT_NODE_ID = ").append(parentTreeMode.getId());
			query.append(" AND ts.ID != ").append(getId());
			query.append(" AND ts.ARCHIVE_TYPE_ID != " + ArchiveType.ID_DELETED);
			query.append(" ORDER BY pgtn.name COLLATE \"C\" ASC ");

			NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query.toString());

			List ids = sqlQuery.list();
			for ( Object idObj : ids )
				siblings.add( TouchpointSelection.findById(((BigInteger)idObj).longValue()) );
		}
		return siblings;
	}

	public List<TouchpointSelection> getAllChildrenAndGrandchildrenOrderByHierarchy(ParameterGroupTreeNode topParameterGroupTreeNode) {
		List<TouchpointSelection> result = new ArrayList<>();

		if(topParameterGroupTreeNode == null) {
			topParameterGroupTreeNode = getParameterGroupTreeNode();
			while (topParameterGroupTreeNode.getParentNode() != null) {
				topParameterGroupTreeNode = topParameterGroupTreeNode.getParentNode();
			}
		}

		TouchpointSelection parent = TouchpointSelection.findByPgTreeNodeId(topParameterGroupTreeNode.getId());
		result = parent.getAllChildrenAndGrandchildrenOrderByHierarchy();

		return result;
	}

	public TouchpointSelection getFirstLevelAncester() {
		int level = this.getLevel();

		if (level <= 1) {
			return null;
		} else {
			ParameterGroupTreeNode parentTreeNode = this.getParameterGroupTreeNode().getParentNode();
			TouchpointSelection parentSelection = TouchpointSelection.findByPgTreeNodeId(parentTreeNode.getId());
			if (level == 2) {
				return parentSelection;
			} else {
				return parentSelection.getFirstLevelAncester();
			}
		}
	}

	public static List<TouchpointSelection> filterVisibleTouchpointSelections(List<TouchpointSelection> unfilteredTouchpointSelections, User requestor) {
		List<TouchpointSelection> filteredTouchpointSelections = new ArrayList<>();
		for (TouchpointSelection tpSelection : unfilteredTouchpointSelections)
			if ( tpSelection.isVisible(requestor) )
				filteredTouchpointSelections.add(tpSelection);
		return filteredTouchpointSelections;
	}

	public Set<Approval> getApprovals() {
		return approvals;
	}

	public void setApprovals(Set<Approval> approvals) {
		this.approvals = approvals;
	}

	public List<TouchpointContentObjectContentSelection> getOwnedContentSelections() {
		List<TouchpointContentObjectContentSelection> contentSelections = new ArrayList<>();

		List<ContentObjectAssociation> mcas = ContentObjectAssociation.findOwnedTpContentSelections(getParameterGroupTreeNode());
		for (ContentObjectAssociation messageContentAssociation : mcas) {
			contentSelections.add(new TouchpointContentObjectContentSelection(messageContentAssociation.getContentObject(), messageContentAssociation.getTouchpointPGTreeNode(), messageContentAssociation.getZonePart()));
		}
		return contentSelections;
	}
/*
	public boolean advanceToNextStep() {
		ConfigurableWorkflowStep nextStep = getCurrentWorkflowStep().getNextStep();
		if (nextStep != null) {
			setCurrentWorkflowStep(nextStep);
			return true;
		} else {
			return false;
		}
	}
*/
	public Date getLatestProductionDate() {
		return latestProductionDate;
	}

	public boolean hasEverBeenActiveOrItIsMaster() {
		if (getLatestProductionDate() != null || this.isMaster()) {
			return true;
		} else {
			return false;
		}
	}

	public void setLatestProductionDate(Date latestProductionDate) {
		this.latestProductionDate = latestProductionDate;
	}

	public boolean isMatchingFilter(int tpSelectionListFilterId, User requestor) {
		if (this.isDeleted())
			return false;

		switch (tpSelectionListFilterId) {
			case TouchpointSelectionListFilterType.ID_ALL:
				return true;
			case TouchpointSelectionListFilterType.ID_MY_ITEMS:
				if (requestor != null && this.getAssigneeId() != null && requestor.getId() == this.getAssigneeId())
					return true;
				break;
			case TouchpointSelectionListFilterType.ID_INACTIVE:
				if (this.isWIP() || this.isAwaitingApproval() || this.isAwaitingFinalApproval())
					return true;
				break;
			case TouchpointSelectionListFilterType.ID_ACTIVE:
				if (this.hasEverBeenActiveOrItIsMaster())
					return true;
				break;
			default:
				break;
		}
		return false;
	}

	public boolean isMatchingSearchFilter (long searchType, String searchName, String[] searchValues) {
		if ( searchType == SEARCH_TYPE_NAME ){
			if(searchName != null){
				return this.getParameterGroupTreeNode().getName().toLowerCase().contains(searchName.toLowerCase());
			}else{
				return true;
			}
		}else if (searchType == SEARCH_TYPE_VALUES) {
			List<String> searchValuesList = Arrays.asList(searchValues);
			if ( this.getParameterGroupTreeNode().getParameterGroupInstanceCollection() == null ){
				return false;
			}else{
				return !ParameterGroupInstance
                        .findExactMatchInCollection(
                                this.getParameterGroupTreeNode().getParameterGroupInstanceCollection().getId(),
                                searchValuesList).isEmpty();
			}
		} else{
			return true;
		}
	}

	public int getLevel() {
		if (this.getParameterGroupTreeNode().getParentNode() == null) {
			return 0;
		} else {
			TouchpointSelection parentSelection = findByPgTreeNodeId(this.getParameterGroupTreeNode().getParentNode().getId());
			return parentSelection.getLevel() + 1;
		}
	}

	public boolean getHasSelectorValues() {
		if (this.getParameterGroupTreeNode().getParameterGroupInstanceCollection() != null &&
				this.getParameterGroupTreeNode().getParameterGroupInstanceCollection().getParameterGroupInstances() != null &&
                !this.getParameterGroupTreeNode().getParameterGroupInstanceCollection().getParameterGroupInstances().isEmpty()) {
			return true;
		} else {
			return false;
		}
	}

	public int getArchiveTypeId() {
		return archiveTypeId;
	}

	public void setArchiveTypeId(int archiveTypeId) {
		this.archiveTypeId = archiveTypeId;
	}

	public static boolean nameExistsInSiblings(long selectionId, TouchpointSelection parentSelection, String name) {
		if (parentSelection == null) {
			TouchpointSelection selection = TouchpointSelection.findById(selectionId);
			if (selection.isMaster()) {
				return false;
			}
			parentSelection = TouchpointSelection.findByPgTreeNodeId(selection.getParameterGroupTreeNode().getParentNode().getId());
		}

		if (parentSelection == null) {
			return false;
		}

		long documentId = parentSelection.getDocument().getId();
		long parentTreeNodeId = parentSelection.getParameterGroupTreeNode().getId();

		StringBuilder query = new StringBuilder();
		query.append("select count(doc.id) from document as doc ");
		query.append("inner join touchpoint_selection tps on tps.document_id = doc.id ");
		query.append("inner join pg_tree_node pgtn on pgtn.id = tps.pg_tree_node_id ");
		query.append("where doc.id = :documentId ");
		query.append("and 	tps.id <> :selectionId ");
		query.append("and 	pgtn.name = :name ");
		query.append("and 	pgtn.parent_node_id = :parentTreeNodeId ");

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query.toString());
		sqlQuery.setParameter("name", name);
		sqlQuery.setParameter("documentId", documentId);
		sqlQuery.setParameter("selectionId", selectionId);
		sqlQuery.setParameter("parentTreeNodeId", parentTreeNodeId);

		return ((BigInteger)sqlQuery.getSingleResult()).intValue() > 0;
	}

	public static boolean nameExistsInSiblings(TouchpointSelection parentSelection, String name) {

		if (parentSelection == null) {
			return false;
		}

		long documentId = parentSelection.getDocument().getId();
		long parentTreeNodeId = parentSelection.getParameterGroupTreeNode().getId();

		StringBuilder query = new StringBuilder();
		query.append("select count(doc.id) from document as doc ");
		query.append("inner join touchpoint_selection tps on tps.document_id = doc.id ");
		query.append("inner join pg_tree_node pgtn on pgtn.id = tps.pg_tree_node_id ");
		query.append("where doc.id = :documentId ");
		query.append("and 	pgtn.name = :name ");
		query.append("and 	pgtn.parent_node_id = :parentTreeNodeId ");

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query.toString());
		sqlQuery.setParameter("name", name);
		sqlQuery.setParameter("documentId", documentId);
		sqlQuery.setParameter("parentTreeNodeId", parentTreeNodeId);

		return ((BigInteger)sqlQuery.getSingleResult()).intValue() > 0;
	}

    public List<ContentObject> getOwnsStructuredObjects() {
        List<TouchpointSelection> tpSelections = new ArrayList<>();
        tpSelections.add(this);
        return getOwnsStructuredObjects(tpSelections);
    }

    public static List<ContentObject> getOwnsStructuredObjects(Collection<TouchpointSelection> tpSelections) {
        if (tpSelections.isEmpty())
            return new ArrayList<>();

        Set<Long> tpSelectionIds = new HashSet<>();
        for (TouchpointSelection tpSelection : tpSelections) {
            tpSelectionIds.add(tpSelection.getId());
        }

        StringBuilder query = new StringBuilder();
        query.append("select 		co ");
        query.append("from 		    ContentObject co ");
        query.append("inner join 	co.owningTouchpointSelection tps ");
        query.append("where         tps.id IN (:tpSelectionIds) ");
        query.append("AND         	co.removed = false ");
        query.append("AND         	co.structuredContentEnabled = true ");

        Map<String, Object> params = new HashMap<>();
        params.put("tpSelectionIds", tpSelectionIds);

        List<ContentObject> contentObjects = (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
        return contentObjects;
    }

    public static boolean ownsNotRemovedContentObjects(TouchpointSelection tpSelection) {
        Set<TouchpointSelection> tpSelections = new HashSet<>();
        tpSelections.add(tpSelection);
        return ownsNotRemovedContentObjects(tpSelections);
    }

    @SuppressWarnings("unchecked")
	public static boolean ownsNotRemovedContentObjects(Collection<TouchpointSelection> tpSelections) {

		if (tpSelections.isEmpty())
			return false;

		Set<Long> tpSelectionIds = new HashSet<>();
		for (TouchpointSelection tpSelection : tpSelections) {
			tpSelectionIds.add(tpSelection.getId());
		}

		StringBuilder query = new StringBuilder();
		query.append("select 		co ");
		query.append("from 		    ContentObject co ");
		query.append("inner join 	co.owningTouchpointSelection tps ");
		query.append("where         tps.id IN (:tpSelectionIds) ");
		query.append("AND         	co.removed = false ");

		Map<String, Object> params = new HashMap<>();
		params.put("tpSelectionIds", tpSelectionIds);

		List<ContentObject> contentObjects = (List<ContentObject>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
		if (contentObjects.isEmpty()) {
			return false;
		}
		return true;
	}

	@SuppressWarnings("unchecked")
	public static TouchpointSelection findByPGTN( long pgtnid )
	{
	    StringBuilder query = new StringBuilder();
        query.append("select        tps ");
        query.append("from          TouchpointSelection tps ");
        query.append("inner join    tps.parameterGroupTreeNode pgtn ");
        query.append("where         pgtn.id = :pgtnid");

        Map<String, Object> params = new HashMap<>();
        params.put("pgtnid", pgtnid);

        List<TouchpointSelection> items = (List<TouchpointSelection>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params);
        if ( items != null && !items.isEmpty())
            return items.get(0);

        return null;
	}

	public static List<TouchpointSelection> findByTemplateVariant(TemplateVariant templateVariant) {
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("templateVariant", templateVariant));
        critList.add(MessagepointRestrictions.ne("archiveTypeId", ArchiveType.ID_DELETED));
		return HibernateUtil.getManager().getObjectsAdvanced(TouchpointSelection.class, critList);
	}

	public static JSONObject getAsJSON(List<TouchpointSelection> touchpointSelections) {
		JSONObject optionsJSON = new JSONObject();
		try {
			for (TouchpointSelection currentSelection: touchpointSelections)
				optionsJSON.put( String.valueOf(currentSelection.getId()) , currentSelection.getName() );
		} catch (JSONException e) {
			log.error("Error generating Touchpoint Selections JSON data - "+e.getMessage(),e);
			log.error("Error: ", e);
		}
		return optionsJSON;
	}

	public static ArrayList<JSONObject> getAsJSONArray(List<TouchpointSelection> touchpointSelections, ContentObject contentObject) {
		ArrayList<JSONObject> options = new ArrayList<>();

		try {
			for (TouchpointSelection currentSelection: touchpointSelections){
				JSONObject currentSelectionJSON = new JSONObject();
				currentSelectionJSON.put("id", String.valueOf(currentSelection.getId()));
				currentSelectionJSON.put("name", currentSelection.getName());
				currentSelectionJSON.put("level", currentSelection.getLevel());
				currentSelectionJSON.put("contentStatus", getContentStatus(contentObject, currentSelection));
				options.add(currentSelectionJSON);
			}
		} catch (JSONException e) {
			log.error("Error generating Touchpoint Selections JSON data - "+e.getMessage(),e);
			log.error("Error: ", e);
		}
		return options;
	}

	public ConfigurableWorkflowAction getWorkflowAction() {
		return workflowAction;
	}

	public void setWorkflowAction(ConfigurableWorkflowAction workflowAction) {
		this.workflowAction = workflowAction;
	}

	public static List<TouchpointSelection> findAllReadyForApproval(){
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("ownContentReadyForApproval", true));
        critList.add(MessagepointRestrictions.ne("archiveTypeId", ArchiveType.ID_DELETED));
		return HibernateUtil.getManager().getObjectsAdvanced(TouchpointSelection.class, critList);
	}

	public static List<TouchpointSelection> findAllByAdvancedQuery(Document document, boolean applyVisibility, String nameSearchStr, int numCap){
        Map<String, String> firstLevelJoinAlias 		= new LinkedHashMap<>();
        Map<String, String> secondLevelJoinAlias 		= new LinkedHashMap<>();
        List<MessagepointCriterion> firstLevelCriterionList 	= new ArrayList<>();
        List<MessagepointCriterion> secondLevelCriterionList 	= new ArrayList<>();
        Map<String, JoinType> secondLevelJoinType	= new HashMap<>();
        List<MessagepointOrder> orderList 						= new ArrayList<>();
        long userId 								= UserUtil.getPrincipalUserId();
        List<TouchpointSelection> selections 		= new ArrayList<>();

		if(document != null && document.isEnabledForVariation()){
			TouchpointSelection touchpointSelection = document.getMasterTouchpointSelection();

			List<Long> pgtnIds = new ArrayList<>();
			if ( applyVisibility ) {

				String query	= "SELECT DISTINCT pgtnc1.id FROM pg_tree_node pgtn " +
								"INNER JOIN touchpoint_selection tps 		ON tps.pg_tree_node_id = pgtn.id " +
								"LEFT OUTER JOIN tp_sel_visible_user tpvu 	ON tpvu.tp_selection_id = tps.id " +

								"LEFT OUTER JOIN pg_tree_node pgtnc1 		ON pgtnc1.parent_node_id = pgtn.id " +
								"LEFT OUTER JOIN touchpoint_selection tps2 	ON tps2.pg_tree_node_id = pgtnc1.id " +
								"LEFT OUTER JOIN tp_sel_visible_user tpvu2 	ON tpvu2.tp_selection_id = tps2.id " +

								"LEFT OUTER JOIN pg_tree_node pgtnc2 		ON pgtnc2.parent_node_id = pgtnc1.id " +
								"LEFT OUTER JOIN touchpoint_selection tps3 	ON tps3.pg_tree_node_id = pgtnc2.id " +
								"LEFT OUTER JOIN tp_sel_visible_user tpvu3 	ON tpvu3.tp_selection_id = tps3.id " +

								"LEFT OUTER JOIN pg_tree_node pgtnc3 		ON pgtnc3.parent_node_id = pgtnc2.id " +
								"LEFT OUTER JOIN touchpoint_selection tps4 	ON tps4.pg_tree_node_id = pgtnc3.id " +
								"LEFT OUTER JOIN tp_sel_visible_user tpvu4 	ON tpvu4.tp_selection_id = tps4.id " +

								"LEFT OUTER JOIN pg_tree_node pgtnc4 		ON pgtnc4.parent_node_id = pgtnc3.id " +
								"LEFT OUTER JOIN touchpoint_selection tps5 	ON tps5.pg_tree_node_id = pgtnc4.id " +
								"LEFT OUTER JOIN tp_sel_visible_user tpvu5 	ON tpvu5.tp_selection_id = tps5.id " +

								"LEFT OUTER JOIN pg_tree_node pgtnc5 		ON pgtnc5.parent_node_id = pgtnc4.id " +
								"LEFT OUTER JOIN touchpoint_selection tps6 	ON tps6.pg_tree_node_id = pgtnc5.id " +
								"LEFT OUTER JOIN tp_sel_visible_user tpvu6 	ON tpvu6.tp_selection_id = tps6.id " +

								"WHERE 	( 	(tps2.fully_visible = true AND tps2.archive_type_id != 1) 	OR tpvu2.user_id = " + userId + " 	OR " +
								"			(tps3.fully_visible = true AND tps3.archive_type_id != 1) 	OR tpvu3.user_id = " + userId + " 	OR " +
								"			(tps4.fully_visible = true AND tps4.archive_type_id != 1) 	OR tpvu4.user_id = " + userId + " 	OR " +
								"			(tps5.fully_visible = true AND tps5.archive_type_id != 1) 	OR tpvu5.user_id = " + userId + " 	OR " +
								"			(tps6.fully_visible = true AND tps6.archive_type_id != 1) 	OR tpvu6.user_id = " + userId + " ) ";


		        NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
		        List ids = sqlQuery.list();
		        for(Object idObj : ids){
		        	pgtnIds.add(((BigInteger)idObj).longValue());
		        }

		        // Adding the Master
		        if(touchpointSelection != null && touchpointSelection.getParameterGroupTreeNode() != null)
		        	pgtnIds.add(touchpointSelection.getParameterGroupTreeNode().getId());

		        if (pgtnIds.isEmpty())
		        	pgtnIds.add(0L);

				secondLevelCriterionList.add(MessagepointRestrictions.in( "pgtn.id", pgtnIds ) );
			}

            /**
             * First Run: Apply the default filters (Document, Versions)
             */
			// Touchpoint filter
            secondLevelJoinAlias.put("document", "doc");
            secondLevelJoinAlias.put("parameterGroupTreeNode", "pgtn");
            secondLevelCriterionList.add(MessagepointRestrictions.eq("doc.id", document.getId()));

            // Cannot be DELETED selection
            firstLevelCriterionList.add(MessagepointRestrictions.ne("archiveTypeId", ArchiveType.ID_DELETED));

	        /**
	         * Second Run: Apply the left filters (search)
	         */
	        // Search for the selection name
	        if(!nameSearchStr.isEmpty() && !nameSearchStr.equals("NULL")){
	        	secondLevelCriterionList.add(MessagepointRestrictions.ilike("pgtn.name", "%" + nameSearchStr + "%"));
	        }

	        PostQueryHandler postHandler = null;

	        ServiceExecutionContext context = HibernatePaginationService.createContext(TouchpointSelection.class, null, firstLevelCriterionList, secondLevelCriterionList, firstLevelJoinAlias, secondLevelJoinAlias, secondLevelJoinType, 1, numCap, orderList, null, postHandler);
	        Service paginationService = MessagepointServiceFactory.getInstance().lookupService(HibernatePaginationService.SERVICE_NAME, HibernatePaginationService.class);
	        paginationService.execute(context);

	        PaginationServiceResponse serviceResponse = (PaginationServiceResponse) context.getResponse();
	        List<?> list = serviceResponse.getPage().getList();

	        for (Object o : list) {
	        	if(o instanceof TouchpointSelection) {
	        		TouchpointSelection selection = (TouchpointSelection) o;
	        		selections.add(selection);
	            }
	        }
		}
        return selections;
	}

	/**
	 * Retrieve the assigned to user name
     */
	public String getAssignedToUserName(){
		String assignedTo = "";
		if(!this.isActive()){	// Not Active
			ConfigurableWorkflowAction wfAction = this.getWorkflowAction();
			if(wfAction != null && !wfAction.isActionRejected()){
				List<User> approvalUsers = new ArrayList<>();

				if(!this.isFullyVisible()){
					for(User user: wfAction.getActionApprovers()){
						if(this.isVisible(user))
							approvalUsers.add(user);
					}
				}else{
					approvalUsers.addAll(wfAction.getActionApprovers());
				}

				if(approvalUsers.size()>1){
					assignedTo = approvalUsers.size() + " users";
				}else if(approvalUsers.size()==1){
					assignedTo = approvalUsers.get(0).getFullName();
				}else{
					assignedTo = "nobody";
				}
			}else{
				if (this.getAssigneeId() != null)
				{
					User user = User.findById(this.getAssigneeId());
					if (user != null)
						assignedTo = user.getFullName();
					else
						assignedTo = "nobody";
				}
				else
					assignedTo = "nobody";
			}
		}
		return assignedTo;
	}

	public String getSegmentationData() {
		return segmentationData;
	}
	public void setSegmentationData(String segmentationData) {
		this.segmentationData = segmentationData;
	}

	public Document getAlternateLayout() {
		return alternateLayout;
	}
	public void setAlternateLayout(Document alternateLayout) {
		this.alternateLayout = alternateLayout;
	}

	public MetadataForm getMetadataForm() {
		return metadataForm;
	}
	public void setMetadataForm(MetadataForm metadataForm) {
		this.metadataForm = metadataForm;
	}

	public boolean isInheritMetadata() {
		return inheritMetadata;
	}
	public void setInheritMetadata(boolean inheritMetadata) {
		this.inheritMetadata = inheritMetadata;
	}

	public String getSegmentationPercentageInStr(){
		String segmentation = "100%";
		if(this.getSegmentationData() != null && !this.getSegmentationData().isEmpty()){
			segmentation = SegmentationAnalysis.getRecentPercentageOfRecipientsInStr(this.getSegmentationData(), UserUtil.getCurrentTouchpointContext().getId());
		}
		return segmentation;
	}

	public double getSegmentationPercentage(){
		return SegmentationAnalysis.getRecentPercentageOfRecipients(this.getSegmentationData(), UserUtil.getCurrentTouchpointContext().getId());
	}

	public static void getAlternatesForZone(
			Document doc,
			Zone zone,
			Map<TouchpointSelection, Zone> outSelectionZoneAlternates)
	{
		for( TouchpointSelection tps : doc.getTouchpointSelectionsSorted() )
		{
			Document alternateDoc = tps.getAlternateLayout();

			if ( alternateDoc == null || alternateDoc.getId() == doc.getId() )
				continue;

			Zone altZone = alternateDoc.findZoneByParent(zone);
			outSelectionZoneAlternates.put(tps, altZone);
		}
	}

	public boolean getAppliesMetadata() {
		return this.getDocument().getVariantMetadataFormDefinition() != null;
	}

	@Override
	public List<TouchpointSelection> getChildren() {
		return getChildrenOrderByName();
	}

	public TouchpointSelection getMaster(){
		if(!this.isMaster()){
			return this.getParent().getMaster();
		}else{
			return this;
		}
	}

	public String getNameWithParentInfo(){
		String name = this.getName();
		if(this.getParent() != null){
			name += " (" + TxtFmtTag.maxTxtLengh(this.getParent().getName(), 30, false) + ")";
		}
		return name;
	}

	public boolean isEnabledForZoneContext(ContentObject contentObject) {
    	if ( contentObject != null && this.getAlternateLayout() != null ) {
    		if ( contentObject.getIsTouchpointLocal() )
    			return true;

    		Zone alternateZone = this.getAlternateLayout().findZoneByParent(contentObject.getZone());
    		if ( alternateZone == null || !alternateZone.isEnabled() )
    			return false;
    	}
    	return true;
	}

	@JsonIgnore
	public ConfigurableWorkflow getWorkflow() {
		return workflow;
	}

	public void setWorkflow(ConfigurableWorkflow workflow) {
		this.workflow = workflow;
	}

	public ConfigurableWorkflow getConnectedWorkflow() {
		return connectedWorkflow;
	}

	public void setConnectedWorkflow(ConfigurableWorkflow connectedWorkflow) {
		this.connectedWorkflow = connectedWorkflow;
	}

	public static ConfigurableWorkflow findRootWorkflow(TouchpointSelection tpSelection){
		ConfigurableWorkflow rootWorkflow = tpSelection.getWorkflow();
		if(rootWorkflow != null){
			return rootWorkflow;
		}else{
			if(tpSelection.getParent() == null){
				return null;
			}else{
				return TouchpointSelection.findRootWorkflow(tpSelection.getParent());
			}
		}
	}

	public static ConfigurableWorkflow findRootConnectedWorkflow(TouchpointSelection tpSelection){
		ConfigurableWorkflow rootWorkflow = tpSelection.getConnectedWorkflow();
		if ( rootWorkflow != null ) {
			return rootWorkflow;
		} else {
			if ( tpSelection.getParent() == null )
				return tpSelection.getDocument().getConnectedWorkflow();
			else
				return TouchpointSelection.findRootConnectedWorkflow( tpSelection.getParent() );
		}
	}

	public JSONObject getDocumentContext() {
		JSONObject documentContextObj = new JSONObject();
		try {
			if ( alternateLayout != null )
				documentContextObj.put("document_context", alternateLayout.getId());
			else
				documentContextObj.put("document_context", getDocument().getId());
		} catch (JSONException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return documentContextObj;
	}

	 public JSONObject getCompareState(Map<ParameterGroupTreeNode, Map<ParameterGroupTreeNode,Object>> compareToPGTNsMapping, long compareFromObjectId, long compareToObjectId, String compareToSchema){
 		JSONObject compareStateObj = new JSONObject();
		try {
			ContentObject compareFromMi = ContentObject.findById(compareFromObjectId);
			ContentObject compareToMi = CloneHelper.queryInSchema(compareToSchema, ()->ContentObject.findById(compareToObjectId));
			String compareState = "";
			boolean isAdded = false, isRemoved = false, isChanged = false;

			TouchpointSelection msgMasterMapFromTs = compareFromMi.getOwningTouchpointSelection()!=null?compareFromMi.getOwningTouchpointSelection():compareFromMi.getDocument().getMasterTouchpointSelection();
			if(this.getId() == msgMasterMapFromTs.getId()){
				TouchpointSelection msgMasterMapToTs = CloneHelper.queryInSchema(compareToSchema, ()->compareToMi.getOwningTouchpointSelection()!=null?compareToMi.getOwningTouchpointSelection():compareToMi.getDocument().getMasterTouchpointSelection());
				isChanged = ContentCompareUtils.isContentObjectContentChanged(compareFromMi, compareToMi, this.getParameterGroupTreeNode(), msgMasterMapToTs.getParameterGroupTreeNode());
				compareState = isChanged?"changed":"";
			}else{
				Set<ParameterGroupTreeNode> parentKeys = compareToPGTNsMapping.keySet();
				for(ParameterGroupTreeNode parentKey : parentKeys){
					Map<ParameterGroupTreeNode, Object> childNodeMap = compareToPGTNsMapping.get(parentKey);
					Object mapValue = childNodeMap.get(this.getParameterGroupTreeNode());
					if(mapValue != null){
					    if(mapValue instanceof Long) {
					        isAdded = (Long) mapValue == ParameterGroupTreeNode.COMPARE_STATE_ADDED;
			        		isRemoved = (Long) mapValue == ParameterGroupTreeNode.COMPARE_STATE_REMOVED;
					    }
			        		isChanged = false;
			        		if(mapValue instanceof ParameterGroupTreeNode){
			        			ParameterGroupTreeNode mapToNode = (ParameterGroupTreeNode) mapValue; // ParameterGroupTreeNode.findById(mapValue);
			        			isChanged = ContentCompareUtils.isContentObjectContentChanged(compareFromMi, compareToMi, this.getParameterGroupTreeNode(), mapToNode);
			        		}
//			        		compareState = isAdded?"added":(isRemoved?"removed":(isChanged?"changed":""));
			        		compareState = (isAdded||isRemoved)?"updated":(isChanged?"changed":"");
						break;
					} else {
					    compareState = "updated";
					}
				}
			}

		 	if(compareState.isEmpty()){
		 		compareState = "unchanged";
		 	}
		 	compareStateObj.put("compare_state", compareState);
		} catch (JSONException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return compareStateObj;
 }

	public String getNameWithContext() {
		String selectionName = this.getName();
		if ( this.getParent() != null )
			selectionName += " (" + TxtFmtTag.maxTxtLengh(this.getParent().getName(), 30, false) + ")";
		return selectionName;
	}

    public static TouchpointSelection findFirstVisible(TouchpointSelection ts, User user) {

    	if ( ts != null && !ts.isVisible(user) ) {
    		List<TouchpointSelection> tsList = ts.getChildrenOrderByName();

    		for (TouchpointSelection currentSelection: tsList )
    			if ( currentSelection != null && currentSelection.isVisible(user) && !currentSelection.isDeleted() )
    				return currentSelection;

    		for (TouchpointSelection currentSelection: tsList ) {
    			TouchpointSelection childts = findFirstVisible(currentSelection, user);
    			if ( childts != null && childts.isVisible(user) && !childts.isDeleted() )
    				return childts;
    		}

    	}

    	return (ts != null && ts.isVisible(user) && !ts.isDeleted() ? ts : null);
    }

    public String getBreadCrumbStr(){
		StringBuilder breadCrumbTrail = new StringBuilder();

		List<TouchpointSelection> variantList = new ArrayList<>();

		TouchpointSelection currentVariant = this;
		while(currentVariant != null){
			variantList.add(0, currentVariant);
			currentVariant = currentVariant.getParent();
		}

		for(int i=0; i<variantList.size(); i++){
			String value = variantList.get(i).getName();
			if(!breadCrumbTrail.isEmpty()){
				breadCrumbTrail.append(" > ");
			}
			breadCrumbTrail.append(value);
		}

		return breadCrumbTrail.toString();
	}

	public Map<Long, TouchpointSelection> getPgTnIdToTpSelectionMap() {
		return pgTnIdToTpSelectionMap;
	}

	public void setPgTnIdToTpSelectionMap(Map<Long, TouchpointSelection> pgTnIdToTpSelectionMap) {
		this.pgTnIdToTpSelectionMap = pgTnIdToTpSelectionMap;
	}

    @Override
    public void preSave(Boolean isNew) {
        super.preSave(isNew);

        if(needRecalculateHash) {
            makeHash(false);
        }
    }

    public boolean isNeedRecalculateHash() {
        return needRecalculateHash;
    }

    public void setNeedRecalculateHash(boolean needRecalculateHash) {
        this.needRecalculateHash = needRecalculateHash;
    }

    @Override
    public String getSha256Hash() {
        return sha256Hash;
    }

    @Override
    public void setSha256Hash(String sha256Hash) {
        this.sha256Hash = sha256Hash;
    }

    @Override
    public String getAttributesHash() {
        return sha256Hash; /*attributesHash;*/
    }

    public void setAttributesHash(String attributesHash) {
        this.attributesHash = attributesHash;
    }

    public String getActiveCopyHash() {
        return activeCopyHash;
    }

    private static final String emptyHash = "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";


    public String getActiveCopyHashSafe() {
        if(getHasActiveCopy()) {
            if(activeCopyHash == null || activeCopyHash.equalsIgnoreCase(emptyHash)) {
                makeHash(false);
            }
        }
        return activeCopyHash;
    }

    public void setActiveCopyHash(String activeCopyHash) {
        this.activeCopyHash = activeCopyHash;
    }

    public LanguageContentHash getActiveCopyLanguageContentHash() {
        return activeCopyLanguageContentHash;
    }

    public void setActiveCopyLanguageContentHash(LanguageContentHash activeCopyLanguageContentHash) {
        this.activeCopyLanguageContentHash = activeCopyLanguageContentHash;
    }

    public String getWorkingCopyHash() {
        return workingCopyHash;
    }

    public String getWorkingCopyHashSafe() {
        if(hasWorkingCopy()) {
            if(workingCopyHash == null || workingCopyHash.equalsIgnoreCase(emptyHash)) {
                makeHash(false);
            }
        }

        return workingCopyHash;
    }

    public void setWorkingCopyHash(String workingCopyHash) {
        this.workingCopyHash = workingCopyHash;
    }

    public LanguageContentHash getWorkingCopyLanguageContentHash() {
        return workingCopyLanguageContentHash;
    }

    public void setWorkingCopyLanguageContentHash(LanguageContentHash workingCopyLanguageContentHash) {
        this.workingCopyLanguageContentHash = workingCopyLanguageContentHash;
    }


    public void makeHash(boolean isAlgorithmChanged) {
        StringBuilder hashDataStringBuilder = new StringBuilder();
        hashDataStringBuilder.append("touchpointSelection");
        if(getName() != null) {
            hashDataStringBuilder.append(" name:").append(getName());
        }
        if(segmentationData != null) {
            hashDataStringBuilder.append(" segmentationData:").append(segmentationData);
        }
        hashDataStringBuilder.append(" fullyVisible:").append(fullyVisible);
        hashDataStringBuilder.append(" connectedFullyVisible:").append(connectedFullyVisible);
//        hashDataStringBuilder.append(" ownContentReadyForApproval:" + ownContentReadyForApproval);

    	ParameterGroupTreeNode parameterGroupTreeNode = getParameterGroupTreeNode();
        if(parameterGroupTreeNode != null) {
//        	String fullDnaPath = ParameterGroupTreeNode.getFullDnaPath(parameterGroupTreeNode);
//            hashDataStringBuilder.append(" fullDnaPath:" + fullDnaPath);
			ParameterGroupInstanceCollection pgic = parameterGroupTreeNode.getParameterGroupInstanceCollection();
			if(pgic != null) {
	            hashDataStringBuilder.append(" parameterGroupInstanceCollection:").append(pgic.getName());
				Set<ParameterGroupInstance> pgInstances = pgic.getParameterGroupInstances();
				List<String> sourcePGIValues = pgInstances.stream().map(pgi->pgi.getValueString()).collect(Collectors.toList());
				Collections.sort(sourcePGIValues);
	            hashDataStringBuilder.append(" parameterGroupInstanceValues:").append(sourcePGIValues);
			}
        }

        if(templateModifiers != null && ! templateModifiers.isEmpty()) {
            List<TemplateModifier> templateModifiersInOrder = new ArrayList<>(templateModifiers);
            Collections.sort(templateModifiersInOrder, this::compareTemplateModifier);

            for(TemplateModifier templateModifier: templateModifiersInOrder) {
                String templateModifierName = templateModifier.getName();
                if(templateModifierName == null) templateModifierName = "";

                String templateModifierConnectorName = templateModifier.getConnectorName();
                if(templateModifierConnectorName == null) templateModifierConnectorName = "";

                TouchpointSelection templateModifierRefVariant = templateModifier.getReferencingTouchpointSelection();

                ComplexValue templateModifierComplexValue = templateModifier.getComplexValue();
                String templateModifierComplexValueHash = templateModifierComplexValue == null ? "" : templateModifierComplexValue.getSha256Hash();

                hashDataStringBuilder.append(" templateModifier [" + " name ").append(templateModifierName).append(" connectorName ").append(templateModifierConnectorName).append(" isActive ").append(templateModifier.getIsActive()).append(" templateManaged ").append(templateModifier.isTemplateManaged()).append(" referencingTouchpointSelection ").append(templateModifierRefVariant == null ? "" : templateModifierRefVariant.getParameterGroupTreeNode().getDna()).append(" valueType ").append(templateModifier.getValueType()).append(" modiferType ").append(templateModifier.getModiferType()).append(" complexValue ").append(templateModifierComplexValueHash == null ? "" : templateModifierComplexValueHash).append("]");

            }
        }

        hashDataStringBuilder.append(" inheritMetadata:").append(inheritMetadata);

        if(metadataForm != null) {
            String metadataFormHash = metadataForm.getHashSafe();
            hashDataStringBuilder.append(" metadataForm:").append(metadataFormHash == null ? "" : metadataFormHash);
        }

        if(isDeleted()) {
            hashDataStringBuilder.append(" isDeleted:").append(isDeleted());
        }
/*
        if (getDocument().isEnabledForVariantWorkflow()) {
            if (getHasWorkingCopy()) {
                hashDataStringBuilder.append(" hasWorkingCopy:" + getHasWorkingCopy());
            }

            if (getHasActiveCopy()) {
                hashDataStringBuilder.append(" hasActiveCopy:" + getHasActiveCopy());
            }
        }
*/
        String objectHashKey = getObjectHashKey();
//        attributesHash = calculateSha256Hash(getGuid() + ".attribute." + objectHashKey, isAlgorithmChanged, sha256Hash, hashDataStringBuilder.toString());

        if(activeCopyLanguageContentHash == null) {
            activeCopyLanguageContentHash = new LanguageContentHash();
        } else {
            activeCopyLanguageContentHash.getContentHashMap().clear();
        }
        try {
            activeCopyLanguageContentHash.save();
        } catch(Exception e){
            throw e;
        }

        activeCopyHash = null;

        if(workingCopyLanguageContentHash == null) {
            workingCopyLanguageContentHash = new LanguageContentHash();
        } else {
            workingCopyLanguageContentHash.getContentHashMap().clear();
        }
        try {
            workingCopyLanguageContentHash.save();
        } catch(Exception e){
            throw e;
        }

        workingCopyHash = null;

        if(! isMaster()) {
            if (getDocument().isEnabledForVariantWorkflow()) {
                if (getHasActiveCopy()) {
                    activeCopyHash = SyncTouchpointUtil.calculateTPSelectionVersionCopyHash(this, ContentObject.DATA_TYPE_ACTIVE, activeCopyLanguageContentHash);
                }

                if (getHasWorkingCopy()) {
                    workingCopyHash = SyncTouchpointUtil.calculateTPSelectionVersionCopyHash(this, ContentObject.DATA_TYPE_WORKING, workingCopyLanguageContentHash);
//                hashDataStringBuilder.append(" workingCopyHash:" + workingCopyHash);
                }
            }
        }

        sha256Hash = calculateSha256Hash(objectHashKey, isAlgorithmChanged, sha256Hash, hashDataStringBuilder.toString());
    }

    private int compareTemplateModifier(TemplateModifier o1, TemplateModifier o2) {
        String s1 = o1.getName();
        if (s1 == null) s1 = "";
        String s2 = o2.getName();
        if (s2 == null) s2 = "";

        int result = s1.compareTo(s2);
        if (result != 0) return result;

        s1 = o1.getConnectorName();
        if (s1 == null) s1 = "";
        s2 = o2.getConnectorName();
        if (s2 == null) s2 = "";

        result = s1.compareTo(s2);
        if (result != 0) return result;

        return 0;
    }
/*
    @Override
    public String getHashSafe() {
        makeHash(false);
        return super.getHashSafe();
    }
*/
    public Map<String, Object> getAttributesMap() {
        Map<String, Object> attributesMap = super.getAttributesMap();
        attributesMap.put("page.label.attribute.name", getName());
        if(segmentationData != null) {
            attributesMap.put("page.label.attribute.segment.data", segmentationData);
        }
        attributesMap.put("page.label.attribute.fully.visible", fullyVisible);
        attributesMap.put("page.label.attribute.connected.fully.visible", connectedFullyVisible);
        ParameterGroupTreeNode parameterGroupTreeNode = getParameterGroupTreeNode();
        if(parameterGroupTreeNode != null) {
            String fullPath = ParameterGroupTreeNode.getFullPath(parameterGroupTreeNode);

            attributesMap.put("page.label.attribute.full.path", fullPath);

            ParameterGroupInstanceCollection pgic = parameterGroupTreeNode.getParameterGroupInstanceCollection();
            if (pgic != null) {
                Set<ParameterGroupInstance> pgInstances = pgic.getParameterGroupInstances();
                List<String> sourcePGIValues = pgInstances.stream().map(pgi -> pgi.getValueString()).collect(Collectors.toList());
                Collections.sort(sourcePGIValues);
                attributesMap.put("page.label.attribute.selection.data", sourcePGIValues);
            }
        }

        attributesMap.put("page.label.attribute.inherit.metadata", inheritMetadata);

        if(metadataForm != null) {
            String metadataFormName = metadataForm.getName();
            String metadataFormHash = metadataForm.getHashSafe();
            attributesMap.put("page.label.attribute.metadata.form", metadataFormName);
            attributesMap.put("page.label.attribute.metadata.form.hash", metadataFormHash);
        }

        if (getHasWorkingCopy()) {
            attributesMap.put("page.label.attribute.has.working.copy", getHasWorkingCopy());
            if(workingCopyHash != null) {
                attributesMap.put("page.label.attribute.content.hash", workingCopyHash);
            }
        }

        if (getHasActiveCopy()) {
            attributesMap.put("page.label.attribute.has.active.copy", getHasActiveCopy());
        }

        if(templateModifiers != null && ! templateModifiers.isEmpty()) {
            List<TemplateModifier> templateModifiersInOrder = new ArrayList<>(templateModifiers);
            Collections.sort(templateModifiersInOrder, this::compareTemplateModifier);
            List<Map<String, Object>> templateModifiersList = new ArrayList<>();
            for (TemplateModifier templateModifier : templateModifiersInOrder) {
                String templateModifierName = templateModifier.getName();
                if(templateModifierName == null) templateModifierName = "";

                String templateModifierConnectorName = templateModifier.getConnectorName();
                if(templateModifierConnectorName == null) templateModifierConnectorName = "";

                TouchpointSelection templateModifierRefVariant = templateModifier.getReferencingTouchpointSelection();

                ComplexValue templateModifierComplexValue = templateModifier.getComplexValue();
                String templateModifierComplexValueHash = templateModifierComplexValue == null ? "" : templateModifierComplexValue.getSha256Hash();

                Map<String, Object> templateModifierAttributeMap = new HashMap<>();

                templateModifierAttributeMap.put("name", templateModifierName);
                templateModifierAttributeMap.put("connectorName", templateModifierConnectorName);
                templateModifierAttributeMap.put("isActive", templateModifier.getIsActive());
                templateModifierAttributeMap.put("templateManaged", templateModifier.isTemplateManaged());
                templateModifierAttributeMap.put("referencingTouchpointSelection", (templateModifierRefVariant == null ? "" : templateModifierRefVariant.getParameterGroupTreeNode().getName()));
                templateModifierAttributeMap.put("valueType", templateModifier.getValueType());
                templateModifierAttributeMap.put("modiferType", templateModifier.getModiferType());
                templateModifierAttributeMap.put("complexValue", (templateModifierComplexValueHash == null ? "" :
                    templateModifierComplexValue.getViewContent())
                );

                templateModifiersList.add(templateModifierAttributeMap);
            }
            attributesMap.put("page.label.attribute.template.modifiers", templateModifiersList);
        }

        attributesMap.put("page.label.attribute.hash", sha256Hash);

        return attributesMap;
    }

	public JSONObject getContentStatus() {
		JSONObject result = new JSONObject();
		if(this.isInTranslationStep() && this.isSelectionContentDirty()) {
			result.put("data-contentStatus", "dirty");
		}
		return result;
	}

	public static String getContentStatus(ContentObject contentObject, TouchpointSelection tpSel) {

		JSONObject result = new JSONObject();
		ParameterGroupTreeNode masterPGTreeNode = null;
		if (contentObject.getOwningTouchpointSelection() != null)
			masterPGTreeNode = contentObject.getOwningTouchpointSelection().getParameterGroupTreeNode();

		ParameterGroupTreeNode touchpointPGTreeNodeInCOA = tpSel.getParameterGroupTreeNode();
		if (touchpointPGTreeNodeInCOA != null && (touchpointPGTreeNodeInCOA.getParentNode() == null || touchpointPGTreeNodeInCOA == masterPGTreeNode))
			touchpointPGTreeNodeInCOA = null;

		List<ContentObjectAssociation> contentObjectAssociations = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObject, contentObject.getFocusOnDataType(), touchpointPGTreeNodeInCOA,
				tpSel.getDocument().getDefaultTouchpointLanguage().getMessagepointLocale(), null, false, false, false, false, false);

		ContentObjectAssociation mca = null;
		for (ContentObjectAssociation coa : contentObjectAssociations) {
			if (coa.getTypeId() != ContentAssociationType.ID_EMPTY) {
				mca = coa;
				break;
			}
		}

		if (mca == null) {
			mca = ContentObjectAssociation.findByContentObjectTouchpointVariantAndLanguageWithReferenceResolution(contentObject, tpSel.getParameterGroupTreeNode(), tpSel.getDocument().getDefaultTouchpointLanguage().getMessagepointLocale(), null);
		}

		try {
			if (contentObject != null) {
				contentObject.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_WORKING);

				if (mca != null) {
					int type = mca.getTypeId();

					if (type == ContentAssociationType.ID_OWNS) {
						result.put("data-contentStatus", "custom");
						if (contentObject.isInTranslationStep()) {
							if (ContentObjectAssociation.isContentDirtyForContentObjectAndTreeNode(contentObject, tpSel.getParameterGroupTreeNode())) {
								if (result.has("data-contentStatus")) {
									result.put("data-contentStatus", result.getString("data-contentStatus") + ";dirty");
								} else {
									result.put("data-contentStatus", "dirty");
								}
							}
						}
					}

					if (type == ContentAssociationType.ID_SUPPRESSES) {
						result.put("data-contentStatus", "suppresses");
					}

					if (type == ContentAssociationType.ID_REFERENCES) {
						result.put("data-contentStatus", "references");
					}
				} else {
					result.put("data-contentStatus", "outOfSync");
				}

			} else {
				result.put("data-contentStatus", "outOfSync");
			}


		} catch (Exception e) {
			LogUtil.getLog(TouchpointSelectionWrapper.class).error(e);
		}

		return result.get("data-contentStatus").toString();
	}

	public boolean isSelectionContentDirty(){
		List<ContentObjectAssociation> cas = ContentObjectAssociation.findAllByContentObjectAndParameters(null, ContentObject.DATA_TYPE_WORKING_AND_ACTIVE, this.getParameterGroupTreeNode(), null);
		for(ContentObjectAssociation ca : cas){
			if(ca.getContent()!=null && ca.getContent().isDirty()){
				return true;
			}
		}
		return false;
	}

	public boolean isInTranslationStep(){
		if(this.getWorkflowAction()==null){
			return false;
		}else if(this.getWorkflowAction().getConfigurableWorkflowStep()==null){
			return false;
		}else{
			return this.getWorkflowAction().getConfigurableWorkflowStep().isTranslationStep();
		}
	}

	public boolean hasDirtyContentForDefaultLanguage(ContentObject contentObject){
		MessagepointLocale defaultLocale = this.getDocument().getDefaultTouchpointLanguage().getMessagepointLocale();
		List<ContentObjectAssociation> cas = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObject, ContentObject.DATA_TYPE_WORKING_AND_ACTIVE, this.getParameterGroupTreeNode(), defaultLocale);
		for(ContentObjectAssociation ca : cas){
			Content content = ca.getContent();
			if(content != null){
				if(content.isDirty()){
					return true;
				}
			}
		}
		return false;
	}

	public boolean skipNextWorkflowStep(ConfigurableWorkflowStep nextStep){
		boolean skipNextStep = false;
		if(nextStep.isTranslationStep()) {
			List<MessagepointLocale> languages = new ArrayList<>(nextStep.getLanguages());
			// If only default language selected and it is not allow to edit the default language, skip it

			if (!nextStep.isAllowEditDefaultLanguage()) {
				MessagepointLocale defaultLocale = MessagepointLocale.getDefaultSystemLanguageLocale();

				if (languages.size() == 1 && languages.get(0).getId() == defaultLocale.getId()) {
					skipNextStep = true;
				}
			}
			skipNextStep = skipNextStep || !(this.hasDirtyContentForDefaultLanguage(null));
		}
		return skipNextStep;
	}

	public boolean isCreatingWCStillRunning(){
		String threadName = "creating-variant-wc-"+this.getId();
		return MessagePointRunnableUtil.isNamedThreadStillRunning(threadName);
	}

	public boolean isActivatingStillRunning(){
		String threadName = "activating-variant-"+this.getId();
		return MessagePointRunnableUtil.isNamedThreadStillRunning(threadName);
	}

	public void removeUnusedFieldsInApplicableVariant() {
		// update applicable variant
		setDocument(null);
		setAlternateLayout(null);
		setWorkflowAction(null);
		setConnectedWorkflow(null);
		setMetadataForm(null);
		setConnectedVisibleUsers(null);
		setTemplateModifiers(null);
		setUpdated(null);
		setCheckoutTimestamp(null);
		setCheckinTimestamp(null);
		setLatestProductionDate(null);
		setContentLastUpdated(null);
		setCreated(null);

		ParameterGroupTreeNode node = new ParameterGroupTreeNode();
		ParameterGroupTreeNode groupTreeNode = getParameterGroupTreeNode();
		if(groupTreeNode != null) {
			node.setName(groupTreeNode.getName());
			node.setId(groupTreeNode.getId());
		}
		groupTreeNode.setCreated(null);
		setParameterGroupTreeNode(node);

		setOriginObject(null);
		setProofDefinitions(null);
		setMetadataForm(null);
		setLatestProductionDate(null);
		setContentLastUpdated(null);
	}
}