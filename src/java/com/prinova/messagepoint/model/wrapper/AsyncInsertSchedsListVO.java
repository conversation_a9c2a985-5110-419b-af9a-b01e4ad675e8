package com.prinova.messagepoint.model.wrapper;

import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.tag.layout.TxtFmtTag;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;

public class AsyncInsertSchedsListVO extends AsyncListVO {
	private InsertSchedule				insertSchedule;
	private String						name;
	private AsyncListVOIFrameData		iFrameData;
	private InsertSchedsListVOFlags		flags;
	
	public AsyncInsertSchedsListVO(){
		super();
	}

	public void setTask(InsertSchedule insertSchedule) {
		this.insertSchedule = insertSchedule;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getName() {
		
		
		long currentTpId = -1;
		String insertViewURL = ApplicationUtil.getWebRoot() + "insert/insert_schedule_view.form";
		String nameHTML = TxtFmtTag.maxTxtLengh("<a id=\"actionLabel_"+ this.insertSchedule.getId() +"\" itemName=\"ITEM_NAME\" href=\"#\" onclick=\"javascript:iFrameView('"+insertViewURL+"?documentId=" + currentTpId + "&insertSchedId=" + this.insertSchedule.getId() + "&statusViewId=1', 'actionLabel_"+this.insertSchedule.getId()+"', event); return false;\" class=\"dataTableLink dataTableItemName\">" + name + "</a>", 100, false);
		nameHTML = nameHTML.replace("ITEM_NAME",name);
		
		boolean hasCurrentSegment = this.insertSchedule.findCurrentSegmentForSchedule() != null;
		String noCurrentSegmentInd = "";
		if ( !hasCurrentSegment )
			noCurrentSegmentInd = "<i class=\"table-icon far fa-clock text-danger ml-2 detailTip\" aria-hidden=\"true\" " +
					"title=\"|<div class='detailTipText'>" + ApplicationUtil.getMessage("page.label.schedule.has.expired") + "</div>\" >" +
					"</i>";

		return nameHTML + noCurrentSegmentInd;
	}
	
	public String getStatus(){
		return this.insertSchedule.getStatusDisplay();
	}
	
	public String getScheduleId(){
		if(this.insertSchedule.getScheduleId()!=null && !this.insertSchedule.getScheduleId().isEmpty()){
			return "<span class=\"dataTableItemName\">" + this.insertSchedule.getScheduleId() + "</span>";
		}else{
			return ApplicationUtil.getMessage("page.label.none");
		}
	}
	
	public String getAssignedTo(){
		return this.insertSchedule.getLockedForName();
	}
	
	public String getDateRange(){
		String dateRangeStr = "";
		if(this.insertSchedule.getPreviousSchedule() != null){
			dateRangeStr += "<i class='table-icon far fa-arrow-left mr-2' aria-hidden='true'></i>";
		}
		dateRangeStr += DateUtil.formatDate(this.insertSchedule.getStartDate()) + " ~ ";
		if(this.insertSchedule.getEndDate() != null){
			dateRangeStr += DateUtil.formatDate(this.insertSchedule.getEndDate());
		}else{
			dateRangeStr += ApplicationUtil.getMessage("page.label.indefinite");
		}
		if(this.insertSchedule.getNextSchedule() != null){
			dateRangeStr += "<i class='table-icon far fa-arrow-right ml-2' aria-hidden='true'></i>";
		}
		return dateRangeStr;
	}
	
	public String getCreatedBy(){
		return this.insertSchedule.getCreatedByName();
	}

	public AsyncListVOIFrameData getIFrameData() {
		return iFrameData;
	}
	public void setIFrameData(AsyncListVOIFrameData iFrameData) {
		this.iFrameData = iFrameData;
	}

	public String getBinding() {
		return "<input id='listItemCheck_" + this.insertSchedule.getId() + "' type='checkbox' value='" + this.insertSchedule.getId() +
				"' style='display : none;' name='selectedIds'><input type='hidden' value='on' name='_selectedIds'>";
	}
	
	public InsertSchedsListVOFlags getFlags() {
		return flags;
	}

	public void setFlags(InsertSchedsListVOFlags flags) {
		this.flags = flags;
	}

	public static class InsertSchedsListVOFlags{
		private boolean	canUpdate;
		private boolean	canApproveReject;
		private boolean canRollForward;
		private boolean canReleaseForApproval;
		private boolean canDeactivate;
		private boolean canDiscard;
		private boolean canReassign;
		private boolean canReleaseForUse;
		private boolean canAddTask;
		
		public boolean isCanUpdate() {
			return canUpdate;
		}
		public void setCanUpdate(boolean canUpdate) {
			this.canUpdate = canUpdate;
		}
		public boolean isCanApproveReject() {
			return canApproveReject;
		}
		public void setCanApproveReject(boolean canApproveReject) {
			this.canApproveReject = canApproveReject;
		}
		public boolean isCanRollForward() {
			return canRollForward;
		}
		public void setCanRollForward(boolean canRollForward) {
			this.canRollForward = canRollForward;
		}
		public boolean isCanReleaseForApproval() {
			return canReleaseForApproval;
		}
		public void setCanReleaseForApproval(boolean canReleaseForApproval) {
			this.canReleaseForApproval = canReleaseForApproval;
		}
		public boolean isCanDeactivate() {
			return canDeactivate;
		}
		public void setCanDeactivate(boolean canDeactivate) {
			this.canDeactivate = canDeactivate;
		}
		public boolean isCanDiscard() {
			return canDiscard;
		}
		public void setCanDiscard(boolean canDiscard) {
			this.canDiscard = canDiscard;
		}
		public boolean isCanReassign() {
			return canReassign;
		}
		public void setCanReassign(boolean canReassign) {
			this.canReassign = canReassign;
		}
		public boolean isCanReleaseForUse() {
			return canReleaseForUse;
		}
		public void setCanReleaseForUse(boolean canReleaseForUse) {
			this.canReleaseForUse = canReleaseForUse;
		}
		public boolean isCanAddTask() {
			return canAddTask;
		}
		public void setCanAddTask(boolean canAddTask) {
			this.canAddTask = canAddTask;
		}
	}
}
