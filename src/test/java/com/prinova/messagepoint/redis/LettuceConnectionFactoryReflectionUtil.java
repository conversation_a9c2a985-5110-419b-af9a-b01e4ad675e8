package com.prinova.messagepoint.redis;

import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

import java.lang.reflect.Field;

/**
 * Utility class to access LettuceConnectionFactory's client property through reflection.
 * This demonstrates how to use reflection to access private fields in Java.
 */
public class LettuceConnectionFactoryReflectionUtil {

    /**
     * Assigns a variable with the value of the client property from LettuceConnectionFactory using reflection.
     * 
     * @param connectionFactory The LettuceConnectionFactory instance
     * @return The client object from the connection factory, or null if not accessible
     * @throws Exception if reflection fails
     */
    public static Object getClientPropertyValue(LettuceConnectionFactory connectionFactory) throws Exception {
        if (connectionFactory == null) {
            throw new IllegalArgumentException("LettuceConnectionFactory cannot be null");
        }

        // Get the class of the LettuceConnectionFactory
        Class<?> factoryClass = connectionFactory.getClass();
        
        // Variable to store the client property value
        Object clientPropertyValue = null;
        
        try {
            // Try to find the 'client' field in the class hierarchy
            Field clientField = findClientField(factoryClass);
            
            if (clientField != null) {
                // Make the field accessible if it's private
                clientField.setAccessible(true);
                
                // Assign the variable with the client property value
                clientPropertyValue = clientField.get(connectionFactory);
                
                // Log the successful access
                System.out.println("Successfully accessed client property via reflection");
                System.out.println("Client type: " + (clientPropertyValue != null ? clientPropertyValue.getClass().getName() : "null"));
            } else {
                System.out.println("Client field not found in LettuceConnectionFactory class hierarchy");
            }
            
        } catch (IllegalAccessException e) {
            throw new Exception("Failed to access client field: " + e.getMessage(), e);
        } catch (SecurityException e) {
            throw new Exception("Security exception when accessing client field: " + e.getMessage(), e);
        }
        
        return clientPropertyValue;
    }
    
    /**
     * Recursively searches for the 'client' field in the class hierarchy.
     * 
     * @param clazz The class to search in
     * @return The Field object if found, null otherwise
     */
    private static Field findClientField(Class<?> clazz) {
        // Search in the current class
        try {
            return clazz.getDeclaredField("client");
        } catch (NoSuchFieldException e) {
            // Field not found in current class, try parent class
            Class<?> superClass = clazz.getSuperclass();
            if (superClass != null && !superClass.equals(Object.class)) {
                return findClientField(superClass);
            }
        }
        return null;
    }
    
    /**
     * Alternative method that searches for any field containing "client" in its name.
     * This is useful when the exact field name is unknown.
     * 
     * @param connectionFactory The LettuceConnectionFactory instance
     * @return The first field containing "client" in its name, or null if not found
     * @throws Exception if reflection fails
     */
    public static Object findAndGetClientLikeField(LettuceConnectionFactory connectionFactory) throws Exception {
        if (connectionFactory == null) {
            throw new IllegalArgumentException("LettuceConnectionFactory cannot be null");
        }

        Class<?> factoryClass = connectionFactory.getClass();
        Object clientLikeFieldValue = null;
        
        // Search through all fields in the class hierarchy
        while (factoryClass != null && !factoryClass.equals(Object.class)) {
            Field[] fields = factoryClass.getDeclaredFields();
            
            for (Field field : fields) {
                String fieldName = field.getName().toLowerCase();
                if (fieldName.contains("client")) {
                    try {
                        field.setAccessible(true);
                        clientLikeFieldValue = field.get(connectionFactory);
                        
                        System.out.println("Found client-like field: " + field.getName());
                        System.out.println("Field type: " + field.getType().getName());
                        System.out.println("Field value type: " + (clientLikeFieldValue != null ? clientLikeFieldValue.getClass().getName() : "null"));
                        
                        return clientLikeFieldValue;
                    } catch (IllegalAccessException e) {
                        System.out.println("Could not access field: " + field.getName());
                    }
                }
            }
            
            factoryClass = factoryClass.getSuperclass();
        }
        
        return clientLikeFieldValue;
    }
    
    /**
     * Demonstrates the usage of reflection to access the client property.
     * This method shows how to assign a variable with the client property value.
     * 
     * @param connectionFactory The LettuceConnectionFactory instance
     */
    public static void demonstrateClientPropertyAccess(LettuceConnectionFactory connectionFactory) {
        try {
            // Assign a variable with the client property value using reflection
            Object clientValue = getClientPropertyValue(connectionFactory);
            
            if (clientValue != null) {
                System.out.println("=== Client Property Access Successful ===");
                System.out.println("Variable assigned with client property value:");
                System.out.println("  - Type: " + clientValue.getClass().getName());
                System.out.println("  - String representation: " + clientValue.toString());
                System.out.println("  - Hash code: " + clientValue.hashCode());
            } else {
                System.out.println("Client property value is null");
            }
            
        } catch (Exception e) {
            System.err.println("Failed to access client property: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
