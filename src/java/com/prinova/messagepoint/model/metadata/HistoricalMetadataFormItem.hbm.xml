<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="com.prinova.messagepoint.model.metadata.HistoricalMetadataFormItem" table="hist_metadata_form_item" >
	
        <id name="id" column="id">
         	<generator class="native"/>
        </id>
        
        <property name="guid" not-null="true" />

        <property 		name="value"					column="value" 						type="org.hibernate.type.TextType"/>
        <property 		name="auxValueData"				column="aux_value_data"				length="4000" />
        
		<set name="valueSet" table="hist_meta_form_item_value_set" cascade="all,delete-orphan">
			<key column="metadata_form_item_id" />
			<element column="string_value" length="4000"	type="string"  not-null="true"	/>
		</set>
		
		<many-to-one 	name="uploadedFile" 			column="upload_file_id" 			class="com.prinova.messagepoint.model.file.DatabaseFile" cascade="save-update"/>
        <many-to-one 	name="itemDefinition" 			column="form_item_definition_id"	class="com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition"  />
        <many-to-one 	name="metadataForm" 			column="metadata_form_id" 			class="com.prinova.messagepoint.model.metadata.HistoricalMetadataForm" />

		<property 		name="updated" />
		<property 		name="updatedBy" 				column="updated_by_id" />
		<property 		name="created" />
		<property 		name="createdBy" 				column="created_by_id" />
   	</class>

</hibernate-mapping>