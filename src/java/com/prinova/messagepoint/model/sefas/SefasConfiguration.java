package com.prinova.messagepoint.model.sefas;

import java.io.Serializable;

import com.prinova.messagepoint.model.admin.ConnectorConfiguration;
import com.prinova.messagepoint.model.admin.OutputFileType;
import com.prinova.messagepoint.model.file.CompositionFileSet;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import org.apache.commons.lang.StringUtils;

public class SefasConfiguration extends ConnectorConfiguration implements Serializable {

    private static final long serialVersionUID = 1L;

    private CompositionFileSet 		compositionFileSet;
    private String					compositionVersion = "0";

    private Integer	outputFileType			= OutputFileType.ID_PDF;
    private Boolean startsOnOddPage			= true;
    private Boolean duplexOutput			= false;
    private Boolean templateControl         = false;
    private Boolean accessibility           = false;
    private Integer linespacePosition       = 2;
    private Boolean tablePaddingInPts       = true;

    // Public default constructor
    //
    public SefasConfiguration()
    {
    }

    // Copy constructor (not public) for cloning 
    //
    protected SefasConfiguration(SefasConfiguration cloneFrom)
    {
        super(cloneFrom);

        if (cloneFrom.compositionFileSet != null) {
            this.compositionFileSet = CloneHelper.clone(cloneFrom.compositionFileSet, fs -> fs.clone(cloneFrom.getDocument()));
            this.compositionFileSet.save();
        }

        this.compositionVersion = cloneFrom.compositionVersion;

        this.outputFileType = cloneFrom.outputFileType;
        this.startsOnOddPage = cloneFrom.startsOnOddPage;
        this.duplexOutput = cloneFrom.duplexOutput;
        this.templateControl = cloneFrom.templateControl;
        this.accessibility = cloneFrom.accessibility;
        this.linespacePosition = cloneFrom.linespacePosition;
        this.tablePaddingInPts = cloneFrom.tablePaddingInPts;
    }

    @Override
    public Object clone()
    {
        return new SefasConfiguration(this);
    }

    @Override
    public <T extends ConnectorConfiguration> void copy(T copyFrom) {
        super.copy(copyFrom);

        if(copyFrom instanceof SefasConfiguration copyFromConfig){
            CompositionFileSet clonedCompositionFileSet = CloneHelper.assign(copyFromConfig.getCompositionFileSet());
            if(clonedCompositionFileSet != null){
                clonedCompositionFileSet.setDocument(getDocument());
            }
            setCompositionFileSet(clonedCompositionFileSet);
            setCompositionVersion(copyFromConfig.getCompositionVersion());
            setOutputFileType(copyFromConfig.getOutputFileType());
            setStartsOnOddPage(copyFromConfig.getStartsOnOddPage());
            setDuplexOutput(copyFromConfig.getDuplexOutput());
            setTemplateControl(copyFromConfig.getTemplateControl());
            setAccessibility(copyFromConfig.getAccessibility());
            setLinespacePosition(copyFromConfig.getLinespacePosition());
            setTablePaddingInPts(copyFromConfig.getTablePaddingInPts());
        }
    }

    public void makeHash(boolean isAlgorithmChanged) {
        String objectHashKey = getObjectHashKey();
        StringBuilder hashDataStringBuilder = new StringBuilder();

        hashDataStringBuilder.append("sefasConfiguration");
        hashDataStringBuilder.append(" connectorConfigurationHash: ").append(super.getHash());

        if(getCompositionFileSet() != null) {
            hashDataStringBuilder.append(" compositionFileSetHash: ").append(getCompositionFileSet().getSha256Hash());
        }
        if(StringUtils.isNotBlank(getCompositionVersion())){
            hashDataStringBuilder.append(" compositionVersion: ").append(getCompositionVersion());
        }
        hashDataStringBuilder.append(" outputFileType: ").append(getOutputFileType());
        hashDataStringBuilder.append(" startsOnOddPage: ").append(getStartsOnOddPage());
        hashDataStringBuilder.append(" duplexOutput: ").append(getDuplexOutput());
        hashDataStringBuilder.append(" templateControl: ").append(getTemplateControl());
        hashDataStringBuilder.append(" accessibility: ").append(getAccessibility());
        hashDataStringBuilder.append(" linespacePosition: ").append(getLinespacePosition());
        hashDataStringBuilder.append(" tablePaddingInPts: ").append(getTablePaddingInPts());

        sha256Hash = calculateSha256Hash(objectHashKey, isAlgorithmChanged, sha256Hash, hashDataStringBuilder.toString());
    }


    public CompositionFileSet getCompositionFileSet() {
        return compositionFileSet;
    }
    public void setCompositionFileSet(CompositionFileSet compositionFileSet) {
        this.compositionFileSet = compositionFileSet;
    }

    public String getCompositionVersion() {
        return compositionVersion;
    }
    public void setCompositionVersion(String compositionVersion) {
        this.compositionVersion = compositionVersion;
    }

    public Integer getOutputFileType() {
        return outputFileType;
    }
    public void setOutputFileType(Integer outputFileType) {
        this.outputFileType = outputFileType;
    }

    public Boolean getStartsOnOddPage() {
        return startsOnOddPage;
    }
    public void setStartsOnOddPage(Boolean startsOnOddPage) {
        this.startsOnOddPage = startsOnOddPage;
    }

    public Boolean getDuplexOutput() {
        return duplexOutput;
    }
    public void setDuplexOutput(Boolean duplexOutput) {
        this.duplexOutput = duplexOutput;
    }

    public Boolean getTemplateControl() { return templateControl; }
    public void setTemplateControl(Boolean templateControl) { this.templateControl = templateControl; }

    public Boolean getAccessibility() { return accessibility; }
    public void setAccessibility(Boolean accessibility) { this.accessibility = accessibility; }

    public Integer getLinespacePosition() { return linespacePosition; }
    public void setLinespacePosition(Integer linespacePosition) { this.linespacePosition = linespacePosition; }

    public Boolean getTablePaddingInPts() { return tablePaddingInPts; }
    public void setTablePaddingInPts(Boolean tablePaddingInPts) { this.tablePaddingInPts = tablePaddingInPts; }

}
