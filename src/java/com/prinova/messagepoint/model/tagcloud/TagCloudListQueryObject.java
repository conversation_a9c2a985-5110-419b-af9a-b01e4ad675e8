package com.prinova.messagepoint.model.tagcloud;

import com.prinova.messagepoint.model.wrapper.AsyncTagCloudListVO;

public class TagCloudListQueryObject {
    private long tag_cloud_id;
    private String tag_cloud_name;
    private long tag_cloud_type_id;
    private String tag_cloud_type_name;
    private String document_name;

    public TagCloudListQueryObject() {
    }

    public TagCloudListQueryObject(long tag_cloud_id, String tag_cloud_name, long tag_cloud_type_id, String tag_cloud_type_name, String document_name) {
        this.tag_cloud_id = tag_cloud_id;
        this.tag_cloud_name = tag_cloud_name;
        this.tag_cloud_type_id = tag_cloud_type_id;
        this.tag_cloud_type_name = tag_cloud_type_name;
        this.document_name = document_name;
    }

    public long getTagCloudId() {
        return tag_cloud_id;
    }

    public void setTagCloudId(long tag_cloud_id) {
        this.tag_cloud_id = tag_cloud_id;
    }

    public String getTagCloudName() {
        return tag_cloud_name;
    }

    public void setTagCloudName(String tag_cloud_name) {
        this.tag_cloud_name = tag_cloud_name;
    }

    public long getTagCloudTypeId() {
        return tag_cloud_type_id;
    }

    public void setTagCloudTypeId(long tag_cloud_type_id) {
        this.tag_cloud_type_id = tag_cloud_type_id;
    }

    public String getTagCloudTypeName() {
        return tag_cloud_type_name;
    }

    public void setTagCloudTypeName(String tag_cloud_type_name) {
        this.tag_cloud_type_name = tag_cloud_type_name;
    }

    public String getDocumentName() {
        return document_name;
    }

    public void setDocumentName(String document_name) {
        this.document_name = document_name;
    }

    public AsyncTagCloudListVO toAsyncTagCloudListVO() {
        return new AsyncTagCloudListVO(tag_cloud_id, tag_cloud_name, tag_cloud_type_id, tag_cloud_type_name, document_name);
    }
}