package com.prinova.messagepoint.connected.rest;

import com.prinova.messagepoint.connected.data.json.support.CommunicationLogData;
import com.prinova.messagepoint.connected.data.json.support.CommunicationLogType;
import com.prinova.messagepoint.connected.rest.data.*;
import com.prinova.messagepoint.connected.util.SupportingDataUtil;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.file.SandboxFile;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.communication.CreateCommunicationProofService;
import com.prinova.messagepoint.platform.ws.client.dews.api.ConnectedFileType;
import com.prinova.messagepoint.platform.ws.client.dews.api.DataRefType;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.util.*;

public class RestClientPMGR extends RestClientPMGRBase {

    public static final boolean CHECK_HASH = false;
    public RestClientPMGR() {
        super();
    }
    public RestClientPMGR(RestRequestContext context) {
        super(context);
    }

    public RestClientPMGR(String tokenId, String tokenCode) {
        super(tokenId, tokenCode);
    }
    public RestClientPMGR(Long userId, String tokenId, String tokenCode) {
        super(userId, tokenId, tokenCode);
    }

    private Long getCommuniationId() {
        Object paramValue = this.getParam(RestRequestContext.COMMUNICATION_ID);
        return  (paramValue != null && paramValue instanceof Number) ? ((Number) paramValue).longValue() : null;
    }

    public SandboxFile downloadBundleAsset(String name, String pmgrBundleId, String nodeName, String podId) throws Exception {
        String mpInstanceId = this.getMpInstanceId(nodeName, podId);
        if (mpInstanceId == null) throw new RuntimeException("downloadBundleAsset: mpInstanceId is NULL, context: (" + this.printRequestContext() + ")");

        String assetId = this.getMpInstanceBundleAssetId(mpInstanceId, pmgrBundleId, name);
        if (assetId == null) throw new RuntimeException("downloadBundleAsset: assetId is NULL, context: (" + this.printRequestContext() + ")");

        return this.downloadMpInstanceBundleAsset(mpInstanceId, pmgrBundleId, assetId, name);
    }

    public SandboxFile downloadBundleAsset(String name, long umhBundleId, String nodeName, String podId) throws Exception {
        String mpInstanceId = this.getMpInstanceId(nodeName, podId);
        if (mpInstanceId == null) throw new RuntimeException("downloadBundleAsset: mpInstanceId is NULL, context: (" + this.printRequestContext() + ")");

        String bundleId = this.getMpInstanceBundleId(mpInstanceId, umhBundleId);
        if (bundleId == null) throw new RuntimeException("downloadBundleAsset: bundleId is NULL, context: (" + this.printRequestContext() + ")");

        String assetId = this.getMpInstanceBundleAssetId(mpInstanceId, bundleId, name);
        if (assetId == null) throw new RuntimeException("downloadBundleAsset: assetId is NULL, context: (" + this.printRequestContext() + ")");

        return this.downloadMpInstanceBundleAsset(mpInstanceId, bundleId, assetId, name);
    }

    public String createConnectedOrder(String externalId, String umhBundleId, boolean isTestOrder, String nodeName, String podId) throws Exception{
        String mpInstanceId = this.getMpInstanceId(nodeName, podId);
        if (mpInstanceId == null) throw new RuntimeException("createConnectedOrder: mpInstanceId is NULL, context: (" + this.printRequestContext() + ")");

        String bundleId = this.getMpInstanceBundleId(mpInstanceId, Long.parseLong(umhBundleId));
        if (bundleId == null) throw new RuntimeException("createConnectedOrder: bundleId is NULL, context: (" + this.printRequestContext() + ")");

        JSONObject req = RestClientPMGRUtils.buildCreateConnectedOrderRequest(
                this.getCompanyId(), mpInstanceId, bundleId,
                externalId, this.getDefaultEnvironment(!isTestOrder));
        return this.createConnectedOrder(req);
    }

    public String createClonedConnectedOrder(String externalId, String bundleId, boolean isTestOrder, String nodeName, String podId) throws Exception{
        String mpInstanceId = this.getMpInstanceId(nodeName, podId);
        if (mpInstanceId == null) throw new RuntimeException("createClonedConnectedOrder: mpInstanceId is NULL, context: (" + this.printRequestContext() + ")");

        if (bundleId == null) throw new RuntimeException("createClonedConnectedOrder: bundleId is NULL, context: (" + this.printRequestContext() + ")");

        JSONObject req = RestClientPMGRUtils.buildCreateConnectedOrderRequest(
                this.getCompanyId(), mpInstanceId, bundleId,
                externalId, this.getDefaultEnvironment(!isTestOrder));
        return this.createConnectedOrder(req);
    }

    public String downloadJobResultFile(String jobUUID, ConnectedFileType fileType) throws Exception {
        String fileName = RestClientPMGRUtils.getFileName(fileType);
        if (fileName == null) throw new RuntimeException("Unknown result file type, context: (" + this.printRequestContext() + ")");

        String fileId = this.getJobResultFileId(jobUUID, fileName);
        if (fileId == null) throw new RuntimeException("File not found..., context: (" + this.printRequestContext() + ")");

        return this.getFile(fileId);
    }

    public SandboxFile downloadConnectedJobResultFile(String jobUUID) throws Exception {
        String fileId = this.getJobResultFileId(jobUUID, "*.pdf");
        if (fileId == null) throw new RuntimeException("File not found...");

        return this.downloadFile(fileId, jobUUID + "_jobresult.pdf");
    }

    public String downloadJobResultFileAllImages(String jobUUID, List<String> imageGUIDs, String imageName, int page, int pageSize, boolean isAsc, String contentValue) throws Exception {
        //TODO
        String fileId = getJobResultFileId(jobUUID, "allImages.json");
        if (fileId == null) throw new RuntimeException("File not found...");

        return this.getFile(fileId);
    }

    public boolean uploadReferenceData(String podId, String branchName, String nodeName, String externalId, String orderId,
                                       DataRefType dataRefType, File file, Map extra) throws Exception {

        boolean addSupportData =  this.getCommuniationId() != null;
        Date dataStart = new Date();
        PmgrOrder order = this.getOrder(orderId);
        if (order == null) throw new RuntimeException("Internal error - called uploadReferenceData without an order");

        String referenceFileId = order.findRefFileIdByType(dataRefType);
        PmgrFile newFile = null;

        if (referenceFileId != null) {
            //Found pre-existing  reference data file
            if (dataRefType == DataRefType.LOCAL_IMAGE_DATA_REF) {
                SandboxFile existingReferenceFile = this.downloadReferenceData(podId, branchName, nodeName, externalId, orderId, dataRefType);
                String newFileContent = RestClientPMGRUtils.processExistingImages(existingReferenceFile.getFileContent(), file);
                newFile = this.replaceFile(referenceFileId, newFileContent,
                        RestClientPMGRUtils.buildRefDataFileName(podId, branchName, nodeName, externalId, dataRefType),
                        orderId, order.getEnvironment(), false, extra);
                referenceFileId = newFile != null ? newFile.getId() : null;
            } else {
                //replace the file with id: existingFileId
                newFile = this.replaceFile(referenceFileId, file,
                        RestClientPMGRUtils.buildRefDataFileName(podId, branchName, nodeName, externalId, dataRefType),
                        orderId, order.getEnvironment(), CHECK_HASH, extra);
                referenceFileId = newFile != null ? newFile.getId() : null;
            }
            if (referenceFileId == null) throw new RuntimeException("Error when trying to replace refDataFile");
            if (addSupportData) {
                Map<String, Object> metadata = Map.of(
                        "replaced", true,
                        "id", newFile.getId(),
                        "name", newFile.getName(),
                        "currentVersionId", newFile.getCurrentVersionId(),
                        "hash", ((newFile.getCurrentVersion() != null) ? newFile.getCurrentVersion().get("hash") : null)
                );
                SupportingDataUtil.logCommunicationActionExtra(this.getCommuniationId(), metadata, dataStart, new Date());
            }
            return true;
        } else {
            if (dataRefType == DataRefType.LOCAL_IMAGE_DATA_REF) {
                String newFileContent = RestClientPMGRUtils.processLocalImageFile(file);
                if (newFileContent == null) throw new RuntimeException("Error when trying to process local images refDataFile");
                newFile = this.uploadFile(newFileContent, RestClientPMGRUtils.buildRefDataFileName(podId, branchName, nodeName, externalId, dataRefType),
                        orderId, order.getEnvironment(), false);
                referenceFileId = newFile != null ? newFile.getId() : null;
            } else {
                newFile = this.uploadFile(file, RestClientPMGRUtils.buildRefDataFileName(podId, branchName, nodeName, externalId, dataRefType),
                        orderId, order.getEnvironment(), CHECK_HASH);
                referenceFileId = newFile != null ? newFile.getId() : null;
            }
            if (referenceFileId == null) throw new RuntimeException("Error when trying to upload refDataFile");
            //update Order (I think it's not necessary if we don't upload but replace)
            if (addSupportData) {
                Map<String, Object> metadata = Map.of(
                        "replaced", false,
                        "id", newFile.getId(),
                        "name", newFile.getName(),
                        "currentVersionId", newFile.getCurrentVersionId(),
                        "hash", ((newFile.getCurrentVersion() != null) ? newFile.getCurrentVersion().get("hash") : null)
                );
                SupportingDataUtil.logCommunicationActionExtra(this.getCommuniationId(), metadata, dataStart, new Date());
            }
            return this.updateOrder(orderId, RestClientPMGRUtils.buildOrderPatchFields(order, dataRefType, referenceFileId));
        }
    }

    public SandboxFile downloadReferenceData(String podId, String branchName, String nodeName,
                                             String externalId, String orderId, DataRefType type) throws Exception {
        PmgrOrder order = this.getOrder(orderId);
        if (order != null) {
            String referenceFileId = order.findRefFileIdByType(type);
            if (referenceFileId == null) throw new RuntimeException("Error when trying to get refDataFile for this order: " + orderId);

            PmgrFile referenceFile = this.fetchFile(referenceFileId);
            if (referenceFile == null) throw new RuntimeException("Error when trying to fetch refDataFile with id: " + referenceFileId);

            SandboxFile file = this.downloadFile(referenceFileId, RestClientPMGRUtils.buildRefDataFileName(podId, branchName, nodeName, externalId, type));

            if (CHECK_HASH && referenceFile.getHash() != null) {
                if (!referenceFile.getHash().equalsIgnoreCase(RestClientPMGRUtils.getFileHash(file.getFileContent())))
                    throw new RuntimeException("Diff hash for this file: " + referenceFileId + ", name: " + file.getName());
            } else {
                //TODO
            }

            return file;
        }
        return null;
    }

    public String uploadJobBundle(File bundleFile, String orderId) throws Exception {
        PmgrOrder order = this.getOrder(orderId);
        if (order == null) throw new RuntimeException("Internal error - called uploadJobBundle without an order");

        PmgrFile uploadedBundleFile = this.uploadFile(bundleFile, bundleFile.getName(), orderId, order.getEnvironment(), CHECK_HASH);
        if (uploadedBundleFile == null || uploadedBundleFile.getId() == null) throw new RuntimeException("Error when trying to upload job bundleFile");

        if (order != null) {
            if (!this.updateOrder(orderId, Map.of("statusCode", 1))) {
                throw new RuntimeException("Error when trying to update the order.");
            }
        }
        return uploadedBundleFile.getId();
    }

    private String uploadDummyCommRefDataFile(PmgrOrder order) throws Exception {
        PmgrFile uploadedDummyFile = this.uploadFile(order.getExternalId(), "CommRefData.dat",
                order.getId(), order.getEnvironment(), false);
        return uploadedDummyFile != null ? uploadedDummyFile.getId() : null;
    }

    @Deprecated
    public String processJobBundleAsync(String nodeName, String podId,
                                        String touchpointGUID, String touchpointName,
                                        String environment, String orderId, String bundleId,
                                        long umhJobId, String umhJobType,
                                        boolean isPreProof, String connectorType, String refFileNameTemplate) throws Exception{
        String mpInstanceId = this.getMpInstanceId(nodeName, podId);
        if (mpInstanceId == null) throw new RuntimeException("mpInstanceId is NULL");

        PmgrApplication app = this.getApplicationId(touchpointGUID);
        if (app == null) throw new RuntimeException("No application matches touchpoint " + touchpointGUID);

        String appVersionId = app.getDefaultVersionId(environment);
        if (appVersionId == null) throw new
                RuntimeException("No application version found in environment " + environment +
                " for Messagepoint Instance " + mpInstanceId + ", touchpoint " + touchpointGUID);

        PmgrApplicationVersion appVersion = this.getApplicationVersion(app.getId(), appVersionId);
        if (appVersion == null) throw new
                RuntimeException("No application version found in environment " + environment +
                " for Messagepoint Instance " + mpInstanceId + ", touchpoint " + touchpointGUID);

        PmgrOrder order = this.getOrder(orderId);
        if (order == null) throw new RuntimeException("No connected order matches Id " + orderId);


        List<String> driverFileIds = new ArrayList<>();
        String driverFileId = order.findRefFileIdByType(DataRefType.PRIMARY_DATA_REF);
        if (driverFileId != null) {
            driverFileIds.add(driverFileId);
        }

        // 1: Connected bundle
        List<String> referenceFileIds = new ArrayList<>(Collections.singletonList(bundleId));
        JSONArray referenceFileMapping = new JSONArray();
        Map<String, Integer> deReferenceFileMapping = new HashMap<>();
        // if we override refFile => we should build a map like this (param & index in referenceFileIds):
        //        "referenceFileMapping": {
        //            "mp_ref_02": 1,
        //            "mp_ref_03": 2,
        //        }

        // 2: Connected reference files (from dataResourceBundleDataRef)
        String dataResourceBundleFileId = order.findRefFileIdByType(DataRefType.DATA_RESOURCE_BUNDLE_DATA_REF);
        if (dataResourceBundleFileId != null) {
            SandboxFile resourcesFile = this.downloadFile(dataResourceBundleFileId, "dataResourceBundleFile.zip");
            if (resourcesFile != null) {
                Map<String, String> referenceFiles = RestClientPMGRUtils.parseDataResourceBundleFile(resourcesFile);
                String driverFileName = referenceFiles.get("driver_file");
                if (driverFileName != null) {
                    referenceFiles.remove("driver_file");
                    //upload driver_file
                    String driverFileContent = RestClientPMGRUtils.getFileContentFromResourceBundleFile(resourcesFile, driverFileName);
                    if (driverFileContent == null) throw new RuntimeException("Error when trying to get the driver file form the resourceDundleFile!");

                    PmgrFile driverFile = uploadFile(driverFileContent, driverFileName, orderId, environment, CHECK_HASH);
                    driverFileId = driverFile != null ? driverFile.getId() : null;
                    if (driverFileId == null) throw new RuntimeException("Error when trying to upload the driver file!");

                    driverFileIds.add(0, driverFileId);
                }
                for (Map.Entry<String, String> referenceFile : referenceFiles.entrySet()) {
                    String param = referenceFile.getKey();
                    String fileName = referenceFile.getValue();

                    String referenceFileContent = RestClientPMGRUtils.getFileContentFromResourceBundleFile(resourcesFile, fileName);
                    if (referenceFileContent == null) throw new RuntimeException("Error when trying to get the driver file form the resourceDundleFile!");

                    PmgrFile uploadedReferenceFile = uploadFile(referenceFileContent, fileName, orderId, environment, CHECK_HASH);
                    String referenceFileId = uploadedReferenceFile != null ? uploadedReferenceFile.getId() : null;
                    if (referenceFileId == null) throw new RuntimeException("Error when trying to upload a reference file!");

                    // Offset all indexes by the number of reference files already present in `referenceFileIds`
                    referenceFileIds.add(referenceFileId);
                    deReferenceFileMapping.put(param, referenceFileIds.indexOf(referenceFileId));
                }
            }
        }
        String commRefDataFileId = this.uploadDummyCommRefDataFile(order);
        if (commRefDataFileId != null) {
            referenceFileIds.add(commRefDataFileId);
            referenceFileMapping.put(
                    new JSONObject()
                            .put("name", "CommRefData.dat")
                            .put("index", referenceFileIds.indexOf(commRefDataFileId)));
        }

        // 1: localImageDataRef
        // 2: order's attachment files
        List<String> attachmentFileIds = order.getAttachmentFileIds();

        // Exclude primaryDataRef, localImageDataRef and dataResourceBundleDataRef as they
        // (or their contents) are passed through other means.

        for (DataRefType dataRefType : DataRefType.values()) {
            switch (dataRefType) {
                case PRIMARY_DATA_REF:
                case DATA_RESOURCE_BUNDLE_DATA_REF:
                    break;
                case LOCAL_IMAGE_DATA_REF: {
                    String refFileId = order.findRefFileIdByType(dataRefType);
                    if (refFileId != null) {
                        attachmentFileIds.add(0, refFileId);
                    }
                }
                    break;
                default: {
                    String refFileId = order.findRefFileIdByType(dataRefType);
                    String refFileName = refFileNameTemplate.replace("{index}",
                            String.valueOf(dataRefType.ordinal()));
                    if (refFileId != null) {
                        referenceFileIds.add(refFileId);
                        referenceFileMapping.put(new JSONObject()
                                .put("index", referenceFileIds.indexOf(refFileId))
                                .put("name", refFileName));
                    }
                }
            }
        }

        String jobId = this.createJob(RestClientPMGRUtils.buildCreateJobRequest(this.getCompanyId(),
                order, driverFileIds, referenceFileIds, referenceFileMapping, attachmentFileIds,
                app.getId(), appVersion, deReferenceFileMapping,
                umhJobId, umhJobType, isPreProof, connectorType, refFileNameTemplate
        ));
        if (jobId == null) throw new RuntimeException("jobId is NULL (umhJobId=" + umhJobType + ", umhJobType=" + umhJobType + ")!");

//        0 = "STATUS_INITIALIZED";
//        1 = "STATUS_PROCESSING";
//        2 = "STATUS_COMPLETED";
//        3 = "STATUS_FAILED";
//        4 = "STATUS_ABORTED";
        int attempt = 0;
        PmgrJob job = this.getJob(jobId);
        boolean processed = job != null && job.getStatusCode() >= 2;
        attempt = 0;

        /*
        This is a temporary solution to check if the job has been completed.
        A new feature should probably be added to the PMGR API to send events to clients.
         */
        while (attempt < 100 && !processed) {
            if (!processed) {
                attempt++;
                Thread.sleep(attempt < 30 ? 1000 : 2000);
                job = this.getJob(jobId);
                processed = job != null && job.getStatusCode() >= 2;
            }
        }
        this.setMpInstanceCallback(mpInstanceId, touchpointGUID, touchpointName, umhJobId, umhJobType, (job.getStatusCode() == 2 ? 0 : 1),  job.getStatusMessage());

        return jobId;
    }


    public String processJobBundleSync(String nodeName, String podId,
                                        String touchpointGUID, String touchpointName,
                                        String orderId, String bundleId,
                                        long umhJobId, String umhJobType,
                                        boolean isPreProof, String connectorType, String refFileNameTemplate) throws Exception{
        String mpInstanceId = this.getMpInstanceId(nodeName, podId);
        if (mpInstanceId == null) throw new RuntimeException("mpInstanceId is NULL");

        PmgrApplication app = this.getApplicationId(touchpointGUID);
        if (app == null) throw new RuntimeException("No application matches touchpoint " + touchpointGUID);

        PmgrOrder order = this.getOrder(orderId);
        if (order == null) throw new RuntimeException("No connected order matches Id " + orderId);

        String appVersionId = app.getDefaultVersionId(order.getEnvironment());
        if (appVersionId == null) throw new
                RuntimeException("No application version found in environment " + order.getEnvironment() +
                " for Messagepoint Instance " + mpInstanceId + ", touchpoint " + touchpointGUID);

        PmgrApplicationVersion appVersion = this.getApplicationVersion(app.getId(), appVersionId);
        if (appVersion == null) throw new
                RuntimeException("No application version found in environment " + order.getEnvironment() +
                " for Messagepoint Instance " + mpInstanceId + ", touchpoint " + touchpointGUID);

        List<String> driverFileIds = new ArrayList<>();
        String driverFileId = order.findRefFileIdByType(DataRefType.PRIMARY_DATA_REF);
        if (driverFileId != null) {
            driverFileIds.add(driverFileId);
        }

        // 1: Connected bundle
        List<String> referenceFileIds = new ArrayList<>(Collections.singletonList(bundleId));
        JSONArray referenceFileMapping = new JSONArray();
        Map<String, Integer> deReferenceFileMapping = new HashMap<>();
        // if we override refFile => we should build a map like this (param & index in referenceFileIds):
        //        "referenceFileMapping": {
        //            "mp_ref_02": 1,
        //            "mp_ref_03": 2,
        //        }

        // 2: Connected reference files (from dataResourceBundleDataRef)
        String dataResourceBundleFileId = order.findRefFileIdByType(DataRefType.DATA_RESOURCE_BUNDLE_DATA_REF);
        if (dataResourceBundleFileId != null) {
            SandboxFile resourcesFile = this.downloadFile(dataResourceBundleFileId, "dataResourceBundleFile.zip");
            if (resourcesFile != null) {
                Map<String, String> referenceFiles = RestClientPMGRUtils.parseDataResourceBundleFile(resourcesFile);
                String driverFileName = referenceFiles.get("driver_file");
                if (driverFileName != null) {
                    referenceFiles.remove("driver_file");
                    //upload driver_file
                    String driverFileContent = RestClientPMGRUtils.getFileContentFromResourceBundleFile(resourcesFile, driverFileName);
                    if (driverFileContent == null) throw new RuntimeException("Error when trying to get the driver file form the resourceDundleFile!");

                    PmgrFile driverFile = uploadFile(driverFileContent, driverFileName, orderId, order.getEnvironment(), CHECK_HASH);
                    driverFileId = driverFile != null ? driverFile.getId() : null;
                    if (driverFileId == null) throw new RuntimeException("Error when trying to upload the driver file!");

                    driverFileIds.add(0, driverFileId);
                }
                for (Map.Entry<String, String> referenceFile : referenceFiles.entrySet()) {
                    String param = referenceFile.getKey();
                    String fileName = referenceFile.getValue();

                    String referenceFileContent = RestClientPMGRUtils.getFileContentFromResourceBundleFile(resourcesFile, fileName);
                    if (referenceFileContent == null) throw new RuntimeException("Error when trying to get the driver file form the resourceDundleFile!");

                    PmgrFile uploadedReferenceFile = uploadFile(referenceFileContent, fileName, orderId, order.getEnvironment(), CHECK_HASH);
                    String referenceFileId = uploadedReferenceFile != null ? uploadedReferenceFile.getId() : null;
                    if (referenceFileId == null) throw new RuntimeException("Error when trying to upload a reference file!");

                    // Offset all indexes by the number of reference files already present in `referenceFileIds`
                    referenceFileIds.add(referenceFileId);
                    deReferenceFileMapping.put(param, referenceFileIds.indexOf(referenceFileId));
                }
            }
        }
        String commRefDataFileId = this.uploadDummyCommRefDataFile(order);
        if (commRefDataFileId != null) {
            referenceFileIds.add(commRefDataFileId);
            referenceFileMapping.put(
                    new JSONObject()
                            .put("name", "CommRefData.dat")
                            .put("index", referenceFileIds.indexOf(commRefDataFileId)));
        }

        // 1: localImageDataRef
        // 2: order's attachment files
        List<String> attachmentFileIds = order.getAttachmentFileIds();

        // Exclude primaryDataRef, localImageDataRef and dataResourceBundleDataRef as they
        // (or their contents) are passed through other means.

        for (DataRefType dataRefType : DataRefType.values()) {
            switch (dataRefType) {
                case PRIMARY_DATA_REF:
                case DATA_RESOURCE_BUNDLE_DATA_REF:
                    break;
                case LOCAL_IMAGE_DATA_REF: {
                    String refFileId = order.findRefFileIdByType(dataRefType);
                    if (refFileId != null) {
                        attachmentFileIds.add(0, refFileId);
                    }
                }
                break;
                default: {
                    String refFileId = order.findRefFileIdByType(dataRefType);
                    String refFileName = refFileNameTemplate.replace("{index}",
                            String.valueOf(dataRefType.ordinal()));
                    if (refFileId != null) {
                        referenceFileIds.add(refFileId);
                        referenceFileMapping.put(new JSONObject()
                                .put("index", referenceFileIds.indexOf(refFileId))
                                .put("name", refFileName));
                    }
                }
            }
        }

        PmgrJob syncJob = this.createSyncJob(RestClientPMGRUtils.buildCreateSyncJobRequest(this.getCompanyId(),
                order, driverFileIds, referenceFileIds, referenceFileMapping, attachmentFileIds,
                app.getId(), appVersion, deReferenceFileMapping,
                umhJobId, umhJobType, isPreProof, connectorType, refFileNameTemplate
        ));
        if (syncJob == null) throw new RuntimeException("job is NULL (umhJobId=" + umhJobType + ", umhJobType=" + umhJobType + ")!");

        this.setMpInstanceCallback(mpInstanceId, touchpointGUID, touchpointName, umhJobId, umhJobType, (syncJob.getStatusCode() == 2 ? 0 : 1),  syncJob.getStatusMessage());
//        0 = "STATUS_INITIALIZED";
//        1 = "STATUS_PROCESSING";
//        2 = "STATUS_COMPLETED";
//        3 = "STATUS_FAILED";
//        4 = "STATUS_ABORTED";

        return syncJob.getId();
    }


    public List<PmgrFileVersion> getContentVersionByOrder(String orderId) throws Exception {
        PmgrOrder order = this.getOrder(orderId);
        if (order == null) throw new RuntimeException("No connected order matches Id " + orderId);

        return  this.getContentVersionByOrder(order);
    }

    public List<PmgrFileVersion> getContentVersionByOrder(PmgrOrder order) throws Exception {
        if (order == null) throw new RuntimeException("order is NULL");

        String referenceFileId = order.findRefFileIdByType(DataRefType.TRANSIENT_ZONE_CONTENT_REF);
        if (referenceFileId == null)
           return new ArrayList<>();

        return this.searchFileVersions(referenceFileId);
    }

    public List<PmgrFile> getProofResultIdsByOrder(String orderId) throws Exception {
        PmgrOrder order = this.getOrder(orderId);
        if (order == null) throw new RuntimeException("No connected order matches Id " + orderId);

        return this.getProofResultIdsByOrder(order);
    }

    public List<PmgrFile> getProofResultIdsByOrder(PmgrOrder order) throws Exception {
        if (order == null) throw new RuntimeException("order is NULL");

        List<PmgrJob> jobs = this.searchJobs(order.getId(), order.getEnvironment(), null);
        List<PmgrFile> proofs = new ArrayList<>();

        if (jobs != null) {
            for (PmgrJob job: jobs) {
                String fileId = this.getJobResultFileId(job.getId(), "*.*");
                if (fileId != null) {
                    PmgrFile file = this.fetchFile(fileId);
                    if (file != null) {
                        proofs.add(file);
                    }
                }
            }
        }

        return proofs;
    }

    public boolean cloneRefDataFile(Communication communication, Communication clonedCommunication, DataRefType dataRefType) throws Exception {
        SandboxFile refDataFile = this.downloadReferenceData(this.getPodId(), this.getBranchName(), this.getNodeName(),
                communication.getGuid(), communication.getPmgrOrderUUID(), dataRefType);

        if (refDataFile != null) {

            File clonedFile = null;
            try {
                clonedFile = new File(FileUtil.appendFilename(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.Job.KEY_Working),
                        clonedCommunication.getGuid() + "_" + dataRefType+ "_transientDataRef.txt"));
                FileUtil.createDirectoryIfNeeded(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.Job.KEY_Working));
                if (clonedFile.exists())
                    clonedFile.delete();

                BufferedOutputStream out = new BufferedOutputStream(new FileOutputStream(clonedFile));
                out.write(refDataFile.getFileContent());
                out.flush();
                out.close();

                return this.uploadReferenceData(this.getPodId(), this.getBranchName(), this.getNodeName(),
                        clonedCommunication.getGuid(), clonedCommunication.getPmgrOrderUUID(),
                        dataRefType, clonedFile, null);

            } finally {
                if (clonedFile != null && clonedFile.exists())
                    clonedFile.delete();
            }
        }

        return false;
    }

    public <T> boolean cloneConnectedOrder(Communication communication, Communication clonedCommunication, boolean cloneInterview, boolean cloneInteractive) throws Exception {
        boolean result = false;
        //1. create connected order
        List<CommunicationLogData> supportingDataActionList = new ArrayList<>();
        supportingDataActionList.add(new CommunicationLogData(CommunicationLogType.CREATE, clonedCommunication.getId(), new Date()));

        PmgrOrder order = this.getOrder(communication.getPmgrOrderUUID());
        if (order == null) throw new RuntimeException("cloneConnectedOrder error: No connected order matches Id " + communication.getPmgrOrderUUID());
        String bundleId = RestClientPMGRUtils.extractBundleId(order.getMetadata());
        if (bundleId == null) throw new RuntimeException("cloneConnectedOrder error: Unable to find bundleId for the original order " + communication.getPmgrOrderUUID());
        String clonedOrderId = this.createClonedConnectedOrder(clonedCommunication.getGuid(), bundleId, clonedCommunication.isTestOrder(),
                this.getNodeName(), this.getPodId());
        if (clonedOrderId != null) {
            clonedCommunication.setPmgrOrderUUID(clonedOrderId);
            if (cloneInterview) {
                Date ds = new Date();
                result = this.cloneRefDataFile(communication, clonedCommunication, DataRefType.CONNECTED_BETA_TRANSIENT_DATA_REF);
                supportingDataActionList.add(new CommunicationLogData(CommunicationLogType.SEND_INTERVIEW_DATA, clonedCommunication.getId(), ds, new Date()));
            }
            if (cloneInteractive) {
                if (order.findRefFileIdByType(DataRefType.TRANSIENT_ZONE_CONTENT_REF) != null) {
                    Date ds = new Date();
                    result = this.cloneRefDataFile(communication, clonedCommunication, DataRefType.TRANSIENT_ZONE_CONTENT_REF);
                    supportingDataActionList.add(new CommunicationLogData(CommunicationLogType.SEND_DATA_RESOURCES_FILES, clonedCommunication.getId(), ds, new Date()));
                }
                if (order.findRefFileIdByType(DataRefType.LOCAL_IMAGE_DATA_REF) != null) {
                    Date ds = new Date();
                    this.cloneRefDataFile(communication, clonedCommunication, DataRefType.LOCAL_IMAGE_DATA_REF);
                    supportingDataActionList.add(new CommunicationLogData(CommunicationLogType.SEND_DATA_RESOURCES_FILES, clonedCommunication.getId(), ds, new Date()));

                }
                if (order.findRefFileIdByType(DataRefType.DATA_RESOURCE_BUNDLE_DATA_REF) != null) {
                    Date ds = new Date();
                    this.cloneRefDataFile(communication, clonedCommunication, DataRefType.DATA_RESOURCE_BUNDLE_DATA_REF);
                    supportingDataActionList.add(new CommunicationLogData(CommunicationLogType.SEND_DATA_RESOURCES_FILES, clonedCommunication.getId(), ds, new Date()));
                }
                // submit pre-proof job
                if (result) {
                    Date ds = new Date();
                    SupportingDataUtil.updateInteractiveInitialized(clonedCommunication, true);
                    SupportingDataUtil.logCommunicationActionList(clonedCommunication, supportingDataActionList);

                    ServiceExecutionContext preProofContext = CreateCommunicationProofService.createContextForPreProof(clonedCommunication.getCustomerIdentifierDataValue(), clonedCommunication, clonedCommunication.getDocument(), UserUtil.getPrincipalUser(), "Cloning Order: Pre-Proof");
                    Service preProofService = MessagepointServiceFactory.getInstance().lookupService(CreateCommunicationProofService.SERVICE_NAME, CreateCommunicationProofService.class);
                    preProofService.execute(preProofContext);
                    if ( !preProofContext.getResponse().isSuccessful() ) {
                        throw new Exception("Unexpected exception when invoking CreateCommunicationProofService execute method!");
                    } else {
                        long proofId = (long) preProofContext.getResponse().getResultValueBean();
                        SupportingDataUtil.logCommPreProofCreated(clonedCommunication, proofId, ds, new Date());
                    }
                }
            }
        }
        return result;
    }

    public String getMpInstanceBundleId(String touchpointGUID, String environment) throws Exception {
        PmgrApplication app = this.getApplicationId(touchpointGUID);
        if (app == null) throw new RuntimeException("No application matches touchpoint " + touchpointGUID);

        String appVersionId = app.getDefaultVersionId(environment);
        if (appVersionId == null) throw new
                RuntimeException("No application version found in environment " + environment +
                " for touchpoint " + touchpointGUID);

        PmgrApplicationVersion appVersion = this.getApplicationVersion(app.getId(), appVersionId);
        if (appVersion == null) throw new
                RuntimeException("No application version found in environment " + environment +
                " for touchpoint " + touchpointGUID);

        Map<String, Object> appVersionSettings = appVersion.getSettings();
        if (appVersionSettings != null) {
            List<String> stepIds = RestClientPMGRUtils.getStepIds(appVersionSettings, "get-bundle");
            if (stepIds != null && !stepIds.isEmpty()) {

            }
        }

        return null;
    }
}
