package com.prinova.messagepoint.model.admin;

import java.util.List;

import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import org.springframework.orm.hibernate5.SessionHolder;

import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.util.HibernateUtil;

public class SecuritySettings extends IdentifiableMessagePointModel{
	private static final long serialVersionUID = -6305602172023858499L;
	
	private String 		minLength;
	private String 		maxLength;
	private boolean		requiresUppercase;
	private boolean		requiresLowercase;
	private boolean		requiresNumeral;
	private boolean		requiresSymbol;
	private boolean		norepeats;
	private boolean 	trackFlag;
	private String 		maxAttempts;
	private boolean 	alphanumericOnly;
	private String 		usernameMinLength;
	private String		usernameMaxLength;
	private String 		pwResetKeepAlive;
	private boolean 	pwExpires;
	private int 		pwExpireDays;
	private boolean 	preventRepeatedPw;
	private int 		pwHistoryEntries;
	private boolean		pwLimitReusePeriod = false;
	private int			pwLimitMonths = 12;
	private String		sessionExpireMins;
	private boolean		softDeactivationEnabled = false;
	private int			softDeactivationLimitDays = 30;
	private boolean		hardDeactivationEnabled = false;
	private int			hardDeactivationLimitDays = 90;
	
	
	public boolean isTrackFlag() {
		return trackFlag;
	}
	public void setTrackFlag(boolean trackFlag) {
		this.trackFlag = trackFlag;
	}
	public boolean isRequiresUppercase() {
		return requiresUppercase;
	}
	public void setRequiresUppercase(boolean requiresUppercase) {
		this.requiresUppercase = requiresUppercase;
	}
	public boolean isRequiresLowercase() {
		return requiresLowercase;
	}
	public void setRequiresLowercase(boolean requiresLowercase) {
		this.requiresLowercase = requiresLowercase;
	}
	public boolean isRequiresNumeral() {
		return requiresNumeral;
	}
	public void setRequiresNumeral(boolean requiresNumeral) {
		this.requiresNumeral = requiresNumeral;
	}
	public boolean isRequiresSymbol() {
		return requiresSymbol;
	}
	public void setRequiresSymbol(boolean requiresSymbol) {
		this.requiresSymbol = requiresSymbol;
	}
	public boolean isNorepeats() {
		return norepeats;
	}
	public void setNorepeats(boolean norepeats) {
		this.norepeats = norepeats;
	}
	public boolean isAlphanumericOnly() {
		return alphanumericOnly;
	}
	public void setAlphanumericOnly(boolean alphanumericOnly) {
		this.alphanumericOnly = alphanumericOnly;
	}
	public String getMinLength() {
		return minLength;
	}
	public void setMinLength(String minLength) {
		this.minLength = minLength;
	}
	public String getMaxLength() {
		return maxLength;
	}
	public void setMaxLength(String maxLength) {
		this.maxLength = maxLength;
	}
	public String getMaxAttempts() {
		return maxAttempts;
	}
	public void setMaxAttempts(String maxAttempts) {
		this.maxAttempts = maxAttempts;
	}
	public String getUsernameMinLength() {
		return usernameMinLength;
	}
	public void setUsernameMinLength(String usernameMinLength) {
		this.usernameMinLength = usernameMinLength;
	}
	public String getUsernameMaxLength() {
		return usernameMaxLength;
	}
	public void setUsernameMaxLength(String usernameMaxLength) {
		this.usernameMaxLength = usernameMaxLength;
	}
	
	public static SecuritySettings findById(long id) {
		return HibernateUtil.getManager().getObject(SecuritySettings.class, id);
	}
	public static List<SecuritySettings> findAll() {
		return HibernateUtil.getManager().getObjects(SecuritySettings.class);
	}

	public static SecuritySettings findForCurrentDomain() {
		SecuritySettings result = null;
		SessionHolder mainSessionHolder = null;
		try {
			mainSessionHolder = HibernateUtil.getManager().openTemporarySession(Node.getCurrentBranch().getDcsSchemaName());

			List<SecuritySettings> securitySettingsList = HibernateUtil.getManager().getObjects(SecuritySettings.class, MessagepointOrder.asc("id"));
			if (securitySettingsList != null && !securitySettingsList.isEmpty()) {
				result = securitySettingsList.get(0);
			}
		} finally {
			if (mainSessionHolder != null)
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}
		return result;
	}

	public static SecuritySettings findForSchema(String schemaName) {
		SecuritySettings result = null;
		SessionHolder mainSessionHolder = null;
		try {
			mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schemaName);

			List<SecuritySettings> securitySettingsList = HibernateUtil.getManager().getObjects(SecuritySettings.class, MessagepointOrder.asc("id"));
			if (securitySettingsList != null && !securitySettingsList.isEmpty()) {
				result = securitySettingsList.get(0);
			}
		} finally {
			if (mainSessionHolder != null)
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}
		return result;
	}

	public void setPwResetKeepAlive(String pwResetKeepAlive) {
		this.pwResetKeepAlive = pwResetKeepAlive;
	}
	public String getPwResetKeepAlive() {
		return pwResetKeepAlive;
	}
	public boolean isPwExpires() {
		return pwExpires;
	}
	public void setPwExpires(boolean pwExpires) {
		this.pwExpires = pwExpires;
	}
	public int getPwExpireDays() {
		return pwExpireDays;
	}
	public void setPwExpireDays(int pwExpireDays) {
		this.pwExpireDays = pwExpireDays;
	}
	public boolean isPreventRepeatedPw() {
		return preventRepeatedPw;
	}
	public void setPreventRepeatedPw(boolean preventRepeatedPw) {
		this.preventRepeatedPw = preventRepeatedPw;
	}
	public int getPwHistoryEntries() {
		return pwHistoryEntries;
	}
	public void setPwHistoryEntries(int pwHistoryEntries) {
		this.pwHistoryEntries = pwHistoryEntries;
	}
	public boolean isPwLimitReusePeriod() {
		return pwLimitReusePeriod;
	}
	public void setPwLimitReusePeriod(boolean pwLimitReusePeriod) {
		this.pwLimitReusePeriod = pwLimitReusePeriod;
	}
	public int getPwLimitMonths() {
		return pwLimitMonths;
	}
	public void setPwLimitMonths(int pwLimitMonths) {
		this.pwLimitMonths = pwLimitMonths;
	}
	public String getSessionExpireMins() {
		return sessionExpireMins;
	}
	public void setSessionExpireMins(String sessionExpireMins) {
		this.sessionExpireMins = sessionExpireMins;
	}
	public boolean isSoftDeactivationEnabled() {
		return softDeactivationEnabled;
	}
	public void setSoftDeactivationEnabled(boolean softDeactivationEnabled) {
		this.softDeactivationEnabled = softDeactivationEnabled;
	}
	public int getSoftDeactivationLimitDays() {
		return softDeactivationLimitDays;
	}
	public void setSoftDeactivationLimitDays(int softDeactivationLimitDays) {
		this.softDeactivationLimitDays = softDeactivationLimitDays;
	}
	public boolean isHardDeactivationEnabled() {
		return hardDeactivationEnabled;
	}
	public void setHardDeactivationEnabled(boolean hardDeactivationEnabled) {
		this.hardDeactivationEnabled = hardDeactivationEnabled;
	}
	public int getHardDeactivationLimitDays() {
		return hardDeactivationLimitDays;
	}
	public void setHardDeactivationLimitDays(int hardDeactivationLimitDays) {
		this.hardDeactivationLimitDays = hardDeactivationLimitDays;
	}
}