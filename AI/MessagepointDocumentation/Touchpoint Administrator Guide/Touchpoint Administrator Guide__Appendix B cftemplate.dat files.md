




# Appendix B: cftemplate.dat files

If you are using OpenText Exstream, Quadient Inspire, or Messagepoint Composer Sefas with Template Controlled set to No, you need to create and upload a cftemplate.dat file as part of your composition package. This appendix provides both OpenText Exstream and Quadient Inspire examples of the cftemplate.dat file.

  * For information on the Messagepoint Composer/Sefas cftemplate.dat file creation, see the “Messagepoint Composer cftemplate.dat Specifications” guide.




You can either create the cftemplate.dat file from a generic template and add your application-specific switches or build on your existing control file. If you are building on your existing control file, you must ensure that you have added the mandatory switches and filemaps required for Messagepoint’s cftemplate.dat file. Any extra filemaps that exist in your control file are ignored by Messagepoint.

Note: The aliases used for the sample files in this chapter reflect the production data source filenames in our starter application. They will need to reflect those in your application.
﻿




## Parameters used

There are key parameters that are used when creating your cftemplate.dat file. OpenText Exstream and Quadient inspire have their own set of required parameters.

Note: When you add the parameters to your file, you need to surround them with %, for example _%MP_JOBREPOSITORY%_
﻿




## OpenText Exstream parameters

The following parameters are for OpenText Exstream:

Parameter |  Definition  
---|---  
MP_PACKAGEFILE |  The full path of the package (.pub) file  
MP_MESSAGEFILE |  The name of the file to which Exstream write logs. Currently hard coded to "message.dat"  
MP_MSGCONTENT |  Used for creating the MessageContent,dat composition file.  
MP_MSGQUALIFICATION |  Used for creating the MessageQualification.dat composition file.  
MP_MSGDELIVERY |  Used for creating the MessageDelivery.dat file.  
MP_MSGVARIABLES |  Used for creating the MsgVariables.dat composition file.  
MP_DRIVERFILE |  Driver file used in the job.  
MP_JOBNAMELOWER |  Name of the output file.  
MP_JOBREPOSITORY |  Job Repository folder.  
MP_CURRENTFOLDER |  Full path to the job working folder.  
MP_CUSTSTART |  Number of starting recipient.  
MP_CUSTEND |  Number of the last recipient.  
MP_CUSTRUNEVERY |  Allows to process very Nth recipient. Typically this is set to 1  
MP_CUSTOMVAR |  Allows the use of a custom template variable to the qualification engine.
﻿




## Quadient Inspire parameters

The following parameters are for Quadient Inspire:

Parameter |  Definition  
---|---  
MP_PACKAGEFILENAME |  The filename of the .wfd, and removes the path  
MP_DRIVERFILE |  Driver file used in the job.  
MP_JOBNAME |  Used to name the resulting PDF file. Usually something like "job100_test".  
MP_CUSTSTART0 |  Number of starting recipient  
MP_CUSTEND0 |  Number of the last recipient.  
MP_JOBREPOSITORY |  Job repository folder.  
MP_CURRENTFOLDER |  Full path to the job working folder.
﻿




## OpenText Exstream cftemplate.dat file

Below is a sample of a basic cftemplate.dat file for OpenText Exstream. Depending on your pub file, there may be some changes in naming and there may be additional parameters or file mapping.

  * Messagepoint cftemplate.dat generic template for Exstream apps running in Messagepoint.com.

  * Mandatory engine switches




-KEYFILE=/Messagepoint/licence/PRINOVA-V8-V86-V9.ekf

-PACKAGEFILE=%MP_PACKAGEFILE%

-MESSAGEFILE=%MP_MESSAGEFILE%

-START=%MP_CUSTSTART%

-END=%MP_CUSTEND%

-NTH=%MP_CUSTRUNEVERY%

-STOPERROR=SEVERE

-RUNMODE=PRODUCTION

-TRACKIN=DISABLE

-TRACKOUT=NONE

  * Include if you are using PUB_Watermark to trigger watermark text display on all MPDC-generated output




-VARSET=PUB_Watermark,Yes

  * Include if you are adding a custom template variable to the qualifica _tion engine. If you are using this parameter, add the -customtemplatevar param- enter when running the qualification engine to replace MP_CUSTOMVAR cftem- plate variable with its value._




*-XPTO=%MP_CUSTOMVAR% 

  * Mandatory filemap switches




-FILEMAP=MsgContent.dat,%MP_MSGCONTENT% 

-FILEMAP=MsgQualification.dat,%MP_MSGQUALIFICATION% 

-FILEMAP=MsgDelivery.dat,%MP_MSGDELIVERY% 

-FILEMAP=MsgVariables.dat,%MP_MSGVARIABLES% 

-FILEMAP=DRIVER,%MP_DRIVERFILE% 

  * If you have additional files, you must upload them in the composition package, and update the cftemplate.dat file with their nam _e. See an example of the syntax below for a file with a production alias of DD:ADDITION- ALFILE and the uploaded file name of AdditionalFile.txt_




*-FILEMAP=AdditionalFile,%MP_JOBREPOSITORY%AdditionalFile.txt 

  * In order for the PDF Backer Exstream applications to work effectively you will need to add the syntax below. JOBREPOSITORY will point to the working folder and ‘dlgFreeFormBackers.dat’ is a hard coded filename that we are generating during the DE process. 




*-FILEMAP= DD:PDFBACKER,%MP_JOBREPOSITORY%dlgFreeFormBackers.dat

  * Output switches -- use one of the following:



  * PUB packaged to produce output type "PDF"




*-OUTPUTFILE=%MP_JOBNAMELOWER%.pdf 

  * PUB packaged to use PDF output queue named by alias -- name your queue DD:PDF or substitute name here




*-FILEMAP=DD:PDF,%MP_JOBNAMELOWER%.pdf 

  * PUB packaged to use Exstream Parameter for naming -- name your parameter MPOutputName or substitute name here




*-VARSET=MPOutputName,%MP_JOBNAMELOWER%.pdf 
﻿




## Quadient Inspire cftemplate.dat file

Below is a basic cftemplate.dat file for Quadient Inspire. Depending on your setup, however, changes may be required.

For the Quadient Control file there are the following parameters:

-e is the selected engine (in this case, PDF).

-f is the output file name.

-o is the selected output object.

-dif* are the input files. 

-rfpsRecipientRange indicates the starting and ending customers. 

-pug3 Enables the print group (in this case, disabled with “false”?).

-c specifies the path of the job file.

Using the parameters, we get the following for the cftemplate.dat file:

_Note_ : When creating the cftemplate.dat file, do not enter carriage returns after each parameter. We added carriage returns in the sample to better show the individual parameters;

"%MP_PACKAGEFILENAME%" -e PDF -f %MP_JOBNAME%.pdf

-difDriverFile "%MP_DRIVERFILE%"

-o MP_Output

-difMP_Content MessageContent.dat

-difMP_Variables MsgVariables.dat

-difMP_Qualification MessageQualification.dat

-rfpsRecipientRange %MP_CUSTSTART0%-%MP_CUSTEND0% -pug3 false

-c/Messagepoint/settings/test.job

*If you have additional files, you must upload them in the composition pack- age and update the cftemplate.dat file with their name.

*-c%JOB_REPOSITORY%AdditionalFile.txt
