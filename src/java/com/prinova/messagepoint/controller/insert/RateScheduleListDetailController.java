package com.prinova.messagepoint.controller.insert;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.insert.RateSchedule;
import com.prinova.messagepoint.util.HibernateUtil;

public class RateScheduleListDetailController implements Controller{

	private String formView;

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		
		Map<String, Object> params = new HashMap<>();
		long rateSchedId = ServletRequestUtils.getLongParameter(request, RateScheduleListController.REQ_PARAM_RATE_SCHEDULE_ID, -1L);
		
		RateSchedule rateSchedule = HibernateUtil.getManager().getObject(RateSchedule.class, rateSchedId);
		params.put("rateSchedule", rateSchedule);

		return new ModelAndView(getFormView(), params);
	}

	public String getFormView() {
		return formView;
	}
	public void setFormView(String formView) {
		this.formView = formView;
	}
}
