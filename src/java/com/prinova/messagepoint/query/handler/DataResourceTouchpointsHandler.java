package com.prinova.messagepoint.query.handler;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import com.prinova.messagepoint.model.admin.DataResource;
import com.prinova.messagepoint.query.DefaultPage;
import com.prinova.messagepoint.query.IPage;

public class DataResourceTouchpointsHandler implements PostQueryHandler {

	private boolean isOrderAsc;
	
	public boolean isOrderAsc() {
		return isOrderAsc;
	}

	public void setOrderAsc(boolean isOrderAsc) {
		this.isOrderAsc = isOrderAsc;
	}
	
	@SuppressWarnings("unchecked")
	public IPage handle(IPage page) {
		List<DataResource> oList = (List<DataResource>)page.getList();
		
		Collections.sort(oList, new Comparator<>() {
            public int compare(DataResource o1, DataResource o2) {
                String c1 = getTouchpoint(o1);
                String c2 = getTouchpoint(o2);
                if (isOrderAsc()) {
                    return c1.compareTo(c2);
                } else {
                    return c2.compareTo(c1);
                }
            }
        });
		
        ((DefaultPage)page).setList(oList);
		return page;
	}
	
	private String getTouchpoint(DataResource dr){
		if(dr.getDocument() != null){
			return dr.getDocument().getName();
		}else{
			return "";
		}
	}
}
