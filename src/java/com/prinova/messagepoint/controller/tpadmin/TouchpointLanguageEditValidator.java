package com.prinova.messagepoint.controller.tpadmin;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.model.TouchpointLanguage;
import com.prinova.messagepoint.model.TpCollectionTouchpointAssignment;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.springframework.validation.Errors;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class TouchpointLanguageEditValidator extends MessagepointInputValidator
{
    /**
     * Validates the Message command object.  Ensures that the name property has been specified.
     */
    public void validateNotGenericInputs(Object command, Errors errors)
    {
    	TouchpointLanguage touchpointLanguage = (TouchpointLanguage)command;
    	long localeId = touchpointLanguage.getMessagepointLocale().getId();
		touchpointLanguage.setMessagepointLocale(MessagepointLocale.getLanguageLocaleByLocaleId(localeId));
		touchpointLanguage.setName(touchpointLanguage.getMessagepointLocale().getName());
    	
		Document document = null;
    	if( touchpointLanguage.getDocument() != null ) {
    		document = HibernateUtil.getManager().getObject(Document.class, touchpointLanguage.getDocument().getId());
    	}
    	
    	// Check for existing languages.
    	if( document != null ) {
        	Set<TouchpointLanguage> touchpointLanguages = document.getTouchpointLanguages();
        	if (touchpointLanguages != null) {
    	    	for (TouchpointLanguage existingTouchpointLanguage : touchpointLanguages) {
    	    		if (existingTouchpointLanguage.getLocaleCode().equals(touchpointLanguage.getLocaleCode())
    	    				&& existingTouchpointLanguage.getId() != touchpointLanguage.getId() ) {
    	    			errors.rejectValue( "messagepointLocale", "error.message.languageexists");
    	    			break;
    	    		}
    	    	}
        	}

			// Check locales against other locales in the touchpoint collection.
			if (document.getTpCollectionTouchpoints() != null) {
				long currentDocumentId = document.getId();
				MessagepointLocale documentLocale = touchpointLanguage.getMessagepointLocale();
				Set<Document> documents = new HashSet<>();
				for(TpCollectionTouchpointAssignment assignment : document.getTpCollectionTouchpoints()){
					documents.addAll(TouchpointCollection.findById(assignment.getTouchpointCollection().getId()).getDocuments().stream().filter(d -> d.getId() != currentDocumentId).collect(Collectors.toList()));
				}
				for (Document document2 : documents) {
					List<MessagepointLocale> document2Locales = document2.getTouchpointLanguages().stream().map(TouchpointLanguage::getMessagepointLocale).collect(Collectors.toList());
					for(MessagepointLocale document2Locale : document2Locales){
						if(documentLocale.getLanguageId() == document2Locale.getLanguageId() && !documentLocale.getCode().equals(document2Locale.getCode())){
							errors.rejectValue("messagepointLocale","error.message.packaged.touchpoint.must.contains.the.same.locale", new String[]{document2Locale.getName()}, "");
							return;
						}
					}
				}
			}
    	}
    }
}
