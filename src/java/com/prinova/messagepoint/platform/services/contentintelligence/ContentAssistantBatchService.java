package com.prinova.messagepoint.platform.services.contentintelligence;

import com.prinova.messagepoint.controller.contentintelligence.ContentAssistantListWrapper;
import com.prinova.messagepoint.model.contentintelligence.ContentAssistant;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.backgroundtask.ContentAssistantBackgroundTask;
import com.prinova.messagepoint.platform.services.contentintelligence.ContentAssistantBatchServiceRequest;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.UserUtil;

public class ContentAssistantBatchService extends AbstractService {

    public static final String SERVICE_NAME = "contentintelligence.ContentAssistantBatchService";

    @Override
    public void execute(ServiceExecutionContext context) {
        validate(context);
        if (hasValidationError(context)) {
            return;
        }

        ContentAssistantBatchServiceRequest request = (ContentAssistantBatchServiceRequest) context.getRequest();

        for (ContentAssistant contentAssistant : request.getWrapper().getSelected()) {
            // Delete the content assistant
            ContentAssistantBackgroundTask task = new ContentAssistantBackgroundTask(contentAssistant, UserUtil.getPrincipalUser());
            MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);
        }

    }

    @Override
    public void validate(ServiceExecutionContext context) {

    }

    public static ServiceExecutionContext createContext(ContentAssistantListWrapper wrapper) {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ContentAssistantBatchServiceRequest request = new ContentAssistantBatchServiceRequest();
        request.setWrapper(wrapper);
        context.setRequest(request);

        SimpleServiceResponse response = new SimpleServiceResponse();
        response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(response);

        return context;
    }
}

