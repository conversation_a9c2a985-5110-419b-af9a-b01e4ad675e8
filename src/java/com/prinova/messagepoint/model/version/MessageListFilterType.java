package com.prinova.messagepoint.model.version;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.common.StaticType;
import com.prinova.messagepoint.util.ApplicationUtil;

public class MessageListFilterType extends StaticType {
	private static final long serialVersionUID = 546293711499914404L;

	public static final int ID_ALL = 1;
	public static final int ID_WORKING_COPIES = 2;
	public static final int ID_MY_WORKING_COPIES = 3;
	public static final int ID_ARCHIVED = 4;

	// PRODUCTION_ONLY, LATEST filters are not valid user list filters, but is added here to assist in get a message list from other part of messagepoint
	public static final int ID_PRODUCTION_ONLY = 5;

	public static final String NAME_ALL = "All";
	public static final String NAME_WORKING_COPIES = "WorkingCopies";
	public static final String NAME_MY_WORKING_COPIES = "MyWorkingCopies";
	public static final String NAME_ARCHIVED = "Archived";

	public static final String MESSAGE_CODE_ALL = "page.label.message.filter.all";	
	public static final String MESSAGE_CODE_WORKING_COPIES = "page.label.message.filter.working.copies";
	public static final String MESSAGE_CODE_MY_WORKING_COPIES = "page.label.message.filter.my.working.copies";
	public static final String MESSAGE_CODE_ARCHIVED = "page.label.message.filter.archived";

	public MessageListFilterType(Integer id) {
		super();
		switch (id) {
		case ID_ALL:
			this.setId(ID_ALL);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ALL));
			this.setDisplayMessageCode(MESSAGE_CODE_ALL);
			break;
		case ID_WORKING_COPIES:
			this.setId(ID_WORKING_COPIES);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WORKING_COPIES));
			this.setDisplayMessageCode(MESSAGE_CODE_WORKING_COPIES);
			break;
		case ID_MY_WORKING_COPIES:
			this.setId(ID_MY_WORKING_COPIES);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_MY_WORKING_COPIES));
			this.setDisplayMessageCode(MESSAGE_CODE_MY_WORKING_COPIES);
			break;
		case ID_ARCHIVED:
			this.setId(ID_ARCHIVED);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ARCHIVED));
			this.setDisplayMessageCode(MESSAGE_CODE_ARCHIVED);
			break;
		default:
			break;
		}
	}

	public MessageListFilterType(String name) {
		super();
		if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_ALL))) { 
			this.setId(ID_ALL);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ALL));
			this.setDisplayMessageCode(MESSAGE_CODE_ALL);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_WORKING_COPIES))) { 
			this.setId(ID_WORKING_COPIES);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WORKING_COPIES));
			this.setDisplayMessageCode(MESSAGE_CODE_WORKING_COPIES);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_MY_WORKING_COPIES))) { 
			this.setId(ID_MY_WORKING_COPIES);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_MY_WORKING_COPIES));
			this.setDisplayMessageCode(MESSAGE_CODE_MY_WORKING_COPIES);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_ARCHIVED))) { 
			this.setId(ID_ARCHIVED);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ARCHIVED));
			this.setDisplayMessageCode(MESSAGE_CODE_ARCHIVED);
		}
	}
	
	public static List<MessageListFilterType> listAll() {
		List<MessageListFilterType> allListFilterTypes = new ArrayList<>();

		MessageListFilterType listFilterType = null;
		
		listFilterType = new MessageListFilterType(ID_ALL);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new MessageListFilterType(ID_WORKING_COPIES);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new MessageListFilterType(ID_MY_WORKING_COPIES);
		allListFilterTypes.add(listFilterType);

		listFilterType = new MessageListFilterType(ID_ARCHIVED);
		allListFilterTypes.add(listFilterType);

		return allListFilterTypes;
	}
}