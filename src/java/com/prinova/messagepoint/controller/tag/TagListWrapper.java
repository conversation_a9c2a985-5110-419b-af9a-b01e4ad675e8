package com.prinova.messagepoint.controller.tag;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.tag.Tag;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;

public class TagListWrapper implements Serializable {

	private static final long serialVersionUID = -5832981509931836375L;

	private List<Long>				selectedIds;
	private String 					actionValue;
	private String 					userNote;
	private User 					assignedToUser;
	private Date 					dateFilter 			= DateUtil.today();
	
	public TagListWrapper() {
		super();
	}

	public List<Long> getSelectedIds() {
		return selectedIds;
	}
	public void setSelectedIds(List<Long> selectedIds) {
		this.selectedIds = selectedIds;
	}
	public List<Tag> getSelectedList(){
		List<Tag> selectedList = new ArrayList<>();
		if(this.selectedIds != null){
			for(Long selectedId : this.selectedIds){
				selectedList.add(HibernateUtil.getManager().getObject(Tag.class, selectedId));
			}
		}
		return selectedList;
	}

	public String getActionValue() {
		return actionValue;
	}
	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}

	public String getUserNote() {
		return userNote;
	}
	public void setUserNote(String userNote) {
		this.userNote = userNote;
	}

	public User getAssignedToUser() {
		return assignedToUser;
	}
	public void setAssignedToUser(User assignedToUser) {
		this.assignedToUser = assignedToUser;
	}

	public Date getDateFilter() {
		return dateFilter;
	}
	public void setDateFilter(Date dateFilter) {
		this.dateFilter = dateFilter;
	}
	
}