package com.prinova.messagepoint.platform.services.job;

import com.prinova.messagepoint.model.TouchpointLanguage;
import com.prinova.messagepoint.model.content.ContentObjectSlice;
import com.prinova.messagepoint.model.util.JobPackerEmbeddedContentUploadWrapper;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

import java.util.Set;

public class GetSelectableContentServiceRequest extends SimpleServiceRequest
{
	private static final long serialVersionUID = -2884046674811579138L;
	private ContentObjectSlice message;
	private Set<TouchpointLanguage> exportLangs;
	private boolean useActiveVariants;
	private JobPackerDatabaseFileWrapper dbfWrapper;
	private JobPackerEmbeddedContentUploadWrapper ecWrapper;
	
	public void setUseActiveVariants(boolean val)
	{
		useActiveVariants = val;
	}
	public boolean isUseActiveVariants()
	{
		return useActiveVariants;
	}
	
	public ContentObjectSlice getMessage() {
		return message;
	}
	public void setMessage(ContentObjectSlice message) {
		this.message = message;
	}

	public Set<TouchpointLanguage> getExportLanguages()
	{
		return exportLangs;
	}
	void setExportLanguages( Set<TouchpointLanguage> values )
	{
		exportLangs = values;
	}

	public void setDatabaseFileWrapper( JobPackerDatabaseFileWrapper wrapper )
	{
		dbfWrapper = wrapper;
	}
	public JobPackerDatabaseFileWrapper getDatabaseFileWrapper() 
	{
		return dbfWrapper;
	}
	
	public void setECWrapper( JobPackerEmbeddedContentUploadWrapper wrapper )
	{
		ecWrapper = wrapper;
	}
	public JobPackerEmbeddedContentUploadWrapper getECWrapper()
	{
		return ecWrapper;
	}
}
