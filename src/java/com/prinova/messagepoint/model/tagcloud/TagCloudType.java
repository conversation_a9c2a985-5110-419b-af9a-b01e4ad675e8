package com.prinova.messagepoint.model.tagcloud;

import com.prinova.messagepoint.model.common.StaticType;
import com.prinova.messagepoint.util.ApplicationUtil;

import java.util.ArrayList;
import java.util.List;

public class TagCloudType extends StaticType{
	private static final long serialVersionUID = 6034854077434986184L;

	private String SQLTable;
	private String HibernateTable;

	//=== Entry here must also be entered in the database table "tag_cloud_type" ===
	public static final int ID_MESSAGE 				    	= 1;
	public static final int ID_EMBEDDED_TEXT 		    	= 2;
	public static final int ID_IMAGE_LIBRARY 		    	= 3;
	public static final int ID_TOUCHPOINT 				    = 4;
	public static final int ID_RULE	 					    = 5;
	public static final int ID_TARGET_GROUP	    			= 6;
	public static final int ID_COMMUNICATION	    		= 7;
	public static final int ID_METADATA_FORM		    	= 8;
	public static final int ID_TASK		    				= 9;
	public static final int ID_PROJECT		    			= 10;
	public static final int ID_RATIONALIZER_APPLICATION	    = 11;
	public static final int ID_RATIONALIZER_DOCUMENT    	= 12;
	public static final int ID_RATIONALIZER_CONTENT	    	= 13;
	public static final int ID_RATIONALIZER_QUERY	    	= 14;
	public static final int ID_TEST_COMMUNICATION	    	= 15;
	public static final int ID_RATIONALIZER_COMBINED	    = 16;
	public static final int ID_RATIONALIZER_SHARED_CONTENT	= 17;
	public static final int ID_VARIABLE						= 18;
	public static final int ID_ZONE							= 19;
	public static final int ID_DATA_RESOURCE				= 20;

	public static final String TABLE_MESSAGE_SQL 					= "content_object_data";
	public static final String TABLE_EMBEDDED_TEXT_SQL 				= "content_object_data";
	public static final String TABLE_IMAGE_LIBRARY_SQL				= "content_object_data";
	public static final String TABLE_TOUCHPOINT_SQL					= "document";
	public static final String TABLE_RULE_SQL						= "condition_element";
	public static final String TABLE_TARGET_GROUP_SQL				= "target_group";
	public static final String TABLE_COMMUNICATION_SQL				= "communication";
	public static final String TABLE_METADATA_FORM_SQL				= "metadata_form_definition";
	public static final String TABLE_TASK_SQL						= "task";
	public static final String TABLE_PROJECT_SQL					= "project";
	public static final String TABLE_ZONE_SQL						= "zone";
	public static final String TABLE_VARIABLE_SQL					= "data_element_variable";
	public static final String TABLE_DATA_RESOURCE_SQL				= "data_resource";
	public static final String TABLE_RATIONALIZER_APPLICATION_SQL	= "rationalizer_application";
	public static final String TABLE_RATIONALIZER_DOCUMENT_SQL		= "rationalizer_document";
	public static final String TABLE_RATIONALIZER_CONTENT_SQL		= "rationalizer_document_content";
	public static final String TABLE_RATIONALIZER_QUERY_SQL			= "rationalizer_query";
	public static final String TABLE_TEST_COMMUNICATION_SQL			= "communication";
	public static final String TABLE_RATIONALIZER_SHARED_CONTENT_SQL= "rationalizer_shared_content";

	public static final String TABLE_MESSAGE_HIBERNATE						= "ContentObjectData";
	public static final String TABLE_EMBEDDED_TEXT_HIBERNATE				= "ContentObjectData";
	public static final String TABLE_IMAGE_LIBRARY_HIBERNATE				= "ContentObjectData";
	public static final String TABLE_TOUCHPOINT_HIBERNATE					= "Document";
	public static final String TABLE_RULE_HIBERNATE							= "ConditionElement";
	public static final String TABLE_TARGET_GROUP_HIBERNATE					= "TargetGroup";
	public static final String TABLE_COMMUNICATION_HIBERNATE				= "Communication";
	public static final String TABLE_METADATA_FORM_HIBERNATE				= "MetadataFormDefinition";
	public static final String TABLE_TASK_HIBERNATE							= "Task";
	public static final String TABLE_PROJECT_HIBERNATE						= "Project";
	public static final String TABLE_ZONE_HIBERNATE							= "Zone";
	public static final String TABLE_VARIABLE_HIBERNATE						= "DataElementVariable";
	public static final String TABLE_DATA_RESOURCE_HIBERNATE				= "DataResource";
	public static final String TABLE_RATIONALIZER_APPLICATION_HIBERNATE		= "RationalizerApplication";
	public static final String TABLE_RATIONALIZER_DOCUMENT_HIBERNATE		= "RationalizerDocument";
	public static final String TABLE_RATIONALIZER_CONTENT_HIBERNATE			= "RationalizerDocumentContent";
	public static final String TABLE_RATIONALIZER_QUERY_HIBERNATE			= "RationalizerQuery";
	public static final String TABLE_TEST_COMMUNICATION_HIBERNATE			= "Communication";
	public static final String TABLE_RATIONALIZER_SHARED_CONTENT_HIBERNATE	= "RationalizerSharedContent";

	public static final String TYPE_MESSAGE_CODE 				= "page.label.message";
	public static final String TYPE_EMBEDDED_TEXT_CODE 			= "page.label.smart.object";
	public static final String TYPE_IMAGE_LIBRARY_CODE 			= "page.label.image";
	public static final String TYPE_TOUCHPOINT_CODE 			= "page.label.touchpoint";
	public static final String TYPE_RULE_CODE 					= "page.label.rule";
	public static final String TYPE_TARGET_GROUP_CODE 			= "page.label.targetgroup";
	public static final String TYPE_COMMUNICATION_CODE 			= "page.label.communication";
	public static final String TYPE_METADATA_FORM				= "page.label.metadata";
	public static final String TYPE_TASK						= "page.label.task";
	public static final String TYPE_PROJECT						= "page.label.project";
	public static final String TYPE_VARIABLE					= "page.label.variable";
	public static final String TYPE_ZONE						= "page.label.zone";
	public static final String TYPE_DATA_RESOURCE				= "page.label.data.resource";
	public static final String TYPE_RATIONALIZER_APPLICATION	= "page.label.rationalizer.application";
	public static final String TYPE_RATIONALIZER_DOCUMENT		= "page.label.rationalizer.document";
	public static final String TYPE_RATIONALIZER_CONTENT		= "page.label.rationalizer.content";
	public static final String TYPE_RATIONALIZER_QUERY			= "page.label.query";
	public static final String TYPE_TEST_COMMUNICATION			= "page.label.test.order";
	public static final String TYPE_RATIONALIZER_COMBINED		= "page.label.rationalizer.content.and.document";
	public static final String TYPE_RATIONALIZER_SHARED_CONTENT = "page.label.rationalizer.shared.content";


	public TagCloudType() {
		super();
	}

	public TagCloudType(Integer id) {
		super();
		switch (id) {
		case ID_MESSAGE:
			this.setId(ID_MESSAGE);
			this.setName(ApplicationUtil.getMessage(TYPE_MESSAGE_CODE));
			this.setDisplayMessageCode(TYPE_MESSAGE_CODE);
			this.setSQLTable(TABLE_MESSAGE_SQL);
			this.setHibernateTable(TABLE_MESSAGE_HIBERNATE);
			break;
		case ID_EMBEDDED_TEXT:
			this.setId(ID_EMBEDDED_TEXT);
			this.setName(ApplicationUtil.getMessage(TYPE_EMBEDDED_TEXT_CODE));
			this.setDisplayMessageCode(TYPE_EMBEDDED_TEXT_CODE);
			this.setSQLTable(TABLE_EMBEDDED_TEXT_SQL);
			this.setHibernateTable(TABLE_EMBEDDED_TEXT_HIBERNATE);
			break;
		case ID_IMAGE_LIBRARY:
			this.setId(ID_IMAGE_LIBRARY);
			this.setName(ApplicationUtil.getMessage(TYPE_IMAGE_LIBRARY_CODE));
			this.setDisplayMessageCode(TYPE_IMAGE_LIBRARY_CODE);
			this.setSQLTable(TABLE_IMAGE_LIBRARY_SQL);
			this.setHibernateTable(TABLE_IMAGE_LIBRARY_HIBERNATE);
			break;
		case ID_TOUCHPOINT:
			this.setId(ID_TOUCHPOINT);
			this.setName(ApplicationUtil.getMessage(TYPE_TOUCHPOINT_CODE));
			this.setDisplayMessageCode(TYPE_TOUCHPOINT_CODE);
			this.setSQLTable(TABLE_TOUCHPOINT_SQL);
			this.setHibernateTable(TABLE_TOUCHPOINT_HIBERNATE);
			break;
		case ID_RULE:
			this.setId(ID_RULE);
			this.setName(ApplicationUtil.getMessage(TYPE_RULE_CODE));
			this.setDisplayMessageCode(TYPE_RULE_CODE);
			this.setSQLTable(TABLE_RULE_SQL);
			this.setHibernateTable(TABLE_RULE_HIBERNATE);
			break;
		case ID_TARGET_GROUP:
			this.setId(ID_TARGET_GROUP);
			this.setName(ApplicationUtil.getMessage(TYPE_TARGET_GROUP_CODE));
			this.setDisplayMessageCode(TYPE_TARGET_GROUP_CODE);
			this.setSQLTable(TABLE_TARGET_GROUP_SQL);
			this.setHibernateTable(TABLE_TARGET_GROUP_HIBERNATE);
			break;	
		case ID_COMMUNICATION:
			this.setId(ID_COMMUNICATION);
			this.setName(ApplicationUtil.getMessage(TYPE_COMMUNICATION_CODE));
			this.setDisplayMessageCode(TYPE_COMMUNICATION_CODE);
			this.setSQLTable(TABLE_COMMUNICATION_SQL);
			this.setHibernateTable(TABLE_COMMUNICATION_HIBERNATE);
			break;
		case ID_METADATA_FORM:
			this.setId(ID_METADATA_FORM);
			this.setName(ApplicationUtil.getMessage(TYPE_METADATA_FORM));
			this.setDisplayMessageCode(TYPE_METADATA_FORM);
			this.setSQLTable(TABLE_METADATA_FORM_SQL);
			this.setHibernateTable(TABLE_METADATA_FORM_HIBERNATE);
			break;
		case ID_TASK:
			this.setId(ID_TASK);
			this.setName(ApplicationUtil.getMessage(TYPE_TASK));
			this.setDisplayMessageCode(TYPE_TASK);
			this.setSQLTable(TABLE_TASK_SQL);
			this.setHibernateTable(TABLE_TASK_HIBERNATE);
			break;
		case ID_PROJECT:
			this.setId(ID_PROJECT);
			this.setName(ApplicationUtil.getMessage(TYPE_PROJECT));
			this.setDisplayMessageCode(TYPE_PROJECT);
			this.setSQLTable(TABLE_PROJECT_SQL);
			this.setHibernateTable(TABLE_PROJECT_HIBERNATE);
			break;
		case ID_RATIONALIZER_APPLICATION:
			this.setId(ID_RATIONALIZER_APPLICATION);
			this.setName(ApplicationUtil.getMessage(TYPE_RATIONALIZER_APPLICATION));
			this.setDisplayMessageCode(TYPE_RATIONALIZER_APPLICATION);
			this.setSQLTable(TABLE_RATIONALIZER_APPLICATION_SQL);
			this.setHibernateTable(TABLE_RATIONALIZER_APPLICATION_HIBERNATE);
			break;		
		case ID_RATIONALIZER_DOCUMENT:
			this.setId(ID_RATIONALIZER_DOCUMENT);
			this.setName(ApplicationUtil.getMessage(TYPE_RATIONALIZER_DOCUMENT));
			this.setDisplayMessageCode(TYPE_RATIONALIZER_DOCUMENT);
			this.setSQLTable(TABLE_RATIONALIZER_DOCUMENT_SQL);
			this.setHibernateTable(TABLE_RATIONALIZER_DOCUMENT_HIBERNATE);
			break;
		case ID_RATIONALIZER_CONTENT:
			this.setId(ID_RATIONALIZER_CONTENT);
			this.setName(ApplicationUtil.getMessage(TYPE_RATIONALIZER_CONTENT));
			this.setDisplayMessageCode(TYPE_RATIONALIZER_CONTENT);
			this.setSQLTable(TABLE_RATIONALIZER_CONTENT_SQL);
			this.setHibernateTable(TABLE_RATIONALIZER_CONTENT_HIBERNATE);
			break;
		case ID_RATIONALIZER_QUERY:
			this.setId(ID_RATIONALIZER_QUERY);
			this.setName(ApplicationUtil.getMessage(TYPE_RATIONALIZER_QUERY));
			this.setDisplayMessageCode(TYPE_RATIONALIZER_QUERY);
			this.setSQLTable(TABLE_RATIONALIZER_QUERY_SQL);
			this.setHibernateTable(TABLE_RATIONALIZER_QUERY_HIBERNATE);
			break;
		case ID_TEST_COMMUNICATION:
			this.setId(ID_TEST_COMMUNICATION);
			this.setName(ApplicationUtil.getMessage(TYPE_TEST_COMMUNICATION));
			this.setDisplayMessageCode(TYPE_TEST_COMMUNICATION);
			this.setSQLTable(TABLE_TEST_COMMUNICATION_SQL);
			this.setHibernateTable(TABLE_TEST_COMMUNICATION_HIBERNATE);
			break;
		case ID_RATIONALIZER_COMBINED:
			this.setId(ID_RATIONALIZER_COMBINED);
			this.setName(ApplicationUtil.getMessage(TYPE_RATIONALIZER_COMBINED));
			this.setDisplayMessageCode(TYPE_RATIONALIZER_COMBINED);
			this.setSQLTable(TABLE_RATIONALIZER_DOCUMENT_SQL);
			this.setHibernateTable(TABLE_RATIONALIZER_DOCUMENT_HIBERNATE);
			break;
		case ID_RATIONALIZER_SHARED_CONTENT:
			this.setId(ID_RATIONALIZER_SHARED_CONTENT);
			this.setName(ApplicationUtil.getMessage(TYPE_RATIONALIZER_SHARED_CONTENT));
			this.setDisplayMessageCode(TYPE_RATIONALIZER_SHARED_CONTENT);
			this.setSQLTable(TABLE_RATIONALIZER_SHARED_CONTENT_SQL);
			this.setHibernateTable(TABLE_RATIONALIZER_SHARED_CONTENT_HIBERNATE);
			break;
		case ID_VARIABLE:
			this.setId(ID_VARIABLE);
			this.setName(ApplicationUtil.getMessage(TYPE_VARIABLE));
			this.setDisplayMessageCode(TYPE_VARIABLE);
			this.setSQLTable(TABLE_VARIABLE_SQL);
			this.setHibernateTable(TABLE_VARIABLE_HIBERNATE);
			break;
		case ID_ZONE:
			this.setId(ID_ZONE);
			this.setName(ApplicationUtil.getMessage(TYPE_ZONE));
			this.setDisplayMessageCode(TYPE_ZONE);
			this.setSQLTable(TABLE_ZONE_SQL);
			this.setHibernateTable(TABLE_ZONE_HIBERNATE);
			break;
			case ID_DATA_RESOURCE:
			this.setId(ID_DATA_RESOURCE);
			this.setName(ApplicationUtil.getMessage(TYPE_DATA_RESOURCE));
			this.setDisplayMessageCode(TYPE_DATA_RESOURCE);
			this.setSQLTable(TABLE_DATA_RESOURCE_SQL);
			this.setHibernateTable(TABLE_DATA_RESOURCE_HIBERNATE);
			break;
		default:
			break;
		}
	}

	public static String getName(int id){
		switch (id) {
		case ID_MESSAGE:
			return ApplicationUtil.getMessage(TYPE_MESSAGE_CODE);
		case ID_EMBEDDED_TEXT:
			return ApplicationUtil.getMessage(TYPE_EMBEDDED_TEXT_CODE);
		case ID_IMAGE_LIBRARY:
			return ApplicationUtil.getMessage(TYPE_IMAGE_LIBRARY_CODE);
		case ID_TOUCHPOINT:
			return ApplicationUtil.getMessage(TYPE_TOUCHPOINT_CODE);
		case ID_RULE:
			return ApplicationUtil.getMessage(TYPE_RULE_CODE);
		case ID_TARGET_GROUP:
			return ApplicationUtil.getMessage(TYPE_TARGET_GROUP_CODE);		
		case ID_COMMUNICATION:
			return ApplicationUtil.getMessage(TYPE_COMMUNICATION_CODE);
		case ID_METADATA_FORM:
			return ApplicationUtil.getMessage(TYPE_METADATA_FORM);
		case ID_TASK:
			return ApplicationUtil.getMessage(TYPE_TASK);
		case ID_PROJECT:
			return ApplicationUtil.getMessage(TYPE_PROJECT);
		case ID_RATIONALIZER_APPLICATION:
			return ApplicationUtil.getMessage(TYPE_RATIONALIZER_APPLICATION);		
		case ID_RATIONALIZER_DOCUMENT:
			return ApplicationUtil.getMessage(TYPE_RATIONALIZER_DOCUMENT);
		case ID_RATIONALIZER_CONTENT:
			return ApplicationUtil.getMessage(TYPE_RATIONALIZER_CONTENT);
		case ID_RATIONALIZER_QUERY:
			return ApplicationUtil.getMessage(TYPE_RATIONALIZER_QUERY);
		case ID_TEST_COMMUNICATION:
			return ApplicationUtil.getMessage(TYPE_TEST_COMMUNICATION);
		case ID_RATIONALIZER_COMBINED:
			return ApplicationUtil.getMessage(TYPE_RATIONALIZER_COMBINED);
		case ID_RATIONALIZER_SHARED_CONTENT:
			return ApplicationUtil.getMessage(TYPE_RATIONALIZER_SHARED_CONTENT);
		case ID_VARIABLE:
			return ApplicationUtil.getMessage(TYPE_VARIABLE);
		case ID_ZONE:
			return ApplicationUtil.getMessage(TYPE_ZONE);
		case ID_DATA_RESOURCE:
			return ApplicationUtil.getMessage(TYPE_DATA_RESOURCE);
		default:
			return null;
		}		
	}

	public static List<TagCloudType> listAll() {
		List<TagCloudType> allTypes = new ArrayList<>();

		TagCloudType type = null;

		type = new TagCloudType(ID_MESSAGE);
		allTypes.add(type);

		type = new TagCloudType(ID_EMBEDDED_TEXT);
		allTypes.add(type);

		type = new TagCloudType(ID_IMAGE_LIBRARY);
		allTypes.add(type);

		type = new TagCloudType(ID_TOUCHPOINT);
		allTypes.add(type);

		type = new TagCloudType(ID_RULE);
		allTypes.add(type);

		type = new TagCloudType(ID_TARGET_GROUP);
		allTypes.add(type);

		type = new TagCloudType(ID_COMMUNICATION);
		allTypes.add(type);

		type = new TagCloudType(ID_METADATA_FORM);
		allTypes.add(type);

		type = new TagCloudType(ID_TASK);
		allTypes.add(type);

		type = new TagCloudType(ID_PROJECT);
		allTypes.add(type);

		type = new TagCloudType(ID_RATIONALIZER_APPLICATION);
		allTypes.add(type);

		type = new TagCloudType(ID_RATIONALIZER_DOCUMENT);
		allTypes.add(type);

		type = new TagCloudType(ID_RATIONALIZER_CONTENT);
		allTypes.add(type);

		type = new TagCloudType(ID_RATIONALIZER_QUERY);
		allTypes.add(type);

		type = new TagCloudType(ID_TEST_COMMUNICATION);
		allTypes.add(type);

		type = new TagCloudType(ID_RATIONALIZER_COMBINED);
		allTypes.add(type);

		type = new TagCloudType(ID_RATIONALIZER_SHARED_CONTENT);
		allTypes.add(type);

		type = new TagCloudType(ID_VARIABLE);
		allTypes.add(type);

		type = new TagCloudType(ID_ZONE);
		allTypes.add(type);

		type = new TagCloudType(ID_DATA_RESOURCE);
		allTypes.add(type);

		return allTypes;
	}

	public static List<TagCloudType> listAllCore() {
		List<TagCloudType> allTypes = new ArrayList<>();

		TagCloudType type = null;

		type = new TagCloudType(ID_MESSAGE);
		allTypes.add(type);

		type = new TagCloudType(ID_EMBEDDED_TEXT);
		allTypes.add(type);

		type = new TagCloudType(ID_IMAGE_LIBRARY);
		allTypes.add(type);

		type = new TagCloudType(ID_TOUCHPOINT);
		allTypes.add(type);

		type = new TagCloudType(ID_RULE);
		allTypes.add(type);

		type = new TagCloudType(ID_TARGET_GROUP);
		allTypes.add(type);

		type = new TagCloudType(ID_COMMUNICATION);
		allTypes.add(type);

		type = new TagCloudType(ID_METADATA_FORM);
		allTypes.add(type);

		type = new TagCloudType(ID_TASK);
		allTypes.add(type);

		type = new TagCloudType(ID_PROJECT);
		allTypes.add(type);

		type = new TagCloudType(ID_VARIABLE);
		allTypes.add(type);

		type = new TagCloudType(ID_ZONE);
		allTypes.add(type);

		type = new TagCloudType(ID_DATA_RESOURCE);
		allTypes.add(type);

		return allTypes;
	}

	public static List<TagCloudType> listAllRationalizer() {
		List<TagCloudType> allTypes = new ArrayList<>();

		TagCloudType type = null;

		type = new TagCloudType(ID_RATIONALIZER_APPLICATION);
		allTypes.add(type);

		type = new TagCloudType(ID_RATIONALIZER_DOCUMENT);
		allTypes.add(type);

		type = new TagCloudType(ID_RATIONALIZER_CONTENT);
		allTypes.add(type);

		type = new TagCloudType(ID_RATIONALIZER_QUERY);
		allTypes.add(type);

		type = new TagCloudType(ID_RATIONALIZER_SHARED_CONTENT);
		allTypes.add(type);

		return allTypes;
	}

	public String getSQLTable() {
		return SQLTable;
	}
	public void setSQLTable(String SQLTable) {
		this.SQLTable = SQLTable;
	}

	public String getHibernateTable() {
		return HibernateTable;
	}
	public void setHibernateTable(String hibernateTable) {
		HibernateTable = hibernateTable;
	}
}
