package com.prinova.messagepoint.platform.services.elasticsearch.action.similarity;

import ai.mpr.marcie.content.rationalizer.ApplicationService;
import ai.mpr.marcie.content.rationalizer.misc.JsonArrayBuilder;
import ai.mpr.marcie.content.rationalizer.misc.JsonObjectBuilder;
import ai.mpr.marcie.content.rationalizer.steps.AbstractStep;
import ai.mpr.marcie.content.rationalizer.steps.ScrollQuery;
import ai.mpr.marcie.content.rationalizer.steps.StepOutput;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static ai.mpr.marcie.content.rationalizer.misc.ApplicationUtils.extractHits;
import static ai.mpr.marcie.content.rationalizer.misc.ApplicationUtils.getValueForJsonPath;
import static com.prinova.messagepoint.model.wrapper.elastic.util.ElasticSearchQueryUtil.DEFAULT_KEYWORD_SIZE_FOR_TEXT;
import static com.prinova.messagepoint.model.wrapper.elastic.util.ElasticSearchQueryUtil.DOCUMENT_ATTRIBUTE_FILENAME;
import static com.prinova.messagepoint.model.wrapper.elastic.util.ElasticSearchQueryUtil.DOCUMENT_ATTRIBUTE_FILENAME_KEYWORD;
import static com.prinova.messagepoint.model.wrapper.elastic.util.ElasticSearchQueryUtil.computeElasticSearchWildcardSearchOnKeywordField;
import static com.prinova.messagepoint.platform.services.elasticsearch.ElasticSearchConstants.MIN_SIMILARITY_FOR_QUERY;
import static com.prinova.messagepoint.platform.services.elasticsearch.FilterUtil.SCORE_1_0;
import static com.prinova.messagepoint.platform.services.elasticsearch.Utils.sortByScoreWithRscIdsOnTopForSameScore;

public class GetFromMatchesSimilarityFieldByContentIdAndDocumentFileName extends AbstractStep<GetFromMatchesSimilarityFieldByContentIdAndDocumentFileName.Request, Map> {

    public static final String TEXT = "text";

    @Override
    public StepOutput<Map> apply(ApplicationService service, GetFromMatchesSimilarityFieldByContentIdAndDocumentFileName.Request req) {

        Map<String, Float> contentIdToScoreMap = (Map<String, Float>) new GetFromMatchesSimilarityFieldByContentId().apply(service,
                GetFromMatchesSimilarityFieldByContentId.Request.builder()
                        .appId(req.appId())
                        .contentId(req.contentId())
                        .minSimilarity(req.minSimilarity())
                        .maxSimilarity(req.maxSimilarity())
                        .build()
        ).result();

        if (MapUtils.isEmpty(contentIdToScoreMap)) {
            return StepOutput.builder(Map.class).result(contentIdToScoreMap).build();
        }

        if (StringUtils.isEmpty(req.documentFileName())) {
            contentIdToScoreMap = sortByScoreWithRscIdsOnTopForSameScore(contentIdToScoreMap);

            return StepOutput.builder(Map.class).result(contentIdToScoreMap).build();
        }

        Set<String> filteredIds = queryByIdsAndDocumentFileName(
                service,
                req.appId(),
                contentIdToScoreMap.keySet(),
                req.documentFileName()
        );

        contentIdToScoreMap = contentIdToScoreMap.entrySet().stream()
                .filter(entry -> {
                            String id = entry.getKey();
                            return StringUtils.isNotEmpty(id) && filteredIds.contains(id);
                        }
                )
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldValue, newValue) -> oldValue, LinkedHashMap::new));

        contentIdToScoreMap = sortByScoreWithRscIdsOnTopForSameScore(contentIdToScoreMap);

        return StepOutput.builder(Map.class).result(contentIdToScoreMap).build();
    }

    private Set<String> queryByIdsAndDocumentFileName(
            ApplicationService service,
            String appId,
            Set<String> contentIds,
            String documentFileName
    ) {
        JsonObject queryJsonObject = new JsonObjectBuilder()
                .add("_source", new JsonArrayBuilder().addAll(List.of("id")).build())
                .add("query", new JsonObjectBuilder()
                        .add("bool", new JsonObjectBuilder()
                                .add("must", new JsonArrayBuilder()
                                        .addAll(
                                                Arrays.asList(
                                                        new JsonObjectBuilder()
                                                                .add("ids", new JsonObjectBuilder()
                                                                        .add("values", new JsonArrayBuilder()
                                                                                .addAll(contentIds)
                                                                                .build()
                                                                        ).build()
                                                                ).build(),
                                                        computeElasticSearchWildcardSearchOnKeywordField(
                                                                DOCUMENT_ATTRIBUTE_FILENAME,
                                                                DOCUMENT_ATTRIBUTE_FILENAME_KEYWORD,
                                                                DEFAULT_KEYWORD_SIZE_FOR_TEXT,
                                                                documentFileName
                                                        )
                                                )
                                        ).build()
                                ).build()
                        ).build()
                ).build();

        final Set<String> result = new LinkedHashSet<>();
        new ScrollQuery().apply(service, ScrollQuery.Request.builder()
                .appId(appId)
                .query(queryJsonObject)
                .responsePageConsumer(responseJson -> {
                    JsonArray hits = extractHits(responseJson);
                    if (hits.isEmpty()) {
                        return;
                    }
                    hits.forEach(jsonElement -> {
                        String id = getValueForJsonPath(jsonElement, "_source/id", "/", String.class);
                        if (id == null) {
                            return;
                        }

                        result.add(id);
                    });
                })
                .build()
        );


        return result;
    }

    public static interface Request {

        String appId();

        String contentId();

        String documentFileName();

        default float minSimilarity() {
            return MIN_SIMILARITY_FOR_QUERY;
        }

        default float maxSimilarity() {
            return SCORE_1_0;
        }

        static GetFromMatchesSimilarityFieldByContentIdAndDocumentFileName.Request.Builder builder() {
            return new GetFromMatchesSimilarityFieldByContentIdAndDocumentFileName.Request.Builder();
        }

        class Builder {
            private String appId;
            private String contentId;
            private String documentFileName;
            private Float minSimilarity;
            private Float maxSimilarity;

            private Builder() {}

            public Request.Builder appId(String appId) {
                this.appId = appId;

                return this;
            }

            public Request.Builder contentId(String contentId) {
                this.contentId = contentId;

                return this;
            }

            public Request.Builder documentFileName(String documentFileName) {
                this.documentFileName = documentFileName;

                return this;
            }

            public Request.Builder minSimilarity(Float minSimilarity) {
                this.minSimilarity = minSimilarity;

                return this;
            }

            public Request.Builder maxSimilarity(Float maxSimilarity) {
                this.maxSimilarity = maxSimilarity;

                return this;
            }

            public Request build() {
                return new Request() {

                    @Override
                    public String appId() {
                        return appId;
                    }

                    @Override
                    public String contentId() {
                        return contentId;
                    }

                    @Override
                    public String documentFileName() {
                        return documentFileName;
                    }

                    @Override
                    public float minSimilarity() {
                        if (minSimilarity != null) {
                            return minSimilarity;
                        }

                        return Request.super.minSimilarity();
                    }

                    @Override
                    public float maxSimilarity() {
                        if (maxSimilarity != null) {
                            return maxSimilarity;
                        }

                        return Request.super.maxSimilarity();
                    }
                };
            }
        }
    }
}
