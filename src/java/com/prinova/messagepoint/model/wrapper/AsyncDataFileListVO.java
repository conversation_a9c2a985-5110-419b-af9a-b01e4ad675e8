package com.prinova.messagepoint.model.wrapper;

import java.util.Set;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.testing.DataFile;
import com.prinova.messagepoint.tag.layout.TxtFmtTag;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HttpRequestUtil;
import com.prinova.messagepoint.util.UserUtil;

public class AsyncDataFileListVO extends AsyncListVO{

	private DataFile 					dataFile;
	private String						name;
	private AsyncListVOIFrameData		iFrameData;
	private DataFileListVOFlags			flags;

	public void setDataFile(DataFile dataFile) {
		this.dataFile = dataFile;
	}
	
	public String getName() {
		String onclickEvent = "";
		if(UserUtil.isPermissionGranted(Permission.ID_ROLE_TOUCHPOINT_DATA_EDIT)){
			String dataFileViewURL = ApplicationUtil.getWebRoot() + "dataadmin/data_files_edit.form";
			onclickEvent = "onclick=\"javascript:iFrameView('"+dataFileViewURL+"?dataFileId=" + this.dataFile.getId() + "', 'actionLabel_"+this.dataFile.getId()+"', event); return false;\"";
		}
		String nameHTML = TxtFmtTag.maxTxtLengh("<a id=\"actionLabel_"+ this.dataFile.getId() +"\" itemName=\"ITEM_NAME\" href=\"#\" " + onclickEvent + " class=\"dataTableLink dataTableItemName\">" + name + "</a>", displayMaxLength(40));
		nameHTML = nameHTML.replace("ITEM_NAME",name);
		return nameHTML;
	}
	
	public String getBinding() {
		return "<input id='listItemCheck_"+this.dataFile.getId()+"' type='checkbox' value='"+this.dataFile.getId()+
				"' style='display : none;' name='selectedIds'><input type='hidden' value='on' name='_selectedIds'>";
	}	

	public void setName(String name) {
		this.name = name;
	}

	public String getTouchpoints() {
		Set<Document> documents = this.dataFile.getDocuments();
		if(documents != null && documents.size() > 0){
			if(documents.size() == 1){
				Document document = documents.iterator().next();
				return document.getName();
			}else{
				StringBuilder tps = new StringBuilder();
				for(Document document : documents){
					tps.append(document.getName()).append("<br/>");
				}
				return "<div class=\"text-center txtFmtTip\" title=\"|<div class='txtFmtTipText'>" + tps + "</div>\">Multiple (" + documents.size() + ")</div>";
			}
		}else{
			return "";
		}		
	}

	public String getCollections() {
		Set<TouchpointCollection> collections = this.dataFile.getTouchpointCollections();
		if(collections != null && collections.size() > 0){
			if(collections.size() == 1){
				TouchpointCollection collection = collections.iterator().next();
				return collection.getName();
			}else{
				StringBuilder cs = new StringBuilder();
				for(TouchpointCollection collection : collections){
					cs.append(collection.getName()).append("<br/>");
				}
				return "<div class=\"text-center txtFmtTip\" title=\"|<div class='txtFmtTipText'>" + cs + "</div>\">Multiple (" + collections.size() + ")</div>";
			}
		}else{
			return "";
		}
	}

	public String getLocationType(){
		if(this.dataFile.isLocal()){
			return ApplicationUtil.getMessage("page.label.local");
		}else{
			return ApplicationUtil.getMessage("page.label.remote");
		}
	}
	
	public String getFileName() {
		if(this.dataFile.isLocal()){
			String encodedSourcePath = "";
			String fileNameStr = "";

			encodedSourcePath 		= HttpRequestUtil.getFileResourceToken(this.dataFile.getFile().getPath());
			String strippedFileName = TxtFmtTag.maxTxtLengh(this.dataFile.getFilename(), displayMaxLength(40), false);
			fileNameStr				= "<a href=\"javascript:javascriptHref('" + ApplicationUtil.getWebRoot() + "download/data.form?resource=" + HttpRequestUtil.buildResourceToken().add("file", this.dataFile.getFile().getPath()) + "')\">" + strippedFileName + "</a>";
							
			if(UserUtil.isPermissionGranted(Permission.ID_ROLE_TOUCHPOINT_DATA_EDIT)){
				fileNameStr 	   += "<i id=\"sourceEditor_"+this.dataFile.getId()+"\" class=\"sourceEditor table-icon far fa-pencil ml-2\" aria-hidden=\"true\"><input type=\"hidden\" value=\""+encodedSourcePath+"\"/></i>";
			}
			
			return fileNameStr;
		}else{
			return TxtFmtTag.maxTxtLengh(this.dataFile.getRemoteDataFilePath(), displayMaxLength(40));
		}
	}

	public String getContentType() {
		return ApplicationUtil.getMessage(this.dataFile.getSourceType().getName());
	}

	public String getUser() {
		return this.dataFile.getUpdatedByName();
	}

	public AsyncListVOIFrameData getIFrameData() {
		return iFrameData;
	}

	public void setIFrameData(AsyncListVOIFrameData iFrameData) {
		this.iFrameData = iFrameData;
	}
	
	public DataFileListVOFlags getFlags() {
		return flags;
	}

	public void setFlags(DataFileListVOFlags flags) {
		this.flags = flags;
	}

	public class DataFileListVOFlags{
		private boolean	canUpdate;
		private boolean canDelete;
		private boolean canClone;

		public boolean isCanUpdate() {
			return canUpdate;
		}
		public void setCanUpdate(boolean canUpdate) {
			this.canUpdate = canUpdate;
		}
		public boolean isCanDelete() {
			return canDelete;
		}
		public void setCanDelete(boolean canDelete) {
			this.canDelete = canDelete;
		}
		public boolean isCanClone() {
			return canClone;
		}
		public void setCanClone(boolean canClone) {
			this.canClone = canClone;
		}		
	}
}
