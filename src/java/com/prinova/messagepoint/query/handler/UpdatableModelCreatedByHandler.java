package com.prinova.messagepoint.query.handler;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import com.prinova.messagepoint.model.UpdatableMessagePointModel;
import com.prinova.messagepoint.query.DefaultPage;
import com.prinova.messagepoint.query.IPage;

public class UpdatableModelCreatedByHandler <T extends UpdatableMessagePointModel> implements PostQueryHandler {

	private boolean isOrderAsc;
	
	public boolean isOrderAsc() {
		return isOrderAsc;
	}

	public void setOrderAsc(boolean isOrderAsc) {
		this.isOrderAsc = isOrderAsc;
	}
	
	@SuppressWarnings("unchecked")
	public IPage handle(IPage page) {
		List<T> items = (List<T>)page.getList();
		
		Collections.sort(items, new Comparator<>() {
            public int compare(T o1, T o2) {
                String createdBy1 = o1.getCreatedByName();
                String createdBy2 = o2.getCreatedByName();
                if (isOrderAsc()) {
                    return createdBy1.compareTo(createdBy2);
                } else {
                    return createdBy2.compareTo(createdBy1);
                }
            }
        });
		
        ((DefaultPage)page).setList(items);
		return page;
	}
}
