package com.prinova.messagepoint.model.admin;

import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.wtu.ReferencableObject;
import org.apache.commons.lang.StringUtils;

import com.prinova.messagepoint.model.ComplexValue;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.DocumentObjectModel;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.hibernate.query.NativeQuery;

import java.math.BigInteger;
import java.nio.charset.Charset;
import java.util.*;

public abstract class ConnectorConfiguration extends DocumentObjectModel {

	private static final long serialVersionUID = 6372106705926124396L;
	
	public final static int 	FLAT_FILE	=1;
	public final static int 	XML_FILE	=2;
	
	public final static int     ENCODING_AUTO_DETECT 	= 0;
	public final static int     ENCODING_WINDOWS_1252 	= 1;
	public final static int     ENCODING_WINDOWS_1250 	= 2;
	public final static int     ENCODING_UTF_8 			= 3;
	public final static int     ENCODING_EBCDIC 		= 4;
	
	public final static String     CODE_ENCODING_AUTO_DETECT 	= "page.label.auto.detect";
	public final static String     CODE_ENCODING_WINDOWS_1252 	= "page.label.windows1252";
	public final static String     CODE_ENCODING_WINDOWS_1250 	= "page.label.windows1250";
	public final static String     CODE_ENCODING_UTF_8 			= "page.label.utf8";
	public final static String     CODE_ENCODING_EBCDIC 		= "page.label.EBCDIC";

	private Connector connector;
	private QualificationOutput qualificationOutput;
	
	private String customerDriverInputFileName;
	
	// On Prem Configuration
	private boolean executeInCloudTest 				= true;
	private boolean executeInCloudPreview 			= true;
	private boolean executeInCloudProof 			= true;
	private boolean executeInCloudSimulation		= true;
	
	private String preQualEngineScript;
	private String postQualEngineScript;
	private String postConnectorScript;
	
	private boolean applyFilenamesToBundledImages 	= false;
	private String filenameSeparatorForBundledImages;
	private boolean playMessageOnEmptyVar			= false;
	
	// Override remote server
	private boolean overrideRemote = false;
	private String remoteServerIP;
	private String remoteServerPort;
	private String remoteServerUser;
	private String remoteServerPassword;

	// DEServerId
	private String DEServerGuid;
	private String BundleNameOverride;
	
	private String appliedDeVersion;
	
	private int	inputCharacterEncoding = 0;
	private int	outputCharacterEncoding = 0;
	
	private ComplexValue outputFilename;
	private ComplexValue outputDocumentTitle;

	private Boolean validateProductionBundle		= false;
	private Boolean escapeTagsInDriverData			= true;
	private Boolean convertTableBorderPxToPts		= false;
	private Boolean evalNotEqualOnMissingTag		= true;
	private Boolean playEmptyAggFirstLastVar		= true;
	private Boolean removeZeroFromStyleConnector	= true;
	private Boolean compTimeParentTagging 			= true;
	private Boolean dataGroupExpressionVarProc		= true;
	private Boolean scriptVarAppliesUndefined		= true;
	private Boolean correctParagraphTextStyles		= true;
	private Boolean fixInlineTargetingStyles		= true;
	private Boolean preserveDataWhitespace			= true;
	private Boolean nbspComposedAsSpace				= true;
	private Boolean normalizeImageLibrary			= true;
	private Boolean normalizeEmbeddedContent		= true;
	private Boolean gmcSpanToTTag					= false;
	private Boolean blueUnderlineLinks				= false;
	private Boolean unalteredZonePDFPassthrough		= true;

	private int listStyleControlType				= StyleControlType.ID_FULL;
	private int colorOutputFormatType				= ColorOutputFormatType.ID_CMYK;
	protected boolean needRecalculateHash = true;
	protected String sha256Hash;

	private int useDefaultImage = UseDefaultImageType.ID_TEST;

	// Public default constructor
	//
	public ConnectorConfiguration()
	{
	}

	// Copy constructor (not public) for cloning 
	//
	protected ConnectorConfiguration(ConnectorConfiguration cloneFrom)
	{
		super(cloneFrom, cloneFrom.getDocument());

        this.connector = CloneHelper.assignConstObject(cloneFrom.connector);
        this.qualificationOutput = CloneHelper.assignConstObject(cloneFrom.qualificationOutput);
		
		this.customerDriverInputFileName = cloneFrom.customerDriverInputFileName;
		
		this.preQualEngineScript = cloneFrom.preQualEngineScript; 
		this.postQualEngineScript = cloneFrom.postQualEngineScript;
		this.postConnectorScript = cloneFrom.postConnectorScript;
		
		this.executeInCloudTest = cloneFrom.executeInCloudTest;
		this.executeInCloudPreview = cloneFrom.executeInCloudPreview;
		this.executeInCloudProof = cloneFrom.executeInCloudProof;
		this.executeInCloudSimulation = cloneFrom.executeInCloudSimulation;
		
		this.applyFilenamesToBundledImages = cloneFrom.applyFilenamesToBundledImages;
		this.filenameSeparatorForBundledImages = cloneFrom.filenameSeparatorForBundledImages;
		this.playMessageOnEmptyVar = cloneFrom.playMessageOnEmptyVar;
		
		this.overrideRemote = cloneFrom.overrideRemote;
		this.remoteServerIP = cloneFrom.remoteServerIP;
		this.remoteServerPort = cloneFrom.remoteServerPort;
		this.remoteServerUser = cloneFrom.remoteServerUser;
		this.remoteServerPassword = cloneFrom.remoteServerPassword;
		
		this.appliedDeVersion = cloneFrom.appliedDeVersion;
		
		this.inputCharacterEncoding = cloneFrom.inputCharacterEncoding;
		this.outputCharacterEncoding = cloneFrom.outputCharacterEncoding;
		
		if (cloneFrom.outputFilename != null) {
		    this.outputFilename = CloneHelper.clone(cloneFrom.outputFilename);
		}
		if (cloneFrom.outputDocumentTitle != null) {
		    this.outputDocumentTitle = CloneHelper.clone(cloneFrom.outputDocumentTitle);
		}

		this.validateProductionBundle = cloneFrom.validateProductionBundle;
		this.escapeTagsInDriverData = cloneFrom.escapeTagsInDriverData;
		this.convertTableBorderPxToPts = cloneFrom.convertTableBorderPxToPts;
		this.evalNotEqualOnMissingTag = cloneFrom.evalNotEqualOnMissingTag;
		this.playEmptyAggFirstLastVar = cloneFrom.playEmptyAggFirstLastVar;
		this.removeZeroFromStyleConnector = cloneFrom.removeZeroFromStyleConnector;
		this.compTimeParentTagging = cloneFrom.compTimeParentTagging;
		this.dataGroupExpressionVarProc = cloneFrom.dataGroupExpressionVarProc;
		this.scriptVarAppliesUndefined = cloneFrom.scriptVarAppliesUndefined;
		this.correctParagraphTextStyles = cloneFrom.correctParagraphTextStyles;
		this.fixInlineTargetingStyles = cloneFrom.fixInlineTargetingStyles;
		this.preserveDataWhitespace = cloneFrom.preserveDataWhitespace;
		this.nbspComposedAsSpace = cloneFrom.nbspComposedAsSpace;
		this.normalizeImageLibrary = cloneFrom.normalizeImageLibrary;
		this.normalizeEmbeddedContent = cloneFrom.normalizeEmbeddedContent;
		this.blueUnderlineLinks = cloneFrom.blueUnderlineLinks;
		this.unalteredZonePDFPassthrough = cloneFrom.unalteredZonePDFPassthrough;
		this.listStyleControlType = cloneFrom.listStyleControlType;
		this.colorOutputFormatType = cloneFrom.colorOutputFormatType;
		this.gmcSpanToTTag = cloneFrom.gmcSpanToTTag;
		this.useDefaultImage = cloneFrom.useDefaultImage;
	}

	public ConnectorConfiguration clone(Document document) {
		Document originDocument = this.getDocument();
		this.setDocument(document);
		ConnectorConfiguration clonedObject = (ConnectorConfiguration) this.clone();
		this.setDocument(originDocument);
		return clonedObject;
	}

	public <T extends ConnectorConfiguration> void copy(T copyFrom){
		setConnector(Connector.findById(copyFrom.getConnector().getId()));
		setQualificationOutput(QualificationOutput.findById(copyFrom.getQualificationOutput().getId()));

		setCustomerDriverInputFileName(copyFrom.getCustomerDriverInputFileName());

		setExecuteInCloudPreview(copyFrom.isExecuteInCloudPreview());
		setExecuteInCloudProof(copyFrom.isExecuteInCloudProof());
		setExecuteInCloudSimulation(copyFrom.isExecuteInCloudSimulation());
		setExecuteInCloudTest(copyFrom.isExecuteInCloudTest());

		setPreQualEngineScript(copyFrom.getPreQualEngineScript());
		setPostConnectorScript(copyFrom.getPostConnectorScript());
		setPostQualEngineScript(copyFrom.getPostQualEngineScript());

		setApplyFilenamesToBundledImages(copyFrom.getApplyFilenamesToBundledImages());
		setFilenameSeparatorForBundledImages(copyFrom.getFilenameSeparatorForBundledImages());
		setPlayMessageOnEmptyVar(copyFrom.getPlayMessageOnEmptyVar());

		setOverrideRemote(copyFrom.isOverrideRemote());
		setRemoteServerIP(copyFrom.getRemoteServerIP());
		setRemoteServerPort(copyFrom.getRemoteServerPort());
		setRemoteServerUser(copyFrom.getRemoteServerUser());
		setRemoteServerPassword(copyFrom.getRemoteServerPassword());
		setAppliedDeVersion(copyFrom.getAppliedDeVersion());
		setInputCharacterEncoding(copyFrom.getInputCharacterEncoding());
		setOutputCharacterEncoding(copyFrom.getOutputCharacterEncoding());
		setDEServerGuid(copyFrom.getDEServerGuid());
		setBundleNameOverride(copyFrom.getBundleNameOverride());

		setOutputFilename(CloneHelper.assign(copyFrom.getOutputFilename()));
		setOutputDocumentTitle(CloneHelper.assign(copyFrom.getOutputDocumentTitle()));

		setValidateProductionBundle(copyFrom.getValidateProductionBundle());
		setEscapeTagsInDriverData(copyFrom.getEscapeTagsInDriverData());
		setConvertTableBorderPxToPts(copyFrom.getConvertTableBorderPxToPts());
		setEvalNotEqualOnMissingTag(copyFrom.getEvalNotEqualOnMissingTag());
		setPlayEmptyAggFirstLastVar(copyFrom.getPlayEmptyAggFirstLastVar());
		setRemoveZeroFromStyleConnector(copyFrom.getRemoveZeroFromStyleConnector());

		setCompTimeParentTagging(copyFrom.getCompTimeParentTagging());
		setDataGroupExpressionVarProc(copyFrom.getDataGroupExpressionVarProc());
		setScriptVarAppliesUndefined(copyFrom.getScriptVarAppliesUndefined());
		setCorrectParagraphTextStyles(copyFrom.getCorrectParagraphTextStyles());
		setFixInlineTargetingStyles(copyFrom.getFixInlineTargetingStyles());
		setPreserveDataWhitespace(copyFrom.getPreserveDataWhitespace());
		setNbspComposedAsSpace(copyFrom.getNbspComposedAsSpace());
		setNormalizeImageLibrary(copyFrom.getNormalizeImageLibrary());
		setNormalizeEmbeddedContent(copyFrom.getNormalizeEmbeddedContent());
		setBlueUnderlineLinks(copyFrom.getBlueUnderlineLinks());
		setListStyleControlType(copyFrom.getListStyleControlType());
		setGmcSpanToTTag(copyFrom.getGmcSpanToTTag());

		setUseDefaultImage(copyFrom.getUseDefaultImage());
	}

	@Override
	public void preSave(Boolean isNew) {
		super.preSave(isNew);
		if(needRecalculateHash) {
			makeHash(false);
		}
	}

	protected String getHash(){
		String objectHashKey = getObjectHashKey();
		StringBuilder hashDataStringBuilder = new StringBuilder();

		hashDataStringBuilder.append("connectorConfiguration");

		if(getConnector() != null){
			hashDataStringBuilder.append(" connectorId: ").append(getConnector().getId());
		}

		if(getQualificationOutput() != null){
			hashDataStringBuilder.append(" qualificationOutputId: ").append(getQualificationOutput().getId());
		}

		if(StringUtils.isNotBlank(getCustomerDriverInputFileName())){
			hashDataStringBuilder.append(" customerDriverInputFileName: ").append(getCustomerDriverInputFileName());
		}


		hashDataStringBuilder.append(" executeInCloudTest: ").append(isExecuteInCloudTest());
		hashDataStringBuilder.append(" executeInCloudPreview: ").append(isExecuteInCloudPreview());
		hashDataStringBuilder.append(" executeInCloudProof: ").append(isExecuteInCloudProof());
		hashDataStringBuilder.append(" executeInCloudSimulation: ").append(isExecuteInCloudSimulation());

		if(StringUtils.isNotBlank(getPreQualEngineScript())){
			hashDataStringBuilder.append(" preQualEngineScript: ").append(getPreQualEngineScript());
		}
		if(StringUtils.isNotBlank(getPostQualEngineScript())){
			hashDataStringBuilder.append(" postQualEngineScript: ").append(getPostQualEngineScript());
		}
		if(StringUtils.isNotBlank(getPostConnectorScript())){
			hashDataStringBuilder.append(" postConnectorScript: ").append(getPostConnectorScript());
		}

		hashDataStringBuilder.append(" applyFilenamesToBundledImages: ").append(getApplyFilenamesToBundledImages());
		if(StringUtils.isNotBlank(getFilenameSeparatorForBundledImages())){
			hashDataStringBuilder.append(" filenameSeparatorForBundledImages: ").append(getFilenameSeparatorForBundledImages());
		}
		hashDataStringBuilder.append(" playMessageOnEmptyVar: ").append(getPlayMessageOnEmptyVar());

		hashDataStringBuilder.append(" overrideRemote: ").append(isOverrideRemote());
		if(StringUtils.isNotBlank(getRemoteServerIP())){
			hashDataStringBuilder.append(" remoteServerIP: ").append(getRemoteServerIP());
		}
		if(StringUtils.isNotBlank(getRemoteServerPort())){
			hashDataStringBuilder.append(" remoteServerPort: ").append(getRemoteServerPort());
		}
		if(StringUtils.isNotBlank(getRemoteServerUser())){
			hashDataStringBuilder.append(" remoteServerUser: ").append(getRemoteServerUser());
		}
		if(StringUtils.isNotBlank(getRemoteServerPassword())){
			hashDataStringBuilder.append(" remoteServerPassword: ").append(getRemoteServerPassword());
		}
		if(StringUtils.isNotBlank(getAppliedDeVersion())){
			hashDataStringBuilder.append(" appliedDeVersion: ").append(getAppliedDeVersion());
		}
		hashDataStringBuilder.append(" inputCharacterEncoding: ").append(getInputCharacterEncoding());
		hashDataStringBuilder.append(" outputCharacterEncoding: ").append(getOutputCharacterEncoding());

		if(StringUtils.isNotBlank(getDEServerGuid())){
			hashDataStringBuilder.append(" DEServerGuid: ").append(getDEServerGuid());
		}
		if(StringUtils.isNotBlank(getBundleNameOverride())){
			hashDataStringBuilder.append(" BundleNameOverride: ").append(getBundleNameOverride());
		}

		if(getOutputFilename() != null){
			hashDataStringBuilder.append(" outputFilenameHash: ").append(getOutputFilename().getSha256Hash());
		}
		if(getOutputDocumentTitle() != null){
			hashDataStringBuilder.append(" outputDocumentTitleHash: ").append(getOutputDocumentTitle().getSha256Hash());
		}

		hashDataStringBuilder.append(" validateProductionBundle: ").append(getValidateProductionBundle());
		hashDataStringBuilder.append(" escapeTagsInDriverData: ").append(getEscapeTagsInDriverData());
		hashDataStringBuilder.append(" convertTableBorderPxToPts: ").append(getConvertTableBorderPxToPts());
		hashDataStringBuilder.append(" evalNotEqualOnMissingTag: ").append(getEvalNotEqualOnMissingTag());
		hashDataStringBuilder.append(" playEmptyAggFirstLastVar: ").append(getPlayEmptyAggFirstLastVar());
		hashDataStringBuilder.append(" removeZeroFromStyleConnector: ").append(getRemoveZeroFromStyleConnector());
		hashDataStringBuilder.append(" compTimeParentTagging: ").append(getCompTimeParentTagging());
		hashDataStringBuilder.append(" dataGroupExpressionVarProc: ").append(getDataGroupExpressionVarProc());
		hashDataStringBuilder.append(" scriptVarAppliesUndefined: ").append(getScriptVarAppliesUndefined());
		hashDataStringBuilder.append(" correctParagraphTextStyles: ").append(getCorrectParagraphTextStyles());
		hashDataStringBuilder.append(" fixInlineTargetingStyles: ").append(getFixInlineTargetingStyles());
		hashDataStringBuilder.append(" preserveDataWhitespace: ").append(getPreserveDataWhitespace());
		hashDataStringBuilder.append(" nbspComposedAsSpace: ").append(getNbspComposedAsSpace());
		hashDataStringBuilder.append(" normalizeImageLibrary: ").append(getNormalizeImageLibrary());
		hashDataStringBuilder.append(" normalizeEmbeddedContent: ").append(getNormalizeEmbeddedContent());
		hashDataStringBuilder.append(" blueUnderlineLinks: ").append(getBlueUnderlineLinks());
		hashDataStringBuilder.append(" listStyleControlType: ").append(getListStyleControlType());
		hashDataStringBuilder.append(" colorOutputFormatType: ").append(getColorOutputFormatType());
		hashDataStringBuilder.append(" gmcSpanToTTag: ").append(getGmcSpanToTTag());
		hashDataStringBuilder.append(" useDefaultImage: ").append(getUseDefaultImage());

		if(getDocument().getCommunicationsDataResource() != null){
			hashDataStringBuilder.append(" docCommunicationsDataResourceDna: ").append(getDocument().getCommunicationsDataResource().getDna());
		}
		if(StringUtils.isNotBlank(getDocument().getConnectorName())){
			hashDataStringBuilder.append(" docConnectorName: ").append(getDocument().getConnectorName());
		}
		hashDataStringBuilder.append(" docProcessUsingCombinedContent: ").append(getDocument().isProcessUsingCombinedContent());

		return calculateSha256Hash(objectHashKey, false, null, hashDataStringBuilder.toString());
	}
	public static ConnectorConfiguration findById(long id) {
		return HibernateUtil.getManager().getObject(ConnectorConfiguration.class, id);
	}

	public static List<ConnectorConfiguration> findAllForDocument(Document document) {
		List<ConnectorConfiguration> connectorConfigurations = new ArrayList<>();

		if(document != null){
			connectorConfigurations.add(HibernateUtil.getManager().getObject(ConnectorConfiguration.class, document.getId()));
			document.getChannelAlternateDocuments().forEach(d -> connectorConfigurations.add(HibernateUtil.getManager().getObject(ConnectorConfiguration.class, d.getId())));
		}

		return connectorConfigurations;
	}

	@Override
	public String getName() {
		return getDocument().getName();
	}

	public String getAppliedDeVersion() {
		return appliedDeVersion;
	}
	public void setAppliedDeVersion(String appliedDeVersion) {
		this.appliedDeVersion = appliedDeVersion;
	}
	public String getCustomerDriverInputFileName() {
		return customerDriverInputFileName;
	}
	public void setCustomerDriverInputFileName(String customerDriverInputFileName) {
		this.customerDriverInputFileName = customerDriverInputFileName;
	}
	
	public Connector getConnector() {
		return connector;
	}
	public void setConnector(Connector connector) {
		this.connector = connector;
	}
	
	public String getPubFile(){return null;}
	public void setFileName(String fileName){}
	public void setFileType(int fileType){}
	public void setFormatType(int formatType){}
	public void setPubFile(String putFileName){}
	public void setWorkflowFile(String workflowFileName){}
	
	public void setQualificationOutput(QualificationOutput qualificationOutput) {
		this.qualificationOutput = qualificationOutput;
	}
	public QualificationOutput getQualificationOutput() {
		return qualificationOutput;
	}

	public Channel getChannel() {
		return this.connector.getChannel();
	}
	public boolean isExecuteInCloudTest() {
		return executeInCloudTest;
	}
	public void setExecuteInCloudTest(boolean executeInCloudTest) {
		this.executeInCloudTest = executeInCloudTest;
	}
	public boolean isExecuteInCloudPreview() {
		return executeInCloudPreview;
	}
	public void setExecuteInCloudPreview(boolean executeInCloudPreview) {
		this.executeInCloudPreview = executeInCloudPreview;
	}
	public boolean isExecuteInCloudProof() {
		return executeInCloudProof;
	}
	public void setExecuteInCloudProof(boolean executeInCloudProof) {
		this.executeInCloudProof = executeInCloudProof;
	}
	public boolean isExecuteInCloudSimulation() {
		return executeInCloudSimulation;
	}
	public void setExecuteInCloudSimulation(boolean executeInCloudSimulation) {
		this.executeInCloudSimulation = executeInCloudSimulation;
	}
	public boolean getApplyFilenamesToBundledImages() {
		return applyFilenamesToBundledImages;
	}
	public void setApplyFilenamesToBundledImages(
			boolean applyFilenamesToBundledImages) {
		this.applyFilenamesToBundledImages = applyFilenamesToBundledImages;
	}
	public String getFilenameSeparatorForBundledImages() { return filenameSeparatorForBundledImages; }
	public void setFilenameSeparatorForBundledImages(String filenameSeparatorForBundledImages) {
		this.filenameSeparatorForBundledImages = filenameSeparatorForBundledImages;
	}
	public boolean getPlayMessageOnEmptyVar() {
		return playMessageOnEmptyVar;
	}
	public void setPlayMessageOnEmptyVar(boolean playMessageOnEmptyVar) {
		this.playMessageOnEmptyVar = playMessageOnEmptyVar;
	}
	public boolean isOverrideRemote() {
		return overrideRemote;
	}
	public void setOverrideRemote(boolean overrideRemote) {
		this.overrideRemote = overrideRemote;
	}
	public String getRemoteServerIP() {
		return remoteServerIP;
	}
	public void setRemoteServerIP(String remoteServerIP) {
		this.remoteServerIP = remoteServerIP;
	}
	public String getRemoteServerPort() {
		return remoteServerPort;
	}
	public void setRemoteServerPort(String remoteServerPort) {
		this.remoteServerPort = remoteServerPort;
	}
	public String getRemoteServerUser(){
		return remoteServerUser;
	}
	public void setRemoteServerUser(String remoteServerUser){
		this.remoteServerUser = remoteServerUser;
	}
	public String getRemoteServerPassword(){
		return remoteServerPassword;
	}
	public void setRemoteServerPassword(String remoteServerPassword){
		this.remoteServerPassword = remoteServerPassword;
	}
	public String getPasswordAsHashCode(){
		return UserUtil.encryptPassword(remoteServerPassword);
	}
	
	public int getInputCharacterEncoding() {
		return inputCharacterEncoding;
	}

	public void setInputCharacterEncoding(int inputCharacterEncoding) {
		this.inputCharacterEncoding = inputCharacterEncoding;
	}

	public int getOutputCharacterEncoding() {
		return outputCharacterEncoding;
	}

	public void setOutputCharacterEncoding(int outputCharacterEncoding) {
		this.outputCharacterEncoding = outputCharacterEncoding;
	}

	public String getPreQualEngineScript() {
		return preQualEngineScript;
	}

	public void setPreQualEngineScript(String preQualEngineScript) {
		this.preQualEngineScript = preQualEngineScript;
	}

	public String getPostQualEngineScript() {
		return postQualEngineScript;
	}

	public void setPostQualEngineScript(String postQualEngineScript) {
		this.postQualEngineScript = postQualEngineScript;
	}

	public String getPostConnectorScript() {
		return postConnectorScript;
	}

	public void setPostConnectorScript(String postConnectorScript) {
		this.postConnectorScript = postConnectorScript;
	}

	public ComplexValue getOutputFilename() {
		return outputFilename;
	}

	public void setOutputFilename(ComplexValue outputFilename) {
		this.outputFilename = outputFilename;
	}

	public ComplexValue getOutputDocumentTitle() {
		return outputDocumentTitle;
	}

	public void setOutputDocumentTitle(ComplexValue outputDocumentTitle) {
		this.outputDocumentTitle = outputDocumentTitle;
	}

	public Boolean getValidateProductionBundle() {
		return validateProductionBundle;
	}

	public void setValidateProductionBundle(Boolean validateProductionBundle) {
		this.validateProductionBundle = validateProductionBundle;
	}

	public Boolean getEscapeTagsInDriverData() { return escapeTagsInDriverData; }
	public void setEscapeTagsInDriverData(Boolean escapeTagsInDriverData) {
		this.escapeTagsInDriverData = escapeTagsInDriverData;
	}

	public Boolean getConvertTableBorderPxToPts() { return convertTableBorderPxToPts; }
	public void setConvertTableBorderPxToPts(Boolean convertTableBorderPxToPts) {
		this.convertTableBorderPxToPts = convertTableBorderPxToPts;
	}

	public Boolean getEvalNotEqualOnMissingTag() { return evalNotEqualOnMissingTag; }
	public void setEvalNotEqualOnMissingTag(Boolean evalNotEqualOnMissingTag) {
		this.evalNotEqualOnMissingTag = evalNotEqualOnMissingTag;
	}

	public Boolean getPlayEmptyAggFirstLastVar() { return playEmptyAggFirstLastVar; }
	public void setPlayEmptyAggFirstLastVar(Boolean playEmptyAggFirstLastVar) {
		this.playEmptyAggFirstLastVar = playEmptyAggFirstLastVar;
	}

	public Boolean getRemoveZeroFromStyleConnector() { return removeZeroFromStyleConnector; }
	public void setRemoveZeroFromStyleConnector(Boolean removeZeroFromStyleConnector) {
		this.removeZeroFromStyleConnector = removeZeroFromStyleConnector;
	}

	public Boolean getCompTimeParentTagging() { return compTimeParentTagging; }
	public void setCompTimeParentTagging(Boolean compTimeParentTagging) {
		this.compTimeParentTagging = compTimeParentTagging;
	}

	public Boolean getDataGroupExpressionVarProc() { return dataGroupExpressionVarProc; }
	public void setDataGroupExpressionVarProc(Boolean dataGroupExpressionVarProc) {
		this.dataGroupExpressionVarProc = dataGroupExpressionVarProc;
	}

	public Boolean getScriptVarAppliesUndefined() { return scriptVarAppliesUndefined; }
	public void setScriptVarAppliesUndefined(Boolean scriptVarAppliesUndefined) {
		this.scriptVarAppliesUndefined = scriptVarAppliesUndefined;
	}

	public Boolean getCorrectParagraphTextStyles() { return correctParagraphTextStyles;	}
	public void setCorrectParagraphTextStyles(Boolean correctParagraphTextStyles) {
		this.correctParagraphTextStyles = correctParagraphTextStyles;
	}

	public Boolean getFixInlineTargetingStyles() { return fixInlineTargetingStyles; }
	public void setFixInlineTargetingStyles(Boolean fixInlineTargetingStyles) {
		this.fixInlineTargetingStyles = fixInlineTargetingStyles;
	}

	public Boolean getPreserveDataWhitespace() { return preserveDataWhitespace; }
	public void setPreserveDataWhitespace(Boolean preserveDataWhitespace) {
		this.preserveDataWhitespace = preserveDataWhitespace;
	}

	public Boolean getNbspComposedAsSpace() { return nbspComposedAsSpace; }
	public void setNbspComposedAsSpace(Boolean nbspComposedAsSpace) {
		this.nbspComposedAsSpace = nbspComposedAsSpace;
	}

	public Boolean getNormalizeImageLibrary() { return normalizeImageLibrary; }
	public void setNormalizeImageLibrary(Boolean normalizeImageLibrary) {
		this.normalizeImageLibrary = normalizeImageLibrary;
	}

	public Boolean getNormalizeEmbeddedContent() { return normalizeEmbeddedContent; }
	public void setNormalizeEmbeddedContent(Boolean normalizeEmbeddedContent) {
		this.normalizeEmbeddedContent = normalizeEmbeddedContent;
	}

	public Boolean getGmcSpanToTTag() { return gmcSpanToTTag; }
	public void setGmcSpanToTTag(Boolean gmcSpanToTTag) {

		this.gmcSpanToTTag = gmcSpanToTTag;
	}

	public Boolean getBlueUnderlineLinks() { return blueUnderlineLinks; }
	public void setBlueUnderlineLinks(Boolean blueUnderlineLinks) {
		this.blueUnderlineLinks = blueUnderlineLinks;
	}

	public Boolean getUnalteredZonePDFPassthrough() { return unalteredZonePDFPassthrough; }
	public void setUnalteredZonePDFPassthrough(Boolean unalteredZonePDFPassthrough) {
		this.unalteredZonePDFPassthrough = unalteredZonePDFPassthrough;
	}

	public int getListStyleControlType() {
		return listStyleControlType;
	}

	public void setListStyleControlType(int listStyleControlType) { this.listStyleControlType = listStyleControlType; }

	public int getColorOutputFormatType() {
		return colorOutputFormatType;
	}

	public void setColorOutputFormatType(int colorOutputFormatType) { this.colorOutputFormatType = colorOutputFormatType; }

	public String getDEServerGuid() {
		return DEServerGuid;
	}

	public void setDEServerGuid(String DEServerGuid) {
		this.DEServerGuid = DEServerGuid;
	}

	public String getBundleNameOverride() {
		return BundleNameOverride;
	}

	public void setBundleNameOverride(String bundleNameOverride) {
		BundleNameOverride = bundleNameOverride;
	}

	public int getUseDefaultImage() {return this.useDefaultImage;}

	public void setUseDefaultImage(int useDefaultImage) {this.useDefaultImage = useDefaultImage;}

	public static String getMessageCodeForCharactorEncode(int code){
		String messageCode;
		switch(code){
			case ENCODING_WINDOWS_1252:
				messageCode = ApplicationUtil.getMessage(CODE_ENCODING_WINDOWS_1252);
				break;
			case ENCODING_WINDOWS_1250:
				messageCode = ApplicationUtil.getMessage(CODE_ENCODING_WINDOWS_1250);
				break;
			case ENCODING_UTF_8:
				messageCode = ApplicationUtil.getMessage(CODE_ENCODING_UTF_8);
				break;
			case ENCODING_EBCDIC:
				messageCode = ApplicationUtil.getMessage(CODE_ENCODING_EBCDIC);
				break;
			default:
				messageCode = ApplicationUtil.getMessage(CODE_ENCODING_AUTO_DETECT);
				break;
		}
		return messageCode;
	}

	@Override
	public String getSha256Hash() {
		return sha256Hash;
	}
	@Override
	public void setSha256Hash(String sha256Hash) {
		this.sha256Hash = sha256Hash;
	}

	public Charset GetInputEncodingCharset() {
		switch (this.inputCharacterEncoding) {
			case ENCODING_WINDOWS_1252:
				return Charset.forName("windows-1252");
			case ENCODING_WINDOWS_1250:
				return Charset.forName("windows-1250");
			case ENCODING_EBCDIC:
				return Charset.forName("IBM500");
			case ENCODING_UTF_8:
				return Charset.forName("UTF-8");
			default:
				return Charset.defaultCharset();
		}
	}

	public boolean isPrint() {
		if (this.getConnector() == null)
			return false;
		return 	this.getConnector().getId() == Connector.DIALOGUE_FILE_ID ||
				this.getConnector().getId() == Connector.GMC_FILE_ID ||
				this.getConnector().getId() == Connector.NATIVE_COMPOSITION_FILE_ID ||
				this.getConnector().getId() == Connector.SEFAS_FILE_ID ||
				this.getConnector().getId() == Connector.MPHCS_FILE_ID;
	}

	public boolean isEmail() {
		if (this.getConnector() == null) {
			return false;
		}
		return this.getConnector().getChannel().getId() == Channel.CHANNEL_EMAIL_ID;
	}

	public boolean isWeb() {
		if (this.getConnector() == null)
			return false;
		return this.getConnector().getChannel().getId() == Channel.CHANNEL_WEB_ID;
	}

	public static Map<Long, List<ReferencableObject>> getAllReferencesMap() {
		Map<Long, List<ReferencableObject>> allReferencesMap = new HashMap<>();
//		Map<Long, Set<Long>> alreadyMappedObjects = new HashMap<>();
//
//		{
//			String query =
//					"select c.id as config_id, cfs.id as file_set_id from dialogue_configuration c\n" +
//							"    inner join composition_file_set cfs on c.composition_file_set_id = cfs.id\n" +
//							"union\n" +
//							"select c.id as config_id, cfs.id as file_set_id from gmc_configuration c\n" +
//							"    inner join composition_file_set cfs on c.composition_file_set_id = cfs.id\n" +
//							"union\n" +
//							"select c.id as config_id, cfs.id as file_set_id from sefas_configuration c\n" +
//							"    inner join composition_file_set cfs on c.composition_file_set_id = cfs.id\n" +
//							"union\n" +
//							"select c.id as config_id, cfs.id as file_set_id from mphcs_configuration c\n" +
//							"    inner join composition_file_set cfs on c.composition_file_set_id = cfs.id";
//			getAllReferencesMapByQuery(query, CompositionFileSet.class, allReferencesMap, alreadyMappedObjects);
//		}

		return allReferencesMap;
	}

	private static void getAllReferencesMapByQuery(String query, Class<? extends IdentifiableMessagePointModel> referencesClass, Map<Long, List<ReferencableObject>> allReferencesMap, Map<Long, Set<Long>> alreadyMappedObjects) {
		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
		for(Object obj : sqlQuery.list()){
			Object[] fields = (Object[])obj;
			BigInteger compositionFileSetId = (BigInteger)fields[1];
			BigInteger configId = (BigInteger)fields[0];

			ConnectorConfiguration connectorConfiguration = ConnectorConfiguration.findById(configId.longValue());
			IdentifiableMessagePointModel referencingModelObject = HibernateUtil.getManager().getObject(referencesClass, compositionFileSetId.longValue());

			if(connectorConfiguration != null && referencingModelObject != null) {
				Set<Long> mappedObjects = alreadyMappedObjects.get(connectorConfiguration.getId());
				if(mappedObjects == null) {
					mappedObjects = new HashSet<>();
					alreadyMappedObjects.put(connectorConfiguration.getId(), mappedObjects);
				}
				if(! mappedObjects.contains(referencingModelObject.getId())) {
					ReferencableObject referencableObject = new ReferencableObject(referencingModelObject);
					List<ReferencableObject> list = allReferencesMap.get(connectorConfiguration.getId());
					if(list == null) {
						list = new ArrayList<>();
						allReferencesMap.put(connectorConfiguration.getId(), list);
					}
					list.add(referencableObject);
					mappedObjects.add(referencingModelObject.getId());
				}
			}
		}
	}
}
