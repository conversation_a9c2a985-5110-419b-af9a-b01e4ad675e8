{"_class": "MSArchiveHeader", "version": 103, "compatibilityVersion": 99, "metadata": {"commit": "4e7e2f5d7940a711b59f89190b5b7e3029f050f5", "appVersion": "50.2", "build": 55047, "app": "com.bohemiancoding.sketch3", "compatibilityVersion": 99, "version": 103, "variant": "NONAPPSTORE"}, "root": {"_class": "sharedStyle", "name": "Body1/Center/Regular/Primary", "value": {"_class": "style", "endDecorationType": 0, "miterLimit": 10, "sharedObjectID": "285E4720-C544-499E-86BF-EDB6177B28DB", "startDecorationType": 0, "textStyle": {"_class": "textStyle", "encodedAttributes": {"paragraphStyle": {"_class": "paragraphStyle", "alignment": 2, "maximumLineHeight": 28.79999923706055, "minimumLineHeight": 28.79999923706055, "allowsDefaultTighteningForTruncation": 0}, "MSAttributedStringFontAttribute": {"_class": "fontDescriptor", "attributes": {"name": "MuseoSans-500", "size": 16}}, "MSAttributedStringColorAttribute": {"_class": "color", "alpha": 1, "blue": 0.4588235294117647, "green": 0.1882352941176471, "red": 0.427450980392157}}, "verticalAlignment": 0}}}}