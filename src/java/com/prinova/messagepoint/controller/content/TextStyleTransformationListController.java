package com.prinova.messagepoint.controller.content;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.ContentEvents;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.font.TextStyleTransformationProfile;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.common.DeleteModelService;
import com.prinova.messagepoint.platform.services.content.ContentTransformService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.FeatureFlag;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

public class TextStyleTransformationListController extends MessagepointController {
	
	private static final Log log = LogUtil.getLog(TextStyleTransformationListController.class);
	
	public static final String REQUEST_PARAM_ACTION 		= "action";

	public static final int ACTION_DELETE 					= 3;
	public static final int ACTION_TRANSFORM 				= 4;

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();

		referenceData.put("documents", Document.findAllDocumentsAndProjectsVisible(true));

		Boolean styleTransformActionEnabled = FeatureFlag.isEnabled(FeatureFlag.Features.StyleTransformActionEnabled, request);
		referenceData.put("styleTransformActionEnabled", styleTransformActionEnabled);

		return referenceData;
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		return new TextStyleTransformationListWrapper();
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}

	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {

		setOriginForm(request, null);
		return super.showForm(request, response, errors);

	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj,
			BindException errors) throws Exception {

		AnalyticsEvent<ContentEvents> analyticsEvent = AnalyticsUtil.requestFor(ContentEvents.TextStyleTransformationProfileList);

		try {
			TextStyleTransformationListWrapper command = (TextStyleTransformationListWrapper)commandObj;
			int action = ServletRequestUtils.getIntParameter(request, REQUEST_PARAM_ACTION, -1);
			TextStyleTransformationProfile textStyleTransformationProfile = command.getSelectedProfile().iterator().next();

            if (action == ACTION_DELETE) {
                analyticsEvent.setAction(Actions.Delete);
                ServiceExecutionContext context = DeleteModelService.createContext(textStyleTransformationProfile.getId(), TextStyleTransformationProfile.class.getName());
                Service deleteModelService = MessagepointServiceFactory.getInstance().lookupService(DeleteModelService.SERVICE_NAME, DeleteModelService.class);
                deleteModelService.execute(context);

                ServiceResponse serviceResponse = context.getResponse();
                if (!serviceResponse.isSuccessful()) {
                    StringBuilder sb = new StringBuilder();
                    sb.append(DeleteModelService.SERVICE_NAME);
                    sb.append(" service call is not successful ");
                    sb.append(" in ").append(this.getClass().getName());
                    sb.append(" text style transformation profile is not deleted. ");
                    log.error(sb.toString());
                    ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
                    return showForm(request, response, errors);
                } else {
                    return new ModelAndView(new RedirectView(getSuccessView()));
                }
            } else if (action == ACTION_TRANSFORM) {
				analyticsEvent.setAction(Actions.Transform);

				ContentTransformWrapper contentTransformWrapper = new ContentTransformWrapper();
				contentTransformWrapper.setSelectedIds(command.getSelectedIds());
				contentTransformWrapper.setTransformAllTouchpoints(command.isTransformAllTouchpoints());
				contentTransformWrapper.setTransformTouchpoints(command.getTransformTouchpoints());
				contentTransformWrapper.setTransformAllObjects(command.isTransformAllObjects());
				contentTransformWrapper.setTransformObjects(command.getTransformObjects());

				ServiceExecutionContext context = ContentTransformService.createContext(contentTransformWrapper);
				Service transformService = MessagepointServiceFactory.getInstance().lookupService(ContentTransformService.SERVICE_NAME, ContentTransformService.class);
				transformService.execute(context);

				ServiceResponse serviceResponse = context.getResponse();
				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(DeleteModelService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					sb.append(" text style transform failed to execute. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);
				} else {
					return new ModelAndView(new RedirectView(getSuccessView()));
				}
			} else {
				return showForm(request, response, errors);
			}

		} finally {
			analyticsEvent.send();
		}
	}
}
