package com.prinova.messagepoint.query.handler;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import com.prinova.messagepoint.model.scenario.AbstractScenario;
import com.prinova.messagepoint.model.scenario.OperationsReportScenario;
import com.prinova.messagepoint.model.scenario.TpDeliveryReportScenario;
import com.prinova.messagepoint.query.DefaultPage;
import com.prinova.messagepoint.query.IPage;

public class ReportTypeHandler implements PostQueryHandler {

	private boolean isOrderAsc;
	
	public boolean isOrderAsc() {
		return isOrderAsc;
	}

	public void setOrderAsc(boolean isOrderAsc) {
		this.isOrderAsc = isOrderAsc;
	}
	
	@SuppressWarnings("unchecked")
	public IPage handle(IPage page) {
		List<AbstractScenario> scenarios = (List<AbstractScenario>)page.getList();
		
		Collections.sort(scenarios, new Comparator<>() {
            public int compare(AbstractScenario o1, AbstractScenario o2) {
                String type1 = getType(o1);
                String type2 = getType(o2);
                if (isOrderAsc()) {
                    return type1.compareTo(type2);
                } else {
                    return type2.compareTo(type1);
                }
            }
        });
		
        ((DefaultPage)page).setList(scenarios);
		return page;
	}
	
	private String getType(AbstractScenario report){
		if(report instanceof OperationsReportScenario){
			return "Batch";
		}else if(report instanceof TpDeliveryReportScenario){
			return "Touchpoint Delivery";
		}else{
			return "Message Delivery";
		}
	}	
}
