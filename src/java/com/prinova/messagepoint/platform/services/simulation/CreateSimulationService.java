package com.prinova.messagepoint.platform.services.simulation;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.model.admin.DataResource;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.interceptor.DeliveryEventCreator;
import com.prinova.messagepoint.model.simulation.Simulation;
import com.prinova.messagepoint.model.util.ContentObjectUtil;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.*;

public class CreateSimulationService extends AbstractService{
	
	public static final String SERVICE_NAME = "simulation.CreateSimulationService";
	private static final Log log = LogUtil.getLog(CreateSimulationService.class);
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			CreateSimulationServiceRequest request = (CreateSimulationServiceRequest)context.getRequest();
			
			Simulation simulation = new Simulation();
			
			simulation.setName(request.getName());
			simulation.setDocument(request.getDocument());
			simulation.setTouchpointCollection(request.getTpCollection());
			simulation.setDataResource(request.getDataResource());
			simulation.setRunDate(request.getRunDate());
			simulation.setRequestDate(DateUtil.now());
			simulation.setUseAllInProcess(request.isUseAllInProcess());
			simulation.setCustomerLevelFlag(request.isCustomerLevelFlag());
			
			simulation.setUserId(UserUtil.getPrincipalUserId());
			simulation.setWorkgroup(UserUtil.getPrincipalUser().getWorkgroup());

			simulation.setDEServerGuid(request.getDEServerGuid());
			simulation.setBundleNameOverride(request.getBundleNameOverride());
			
			Set<ContentObject> messageObjects = new HashSet<>() ;

			if (!simulation.isUseAllInProcess()) {
				if (request.getMessages() != null) {
					for (Integer messageId : request.getMessages()) {
						ContentObject message = ContentObject.findById(messageId.longValue());
						if (message != null) {
							messageObjects.add(message);
						}
					}
					simulation.setMessages(messageObjects);
				}
			} else {
	    		if (simulation.getMessages() == null) {
	    			simulation.setMessages(new HashSet<>());
	    		}
	    		simulation.getMessages().clear();
    			if(simulation.getDocument() != null){
	    			Document doc = HibernateUtil.getManager().getObject(Document.class, simulation.getDocument().getId());
	    			simulation.getMessages().addAll(ContentObjectUtil.getInProcessMessages(doc));
    			}else if(simulation.getTouchpointCollection() != null){
    				TouchpointCollection tpCollection = HibernateUtil.getManager().getObject(TouchpointCollection.class, simulation.getTouchpointCollection().getId());
    				simulation.getMessages().addAll(ContentObjectUtil.getInProcessMessages(tpCollection));
    			}
	    	}
			
	    	HibernateUtil.getManager().saveObject(simulation);
	    	DeliveryEventCreator.createDeliveryEvent( simulation );

	    	// Audit (Execute: Simulation)
	    	AuditEventUtil.push(AuditEventType.ID_EXECUTE, AuditObjectType.ID_SIMULATION, simulation.getName(), simulation.getId(), AuditActionType.ID_SIMULATION_RUN, null);
		} catch (Exception e) {
			log.error(" unexpected exception when invoking CreateSimulationService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
				context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
		
	}
	
	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
		
	}
	public static ServiceExecutionContext createContext(String name,
														Document document,
														TouchpointCollection tpCollection,
														DataResource dataResource,
														Date runDate,
														boolean useAllInProcess,
														boolean customerLevelFlag,
														Set<Integer> messages,
														String DEServerGuid,
														String bundleNameOverride) {
		
		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		CreateSimulationServiceRequest request = new CreateSimulationServiceRequest();
		
		request.setName(name);
		request.setDocument(document);
		request.setTpCollection(tpCollection);
		request.setDataResource(dataResource);
		request.setRunDate(runDate);
		request.setUseAllInProcess(useAllInProcess);
		request.setMessages(messages);
		request.setCustomerLevelFlag(customerLevelFlag);
		request.setDEServerGuid(DEServerGuid);
		request.setBundleNameOverride(bundleNameOverride);
		
		context.setRequest(request);	
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);
		
		return context;
	}
}
