package com.prinova.messagepoint.query.handler;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.query.DefaultPage;
import com.prinova.messagepoint.query.IPage;

public class InsertScheduleStatusHandler implements PostQueryHandler {

	private boolean isOrderAsc;
	
	public boolean isOrderAsc() {
		return isOrderAsc;
	}

	public void setOrderAsc(boolean isOrderAsc) {
		this.isOrderAsc = isOrderAsc;
	}
	
	@SuppressWarnings("unchecked")
	public IPage handle(IPage page) {
		List<InsertSchedule> items = (List<InsertSchedule>)page.getList();
		
		Collections.sort(items, new Comparator<>() {
            public int compare(InsertSchedule o1, InsertSchedule o2) {
                String status1 = o1.getStatusDisplay();
                String status2 = o2.getStatusDisplay();
                if (isOrderAsc()) {
                    return status1.compareTo(status2);
                } else {
                    return status2.compareTo(status1);
                }
            }
        });
		
        ((DefaultPage)page).setList(items);
		return page;
	}
}
