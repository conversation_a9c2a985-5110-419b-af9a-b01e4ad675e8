package com.prinova.messagepoint.connected.touchpointrendering.handler;

import com.prinova.messagepoint.connected.util.ObjectMapperUtil;

import java.util.HashMap;

public class InteractiveResponseFactory {

    private static final String ERROR_STATUS = "error";
    private static final String SUCCESS_STATUS = "success";

    public static String successStringJsonResponse(String successReponse) {
        HashMap responseJson = ObjectMapperUtil.readValueAsObject(successReponse, HashMap.class);
        return success(responseJson);
    }

    public static String error(String... errorMessages) {
        return ObjectMapperUtil.writeValueAsString(new InteractiveResponse<>(ERROR_STATUS, null, errorMessages));
    }

    public static <T> String success(T responseObject){
        return ObjectMapperUtil.writeValueAsString(new InteractiveResponse<>(SUCCESS_STATUS, responseObject));
    }
}
