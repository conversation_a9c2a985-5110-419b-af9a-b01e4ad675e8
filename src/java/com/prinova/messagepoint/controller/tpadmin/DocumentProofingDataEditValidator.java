package com.prinova.messagepoint.controller.tpadmin;

import java.util.Collection;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.model.SystemPropertyKeys.DispatchSettings;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class DocumentProofingDataEditValidator extends MessagepointInputValidator {

	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		DocumentProofingDataEditWrapper command = (DocumentProofingDataEditWrapper) commandObj;

		boolean rangeNotProvidedError = false;
		boolean invalidNumbersError = false;
		boolean fromGreaterThanToError = false;
		boolean rangeTooLargeError = false;
		
		for (String languageCode : command.getProofingDataMap().keySet()) {
			Collection<DocumentProofingDataEditVO> proofingDataVOs = command.getProofingDataMap().get(languageCode).values();
			for (DocumentProofingDataEditVO proofingDataVO : proofingDataVOs) {
				if (proofingDataVO.getDataResource() != null) {

					String fromString = proofingDataVO.getRecepientRangeFrom() == null ? "" : proofingDataVO.getRecepientRangeFrom().trim();
					String toString = proofingDataVO.getRecepientRangeTo() == null ? "" : proofingDataVO.getRecepientRangeTo().trim();
					int fromInt = -1;
					int toInt = -1;

					if (fromString.isEmpty()) {
						if (!rangeNotProvidedError) {
							errors.reject("error.document.proofing.data.customer.range.not.provided");
							rangeNotProvidedError = true;
						}
					} else {
						try {
							fromInt = Integer.parseInt(fromString);
							if (fromInt <= 0 || fromInt > 99999999) {
								if (!invalidNumbersError) {
									errors.reject("error.document.proofing.data.range.must.be.numbers.greater.than.zero");
									invalidNumbersError = true;
								}
							}
						} catch (NumberFormatException e) {
							if (!invalidNumbersError) {
								errors.reject("error.document.proofing.data.range.must.be.numbers.greater.than.zero");
								invalidNumbersError = true;
							}
						}
					}

					if (toString.isEmpty()) {
						if (!rangeNotProvidedError) {
							errors.reject("error.document.proofing.data.customer.range.not.provided");
							rangeNotProvidedError = true;
						}
					} else {
						try {
							toInt = Integer.parseInt(toString);
							if (toInt <= 0 || toInt > 99999999) {
								if (!invalidNumbersError) {
									errors.reject("error.document.proofing.data.range.must.be.numbers.greater.than.zero");
									invalidNumbersError = true;
								}
							}
						} catch (NumberFormatException e) {
							if (!invalidNumbersError) {
								errors.reject("error.document.proofing.data.range.must.be.numbers.greater.than.zero");
								invalidNumbersError = true;
							}
						}
					}

					if (fromInt > 0 && toInt > 0) {
						if (fromInt > toInt) {
							if (!fromGreaterThanToError) {
								errors.reject("error.document.proofing.data.upper.range.must.be.greater.than.lower.range");
								fromGreaterThanToError = true;
							}
						}
					}
					
					SystemPropertyManager spm = SystemPropertyManager.getInstance();
					String maxCustomers = spm.getSystemProperty(DispatchSettings.KEY_JobProofMaxCustomers);
					int proofMaxCusts = 500;
					if(!maxCustomers.equalsIgnoreCase(""))
						proofMaxCusts = Integer.parseInt(maxCustomers);
					
					if ( (toInt - fromInt) > proofMaxCusts )
					{
						if ( !rangeTooLargeError )
						{
							errors.reject("error.document.proof.data.range.too.large");
							rangeTooLargeError = true;
						}
					}
				}
			}
		}
	}
}