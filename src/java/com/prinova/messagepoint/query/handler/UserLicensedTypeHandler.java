package com.prinova.messagepoint.query.handler;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.query.DefaultPage;
import com.prinova.messagepoint.query.IPage;

public class UserLicensedTypeHandler implements PostQueryHandler {

	private boolean isOrderAsc;
	
	public boolean isOrderAsc() {
		return isOrderAsc;
	}

	public void setOrderAsc(boolean isOrderAsc) {
		this.isOrderAsc = isOrderAsc;
	}

	@SuppressWarnings("unchecked")
	@Override
	public IPage handle(IPage page) {
		List<User> users = (List<User>) page.getList();

		Collections.sort(users, new Comparator<>() {
            public int compare(User o1, User o2) {
                int result = 0;

                if (isOrderAsc()) {
                    result = o1.getLicensedTypeName().compareTo(o2.getLicensedTypeName());
                } else {
                    result = o2.getLicensedTypeName().compareTo(o1.getLicensedTypeName());
                }

                // If primary sort is equal, use secondary sort by name
                if (result == 0) {
                    if (isOrderAsc()) {
                        return o1.getName().compareTo(o2.getName());
                    } else {
                        return o2.getName().compareTo(o1.getName());
                    }
                }

                return result;
            }
        });

		((DefaultPage) page).setList(users);
		return page;
	}

}
